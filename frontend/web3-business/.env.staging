# 预发布环境配置
NODE_ENV=production

# 应用配置
VITE_APP_TITLE=萌宠养成代币游戏 (Staging)
VITE_APP_VERSION=1.0.0-staging
VITE_APP_DESCRIPTION=基于区块链的萌宠养成游戏 - 预发布版本

# 区块链网络配置 (测试网)
VITE_CHAIN_ID=11155111
VITE_CHAIN_NAME=Sepolia Testnet
VITE_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
VITE_EXPLORER_URL=https://sepolia.etherscan.io

# 智能合约地址 (测试网合约地址)
VITE_TOKEN_CONTRACT_ADDRESS=******************************************
VITE_TOKEN_V2_CONTRACT_ADDRESS=******************************************

# API配置
VITE_API_BASE_URL=https://staging-api.petgame.com
VITE_WS_URL=wss://staging-ws.petgame.com

# 功能开关
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOG=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=false

# 第三方服务
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GA_TRACKING_ID=

# 游戏配置
VITE_MAX_PETS_PER_USER=3
VITE_DEFAULT_GAME_COINS=10000
VITE_ENABLE_MULTIPLAYER=true

# 安全配置
VITE_ENABLE_CSP=false
VITE_ALLOWED_ORIGINS=https://staging.petgame.com

# 性能配置
VITE_ENABLE_PWA=false
VITE_ENABLE_SERVICE_WORKER=false
VITE_CACHE_MAX_AGE=3600

# 部署配置
VITE_BASE_URL=/
VITE_PUBLIC_PATH=/
VITE_OUTPUT_DIR=dist