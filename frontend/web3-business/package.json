{"name": "pet-token-game", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --force", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "test": "vitest --run", "test:watch": "vitest", "format": "prettier --write src/"}, "dependencies": {"@vant/icons": "^1.8.0", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.3.0", "@vueuse/motion": "^2.0.0", "@walletconnect/ethereum-provider": "^2.10.0", "@walletconnect/modal": "^2.6.1", "autoprefixer": "^10.4.15", "dayjs": "^1.11.9", "ethers": "^6.7.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "uuid": "^9.0.1", "vant": "^4.6.8", "vue": "^3.3.4", "vue-router": "^4.2.4", "vue3-lottie": "^3.1.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.17.15", "@types/uuid": "^9.0.4", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^22.1.0", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.9", "vite-plugin-vue-devtools": "^1.0.0-rc.5", "vitest": "^0.34.4", "vue-tsc": "^1.8.10"}}