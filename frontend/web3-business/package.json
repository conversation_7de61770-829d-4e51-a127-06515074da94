{"name": "pet-token-game", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --force", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "test": "vitest --run", "test:watch": "vitest", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html", "build:clean": "rm -rf dist && npm run build:prod", "preview:staging": "vite preview --mode staging", "preview:prod": "vite preview --mode production", "deploy:staging": "npm run build:staging && npm run deploy:staging:upload", "deploy:prod": "npm run build:prod && npm run deploy:prod:upload", "deploy:staging:upload": "echo 'Upload to staging server'", "deploy:prod:upload": "echo 'Upload to production server'", "test:coverage": "vitest --run --coverage", "test:unit": "vitest --run --config vitest.config.ts", "test:integration": "vitest --run src/services/__tests__/contract-integration.test.ts", "test:e2e": "vitest --run src/test/e2e/", "test:performance": "vitest --run src/test/performance/", "test:components": "vitest --run src/components/", "test:stores": "vitest --run src/stores/", "test:services": "vitest --run src/services/", "test:utils": "vitest --run src/utils/", "test:types": "vitest --run src/types/", "test:composables": "vitest --run src/composables/", "test:ci": "vitest --run --coverage --reporter=json --reporter=html", "test:debug": "vitest --run --reporter=verbose", "test:changed": "vitest related --run", "test:clear-cache": "vitest --run --no-cache", "quality:check": "npm run lint && npm run type-check && npm run test:ci", "quality:fix": "npm run format && npm run lint", "docker:build": "docker build -t pet-token-game .", "docker:run": "docker run -p 3000:3000 pet-token-game", "size:analyze": "npx vite-bundle-analyzer dist/stats.html", "size:check": "npm run build:prod && du -sh dist/*"}, "dependencies": {"@vant/icons": "^1.8.0", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.11.1", "@vueuse/motion": "^2.0.0", "@walletconnect/ethereum-provider": "^2.10.0", "@walletconnect/modal": "^2.6.1", "autoprefixer": "^10.4.15", "dayjs": "^1.11.9", "ethers": "^6.7.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^3.0.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "uuid": "^9.0.1", "vant": "^4.6.8", "vue": "^3.3.4", "vue-router": "^4.2.4", "vue3-lottie": "^3.1.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.17.15", "@types/uuid": "^9.0.4", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^22.1.0", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.9", "vite-plugin-vue-devtools": "^1.0.0-rc.5", "vitest": "^0.34.4", "vue-tsc": "^1.8.10"}}