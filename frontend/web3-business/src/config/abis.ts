// MyTokenProduction 合约 ABI
export const TOKEN_ABI = [
  // 基础 ERC20 函数
  "function name() view returns (string)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
  "function totalSupply() view returns (uint256)",
  "function balanceOf(address) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function transferFrom(address from, address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",

  // 扩展功能
  "function mint(address to, uint256 amount)",
  "function burn(uint256 amount)",
  "function burnFrom(address account, uint256 value)",
  "function pause(string reason)",
  "function unpause()",
  "function paused() view returns (bool)",
  "function owner() view returns (address)",

  // 安全转账函数
  "function safeTransfer(address to, uint256 amount) returns (bool)",
  "function safeApprove(address spender, uint256 amount) returns (bool)",
  "function safeTransferFrom(address from, address to, uint256 amount) returns (bool)",
  "function batchTransfer(address[] recipients, uint256[] amounts) returns (bool)",

  // 查询功能
  "function canTrade(address account) view returns (bool)",
  "function getContractInfo() view returns (uint256 totalSupply_, uint256 maxSupply_, bool tradingEnabled_, bool mintingFinished_, bool antiWhaleEnabled_, uint256 launchTime_)",
  "function version() view returns (string)",
  "function config() view returns (bool tradingEnabled, bool mintingFinished, bool antiWhaleEnabled, uint64 launchTime, uint32 version)",
  "function blacklisted(address) view returns (bool)",
  "function whitelisted(address) view returns (bool)",
  "function lastTransactionTime(address) view returns (uint256)",
  "function transactionCooldown() view returns (uint256)",

  // 管理功能
  "function enableTrading()",
  "function finishMinting()",
  "function setAntiWhaleEnabled(bool enabled)",
  "function updateBlacklist(address account, bool isBlacklisted)",
  "function updateWhitelist(address account, bool isWhitelisted)",
  "function batchUpdateBlacklist(address[] accounts, bool isBlacklisted)",
  "function setTransactionCooldown(uint256 newCooldown)",
  "function transferOwnership(address newOwner)",
  "function renounceOwnership()",
  "function recoverERC20(address tokenAddress, uint256 tokenAmount)",
  "function recoverETH()",

  // 时间锁相关
  "function scheduleTimelockOperation(string operation, bytes data) returns (bytes32)",
  "function executeTimelockOperation(bytes32 operationHash, string operation)",
  "function cancelTimelockOperation(bytes32 operationHash)",
  "function timelockOperations(bytes32) view returns (uint256 executeTime, bool executed, bytes data)",
  "function TIMELOCK_DURATION() view returns (uint256)",

  // 常量
  "function MAX_SUPPLY() view returns (uint256)",
  "function MAX_TX_AMOUNT() view returns (uint256)",
  "function MAX_WALLET_AMOUNT() view returns (uint256)",
  "function LAUNCH_PROTECTION_DURATION() view returns (uint256)",
  "function UPGRADE_INTERFACE_VERSION() view returns (string)",
  "function proxiableUUID() view returns (bytes32)",

  // 事件
  "event Transfer(address indexed from, address indexed to, uint256 value)",
  "event Approval(address indexed owner, address indexed spender, uint256 value)",
  "event TradingEnabled(uint256 timestamp)",
  "event MintCompleted(address indexed to, uint256 amount)",
  "event MintingFinishedEvent(uint256 timestamp)",
  "event SafeTransferCompleted(address indexed from, address indexed to, uint256 amount)",
  "event SafeTransferFromCompleted(address indexed from, address indexed to, address indexed spender, uint256 amount)",
  "event SafeApprovalCompleted(address indexed owner, address indexed spender, uint256 amount)",
  "event BatchTransferCompleted(address indexed from, uint256 recipientsCount, uint256 totalAmount)",
  "event AntiWhaleStatusChanged(bool enabled)",
  "event BlacklistUpdated(address indexed account, bool isBlacklisted)",
  "event WhitelistUpdated(address indexed account, bool isWhitelisted)",
  "event TransactionCooldownChanged(uint256 oldCooldown, uint256 newCooldown)",
  "event ContractPaused(address indexed by, string reason)",
  "event ContractUnpaused(address indexed by)",
  "event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)",
  "event Paused(address account)",
  "event Unpaused(address account)",
  "event TokensRecovered(address indexed token, address indexed to, uint256 amount)",
  "event TimelockOperationScheduled(bytes32 indexed operationHash, uint256 executeTime, string operation)",
  "event TimelockOperationExecuted(bytes32 indexed operationHash, string operation)",
  "event TimelockOperationCancelled(bytes32 indexed operationHash)",
  "event ContractUpgraded(address indexed oldImplementation, address indexed newImplementation)",
  "event Upgraded(address indexed implementation)",
  "event Initialized(uint64 version)"
]

// MyTokenV2Production 合约 ABI (包含税收功能)
export const TOKEN_V2_ABI = [
  ...TOKEN_ABI,

  // V2 税收功能
  "function taxRate() view returns (uint256)",
  "function taxCollector() view returns (address)",
  "function totalTaxCollected() view returns (uint256)",
  "function isExcludedFromTax(address account) view returns (bool)",
  "function getTaxInfo() view returns (uint256 currentTaxRate, address currentTaxCollector, uint256 totalCollected, bool taxEnabled)",
  "function getFullContractInfo() view returns (uint256 totalSupply_, uint256 maxSupply_, bool tradingEnabled_, bool mintingFinished_, bool antiWhaleEnabled_, uint256 launchTime_, uint256 taxRate_, address taxCollector_, uint256 totalTaxCollected_)",
  "function calculateTax(uint256 amount) view returns (uint256 netAmount, uint256 taxAmount)",
  "function calculateTaxForTransfer(address from, address to, uint256 amount) view returns (uint256 netAmount, uint256 taxAmount)",
  "function setExcludedFromTax(address account, bool excluded)",

  // V2 管理功能
  "function initializeV2(uint256 newTaxRate, address newTaxCollector)",
  "function batchSetExcludedFromTax(address[] accounts, bool excluded)",
  "function scheduleTaxRateChange(uint256 newTaxRate)",
  "function executeTaxRateChange()",
  "function cancelTaxRateChange()",
  "function setTaxCollector(address newTaxCollector)",
  "function emergencyDisableTax()",
  "function emergencyResetTaxCollector(address newCollector)",
  "function pendingTaxRate() view returns (uint256 rate, uint256 effectiveTime, bool isPending)",

  // V2 常量
  "function MAX_TAX_RATE() view returns (uint256)",

  // V2 事件
  "event TaxRateChanged(uint256 oldRate, uint256 newRate)",
  "event TaxCollectorChanged(address indexed oldCollector, address indexed newCollector)",
  "event TaxCollected(address indexed from, address indexed to, uint256 taxAmount, uint256 netAmount)",
  "event AddressExcludedFromTax(address indexed account, bool excluded)",
  "event BatchExcludedFromTax(uint256 count, bool excluded)",
  "event TaxSystemEnabled(bool enabled)",
  "event TaxRateChangeScheduled(uint256 currentRate, uint256 pendingRate, uint256 effectiveTime)",
  "event TaxRateChangeExecuted(uint256 oldRate, uint256 newRate)",
  "event TaxRateChangeCancelled(uint256 pendingRate)"
]

// 游戏合约 ABI (如果需要单独的游戏合约)
export const GAME_ABI = [
  // 萌宠相关
  "function createPet(string name, uint8 petType) returns (uint256)",
  "function getPet(uint256 petId) view returns (tuple(uint256 id, string name, uint8 petType, uint256 level, uint256 experience, uint256 health, uint256 happiness, uint8 rarity, uint256 lastFeedTime, uint256 lastPlayTime, uint256 birthTime))",
  "function getUserPets(address user) view returns (uint256[])",
  "function feedPet(uint256 petId)",
  "function playWithPet(uint256 petId)",
  "function trainPet(uint256 petId)",

  // 代币兑换
  "function exchangeTokens(uint256 petId) returns (uint256)",
  "function calculateTokenReward(uint256 petId) view returns (uint256)",

  // 装备系统
  "function equipItem(uint256 petId, uint256 itemId)",
  "function unequipItem(uint256 petId, uint256 itemId)",
  "function getPetEquipment(uint256 petId) view returns (uint256[])",

  // 成就系统
  "function getUserAchievements(address user) view returns (uint256[])",
  "function claimAchievement(uint256 achievementId)",

  // 事件
  "event PetCreated(address indexed owner, uint256 indexed petId, string name, uint8 petType)",
  "event PetLevelUp(uint256 indexed petId, uint256 newLevel)",
  "event TokensExchanged(address indexed user, uint256 indexed petId, uint256 amount)",
  "event AchievementUnlocked(address indexed user, uint256 indexed achievementId)"
]
