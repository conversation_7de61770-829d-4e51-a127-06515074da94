import { PetType, PetRarity, EquipmentType } from '@/types'

// 合约配置
export const CONTRACT_CONFIG = {
  // 合约地址 (从部署文件中获取)
  TOKEN_ADDRESS: '0x4593ed9CbE6003e687e5e77368534bb04b162503', // MyTokenProduction 代理地址
  TOKEN_V2_ADDRESS: '0x75c68e69775fA3E9DD38eA32E554f6BF259C1135', // MyTokenV2Production 代理地址
  CHAIN_ID: 31337, // 本地测试网
  RPC_URL: 'http://localhost:8545',
  EXPLORER_URL: '' // 本地网络没有区块浏览器
}

// 游戏配置
export const GAME_CONFIG = {
  // 等级配置
  MAX_LEVEL: 100,
  EXP_PER_LEVEL: 100,
  EXP_MULTIPLIER: 1.2,

  // 属性配置
  MAX_HEALTH: 100,
  MAX_HAPPINESS: 100,
  MAX_ENERGY: 100,

  // 时间配置（毫秒）
  FEED_COOLDOWN: 2 * 60 * 60 * 1000, // 2小时
  PLAY_COOLDOWN: 1 * 60 * 60 * 1000, // 1小时
  HEALTH_DECAY_INTERVAL: 6 * 60 * 60 * 1000, // 6小时
  HAPPINESS_DECAY_INTERVAL: 4 * 60 * 60 * 1000, // 4小时

  // 代币兑换配置
  TOKEN_EXCHANGE_RATES: {
    [PetRarity.COMMON]: 10,
    [PetRarity.UNCOMMON]: 25,
    [PetRarity.RARE]: 50,
    [PetRarity.EPIC]: 100,
    [PetRarity.LEGENDARY]: 250
  },

  // 等级奖励倍数
  LEVEL_MULTIPLIER: {
    1: 1,
    10: 1.2,
    25: 1.5,
    50: 2,
    75: 2.5,
    100: 3
  }
}

// 萌宠配置
export const PET_CONFIG = {
  TYPES: {
    [PetType.CAT]: {
      name: '小猫',
      emoji: '🐱',
      baseStats: { strength: 5, intelligence: 8, agility: 9, charm: 7 },
      description: '可爱的小猫咪，聪明敏捷'
    },
    [PetType.DOG]: {
      name: '小狗',
      emoji: '🐶',
      baseStats: { strength: 8, intelligence: 6, agility: 7, charm: 9 },
      description: '忠诚的小狗狗，活泼可爱'
    },
    [PetType.RABBIT]: {
      name: '兔子',
      emoji: '🐰',
      baseStats: { strength: 4, intelligence: 7, agility: 10, charm: 8 },
      description: '温顺的小兔子，速度超快'
    },
    [PetType.BIRD]: {
      name: '小鸟',
      emoji: '🐦',
      baseStats: { strength: 3, intelligence: 9, agility: 10, charm: 6 },
      description: '自由的小鸟，智慧超群'
    },
    [PetType.DRAGON]: {
      name: '小龙',
      emoji: '🐲',
      baseStats: { strength: 10, intelligence: 8, agility: 6, charm: 8 },
      description: '神秘的小龙，力量强大'
    },
    [PetType.UNICORN]: {
      name: '独角兽',
      emoji: '🦄',
      baseStats: { strength: 7, intelligence: 10, agility: 8, charm: 10 },
      description: '传说中的独角兽，魅力无限'
    }
  },

  RARITIES: {
    [PetRarity.COMMON]: {
      name: '普通',
      color: '#9CA3AF',
      probability: 0.5,
      statMultiplier: 1
    },
    [PetRarity.UNCOMMON]: {
      name: '不凡',
      color: '#10B981',
      probability: 0.3,
      statMultiplier: 1.2
    },
    [PetRarity.RARE]: {
      name: '稀有',
      color: '#3B82F6',
      probability: 0.15,
      statMultiplier: 1.5
    },
    [PetRarity.EPIC]: {
      name: '史诗',
      color: '#8B5CF6',
      probability: 0.04,
      statMultiplier: 2
    },
    [PetRarity.LEGENDARY]: {
      name: '传说',
      color: '#F59E0B',
      probability: 0.01,
      statMultiplier: 3
    }
  }
}

// 装备配置
export const EQUIPMENT_CONFIG = {
  TYPES: {
    [EquipmentType.HELMET]: {
      name: '头盔',
      icon: '🎩',
      slot: 'head'
    },
    [EquipmentType.ACCESSORY]: {
      name: '饰品',
      icon: '💎',
      slot: 'accessory'
    },
    [EquipmentType.TOY]: {
      name: '玩具',
      icon: '🎾',
      slot: 'toy'
    },
    [EquipmentType.FOOD]: {
      name: '食物',
      icon: '🍖',
      slot: 'consumable'
    }
  }
}

// 活动配置
export const ACTIVITY_CONFIG = {
  FEED: {
    name: '喂食',
    icon: '🍖',
    duration: 0,
    cost: { energy: 0 },
    rewards: { experience: 10, health: 20, happiness: 10 },
    cooldown: GAME_CONFIG.FEED_COOLDOWN
  },
  PLAY: {
    name: '玩耍',
    icon: '🎾',
    duration: 30 * 60 * 1000, // 30分钟
    cost: { energy: 20 },
    rewards: { experience: 15, happiness: 30 },
    cooldown: GAME_CONFIG.PLAY_COOLDOWN
  },
  TRAIN: {
    name: '训练',
    icon: '💪',
    duration: 60 * 60 * 1000, // 1小时
    cost: { energy: 30 },
    rewards: { experience: 25, happiness: -5 },
    cooldown: 2 * 60 * 60 * 1000 // 2小时
  },
  ADVENTURE: {
    name: '冒险',
    icon: '🗺️',
    duration: 2 * 60 * 60 * 1000, // 2小时
    cost: { energy: 50 },
    rewards: { experience: 50, tokens: 5 },
    cooldown: 4 * 60 * 60 * 1000, // 4小时
    requirements: { level: 10 }
  },
  REST: {
    name: '休息',
    icon: '😴',
    duration: 30 * 60 * 1000, // 30分钟
    cost: {},
    rewards: { health: 15, happiness: 5 },
    cooldown: 60 * 60 * 1000 // 1小时
  }
}

// 成就配置
export const ACHIEVEMENTS = [
  {
    id: 'first_pet',
    name: '第一只萌宠',
    description: '获得你的第一只萌宠',
    icon: '🎉',
    reward: { tokens: 50 }
  },
  {
    id: 'level_10',
    name: '初级训练师',
    description: '萌宠达到10级',
    icon: '⭐',
    reward: { tokens: 100 }
  },
  {
    id: 'level_25',
    name: '中级训练师',
    description: '萌宠达到25级',
    icon: '🌟',
    reward: { tokens: 250 }
  },
  {
    id: 'level_50',
    name: '高级训练师',
    description: '萌宠达到50级',
    icon: '✨',
    reward: { tokens: 500 }
  },
  {
    id: 'rare_pet',
    name: '稀有收藏家',
    description: '获得稀有萌宠',
    icon: '💎',
    reward: { tokens: 200 }
  },
  {
    id: 'legendary_pet',
    name: '传说猎人',
    description: '获得传说萌宠',
    icon: '👑',
    reward: { tokens: 1000 }
  }
]

// 本地存储键名
export const STORAGE_KEYS = {
  GAME_STATE: 'pet_game_state',
  PET_STATE: 'pet_state',
  USER_SETTINGS: 'pet_game_settings',
  WALLET_STATE: 'wallet_state'
}

// API端点
export const API_ENDPOINTS = {
  LEADERBOARD: '/api/leaderboard',
  SAVE_GAME: '/api/save',
  LOAD_GAME: '/api/load'
}

// 错误消息
export const ERROR_MESSAGES = {
  WALLET_NOT_CONNECTED: '请先连接钱包',
  INSUFFICIENT_BALANCE: '余额不足',
  TRANSACTION_FAILED: '交易失败',
  NETWORK_ERROR: '网络错误',
  INVALID_INPUT: '输入无效',
  PET_NOT_FOUND: '萌宠不存在',
  COOLDOWN_ACTIVE: '冷却时间未结束',
  INSUFFICIENT_ENERGY: '能量不足',
  LEVEL_TOO_LOW: '等级不够'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  WALLET_CONNECTED: '钱包连接成功',
  TRANSACTION_SUCCESS: '交易成功',
  PET_CREATED: '萌宠创建成功',
  LEVEL_UP: '恭喜升级！',
  ACHIEVEMENT_UNLOCKED: '成就解锁！',
  TOKENS_EARNED: '获得代币！'
}
