/**
 * 数据持久化组合式函数
 * 提供数据保存、加载、备份和恢复的便捷接口
 */

import { ref, watch, onMounted, onUnmounted, readonly } from 'vue'
import { storeToRefs } from 'pinia'
import { usePetStore } from '../stores/pet'
import { useGameStore } from '../stores/game'
import { useWalletStore } from '../stores/wallet'
import {
  dataPersistenceService,
  initializeDataPersistence,
  saveAllGameData,
  loadAllGameData
} from '../services/data-persistence.service'
import {
  backupManager,
  exportGameBackup,
  importGameBackup,
  performAutoBackup
} from '../utils/backup'
import { storageUtil } from '../utils/storage'
import type { BackupResult, RestoreResult } from '../utils/backup'

/**
 * 持久化状态
 */
export interface PersistenceStatus {
  isInitialized: boolean
  isLoading: boolean
  isSaving: boolean
  lastSaved: number
  lastBackup: number
  error: string | null
}

/**
 * 数据持久化组合式函数
 */
export function usePersistence() {
  // 状态
  const status = ref<PersistenceStatus>({
    isInitialized: false,
    isLoading: false,
    isSaving: false,
    lastSaved: 0,
    lastBackup: 0,
    error: null
  })

  // 获取store实例
  const petStore = usePetStore()
  const gameStore = useGameStore()
  const walletStore = useWalletStore()

  // 获取store状态
  const { pets, currentPetId } = storeToRefs(petStore)
  const { coins, tokens, inventory, achievements, settings } = storeToRefs(gameStore)
  const { walletInfo } = storeToRefs(walletStore)

  // 防抖保存定时器
  let saveTimer: number | null = null
  const SAVE_DEBOUNCE_DELAY = 1000 // 1秒防抖

  /**
   * 初始化持久化系统
   */
  const initialize = async (): Promise<boolean> => {
    try {
      status.value.isLoading = true
      status.value.error = null

      const walletAddress = walletInfo.value?.address
      const success = await initializeDataPersistence(walletAddress)

      if (success) {
        status.value.isInitialized = true
        await loadAllData()
        setupAutoSave()
        console.log('持久化系统初始化成功')
      } else {
        status.value.error = '持久化系统初始化失败'
      }

      return success
    } catch (error) {
      status.value.error = error instanceof Error ? error.message : '初始化失败'
      console.error('持久化系统初始化错误:', error)
      return false
    } finally {
      status.value.isLoading = false
    }
  }

  /**
   * 销毁持久化系统
   */
  const destroy = (): void => {
    dataPersistenceService.destroy()
    clearAutoSave()
    status.value.isInitialized = false
    console.log('持久化系统已销毁')
  }

  /**
   * 保存所有数据
   */
  const saveAll = async (): Promise<boolean> => {
    if (!status.value.isInitialized) {
      console.warn('持久化系统未初始化')
      return false
    }

    try {
      status.value.isSaving = true
      status.value.error = null

      const gameData = {
        coins: coins.value,
        tokens: tokens.value,
        inventory: inventory.value,
        achievements: achievements.value
      }

      const success = await saveAllGameData(
        pets.value,
        currentPetId.value,
        gameData,
        settings.value
      )

      if (success) {
        status.value.lastSaved = Date.now()
        console.log('所有数据保存成功')
      } else {
        status.value.error = '数据保存失败'
      }

      return success
    } catch (error) {
      status.value.error = error instanceof Error ? error.message : '保存失败'
      console.error('保存数据错误:', error)
      return false
    } finally {
      status.value.isSaving = false
    }
  }

  /**
   * 加载所有数据
   */
  const loadAll = async (): Promise<boolean> => {
    try {
      status.value.isLoading = true
      status.value.error = null

      const { petData, gameData, settings: userSettings } = await loadAllGameData()

      // 恢复萌宠数据
      if (petData) {
        petStore.clearPets()
        petData.pets.forEach(pet => petStore.addPet(pet))
        if (petData.currentPetId) {
          petStore.setCurrentPet(petData.currentPetId)
        }
      }

      // 恢复游戏数据
      if (gameData) {
        if (typeof gameData.coins === 'number') {
          gameStore.coins = gameData.coins
        }
        if (typeof gameData.tokens === 'string') {
          gameStore.setTokens(gameData.tokens)
        }
        if (Array.isArray(gameData.inventory)) {
          gameStore.inventory = gameData.inventory
        }
        if (Array.isArray(gameData.achievements)) {
          gameStore.achievements = gameData.achievements
        }
      }

      // 恢复用户设置
      if (userSettings) {
        gameStore.updateSettings(userSettings)
      }

      console.log('所有数据加载成功')
      return true
    } catch (error) {
      status.value.error = error instanceof Error ? error.message : '加载失败'
      console.error('加载数据错误:', error)
      return false
    } finally {
      status.value.isLoading = false
    }
  }

  /**
   * 防抖保存
   */
  const debouncedSave = (): void => {
    if (saveTimer) {
      clearTimeout(saveTimer)
    }

    saveTimer = window.setTimeout(() => {
      saveAll()
    }, SAVE_DEBOUNCE_DELAY)
  }

  /**
   * 设置自动保存监听
   */
  const setupAutoSave = (): void => {
    // 监听萌宠数据变化
    watch([pets, currentPetId], () => {
      debouncedSave()
    }, { deep: true })

    // 监听游戏数据变化
    watch([coins, tokens, inventory, achievements], () => {
      debouncedSave()
    }, { deep: true })

    // 监听设置变化
    watch(settings, () => {
      debouncedSave()
    }, { deep: true })

    console.log('自动保存监听已设置')
  }

  /**
   * 清除自动保存
   */
  const clearAutoSave = (): void => {
    if (saveTimer) {
      clearTimeout(saveTimer)
      saveTimer = null
    }
  }

  /**
   * 导出备份
   */
  const exportBackup = async (): Promise<BackupResult> => {
    try {
      const walletAddress = walletInfo.value?.address
      const result = await exportGameBackup(walletAddress)

      if (result.success) {
        status.value.lastBackup = Date.now()
      }

      return result
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 导入备份
   */
  const importBackup = async (file: File): Promise<RestoreResult> => {
    try {
      const result = await importGameBackup(file)

      if (result.success) {
        // 重新加载数据
        await loadAll()
      }

      return result
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '导入失败',
        restoredItems: [],
        skippedItems: []
      }
    }
  }

  /**
   * 手动备份
   */
  const manualBackup = async (): Promise<boolean> => {
    try {
      const walletAddress = walletInfo.value?.address
      const success = await performAutoBackup(walletAddress)

      if (success) {
        status.value.lastBackup = Date.now()
      }

      return success
    } catch (error) {
      console.error('手动备份失败:', error)
      return false
    }
  }

  /**
   * 清除所有数据
   */
  const clearAllData = async (): Promise<boolean> => {
    try {
      const success = await dataPersistenceService.clearAllData()

      if (success) {
        // 重置store状态
        petStore.clearPets()
        gameStore.resetGame()
        status.value.lastSaved = 0
        status.value.lastBackup = 0
      }

      return success
    } catch (error) {
      console.error('清除数据失败:', error)
      return false
    }
  }

  /**
   * 获取存储统计
   */
  const getStorageStats = () => {
    return dataPersistenceService.getStorageStats()
  }

  /**
   * 检查数据完整性
   */
  const checkDataIntegrity = async (): Promise<boolean> => {
    try {
      // 检查关键数据是否存在
      const hasGameData = storageUtil.hasItem('pet_game_state')
      const hasPetData = storageUtil.hasItem('pet_state')
      const hasSettings = storageUtil.hasItem('pet_game_settings')

      console.log('数据完整性检查:', {
        hasGameData,
        hasPetData,
        hasSettings
      })

      return hasGameData || hasPetData || hasSettings
    } catch (error) {
      console.error('数据完整性检查失败:', error)
      return false
    }
  }

  // 生命周期钩子
  onMounted(() => {
    // 组件挂载时不自动初始化，由用户手动调用
  })

  onUnmounted(() => {
    clearAutoSave()
  })

  return {
    // 状态
    status: readonly(status),

    // 方法
    initialize,
    destroy,
    saveAll,
    loadAll,
    exportBackup,
    importBackup,
    manualBackup,
    clearAllData,
    getStorageStats,
    checkDataIntegrity,

    // 便捷方法
    debouncedSave
  }
}

/**
 * 全局持久化实例（单例）
 */
let globalPersistence: ReturnType<typeof usePersistence> | null = null

export function useGlobalPersistence() {
  if (!globalPersistence) {
    globalPersistence = usePersistence()
  }
  return globalPersistence
}
