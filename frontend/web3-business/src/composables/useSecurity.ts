/**
 * 安全相关的组合式函数
 * Security-related composable functions
 */

import { ref, computed } from 'vue'
import { ethers } from 'ethers'
import { securityService, type SecureTransactionParams } from '../services/security.service'
import { InputValidator, InputSanitizer, SecurityValidator } from '../utils/validation'
import { useWallet } from './useWallet'

export function useSecurity() {
  const { provider } = useWallet()
  const isSecurityEnabled = ref(true)
  const securityLevel = ref<'low' | 'medium' | 'high'>('medium')

  // 设置安全服务的provider
  if (provider.value) {
    securityService.setProvider(provider.value)
  }

  /**
   * 执行安全交易
   */
  const executeSecureTransaction = async (
    contractMethod: () => Promise<ethers.TransactionResponse>,
    params: SecureTransactionParams
  ) => {
    if (!isSecurityEnabled.value) {
      // 如果安全检查被禁用，直接执行
      const tx = await contractMethod()
      return await tx.wait()
    }

    return await securityService.executeSecureTransaction(contractMethod, params)
  }

  /**
   * 验证输入数据
   */
  const validateInput = {
    address: (address: string) => InputValidator.validateAddress(address),
    amount: (amount: string) => InputValidator.validateAmount(amount),
    petName: (name: string) => InputValidator.validatePetName(name),
    email: (email: string) => InputValidator.validateEmail(email),
    url: (url: string) => InputValidator.validateUrl(url),
    transactionHash: (hash: string) => InputValidator.validateTransactionHash(hash),
    chainId: (chainId: number | string) => InputValidator.validateChainId(chainId),
    numberRange: (value: number, min: number, max: number) =>
      InputValidator.validateNumberRange(value, min, max)
  }

  /**
   * 清理输入数据
   */
  const sanitizeInput = {
    html: (input: string) => InputSanitizer.sanitizeHtml(input),
    petName: (name: string) => InputSanitizer.sanitizePetName(name),
    number: (input: string) => InputSanitizer.sanitizeNumber(input),
    address: (address: string) => InputSanitizer.sanitizeAddress(address),
    url: (url: string) => InputSanitizer.sanitizeUrl(url)
  }

  /**
   * 安全验证器
   */
  const securityValidators = {
    checkSuspiciousContract: async (address: string) => {
      if (!provider.value) return true
      return await SecurityValidator.checkSuspiciousContract(address, provider.value)
    },
    validateTransactionSecurity: (params: any) =>
      SecurityValidator.validateTransactionSecurity(params),
    validateAmountSafety: (amount: string, maxAmount?: string) =>
      SecurityValidator.validateAmountSafety(amount, maxAmount)
  }

  /**
   * 获取交易日志
   */
  const getTransactionLogs = () => securityService.getTransactionLogs()

  /**
   * 清除交易日志
   */
  const clearTransactionLogs = () => securityService.clearTransactionLogs()

  /**
   * 验证钱包连接安全性
   */
  const validateWalletSecurity = async () => {
    if (!provider.value) {
      return { isSecure: false, warnings: ['钱包未连接'] }
    }
    return await securityService.validateWalletConnection(provider.value)
  }

  /**
   * 更新安全配置
   */
  const updateSecurityConfig = (config: any) => {
    securityService.updateSecurityConfig(config)
  }

  /**
   * 获取安全配置
   */
  const getSecurityConfig = () => securityService.getSecurityConfig()

  /**
   * 切换安全检查状态
   */
  const toggleSecurity = (enabled: boolean) => {
    isSecurityEnabled.value = enabled
  }

  /**
   * 设置安全级别
   */
  const setSecurityLevel = (level: 'low' | 'medium' | 'high') => {
    securityLevel.value = level

    // 根据安全级别调整配置
    const configs = {
      low: {
        maxTransactionAmount: '1000',
        requireConfirmationAbove: '100',
        suspiciousGasLimit: 5000000
      },
      medium: {
        maxTransactionAmount: '100',
        requireConfirmationAbove: '10',
        suspiciousGasLimit: 1000000
      },
      high: {
        maxTransactionAmount: '10',
        requireConfirmationAbove: '1',
        suspiciousGasLimit: 500000
      }
    }

    updateSecurityConfig(configs[level])
  }

  /**
   * 创建安全的表单验证规则
   */
  const createValidationRules = () => ({
    address: [
      { required: true, message: '请输入地址' },
      { validator: (value: string) => validateInput.address(value), message: '请输入有效的以太坊地址' }
    ],
    amount: [
      { required: true, message: '请输入金额' },
      { validator: (value: string) => validateInput.amount(value), message: '请输入有效的金额' }
    ],
    petName: [
      { required: true, message: '请输入萌宠名称' },
      { validator: (value: string) => validateInput.petName(value), message: '萌宠名称长度应在1-20字符之间，不能包含特殊字符' }
    ],
    email: [
      { required: true, message: '请输入邮箱' },
      { validator: (value: string) => validateInput.email(value), message: '请输入有效的邮箱地址' }
    ]
  })

  /**
   * 安全状态计算属性
   */
  const securityStatus = computed(() => ({
    enabled: isSecurityEnabled.value,
    level: securityLevel.value,
    config: getSecurityConfig()
  }))

  return {
    // 状态
    isSecurityEnabled,
    securityLevel,
    securityStatus,

    // 方法
    executeSecureTransaction,
    validateInput,
    sanitizeInput,
    securityValidators,
    getTransactionLogs,
    clearTransactionLogs,
    validateWalletSecurity,
    updateSecurityConfig,
    getSecurityConfig,
    toggleSecurity,
    setSecurityLevel,
    createValidationRules
  }
}

/**
 * 安全装饰器 - 用于包装需要安全检查的函数
 */
export function withSecurity<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  securityParams: Omit<SecureTransactionParams, 'to'>
): T {
  return (async (...args: Parameters<T>) => {
    const { executeSecureTransaction } = useSecurity()

    return await executeSecureTransaction(
      () => fn(...args),
      {
        to: '******************************************', // 默认值，实际使用时应该传入正确的地址
        ...securityParams
      }
    )
  }) as T
}

/**
 * 输入验证装饰器
 */
export function withValidation(validationRules: Record<string, any>) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = function (...args: any[]) {
      const { validateInput } = useSecurity()

      // 验证输入参数
      for (const [key, rule] of Object.entries(validationRules)) {
        const value = args[parseInt(key)] || args[0]?.[key]
        if (value !== undefined && !rule.validator(value)) {
          throw new Error(rule.message || `Invalid input for ${key}`)
        }
      }

      return originalMethod.apply(this, args)
    }

    return descriptor
  }
}
