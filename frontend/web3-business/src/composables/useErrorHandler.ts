import { ref, computed } from 'vue'
import { errorHandler, type AppError, ErrorType } from '@/services/error-handler.service'
import { transactionTracker } from '@/services/transaction-tracker.service'
import { networkMonitor } from '@/services/network-monitor.service'

export interface UseErrorHandlerOptions {
  showToast?: boolean
  logError?: boolean
  retryable?: boolean
  maxRetries?: number
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    showToast = true,
    logError = true,
    retryable = false,
    maxRetries = 3
  } = options

  const currentError = ref<AppError | null>(null)
  const isLoading = ref(false)
  const retryCount = ref(0)

  /**
   * 处理错误
   */
  const handleError = (error: any, context?: Record<string, any>): AppError => {
    const appError = errorHandler.handleError(error, context)
    currentError.value = appError
    return appError
  }

  /**
   * 清除当前错误
   */
  const clearError = () => {
    currentError.value = null
    retryCount.value = 0
  }

  /**
   * 执行带错误处理的异步操作
   */
  const executeWithErrorHandling = async <T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T | null> => {
    isLoading.value = true
    clearError()

    try {
      const result = await operation()
      return result
    } catch (error) {
      handleError(error, context)
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 执行带重试的操作
   */
  const executeWithRetry = async <T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T | null> => {
    if (!retryable) {
      return executeWithErrorHandling(operation, context)
    }

    return errorHandler.executeWithRetry(
      operation,
      { maxAttempts: maxRetries, delay: 1000, backoff: true },
      context
    ).catch(error => {
      handleError(error, { ...context, finalAttempt: true })
      return null
    })
  }

  /**
   * 执行网络相关操作
   */
  const executeNetworkOperation = async <T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T | null> => {
    isLoading.value = true
    clearError()

    try {
      const result = await networkMonitor.executeWithNetworkRetry(operation)
      return result
    } catch (error) {
      handleError(error, { ...context, networkStatus: networkMonitor.getNetworkStatus() })
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 执行交易操作
   */
  const executeTransaction = async <T>(
    transactionPromise: Promise<T>,
    type: string,
    description: string,
    context?: Record<string, any>
  ): Promise<T | null> => {
    isLoading.value = true
    clearError()

    try {
      const result = await transactionTracker.trackTransaction(
        transactionPromise as any,
        type,
        description,
        context
      )
      return result as T
    } catch (error) {
      handleError(error, { ...context, transactionType: type })
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 显示成功消息
   */
  const showSuccess = (message: string) => {
    errorHandler.showSuccess(message)
  }

  /**
   * 显示警告消息
   */
  const showWarning = (message: string) => {
    errorHandler.showWarning(message)
  }

  /**
   * 显示信息消息
   */
  const showInfo = (message: string) => {
    errorHandler.showInfo(message)
  }

  /**
   * 显示加载消息
   */
  const showLoading = (message?: string) => {
    return errorHandler.showLoading(message)
  }

  /**
   * 检查是否有错误
   */
  const hasError = computed(() => currentError.value !== null)

  /**
   * 检查是否为特定类型的错误
   */
  const isErrorType = (type: ErrorType) => {
    return computed(() => currentError.value?.type === type)
  }

  /**
   * 获取错误消息
   */
  const errorMessage = computed(() => currentError.value?.message || '')

  /**
   * 检查是否可以重试
   */
  const canRetry = computed(() => {
    if (!retryable || !currentError.value) return false

    // 用户取消的操作不能重试
    if (currentError.value.type === ErrorType.WALLET &&
        (currentError.value.code === '4001' || currentError.value.code === 'ACTION_REJECTED')) {
      return false
    }

    return retryCount.value < maxRetries
  })

  /**
   * 重试操作
   */
  const retry = async <T>(operation: () => Promise<T>): Promise<T | null> => {
    if (!canRetry.value) return null

    retryCount.value++
    return executeWithErrorHandling(operation, { retryAttempt: retryCount.value })
  }

  /**
   * 获取错误历史
   */
  const getErrorHistory = () => {
    return errorHandler.getErrorHistory()
  }

  /**
   * 获取错误统计
   */
  const getErrorStats = () => {
    return errorHandler.getErrorStats()
  }

  return {
    // 状态
    currentError: computed(() => currentError.value),
    isLoading: computed(() => isLoading.value),
    hasError,
    errorMessage,
    canRetry,
    retryCount: computed(() => retryCount.value),

    // 方法
    handleError,
    clearError,
    executeWithErrorHandling,
    executeWithRetry,
    executeNetworkOperation,
    executeTransaction,
    retry,

    // 消息显示
    showSuccess,
    showWarning,
    showInfo,
    showLoading,

    // 错误类型检查
    isErrorType,
    isNetworkError: isErrorType(ErrorType.NETWORK),
    isContractError: isErrorType(ErrorType.CONTRACT),
    isWalletError: isErrorType(ErrorType.WALLET),
    isTransactionError: isErrorType(ErrorType.TRANSACTION),
    isValidationError: isErrorType(ErrorType.VALIDATION),

    // 工具方法
    getErrorHistory,
    getErrorStats
  }
}

export default useErrorHandler
