import { ref, computed, onMounted, onUnmounted, readonly } from 'vue'
import { useGlobalTheme } from './useTheme'

export interface AnimationConfig {
  duration?: number
  delay?: number
  easing?: string
  iterations?: number
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse'
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both'
}

interface PerformanceMetrics {
  fps: number
  frameDrops: number
  lastFrameTime: number
}

type PerformanceMode = 'high' | 'medium' | 'low'

export interface AnimationPreset {
  name: string
  keyframes: Keyframe[]
  config: AnimationConfig
}

// 预定义动画
const animationPresets: Record<string, AnimationPreset> = {
  fadeIn: {
    name: 'fadeIn',
    keyframes: [
      { opacity: 0 },
      { opacity: 1 }
    ],
    config: {
      duration: 300,
      easing: 'ease-out',
      fillMode: 'forwards'
    }
  },

  fadeOut: {
    name: 'fadeOut',
    keyframes: [
      { opacity: 1 },
      { opacity: 0 }
    ],
    config: {
      duration: 300,
      easing: 'ease-in',
      fillMode: 'forwards'
    }
  },

  slideInUp: {
    name: 'slideInUp',
    keyframes: [
      { transform: 'translateY(20px)', opacity: 0 },
      { transform: 'translateY(0)', opacity: 1 }
    ],
    config: {
      duration: 400,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      fillMode: 'forwards'
    }
  },

  slideInDown: {
    name: 'slideInDown',
    keyframes: [
      { transform: 'translateY(-20px)', opacity: 0 },
      { transform: 'translateY(0)', opacity: 1 }
    ],
    config: {
      duration: 400,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      fillMode: 'forwards'
    }
  },

  slideInLeft: {
    name: 'slideInLeft',
    keyframes: [
      { transform: 'translateX(-20px)', opacity: 0 },
      { transform: 'translateX(0)', opacity: 1 }
    ],
    config: {
      duration: 400,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      fillMode: 'forwards'
    }
  },

  slideInRight: {
    name: 'slideInRight',
    keyframes: [
      { transform: 'translateX(20px)', opacity: 0 },
      { transform: 'translateX(0)', opacity: 1 }
    ],
    config: {
      duration: 400,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      fillMode: 'forwards'
    }
  },

  scaleIn: {
    name: 'scaleIn',
    keyframes: [
      { transform: 'scale(0.8)', opacity: 0 },
      { transform: 'scale(1)', opacity: 1 }
    ],
    config: {
      duration: 300,
      easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      fillMode: 'forwards'
    }
  },

  bounce: {
    name: 'bounce',
    keyframes: [
      { transform: 'translateY(0)' },
      { transform: 'translateY(-10px)' },
      { transform: 'translateY(0)' }
    ],
    config: {
      duration: 600,
      easing: 'ease-in-out',
      iterations: 'infinite'
    }
  },

  pulse: {
    name: 'pulse',
    keyframes: [
      { transform: 'scale(1)', opacity: 1 },
      { transform: 'scale(1.05)', opacity: 0.8 },
      { transform: 'scale(1)', opacity: 1 }
    ],
    config: {
      duration: 2000,
      easing: 'ease-in-out',
      iterations: 'infinite'
    }
  },

  wiggle: {
    name: 'wiggle',
    keyframes: [
      { transform: 'rotate(0deg)' },
      { transform: 'rotate(-3deg)' },
      { transform: 'rotate(3deg)' },
      { transform: 'rotate(0deg)' }
    ],
    config: {
      duration: 500,
      easing: 'ease-in-out'
    }
  },

  float: {
    name: 'float',
    keyframes: [
      { transform: 'translateY(0px)' },
      { transform: 'translateY(-10px)' },
      { transform: 'translateY(0px)' }
    ],
    config: {
      duration: 3000,
      easing: 'ease-in-out',
      iterations: 'infinite'
    }
  },

  glow: {
    name: 'glow',
    keyframes: [
      { boxShadow: '0 0 5px rgba(168, 85, 247, 0.5)' },
      { boxShadow: '0 0 20px rgba(168, 85, 247, 0.8)' },
      { boxShadow: '0 0 5px rgba(168, 85, 247, 0.5)' }
    ],
    config: {
      duration: 2000,
      easing: 'ease-in-out',
      iterations: 'infinite'
    }
  }
}

export function useAnimation() {
  const theme = useGlobalTheme()

  // 性能监控
  const performanceMode = ref<PerformanceMode>('high')
  const performanceMetrics = ref<PerformanceMetrics>({
    fps: 60,
    frameDrops: 0,
    lastFrameTime: performance.now()
  })
  const activeAnimations = ref(new Set<Animation>())

  let performanceObserver: PerformanceObserver | null = null
  let animationFrameId: number | null = null

  // 动画是否启用
  const animationsEnabled = computed(() => {
    return theme.themeConfig.value.animations &&
           !theme.themeConfig.value.reducedMotion &&
           !prefersReducedMotion.value &&
           performanceMode.value !== 'low'
  })

  // 检测用户偏好
  const prefersReducedMotion = ref(false)

  // 性能监控
  const startPerformanceMonitoring = () => {
    if (typeof window === 'undefined') return

    // 监控长任务
    if ('PerformanceObserver' in window) {
      performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.duration > 16.67) { // 超过一帧的时间
            performanceMetrics.value.frameDrops++

            // 如果性能下降，自动降级
            if (performanceMetrics.value.frameDrops > 10) {
              adjustPerformanceMode()
            }
          }
        })
      })

      try {
        performanceObserver.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        console.warn('Performance monitoring not supported')
      }
    }

    // FPS 监控
    const measureFPS = () => {
      const now = performance.now()
      const delta = now - performanceMetrics.value.lastFrameTime
      performanceMetrics.value.fps = Math.round(1000 / delta)
      performanceMetrics.value.lastFrameTime = now

      animationFrameId = requestAnimationFrame(measureFPS)
    }
    measureFPS()
  }

  // 根据性能调整动画质量
  const adjustPerformanceMode = () => {
    const fps = performanceMetrics.value.fps

    if (fps < 30) {
      performanceMode.value = 'low'
      // 停止所有非关键动画
      stopNonCriticalAnimations()
    } else if (fps < 45) {
      performanceMode.value = 'medium'
    } else {
      performanceMode.value = 'high'
    }
  }

  // 停止非关键动画
  const stopNonCriticalAnimations = () => {
    activeAnimations.value.forEach(animation => {
      if (animation.playState === 'running') {
        animation.cancel()
      }
    })
    activeAnimations.value.clear()
  }

  // 获取性能优化后的配置
  const getOptimizedConfig = (config: AnimationConfig): AnimationConfig => {
    const baseConfig = { ...config }

    switch (performanceMode.value) {
      case 'low':
        return {
          ...baseConfig,
          duration: Math.min(baseConfig.duration || 300, 150), // 最大150ms
          iterations: 1 // 只执行一次
        }
      case 'medium':
        return {
          ...baseConfig,
          duration: (baseConfig.duration || 300) * 0.75,
          iterations: Math.min(baseConfig.iterations || 1, 3)
        }
      default:
        return baseConfig
    }
  }

  onMounted(() => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
      prefersReducedMotion.value = mediaQuery.matches

      mediaQuery.addEventListener('change', (e) => {
        prefersReducedMotion.value = e.matches
        if (e.matches) {
          performanceMode.value = 'low'
        }
      })

      startPerformanceMonitoring()
    }
  })

  onUnmounted(() => {
    if (performanceObserver) {
      performanceObserver.disconnect()
    }
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }
    stopNonCriticalAnimations()
  })

  // 创建动画
  const animate = (
    element: HTMLElement,
    preset: string | AnimationPreset,
    customConfig?: Partial<AnimationConfig>
  ): Animation | null => {
    if (!animationsEnabled.value) {
      return null
    }

    let animationData: AnimationPreset

    if (typeof preset === 'string') {
      animationData = animationPresets[preset]
      if (!animationData) {
        console.warn(`Animation preset "${preset}" not found`)
        return null
      }
    } else {
      animationData = preset
    }

    const config = getOptimizedConfig({ ...animationData.config, ...customConfig })

    const animation = element.animate(animationData.keyframes, {
      duration: config.duration,
      delay: config.delay,
      easing: config.easing,
      iterations: config.iterations,
      direction: config.direction,
      fill: config.fillMode
    })

    // 跟踪活动动画
    activeAnimations.value.add(animation)

    // 动画完成后清理
    animation.addEventListener('finish', () => {
      activeAnimations.value.delete(animation)
    })

    animation.addEventListener('cancel', () => {
      activeAnimations.value.delete(animation)
    })

    return animation
  }

  // 创建交错动画
  const staggeredAnimate = (
    elements: HTMLElement[],
    preset: string | AnimationPreset,
    staggerDelay: number = 100,
    customConfig?: Partial<AnimationConfig>
  ): Animation[] => {
    if (!animationsEnabled.value || prefersReducedMotion.value) {
      return []
    }

    return elements.map((element, index) => {
      const config = {
        ...customConfig,
        delay: (customConfig?.delay || 0) + (index * staggerDelay)
      }

      return animate(element, preset, config)
    }).filter(Boolean) as Animation[]
  }

  // 创建序列动画
  const sequenceAnimate = async (
    element: HTMLElement,
    presets: (string | AnimationPreset)[],
    customConfig?: Partial<AnimationConfig>
  ): Promise<void> => {
    if (!animationsEnabled.value || prefersReducedMotion.value) {
      return
    }

    for (const preset of presets) {
      const animation = animate(element, preset, customConfig)
      if (animation) {
        await animation.finished
      }
    }
  }

  // 创建并行动画
  const parallelAnimate = (
    elements: HTMLElement[],
    presets: (string | AnimationPreset)[],
    customConfig?: Partial<AnimationConfig>
  ): Animation[] => {
    if (!animationsEnabled.value || prefersReducedMotion.value) {
      return []
    }

    const animations: Animation[] = []

    elements.forEach((element, index) => {
      const preset = presets[index % presets.length]
      const animation = animate(element, preset, customConfig)
      if (animation) {
        animations.push(animation)
      }
    })

    return animations
  }

  // 创建自定义动画
  const createCustomAnimation = (
    element: HTMLElement,
    keyframes: Keyframe[],
    config: AnimationConfig
  ): Animation | null => {
    if (!animationsEnabled.value || prefersReducedMotion.value) {
      return null
    }

    return element.animate(keyframes, {
      duration: config.duration,
      delay: config.delay,
      easing: config.easing,
      iterations: config.iterations,
      direction: config.direction,
      fill: config.fillMode
    })
  }

  // 获取动画预设
  const getPreset = (name: string): AnimationPreset | undefined => {
    return animationPresets[name]
  }

  // 注册新的动画预设
  const registerPreset = (name: string, preset: AnimationPreset) => {
    animationPresets[name] = preset
  }

  // 获取所有预设名称
  const getPresetNames = (): string[] => {
    return Object.keys(animationPresets)
  }

  // 创建 CSS 动画类
  const createAnimationClass = (
    preset: string | AnimationPreset,
    customConfig?: Partial<AnimationConfig>
  ): string => {
    if (!animationsEnabled.value) {
      return ''
    }

    let animationData: AnimationPreset

    if (typeof preset === 'string') {
      animationData = animationPresets[preset]
      if (!animationData) {
        return ''
      }
    } else {
      animationData = preset
    }

    const config = { ...animationData.config, ...customConfig }
    const className = `animation-${animationData.name}-${Date.now()}`

    // 创建 CSS 规则
    const keyframesRule = `
      @keyframes ${className} {
        ${animationData.keyframes.map((frame, index) => {
          const percentage = (index / (animationData.keyframes.length - 1)) * 100
          const properties = Object.entries(frame)
            .map(([key, value]) => `${key}: ${value}`)
            .join('; ')
          return `${percentage}% { ${properties} }`
        }).join('\n')}
      }
    `

    const animationRule = `
      .${className} {
        animation: ${className} ${config.duration}ms ${config.easing || 'ease'} ${config.delay || 0}ms ${config.iterations || 1} ${config.direction || 'normal'} ${config.fillMode || 'none'};
      }
    `

    // 添加到页面
    const style = document.createElement('style')
    style.textContent = keyframesRule + animationRule
    document.head.appendChild(style)

    return className
  }

  return {
    // 状态
    animationsEnabled: readonly(animationsEnabled),
    prefersReducedMotion: readonly(prefersReducedMotion),
    performanceMode: readonly(performanceMode),
    performanceMetrics: readonly(performanceMetrics),

    // 动画方法
    animate,
    staggeredAnimate,
    sequenceAnimate,
    parallelAnimate,
    createCustomAnimation,

    // 预设管理
    getPreset,
    registerPreset,
    getPresetNames,

    // CSS 动画
    createAnimationClass,

    // 性能管理
    getOptimizedConfig,
    stopNonCriticalAnimations,
    activeAnimationsCount: computed(() => activeAnimations.value.size)
  }
}

// 全局动画实例
let globalAnimation: ReturnType<typeof useAnimation> | null = null

export function useGlobalAnimation() {
  if (!globalAnimation) {
    globalAnimation = useAnimation()
  }
  return globalAnimation
}
