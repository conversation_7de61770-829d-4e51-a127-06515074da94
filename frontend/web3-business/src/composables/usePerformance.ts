/**
 * 性能优化组合式函数
 * 整合所有性能优化功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMemoryManager } from '@/utils/memoryManager'
import { useRequestCache, useDebounceThrottle } from '@/utils/requestCache'
import { useImageOptimization } from '@/utils/imageOptimization'
import { useGlobalAnimation } from '@/composables/useAnimation'

interface PerformanceConfig {
  enableMemoryMonitoring?: boolean
  enableRequestCaching?: boolean
  enableImageOptimization?: boolean
  enableAnimationOptimization?: boolean
  memoryThreshold?: number // 内存使用率阈值
  cacheCleanupInterval?: number // 缓存清理间隔（毫秒）
  performanceMode?: 'auto' | 'high' | 'medium' | 'low'
}

interface PerformanceMetrics {
  memory: {
    usage: number
    peak: number
    average: number
    resourceCount: number
  }
  cache: {
    hitRate: number
    errorRate: number
    size: number
    totalRequests: number
  }
  animation: {
    fps: number
    frameDrops: number
    activeAnimations: number
    performanceMode: string
  }
  overall: {
    score: number // 0-100 的性能评分
    status: 'excellent' | 'good' | 'fair' | 'poor'
  }
}

export function usePerformance(config: PerformanceConfig = {}) {
  const {
    enableMemoryMonitoring = true,
    enableRequestCaching = true,
    enableImageOptimization = true,
    enableAnimationOptimization = true,
    memoryThreshold = 80,
    cacheCleanupInterval = 60000,
    performanceMode = 'auto'
  } = config

  // 性能监控状态
  const isMonitoring = ref(false)
  const performanceScore = ref(100)
  const performanceStatus = ref<'excellent' | 'good' | 'fair' | 'poor'>('excellent')

  // 工具实例
  const memoryManager = enableMemoryMonitoring ? useMemoryManager() : null
  const requestCache = enableRequestCaching ? useRequestCache() : null
  const imageOptimization = enableImageOptimization ? useImageOptimization() : null
  const animation = enableAnimationOptimization ? useGlobalAnimation() : null
  const debounceThrottle = useDebounceThrottle()

  // 定时器
  let monitoringInterval: number | null = null
  let cleanupInterval: number | null = null

  // 性能指标
  const metrics = computed<PerformanceMetrics>(() => {
    const memoryStats = memoryManager?.stats.value
    const cacheStats = requestCache?.stats.value
    const animationStats = animation?.performanceMetrics.value

    return {
      memory: {
        usage: memoryStats?.memoryInfo?.usage || 0,
        peak: memoryStats?.peakUsage || 0,
        average: memoryStats?.averageUsage || 0,
        resourceCount: memoryStats?.resourceCount || 0
      },
      cache: {
        hitRate: parseFloat(requestCache?.hitRate.value || '0'),
        errorRate: parseFloat(requestCache?.errorRate.value || '0'),
        size: requestCache?.cacheSize.value || 0,
        totalRequests: cacheStats?.totalRequests || 0
      },
      animation: {
        fps: animationStats?.fps || 60,
        frameDrops: animationStats?.frameDrops || 0,
        activeAnimations: animation?.activeAnimationsCount.value || 0,
        performanceMode: animation?.performanceMode.value || 'high'
      },
      overall: {
        score: performanceScore.value,
        status: performanceStatus.value
      }
    }
  })

  // 计算性能评分
  const calculatePerformanceScore = () => {
    let score = 100
    const currentMetrics = metrics.value

    // 内存使用率影响 (30%)
    if (currentMetrics.memory.usage > 90) {
      score -= 30
    } else if (currentMetrics.memory.usage > 70) {
      score -= 15
    } else if (currentMetrics.memory.usage > 50) {
      score -= 5
    }

    // FPS 影响 (25%)
    if (currentMetrics.animation.fps < 30) {
      score -= 25
    } else if (currentMetrics.animation.fps < 45) {
      score -= 15
    } else if (currentMetrics.animation.fps < 55) {
      score -= 5
    }

    // 缓存命中率影响 (20%)
    if (currentMetrics.cache.hitRate < 50) {
      score -= 20
    } else if (currentMetrics.cache.hitRate < 70) {
      score -= 10
    } else if (currentMetrics.cache.hitRate < 85) {
      score -= 5
    }

    // 错误率影响 (15%)
    if (currentMetrics.cache.errorRate > 10) {
      score -= 15
    } else if (currentMetrics.cache.errorRate > 5) {
      score -= 8
    } else if (currentMetrics.cache.errorRate > 2) {
      score -= 3
    }

    // 资源泄漏影响 (10%)
    if (currentMetrics.memory.resourceCount > 100) {
      score -= 10
    } else if (currentMetrics.memory.resourceCount > 50) {
      score -= 5
    }

    performanceScore.value = Math.max(0, Math.min(100, score))

    // 更新状态
    if (performanceScore.value >= 85) {
      performanceStatus.value = 'excellent'
    } else if (performanceScore.value >= 70) {
      performanceStatus.value = 'good'
    } else if (performanceScore.value >= 50) {
      performanceStatus.value = 'fair'
    } else {
      performanceStatus.value = 'poor'
    }
  }

  // 自动优化
  const autoOptimize = () => {
    const currentMetrics = metrics.value

    // 内存优化
    if (currentMetrics.memory.usage > memoryThreshold) {
      console.log('High memory usage detected, performing cleanup...')
      memoryManager?.cleanup()
      requestCache?.clearCache()

      if (imageOptimization) {
        // 清理图片缓存
        // imageOptimization.clearCache() // 如果有这个方法
      }
    }

    // 动画优化
    if (currentMetrics.animation.fps < 45) {
      console.log('Low FPS detected, stopping non-critical animations...')
      animation?.stopNonCriticalAnimations()
    }

    // 缓存优化
    if (currentMetrics.cache.errorRate > 5) {
      console.log('High error rate detected, clearing cache...')
      requestCache?.clearCache()
    }
  }

  // 开始监控
  const startMonitoring = () => {
    if (isMonitoring.value) return

    isMonitoring.value = true

    // 性能评分更新
    monitoringInterval = setInterval(() => {
      calculatePerformanceScore()

      if (performanceMode === 'auto') {
        autoOptimize()
      }
    }, 5000) // 每5秒更新一次

    // 定期清理
    cleanupInterval = setInterval(() => {
      requestCache?.cleanupExpired()
      memoryManager?.updateStats()
    }, cacheCleanupInterval)

    console.log('Performance monitoring started')
  }

  // 停止监控
  const stopMonitoring = () => {
    if (!isMonitoring.value) return

    isMonitoring.value = false

    if (monitoringInterval) {
      clearInterval(monitoringInterval)
      monitoringInterval = null
    }

    if (cleanupInterval) {
      clearInterval(cleanupInterval)
      cleanupInterval = null
    }

    console.log('Performance monitoring stopped')
  }

  // 手动优化
  const optimize = () => {
    console.log('Manual optimization triggered')

    // 清理内存
    memoryManager?.cleanup()

    // 清理缓存
    requestCache?.clearCache()

    // 停止非关键动画
    animation?.stopNonCriticalAnimations()

    // 重新计算评分
    setTimeout(calculatePerformanceScore, 1000)
  }

  // 获取优化建议
  const getOptimizationSuggestions = (): string[] => {
    const suggestions: string[] = []
    const currentMetrics = metrics.value

    if (currentMetrics.memory.usage > 80) {
      suggestions.push('内存使用率过高，建议清理不必要的资源')
    }

    if (currentMetrics.animation.fps < 45) {
      suggestions.push('帧率较低，建议减少动画效果或降低动画质量')
    }

    if (currentMetrics.cache.hitRate < 70) {
      suggestions.push('缓存命中率较低，建议优化缓存策略')
    }

    if (currentMetrics.cache.errorRate > 5) {
      suggestions.push('请求错误率较高，建议检查网络连接或API状态')
    }

    if (currentMetrics.memory.resourceCount > 50) {
      suggestions.push('活动资源过多，建议及时清理不使用的资源')
    }

    if (suggestions.length === 0) {
      suggestions.push('性能表现良好，无需特别优化')
    }

    return suggestions
  }

  // 创建防抖函数
  const createDebounced = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ) => debounceThrottle.createDebounced(fn, delay, key)

  // 创建节流函数
  const createThrottled = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ) => debounceThrottle.createThrottled(fn, delay, key)

  // 预加载资源
  const preloadResources = async (urls: string[]) => {
    if (imageOptimization) {
      try {
        await imageOptimization.preloadImages(urls, { priority: 'high' })
        console.log(`Preloaded ${urls.length} resources`)
      } catch (error) {
        console.warn('Failed to preload resources:', error)
      }
    }
  }

  // 生命周期管理
  onMounted(() => {
    if (performanceMode !== 'low') {
      startMonitoring()
    }
  })

  onUnmounted(() => {
    stopMonitoring()
    memoryManager?.cleanup()
  })

  return {
    // 状态
    isMonitoring: computed(() => isMonitoring.value),
    metrics: computed(() => metrics.value),
    performanceScore: computed(() => performanceScore.value),
    performanceStatus: computed(() => performanceStatus.value),

    // 方法
    startMonitoring,
    stopMonitoring,
    optimize,
    getOptimizationSuggestions,
    calculatePerformanceScore,

    // 工具方法
    createDebounced,
    createThrottled,
    preloadResources,

    // 子模块访问
    memoryManager,
    requestCache,
    imageOptimization,
    animation
  }
}
