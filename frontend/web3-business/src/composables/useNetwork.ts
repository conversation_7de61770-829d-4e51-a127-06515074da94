import { ref, computed, watch } from 'vue'
import { useWalletStore } from '../stores/wallet'
import { walletService } from '../services/wallet.service'
import { showToast } from 'vant'
import { showConfirmDialog } from 'vant/es'
import type { NetworkConfig } from '@/types'

export function useNetwork() {
  const walletStore = useWalletStore()

  // 响应式状态
  const isSwitching = ref(false)
  const networkError = ref('')

  // 计算属性
  const currentChainId = computed(() => walletStore.chainId)
  const isCorrectNetwork = computed(() => walletStore.isCorrectNetwork)
  const networkName = computed(() => walletStore.networkName)

  const targetChainId = computed(() => {
    return parseInt((import.meta as any).env.VITE_CHAIN_ID || '31337')
  })

  const supportedNetworks = computed(() => {
    return walletService.getSupportedNetworks()
  })

  const currentNetwork = computed(() => {
    return supportedNetworks.value.find(network => network.chainId === currentChainId.value)
  })

  const targetNetwork = computed(() => {
    return supportedNetworks.value.find(network => network.chainId === targetChainId.value)
  })

  // 检查网络兼容性
  const checkNetworkCompatibility = () => {
    if (!walletStore.isConnected) return true

    const isSupported = walletService.isSupportedNetwork(currentChainId.value)
    const isCorrect = currentChainId.value === targetChainId.value

    if (!isSupported) {
      networkError.value = `不支持的网络: ${networkName.value}`
      return false
    }

    if (!isCorrect) {
      networkError.value = `请切换到 ${targetNetwork.value?.chainName || '目标网络'}`
      return false
    }

    networkError.value = ''
    return true
  }

  // 切换到目标网络
  const switchToTargetNetwork = async () => {
    if (isSwitching.value) return

    isSwitching.value = true
    try {
      await walletService.switchNetwork(targetChainId.value)
      networkError.value = ''
      showToast({
        type: 'success',
        message: '网络切换成功'
      })
    } catch (error: any) {
      console.error('网络切换失败:', error)
      showToast({
        type: 'fail',
        message: '网络切换失败：' + (error.message || '未知错误')
      })
    } finally {
      isSwitching.value = false
    }
  }

  // 切换到指定网络
  const switchToNetwork = async (chainId: number) => {
    if (isSwitching.value) return

    const network = supportedNetworks.value.find(n => n.chainId === chainId)
    if (!network) {
      showToast({
        type: 'fail',
        message: '不支持的网络'
      })
      return
    }

    isSwitching.value = true
    try {
      await walletService.switchNetwork(chainId)
      showToast({
        type: 'success',
        message: `已切换到 ${network.chainName}`
      })
    } catch (error: any) {
      console.error('网络切换失败:', error)
      showToast({
        type: 'fail',
        message: '网络切换失败：' + (error.message || '未知错误')
      })
    } finally {
      isSwitching.value = false
    }
  }

  // 自动提示切换网络
  const promptNetworkSwitch = async () => {
    if (isCorrectNetwork.value || !walletStore.isConnected) return

    try {
      await showConfirmDialog({
        title: '网络不匹配',
        message: `当前网络: ${networkName.value}\n目标网络: ${targetNetwork.value?.chainName}\n\n是否切换到正确的网络？`,
        confirmButtonText: '切换',
        cancelButtonText: '取消'
      })
      await switchToTargetNetwork()
    } catch (error) {
      // 用户取消了操作
      console.log('用户取消网络切换')
    }
  }

  // 获取网络状态信息
  const getNetworkStatus = () => {
    return {
      isConnected: walletStore.isConnected,
      currentChainId: currentChainId.value,
      targetChainId: targetChainId.value,
      isCorrectNetwork: isCorrectNetwork.value,
      networkName: networkName.value,
      networkError: networkError.value,
      currentNetwork: currentNetwork.value,
      targetNetwork: targetNetwork.value
    }
  }

  // 监听网络变化
  watch(currentChainId, (newChainId, oldChainId) => {
    if (newChainId !== oldChainId && walletStore.isConnected) {
      checkNetworkCompatibility()

      // 如果网络不正确，延迟提示切换
      if (!isCorrectNetwork.value) {
        setTimeout(() => {
          promptNetworkSwitch()
        }, 1000)
      }
    }
  })

  // 监听连接状态变化
  watch(() => walletStore.isConnected, (connected) => {
    if (connected) {
      checkNetworkCompatibility()
    } else {
      networkError.value = ''
    }
  })

  return {
    // 状态
    currentChainId,
    targetChainId,
    isCorrectNetwork,
    networkName,
    networkError,
    isSwitching,
    supportedNetworks,
    currentNetwork,
    targetNetwork,

    // 方法
    checkNetworkCompatibility,
    switchToTargetNetwork,
    switchToNetwork,
    promptNetworkSwitch,
    getNetworkStatus
  }
}
