import { describe, it, expect, beforeEach, vi } from 'vitest'
import { usePetNurturing } from '../usePetNurturing'
import { PetRarity, PetType, PetStatus, PetMood, GrowthStage } from '../../types/typesWithoutCircular'
import type { Pet } from '../../types/typesWithoutCircular'

// Mock the pet store
const mockPetStore = {
  pets: [] as Pet[],
  updatePet: vi.fn(),
  checkLevelUp: vi.fn()
}

vi.mock('../../stores/pet', () => ({
  usePetStore: () => mockPetStore
}))

// Mock the data persistence service
vi.mock('../../services/data-persistence.service', () => ({
  dataPersistenceService: {
    savePetData: vi.fn(),
    loadPetData: vi.fn()
  }
}))

describe('usePetNurturing', () => {
  let nurturing: ReturnType<typeof usePetNurturing>
  let mockPet: Pet

  beforeEach(() => {
    // Reset mocks
    mockPetStore.pets = []
    mockPetStore.updatePet.mockClear()
    mockPetStore.checkLevelUp.mockClear()

    nurturing = usePetNurturing()

    mockPet = {
      id: 'test-pet-1',
      name: '测试萌宠',
      type: PetType.CAT,
      level: 5,
      experience: 50,
      maxExperience: 100,
      health: 80,
      maxHealth: 100,
      happiness: 70,
      maxHappiness: 100,
      energy: 60,
      maxEnergy: 100,
      rarity: PetRarity.COMMON,
      equipment: [],
      lastFeedTime: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
      lastPlayTime: Date.now() - 1 * 60 * 60 * 1000, // 1 hour ago
      lastTrainTime: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
      lastRestTime: Date.now() - 30 * 60 * 1000, // 30 minutes ago
      birthTime: Date.now() - 24 * 60 * 60 * 1000, // 1 day ago
      avatar: '/images/pets/cat/orange_striped.png',
      stats: {
        strength: 15,
        intelligence: 20,
        agility: 25,
        charm: 18,
        vitality: 16,
        luck: 12
      },
      baseStats: {
        strength: 15,
        intelligence: 20,
        agility: 25,
        charm: 18,
        vitality: 16,
        luck: 12
      },
      appearance: {
        species: 'cat',
        color: 'orange',
        pattern: 'striped',
        size: 'small' as any,
        accessories: [],
        specialEffects: [],
        animations: ['idle', 'walk', 'play', 'sleep']
      },
      status: PetStatus.HEALTHY,
      growthStage: GrowthStage.CHILD,
      mood: PetMood.CONTENT,
      skills: [],
      achievements: [],
      totalTokensEarned: '0',
      evolutionPoints: 0,
      breedCount: 0,
      generation: 1,
      traits: [],
      metadata: {
        version: '1.0.0',
        createdBy: 'test',
        lastModified: Date.now(),
        checksum: 'test-checksum',
        tags: [],
        description: '测试萌宠'
      }
    }

    mockPetStore.pets = [mockPet]
  })

  describe('action configurations', () => {
    it('should have valid feeding actions', () => {
      expect(nurturing.FEEDING_ACTIONS).toBeDefined()
      expect(nurturing.FEEDING_ACTIONS.basic_food).toBeDefined()
      expect(nurturing.FEEDING_ACTIONS.basic_food.type).toBe('feed')
      expect(nurturing.FEEDING_ACTIONS.basic_food.effects.health).toBeGreaterThan(0)
    })

    it('should have valid training types', () => {
      expect(nurturing.TRAINING_TYPES).toBeDefined()
      expect(nurturing.TRAINING_TYPES.strength_training).toBeDefined()
      expect(nurturing.TRAINING_TYPES.strength_training.targetStat).toBe('strength')
      expect(nurturing.TRAINING_TYPES.strength_training.energyCost).toBeGreaterThan(0)
    })

    it('should have valid play actions', () => {
      expect(nurturing.PLAY_ACTIONS).toBeDefined()
      expect(nurturing.PLAY_ACTIONS.simple_play).toBeDefined()
      expect(nurturing.PLAY_ACTIONS.simple_play.type).toBe('play')
      expect(nurturing.PLAY_ACTIONS.simple_play.effects.happiness).toBeGreaterThan(0)
    })
  })

  describe('canPerformAction', () => {
    it('should allow feeding after cooldown period', () => {
      const result = nurturing.canPerformAction(mockPet.id, 'basic_food')
      expect(result.canPerform).toBe(true)
    })

    it('should prevent training when energy is insufficient', () => {
      const lowEnergyPet = {
        ...mockPet,
        energy: 10
      }
      mockPetStore.pets = [lowEnergyPet]

      const result = nurturing.canPerformAction(lowEnergyPet.id, 'strength_training')
      expect(result.canPerform).toBe(false)
      expect(result.reason).toContain('能量不足')
    })

    it('should prevent actions when pet is sick', () => {
      const sickPet = {
        ...mockPet,
        status: PetStatus.SICK
      }
      mockPetStore.pets = [sickPet]

      const result = nurturing.canPerformAction(sickPet.id, 'basic_food')
      expect(result.canPerform).toBe(false)
      expect(result.reason).toContain('生病')
    })
  })

  describe('feedPet', () => {
    it('should successfully feed pet with basic food', async () => {
      const result = await nurturing.feedPet(mockPet.id, 'basic_food')

      expect(result).toBe(true)
      expect(mockPetStore.updatePet).toHaveBeenCalledWith(
        mockPet.id,
        expect.objectContaining({
          health: expect.any(Number),
          happiness: expect.any(Number),
          experience: expect.any(Number),
          lastFeedTime: expect.any(Number)
        })
      )
      expect(mockPetStore.checkLevelUp).toHaveBeenCalledWith(mockPet.id)
    })

    it('should throw error when pet does not exist', async () => {
      await expect(nurturing.feedPet('non-existent-pet', 'basic_food'))
        .rejects.toThrow('萌宠不存在')
    })
  })

  describe('trainPet', () => {
    it('should successfully train pet strength', async () => {
      const result = await nurturing.trainPet(mockPet.id, 'strength_training')

      expect(result).toBe(true)
      expect(mockPetStore.updatePet).toHaveBeenCalledWith(
        mockPet.id,
        expect.objectContaining({
          energy: expect.any(Number),
          experience: expect.any(Number),
          stats: expect.objectContaining({
            strength: expect.any(Number)
          }),
          lastTrainTime: expect.any(Number)
        })
      )
      expect(mockPetStore.checkLevelUp).toHaveBeenCalledWith(mockPet.id)
    })

    it('should handle comprehensive training with random stat', async () => {
      const result = await nurturing.trainPet(mockPet.id, 'comprehensive_training')

      expect(result).toBe(true)
      expect(mockPetStore.updatePet).toHaveBeenCalledWith(
        mockPet.id,
        expect.objectContaining({
          stats: expect.any(Object)
        })
      )
    })

    it('should throw error when energy is insufficient', async () => {
      const lowEnergyPet = {
        ...mockPet,
        energy: 10
      }
      mockPetStore.pets = [lowEnergyPet]

      await expect(nurturing.trainPet(lowEnergyPet.id, 'strength_training'))
        .rejects.toThrow('能量不足')
    })
  })

  describe('playWithPet', () => {
    it('should successfully play with pet', async () => {
      const result = await nurturing.playWithPet(mockPet.id, 'simple_play')

      expect(result).toBe(true)
      expect(mockPetStore.updatePet).toHaveBeenCalledWith(
        mockPet.id,
        expect.objectContaining({
          happiness: expect.any(Number),
          experience: expect.any(Number),
          energy: expect.any(Number),
          lastPlayTime: expect.any(Number)
        })
      )
      expect(mockPetStore.checkLevelUp).toHaveBeenCalledWith(mockPet.id)
    })

    it('should consume energy during play', async () => {
      await nurturing.playWithPet(mockPet.id, 'simple_play')

      const updateCall = mockPetStore.updatePet.mock.calls[0][1]
      expect(updateCall.energy).toBeLessThan(mockPet.energy)
    })
  })

  describe('restPet', () => {
    it('should successfully rest pet', async () => {
      const result = await nurturing.restPet(mockPet.id)

      expect(result).toBe(true)
      expect(mockPetStore.updatePet).toHaveBeenCalledWith(
        mockPet.id,
        expect.objectContaining({
          energy: expect.any(Number),
          health: expect.any(Number),
          lastRestTime: expect.any(Number),
          status: PetStatus.SLEEPING
        })
      )
    })

    it('should restore energy and health', async () => {
      const tiredPet = {
        ...mockPet,
        energy: 50,
        health: 70
      }
      mockPetStore.pets = [tiredPet]

      await nurturing.restPet(tiredPet.id)

      const updateCall = mockPetStore.updatePet.mock.calls[0][1]
      expect(updateCall.energy).toBeGreaterThan(tiredPet.energy)
      expect(updateCall.health).toBeGreaterThan(tiredPet.health)
    })
  })

  describe('getCooldownRemaining', () => {
    it('should return 0 when no cooldown', () => {
      const remaining = nurturing.getCooldownRemaining(mockPet.id, 'basic_food')
      expect(remaining).toBe(0)
    })
  })

  describe('startAutoGrowth', () => {
    it('should return cleanup function', () => {
      const cleanup = nurturing.startAutoGrowth(mockPet.id)
      expect(typeof cleanup).toBe('function')

      // Call cleanup to prevent memory leaks in tests
      cleanup()
    })
  })
})