import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWalletStore } from '../stores/wallet'
import { tokenManagementService } from '../services/token-management.service'
import type {
  TokenBalance,
  TransferRequest,
  TransactionHistory,
  TokenStats
} from '../services/token-management.service'
import type { TaxInfo } from '../services/contract.service'
import { showToast } from 'vant'

export function useTokenManagement() {
  const walletStore = useWalletStore()

  // 响应式数据
  const tokenBalance = ref<TokenBalance | null>(null)
  const transactionHistory = ref<TransactionHistory[]>([])
  const stats = ref<TokenStats>({
    totalTransactions: 0,
    totalSent: '0',
    totalReceived: '0',
    totalTaxPaid: '0',
    averageTransactionAmount: '0',
    lastTransactionTime: 0
  })
  const taxInfo = ref<TaxInfo | null>(null)

  // 加载状态
  const balanceLoading = ref(false)
  const transferLoading = ref(false)
  const taxInfoLoading = ref(false)

  // 计算属性
  const hasBalance = computed(() => {
    return tokenBalance.value && parseFloat(tokenBalance.value.balanceFormatted) > 0
  })

  const formattedBalance = computed(() => {
    if (!tokenBalance.value) return '0.000000'
    return tokenBalance.value.balanceFormatted
  })

  const balanceSymbol = computed(() => {
    return tokenBalance.value?.symbol || 'TOKEN'
  })

  const recentTransactions = computed(() => {
    return transactionHistory.value.slice(0, 5)
  })

  const pendingTransactions = computed(() => {
    return transactionHistory.value.filter(tx => tx.status === 'pending')
  })

  const confirmedTransactions = computed(() => {
    return transactionHistory.value.filter(tx => tx.status === 'confirmed')
  })

  const failedTransactions = computed(() => {
    return transactionHistory.value.filter(tx => tx.status === 'failed')
  })

  // 方法
  const loadBalance = async (forceRefresh = false) => {
    if (!walletStore.address) return

    balanceLoading.value = true
    try {
      tokenBalance.value = await tokenManagementService.getBalance(walletStore.address, forceRefresh)
    } catch (error) {
      console.error('加载余额失败:', error)
      showToast('加载余额失败')
    } finally {
      balanceLoading.value = false
    }
  }

  const refreshBalance = async () => {
    await loadBalance(true)
    showToast('余额已更新')
  }

  const loadTransactionHistory = () => {
    if (!walletStore.address) return

    transactionHistory.value = tokenManagementService.getTransactionHistory(walletStore.address)
    stats.value = tokenManagementService.getTokenStats(walletStore.address)
  }

  const loadTaxInfo = async () => {
    taxInfoLoading.value = true
    try {
      taxInfo.value = await tokenManagementService.getTaxInfo()
    } catch (error) {
      console.error('加载税收信息失败:', error)
    } finally {
      taxInfoLoading.value = false
    }
  }

  const transfer = async (request: TransferRequest) => {
    if (!walletStore.address) {
      throw new Error('请先连接钱包')
    }

    transferLoading.value = true
    try {
      const transaction = await tokenManagementService.transfer(request, walletStore.address)

      // 刷新数据
      await loadBalance()
      loadTransactionHistory()

      return transaction
    } finally {
      transferLoading.value = false
    }
  }

  const safeTransfer = async (request: TransferRequest) => {
    if (!walletStore.address) {
      throw new Error('请先连接钱包')
    }

    transferLoading.value = true
    try {
      const transaction = await tokenManagementService.safeTransfer(request, walletStore.address)

      // 刷新数据
      await loadBalance()
      loadTransactionHistory()

      return transaction
    } finally {
      transferLoading.value = false
    }
  }

  const batchTransfer = async (recipients: string[], amounts: string[]) => {
    if (!walletStore.address) {
      throw new Error('请先连接钱包')
    }

    transferLoading.value = true
    try {
      const transaction = await tokenManagementService.batchTransfer(recipients, amounts, walletStore.address)

      // 刷新数据
      await loadBalance()
      loadTransactionHistory()

      return transaction
    } finally {
      transferLoading.value = false
    }
  }

  const previewV2Transfer = async (to: string, amount: string) => {
    if (!walletStore.address) {
      throw new Error('请先连接钱包')
    }

    return await tokenManagementService.previewV2Transfer(walletStore.address, to, amount)
  }

  const calculateTax = async (amount: string) => {
    return await tokenManagementService.calculateTax(amount)
  }

  const clearHistory = () => {
    tokenManagementService.clearTransactionHistory()
    loadTransactionHistory()
  }

  const clearCache = () => {
    tokenManagementService.clearBalanceCache()
  }

  // 事件监听器
  const setupEventListeners = () => {
    tokenManagementService.addEventListener('balanceUpdated', () => {
      loadBalance()
    })

    tokenManagementService.addEventListener('transactionAdded', () => {
      loadTransactionHistory()
    })

    tokenManagementService.addEventListener('transferCompleted', (data) => {
      loadBalance()
      loadTransactionHistory()

      if (data.success) {
        showToast('转账成功')
      } else {
        showToast(data.error || '转账失败')
      }
    })
  }

  const cleanup = () => {
    tokenManagementService.cleanup()
  }

  // 初始化
  const initialize = async () => {
    if (walletStore.isConnected && walletStore.address) {
      await loadBalance()
      loadTransactionHistory()
      await loadTaxInfo()
    }
    setupEventListeners()
  }

  // 生命周期钩子
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // 响应式数据
    tokenBalance,
    transactionHistory,
    stats,
    taxInfo,

    // 加载状态
    balanceLoading,
    transferLoading,
    taxInfoLoading,

    // 计算属性
    hasBalance,
    formattedBalance,
    balanceSymbol,
    recentTransactions,
    pendingTransactions,
    confirmedTransactions,
    failedTransactions,

    // 方法
    loadBalance,
    refreshBalance,
    loadTransactionHistory,
    loadTaxInfo,
    transfer,
    safeTransfer,
    batchTransfer,
    previewV2Transfer,
    calculateTax,
    clearHistory,
    clearCache,
    initialize,
    cleanup
  }
}
