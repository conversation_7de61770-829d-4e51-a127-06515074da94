import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface BreakpointConfig {
  sm: number
  md: number
  lg: number
  xl: number
  '2xl': number
}

const defaultBreakpoints: BreakpointConfig = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

export function useResponsive(breakpoints: Partial<BreakpointConfig> = {}) {
  const config = { ...defaultBreakpoints, ...breakpoints }

  // 当前窗口宽度
  const windowWidth = ref(0)

  // 更新窗口宽度
  const updateWidth = () => {
    windowWidth.value = window.innerWidth
  }

  // 响应式断点检测
  const isSm = computed(() => windowWidth.value >= config.sm)
  const isMd = computed(() => windowWidth.value >= config.md)
  const isLg = computed(() => windowWidth.value >= config.lg)
  const isXl = computed(() => windowWidth.value >= config.xl)
  const is2xl = computed(() => windowWidth.value >= config['2xl'])

  // 当前断点
  const currentBreakpoint = computed(() => {
    if (windowWidth.value >= config['2xl']) return '2xl'
    if (windowWidth.value >= config.xl) return 'xl'
    if (windowWidth.value >= config.lg) return 'lg'
    if (windowWidth.value >= config.md) return 'md'
    if (windowWidth.value >= config.sm) return 'sm'
    return 'xs'
  })

  // 是否为移动端
  const isMobile = computed(() => windowWidth.value < config.md)

  // 是否为平板端
  const isTablet = computed(() => windowWidth.value >= config.md && windowWidth.value < config.lg)

  // 是否为桌面端
  const isDesktop = computed(() => windowWidth.value >= config.lg)

  // 响应式网格列数
  const gridCols = computed(() => {
    if (windowWidth.value >= config['2xl']) return 6
    if (windowWidth.value >= config.xl) return 5
    if (windowWidth.value >= config.lg) return 4
    if (windowWidth.value >= config.md) return 3
    if (windowWidth.value >= config.sm) return 2
    return 1
  })

  // 响应式间距
  const spacing = computed(() => {
    if (windowWidth.value >= config.lg) return 'lg'
    if (windowWidth.value >= config.md) return 'md'
    return 'sm'
  })

  // 响应式字体大小
  const fontSize = computed(() => {
    if (windowWidth.value >= config.lg) return 'lg'
    if (windowWidth.value >= config.md) return 'md'
    return 'sm'
  })

  // 响应式容器宽度
  const containerWidth = computed(() => {
    if (windowWidth.value >= config['2xl']) return 'max-w-7xl'
    if (windowWidth.value >= config.xl) return 'max-w-6xl'
    if (windowWidth.value >= config.lg) return 'max-w-5xl'
    if (windowWidth.value >= config.md) return 'max-w-4xl'
    return 'max-w-full'
  })

  // 响应式内边距
  const containerPadding = computed(() => {
    if (windowWidth.value >= config.lg) return 'px-8'
    if (windowWidth.value >= config.md) return 'px-6'
    return 'px-4'
  })

  // 获取响应式值
  const getResponsiveValue = <T>(values: {
    xs?: T
    sm?: T
    md?: T
    lg?: T
    xl?: T
    '2xl'?: T
  }, fallback: T): T => {
    const breakpoint = currentBreakpoint.value

    // 按优先级查找值
    if (breakpoint === '2xl' && values['2xl'] !== undefined) return values['2xl']
    if ((breakpoint === '2xl' || breakpoint === 'xl') && values.xl !== undefined) return values.xl
    if ((breakpoint === '2xl' || breakpoint === 'xl' || breakpoint === 'lg') && values.lg !== undefined) return values.lg
    if ((breakpoint !== 'xs' && breakpoint !== 'sm') && values.md !== undefined) return values.md
    if (breakpoint !== 'xs' && values.sm !== undefined) return values.sm
    if (values.xs !== undefined) return values.xs

    return fallback
  }

  // 检测设备类型
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })

  // 检测屏幕方向
  const orientation = ref<'portrait' | 'landscape'>('portrait')

  const updateOrientation = () => {
    orientation.value = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
  }

  // 检测触摸设备
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  })

  // 检测高分辨率屏幕
  const isHighDPI = computed(() => {
    return window.devicePixelRatio > 1
  })

  // 生命周期管理
  onMounted(() => {
    updateWidth()
    updateOrientation()

    window.addEventListener('resize', updateWidth, { passive: true })
    window.addEventListener('orientationchange', updateOrientation, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
    window.removeEventListener('orientationchange', updateOrientation)
  })

  return {
    // 窗口信息
    windowWidth: readonly(windowWidth),

    // 断点检测
    isSm: readonly(isSm),
    isMd: readonly(isMd),
    isLg: readonly(isLg),
    isXl: readonly(isXl),
    is2xl: readonly(is2xl),
    currentBreakpoint: readonly(currentBreakpoint),

    // 设备类型
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
    deviceType: readonly(deviceType),

    // 屏幕信息
    orientation: readonly(orientation),
    isTouchDevice: readonly(isTouchDevice),
    isHighDPI: readonly(isHighDPI),

    // 响应式值
    gridCols: readonly(gridCols),
    spacing: readonly(spacing),
    fontSize: readonly(fontSize),
    containerWidth: readonly(containerWidth),
    containerPadding: readonly(containerPadding),

    // 工具方法
    getResponsiveValue
  }
}

// 全局响应式实例
let globalResponsive: ReturnType<typeof useResponsive> | null = null

export function useGlobalResponsive() {
  if (!globalResponsive) {
    globalResponsive = useResponsive()
  }
  return globalResponsive
}
