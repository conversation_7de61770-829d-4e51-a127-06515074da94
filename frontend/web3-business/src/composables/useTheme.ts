import { ref, computed, watch, readonly } from 'vue'
import { useStorage } from '@vueuse/core'

export type ThemeMode = 'light' | 'dark' | 'auto'
export type ColorScheme = 'purple' | 'blue' | 'green' | 'orange' | 'pink'

interface ThemeConfig {
  mode: ThemeMode
  colorScheme: ColorScheme
  fontSize: 'small' | 'medium' | 'large'
  animations: boolean
  reducedMotion: boolean
}

const defaultTheme: ThemeConfig = {
  mode: 'light',
  colorScheme: 'purple',
  fontSize: 'medium',
  animations: true,
  reducedMotion: false
}

// 持久化主题配置
const themeConfig = useStorage('pet-game-theme', defaultTheme)

// 系统主题检测
const systemTheme = ref<'light' | 'dark'>('light')

// 检测系统主题偏好
const detectSystemTheme = () => {
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemTheme.value = mediaQuery.matches ? 'dark' : 'light'

    mediaQuery.addEventListener('change', (e) => {
      systemTheme.value = e.matches ? 'dark' : 'light'
    })
  }
}

// 检测用户是否偏好减少动画
const detectReducedMotion = () => {
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    themeConfig.value.reducedMotion = mediaQuery.matches

    mediaQuery.addEventListener('change', (e) => {
      themeConfig.value.reducedMotion = e.matches
    })
  }
}

export function useTheme() {
  // 计算当前主题
  const currentTheme = computed(() => {
    if (themeConfig.value.mode === 'auto') {
      return systemTheme.value
    }
    return themeConfig.value.mode
  })

  // 主题类名
  const themeClass = computed(() => {
    const classes = []

    // 主题模式
    classes.push(`theme-${currentTheme.value}`)

    // 颜色方案
    classes.push(`color-${themeConfig.value.colorScheme}`)

    // 字体大小
    classes.push(`font-${themeConfig.value.fontSize}`)

    // 动画设置
    if (!themeConfig.value.animations || themeConfig.value.reducedMotion) {
      classes.push('no-animations')
    }

    return classes.join(' ')
  })

  // CSS 变量
  const cssVariables = computed(() => {
    const colorSchemes = {
      purple: {
        '--primary-50': '#faf5ff',
        '--primary-100': '#f3e8ff',
        '--primary-200': '#e9d5ff',
        '--primary-300': '#d8b4fe',
        '--primary-400': '#c084fc',
        '--primary-500': '#a855f7',
        '--primary-600': '#9333ea',
        '--primary-700': '#7c3aed',
        '--primary-800': '#6b21a8',
        '--primary-900': '#581c87'
      },
      blue: {
        '--primary-50': '#eff6ff',
        '--primary-100': '#dbeafe',
        '--primary-200': '#bfdbfe',
        '--primary-300': '#93c5fd',
        '--primary-400': '#60a5fa',
        '--primary-500': '#3b82f6',
        '--primary-600': '#2563eb',
        '--primary-700': '#1d4ed8',
        '--primary-800': '#1e40af',
        '--primary-900': '#1e3a8a'
      },
      green: {
        '--primary-50': '#f0fdf4',
        '--primary-100': '#dcfce7',
        '--primary-200': '#bbf7d0',
        '--primary-300': '#86efac',
        '--primary-400': '#4ade80',
        '--primary-500': '#22c55e',
        '--primary-600': '#16a34a',
        '--primary-700': '#15803d',
        '--primary-800': '#166534',
        '--primary-900': '#14532d'
      },
      orange: {
        '--primary-50': '#fff7ed',
        '--primary-100': '#ffedd5',
        '--primary-200': '#fed7aa',
        '--primary-300': '#fdba74',
        '--primary-400': '#fb923c',
        '--primary-500': '#f97316',
        '--primary-600': '#ea580c',
        '--primary-700': '#c2410c',
        '--primary-800': '#9a3412',
        '--primary-900': '#7c2d12'
      },
      pink: {
        '--primary-50': '#fdf2f8',
        '--primary-100': '#fce7f3',
        '--primary-200': '#fbcfe8',
        '--primary-300': '#f9a8d4',
        '--primary-400': '#f472b6',
        '--primary-500': '#ec4899',
        '--primary-600': '#db2777',
        '--primary-700': '#be185d',
        '--primary-800': '#9d174d',
        '--primary-900': '#831843'
      }
    }

    const fontSizes = {
      small: {
        '--font-size-xs': '0.75rem',
        '--font-size-sm': '0.875rem',
        '--font-size-base': '1rem',
        '--font-size-lg': '1.125rem',
        '--font-size-xl': '1.25rem',
        '--font-size-2xl': '1.5rem',
        '--font-size-3xl': '1.875rem'
      },
      medium: {
        '--font-size-xs': '0.75rem',
        '--font-size-sm': '0.875rem',
        '--font-size-base': '1rem',
        '--font-size-lg': '1.125rem',
        '--font-size-xl': '1.25rem',
        '--font-size-2xl': '1.5rem',
        '--font-size-3xl': '1.875rem'
      },
      large: {
        '--font-size-xs': '0.875rem',
        '--font-size-sm': '1rem',
        '--font-size-base': '1.125rem',
        '--font-size-lg': '1.25rem',
        '--font-size-xl': '1.5rem',
        '--font-size-2xl': '1.875rem',
        '--font-size-3xl': '2.25rem'
      }
    }

    return {
      ...colorSchemes[themeConfig.value.colorScheme],
      ...fontSizes[themeConfig.value.fontSize]
    }
  })

  // 应用主题到 DOM
  const applyTheme = () => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement

      // 应用主题类
      root.className = root.className
        .split(' ')
        .filter(cls => !cls.startsWith('theme-') && !cls.startsWith('color-') && !cls.startsWith('font-') && cls !== 'no-animations')
        .concat(themeClass.value.split(' '))
        .join(' ')

      // 应用 CSS 变量
      Object.entries(cssVariables.value).forEach(([key, value]) => {
        root.style.setProperty(key, value)
      })
    }
  }

  // 设置主题模式
  const setThemeMode = (mode: ThemeMode) => {
    themeConfig.value.mode = mode
  }

  // 设置颜色方案
  const setColorScheme = (scheme: ColorScheme) => {
    themeConfig.value.colorScheme = scheme
  }

  // 设置字体大小
  const setFontSize = (size: 'small' | 'medium' | 'large') => {
    themeConfig.value.fontSize = size
  }

  // 切换动画
  const toggleAnimations = () => {
    themeConfig.value.animations = !themeConfig.value.animations
  }

  // 切换主题模式
  const toggleTheme = () => {
    const modes: ThemeMode[] = ['light', 'dark', 'auto']
    const currentIndex = modes.indexOf(themeConfig.value.mode)
    const nextIndex = (currentIndex + 1) % modes.length
    setThemeMode(modes[nextIndex])
  }

  // 重置主题
  const resetTheme = () => {
    themeConfig.value = { ...defaultTheme }
  }

  // 获取主题预设
  const getThemePresets = () => {
    return [
      { name: '紫色梦幻', mode: 'light' as ThemeMode, colorScheme: 'purple' as ColorScheme },
      { name: '深蓝科技', mode: 'dark' as ThemeMode, colorScheme: 'blue' as ColorScheme },
      { name: '清新绿意', mode: 'light' as ThemeMode, colorScheme: 'green' as ColorScheme },
      { name: '温暖橙光', mode: 'light' as ThemeMode, colorScheme: 'orange' as ColorScheme },
      { name: '浪漫粉色', mode: 'light' as ThemeMode, colorScheme: 'pink' as ColorScheme },
      { name: '跟随系统', mode: 'auto' as ThemeMode, colorScheme: 'purple' as ColorScheme }
    ]
  }

  // 应用预设主题
  const applyPreset = (preset: { mode: ThemeMode; colorScheme: ColorScheme }) => {
    themeConfig.value.mode = preset.mode
    themeConfig.value.colorScheme = preset.colorScheme
  }

  // 监听主题变化
  watch(
    [themeConfig, systemTheme],
    () => {
      applyTheme()
    },
    { deep: true, immediate: true }
  )

  // 初始化
  const initTheme = () => {
    detectSystemTheme()
    detectReducedMotion()
    applyTheme()
  }

  return {
    // 状态
    themeConfig: readonly(themeConfig),
    currentTheme,
    themeClass,
    cssVariables,

    // 方法
    setThemeMode,
    setColorScheme,
    setFontSize,
    toggleAnimations,
    toggleTheme,
    resetTheme,
    getThemePresets,
    applyPreset,
    initTheme
  }
}

// 全局主题实例
let globalTheme: ReturnType<typeof useTheme> | null = null

export function useGlobalTheme() {
  if (!globalTheme) {
    globalTheme = useTheme()
  }
  return globalTheme
}
