import { ref, computed, watch } from 'vue'
import { usePetStore } from '../stores/pet'
import type { Pet, Item } from '../types/typesWithoutCircular'
import { PetStatus, PetMood } from '../types/typesWithoutCircular'

export interface NurturingAction {
  type: 'feed' | 'train' | 'play' | 'rest'
  name: string
  description: string
  energyCost: number
  cooldown: number // in milliseconds
  effects: {
    health?: number
    happiness?: number
    experience?: number
    energy?: number
    stats?: Partial<Pet['stats']>
  }
  requirements?: {
    minEnergy?: number
    minHealth?: number
    items?: string[]
  }
}

export interface TrainingType {
  id: string
  name: string
  description: string
  targetStat: keyof Pet['stats']
  energyCost: number
  experienceGain: number
  statGain: number
  cooldown: number
  requirements: {
    minLevel: number
    minEnergy: number
  }
}

// 喂食动作配置
const FEEDING_ACTIONS: Record<string, NurturingAction> = {
  basic_food: {
    type: 'feed',
    name: '基础食物',
    description: '提供基本营养，恢复健康度和少量经验',
    energyCost: 0,
    cooldown: 30 * 60 * 1000, // 30分钟
    effects: {
      health: 20,
      happiness: 10,
      experience: 5
    }
  },
  premium_food: {
    type: 'feed',
    name: '高级食物',
    description: '营养丰富，大幅恢复健康度和经验',
    energyCost: 0,
    cooldown: 60 * 60 * 1000, // 1小时
    effects: {
      health: 40,
      happiness: 20,
      experience: 15
    },
    requirements: {
      items: ['premium_food']
    }
  },
  magical_food: {
    type: 'feed',
    name: '魔法食物',
    description: '神奇的食物，全面提升萌宠状态',
    energyCost: 0,
    cooldown: 2 * 60 * 60 * 1000, // 2小时
    effects: {
      health: 60,
      happiness: 30,
      experience: 25,
      energy: 20
    },
    requirements: {
      items: ['magical_food']
    }
  }
}

// 训练类型配置
const TRAINING_TYPES: Record<string, TrainingType> = {
  strength_training: {
    id: 'strength_training',
    name: '力量训练',
    description: '通过重量训练提升力量属性',
    targetStat: 'strength',
    energyCost: 25,
    experienceGain: 15,
    statGain: 2,
    cooldown: 60 * 60 * 1000, // 1小时
    requirements: {
      minLevel: 1,
      minEnergy: 25
    }
  },
  intelligence_training: {
    id: 'intelligence_training',
    name: '智力训练',
    description: '通过学习和思考提升智力属性',
    targetStat: 'intelligence',
    energyCost: 20,
    experienceGain: 18,
    statGain: 2,
    cooldown: 45 * 60 * 1000, // 45分钟
    requirements: {
      minLevel: 1,
      minEnergy: 20
    }
  },
  agility_training: {
    id: 'agility_training',
    name: '敏捷训练',
    description: '通过敏捷练习提升敏捷属性',
    targetStat: 'agility',
    energyCost: 30,
    experienceGain: 12,
    statGain: 3,
    cooldown: 50 * 60 * 1000, // 50分钟
    requirements: {
      minLevel: 1,
      minEnergy: 30
    }
  },
  charm_training: {
    id: 'charm_training',
    name: '魅力训练',
    description: '通过社交活动提升魅力属性',
    targetStat: 'charm',
    energyCost: 15,
    experienceGain: 10,
    statGain: 2,
    cooldown: 40 * 60 * 1000, // 40分钟
    requirements: {
      minLevel: 3,
      minEnergy: 15
    }
  },
  vitality_training: {
    id: 'vitality_training',
    name: '体质训练',
    description: '通过耐力训练提升体质属性',
    targetStat: 'vitality',
    energyCost: 35,
    experienceGain: 20,
    statGain: 2,
    cooldown: 70 * 60 * 1000, // 70分钟
    requirements: {
      minLevel: 5,
      minEnergy: 35
    }
  },
  comprehensive_training: {
    id: 'comprehensive_training',
    name: '综合训练',
    description: '全面训练，随机提升一个属性',
    targetStat: 'strength', // 会被随机覆盖
    energyCost: 40,
    experienceGain: 25,
    statGain: 1,
    cooldown: 90 * 60 * 1000, // 90分钟
    requirements: {
      minLevel: 10,
      minEnergy: 40
    }
  }
}

// 玩耍动作配置
const PLAY_ACTIONS: Record<string, NurturingAction> = {
  simple_play: {
    type: 'play',
    name: '简单玩耍',
    description: '和萌宠一起玩耍，提升快乐度',
    energyCost: 10,
    cooldown: 20 * 60 * 1000, // 20分钟
    effects: {
      happiness: 15,
      experience: 3
    },
    requirements: {
      minEnergy: 10
    }
  },
  interactive_play: {
    type: 'play',
    name: '互动游戏',
    description: '进行互动游戏，大幅提升快乐度和经验',
    energyCost: 20,
    cooldown: 45 * 60 * 1000, // 45分钟
    effects: {
      happiness: 25,
      experience: 8,
      energy: -5
    },
    requirements: {
      minEnergy: 20
    }
  },
  adventure_play: {
    type: 'play',
    name: '冒险探索',
    description: '带萌宠去冒险，获得大量经验和可能的奖励',
    energyCost: 35,
    cooldown: 2 * 60 * 60 * 1000, // 2小时
    effects: {
      happiness: 20,
      experience: 30,
      energy: -10
    },
    requirements: {
      minEnergy: 35,
      minHealth: 50
    }
  }
}

export function usePetNurturing() {
  const petStore = usePetStore()
  const isProcessing = ref(false)
  const lastActionTime = ref<Record<string, number>>({})
  const actionHistory = ref<Array<{
    petId: string
    action: string
    timestamp: number
    result: string
  }>>([])

  // 计算冷却时间
  const getCooldownRemaining = (petId: string, actionType: string): number => {
    const key = `${petId}_${actionType}`
    const lastTime = lastActionTime.value[key] || 0
    const action = getAllActions()[actionType]
    if (!action) return 0

    const elapsed = Date.now() - lastTime
    return Math.max(0, action.cooldown - elapsed)
  }

  // 检查动作是否可用
  const canPerformAction = (petId: string, actionType: string): { canPerform: boolean; reason?: string } => {
    const pet = petStore.pets.find(p => p.id === petId)
    if (!pet) {
      return { canPerform: false, reason: '萌宠不存在' }
    }

    const action = getAllActions()[actionType]
    if (!action) {
      return { canPerform: false, reason: '动作不存在' }
    }

    // 检查冷却时间
    const cooldownRemaining = getCooldownRemaining(petId, actionType)
    if (cooldownRemaining > 0) {
      const minutes = Math.ceil(cooldownRemaining / (60 * 1000))
      return { canPerform: false, reason: `还需等待 ${minutes} 分钟` }
    }

    // 检查能量要求
    if (action.requirements?.minEnergy && pet.energy < action.requirements.minEnergy) {
      return { canPerform: false, reason: `能量不足，需要 ${action.requirements.minEnergy} 点能量` }
    }

    // 检查健康度要求
    if (action.requirements?.minHealth && pet.health < action.requirements.minHealth) {
      return { canPerform: false, reason: `健康度不足，需要 ${action.requirements.minHealth} 点健康度` }
    }

    // 检查萌宠状态
    if (pet.status === PetStatus.SICK) {
      return { canPerform: false, reason: '萌宠生病了，需要先治疗' }
    }

    if (pet.status === PetStatus.SLEEPING) {
      return { canPerform: false, reason: '萌宠正在睡觉' }
    }

    return { canPerform: true }
  }

  // 喂食萌宠
  const feedPet = async (petId: string, foodType: string = 'basic_food'): Promise<boolean> => {
    if (isProcessing.value) return false

    const canPerform = canPerformAction(petId, foodType)
    if (!canPerform.canPerform) {
      throw new Error(canPerform.reason)
    }

    isProcessing.value = true

    try {
      const pet = petStore.pets.find(p => p.id === petId)!
      const action = FEEDING_ACTIONS[foodType]

      // 应用喂食效果
      const updates: Partial<Pet> = {
        lastFeedTime: Date.now()
      }

      if (action.effects.health) {
        updates.health = Math.min(pet.health + action.effects.health, pet.maxHealth)
      }

      if (action.effects.happiness) {
        updates.happiness = Math.min(pet.happiness + action.effects.happiness, pet.maxHappiness)
      }

      if (action.effects.experience) {
        updates.experience = pet.experience + action.effects.experience
      }

      if (action.effects.energy) {
        updates.energy = Math.min(pet.energy + action.effects.energy, pet.maxEnergy)
      }

      // 更新萌宠状态
      petStore.updatePet(petId, updates)

      // 检查升级
      petStore.checkLevelUp(petId)

      // 记录动作时间
      lastActionTime.value[`${petId}_${foodType}`] = Date.now()

      // 记录历史
      actionHistory.value.push({
        petId,
        action: `喂食: ${action.name}`,
        timestamp: Date.now(),
        result: '成功'
      })

      // 更新萌宠心情
      updatePetMood(petId, 'happy')

      return true
    } catch (error) {
      console.error('喂食失败:', error)
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  // 训练萌宠
  const trainPet = async (petId: string, trainingType: string): Promise<boolean> => {
    if (isProcessing.value) return false

    const canPerform = canPerformAction(petId, trainingType)
    if (!canPerform.canPerform) {
      throw new Error(canPerform.reason)
    }

    isProcessing.value = true

    try {
      const pet = petStore.pets.find(p => p.id === petId)!
      const training = TRAINING_TYPES[trainingType]

      // 消耗能量
      const newEnergy = pet.energy - training.energyCost

      // 获得经验
      const newExperience = pet.experience + training.experienceGain

      // 提升属性
      let targetStat = training.targetStat
      if (trainingType === 'comprehensive_training') {
        // 综合训练随机选择属性
        const stats = ['strength', 'intelligence', 'agility', 'charm', 'vitality', 'luck'] as const
        targetStat = stats[Math.floor(Math.random() * stats.length)]
      }

      const newStats = {
        ...pet.stats,
        [targetStat]: pet.stats[targetStat] + training.statGain
      }

      // 更新萌宠
      petStore.updatePet(petId, {
        energy: newEnergy,
        experience: newExperience,
        stats: newStats,
        lastTrainTime: Date.now()
      })

      // 检查升级
      petStore.checkLevelUp(petId)

      // 记录动作时间
      lastActionTime.value[`${petId}_${trainingType}`] = Date.now()

      // 记录历史
      actionHistory.value.push({
        petId,
        action: `训练: ${training.name}`,
        timestamp: Date.now(),
        result: `${targetStat} +${training.statGain}`
      })

      // 更新萌宠心情
      updatePetMood(petId, 'focused')

      return true
    } catch (error) {
      console.error('训练失败:', error)
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  // 和萌宠玩耍
  const playWithPet = async (petId: string, playType: string = 'simple_play'): Promise<boolean> => {
    if (isProcessing.value) return false

    const canPerform = canPerformAction(petId, playType)
    if (!canPerform.canPerform) {
      throw new Error(canPerform.reason)
    }

    isProcessing.value = true

    try {
      const pet = petStore.pets.find(p => p.id === petId)!
      const action = PLAY_ACTIONS[playType]

      // 应用玩耍效果
      const updates: Partial<Pet> = {
        lastPlayTime: Date.now()
      }

      if (action.effects.happiness) {
        updates.happiness = Math.min(pet.happiness + action.effects.happiness, pet.maxHappiness)
      }

      if (action.effects.experience) {
        updates.experience = pet.experience + action.effects.experience
      }

      if (action.effects.energy) {
        updates.energy = Math.max(0, pet.energy + action.effects.energy)
      }

      // 消耗能量
      updates.energy = Math.max(0, (updates.energy || pet.energy) - action.energyCost)

      // 更新萌宠
      petStore.updatePet(petId, updates)

      // 检查升级
      petStore.checkLevelUp(petId)

      // 记录动作时间
      lastActionTime.value[`${petId}_${playType}`] = Date.now()

      // 记录历史
      actionHistory.value.push({
        petId,
        action: `玩耍: ${action.name}`,
        timestamp: Date.now(),
        result: '成功'
      })

      // 更新萌宠心情
      updatePetMood(petId, 'playful')

      return true
    } catch (error) {
      console.error('玩耍失败:', error)
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  // 让萌宠休息
  const restPet = async (petId: string): Promise<boolean> => {
    if (isProcessing.value) return false

    isProcessing.value = true

    try {
      const pet = petStore.pets.find(p => p.id === petId)
      if (!pet) {
        throw new Error('萌宠不存在')
      }

      const now = Date.now()
      const energyGain = Math.min(30, pet.maxEnergy - pet.energy)
      const healthGain = Math.min(10, pet.maxHealth - pet.health)

      petStore.updatePet(petId, {
        energy: pet.energy + energyGain,
        health: pet.health + healthGain,
        lastRestTime: now,
        status: PetStatus.SLEEPING
      })

      // 记录历史
      actionHistory.value.push({
        petId,
        action: '休息',
        timestamp: now,
        result: `能量 +${energyGain}, 健康 +${healthGain}`
      })

      // 设置定时器，一段时间后唤醒萌宠
      setTimeout(() => {
        const currentPet = petStore.pets.find(p => p.id === petId)
        if (currentPet && currentPet.status === PetStatus.SLEEPING) {
          petStore.updatePet(petId, {
            status: PetStatus.HEALTHY
          })
          updatePetMood(petId, 'content')
        }
      }, 30 * 60 * 1000) // 30分钟后自动醒来

      return true
    } catch (error) {
      console.error('休息失败:', error)
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  // 更新萌宠心情
  const updatePetMood = (petId: string, mood: PetMood) => {
    petStore.updatePet(petId, { mood })
  }

  // 获取所有动作
  const getAllActions = () => ({
    ...FEEDING_ACTIONS,
    ...TRAINING_TYPES,
    ...PLAY_ACTIONS
  })

  // 自动成长系统
  const startAutoGrowth = (petId: string) => {
    const interval = setInterval(() => {
      const pet = petStore.pets.find(p => p.id === petId)
      if (!pet) {
        clearInterval(interval)
        return
      }

      const now = Date.now()
      const updates: Partial<Pet> = {}

      // 时间流逝导致的状态变化
      const timeSinceLastFeed = now - pet.lastFeedTime
      const timeSinceLastPlay = now - pet.lastPlayTime

      // 饥饿度影响
      if (timeSinceLastFeed > 2 * 60 * 60 * 1000) { // 2小时没喂食
        if (pet.health > 10) {
          updates.health = Math.max(10, pet.health - 1)
        }
        if (pet.happiness > 0) {
          updates.happiness = Math.max(0, pet.happiness - 2)
        }
      }

      // 缺乏互动影响
      if (timeSinceLastPlay > 4 * 60 * 60 * 1000) { // 4小时没玩耍
        if (pet.happiness > 0) {
          updates.happiness = Math.max(0, pet.happiness - 1)
        }
      }

      // 自然能量恢复
      if (pet.energy < pet.maxEnergy && pet.status !== PetStatus.SLEEPING) {
        updates.energy = Math.min(pet.maxEnergy, pet.energy + 1)
      }

      // 根据状态更新心情
      if (pet.health < 30) {
        updates.mood = PetMood.SICK
        updates.status = PetStatus.SICK
      } else if (pet.happiness < 20) {
        updates.mood = PetMood.SAD
      } else if (pet.energy < 20) {
        updates.mood = PetMood.TIRED
      } else if (pet.happiness > 80 && pet.health > 80) {
        updates.mood = PetMood.HAPPY
      } else {
        updates.mood = PetMood.CONTENT
      }

      // 应用更新
      if (Object.keys(updates).length > 0) {
        petStore.updatePet(petId, updates)
      }
    }, 60 * 1000) // 每分钟检查一次

    return () => clearInterval(interval)
  }

  // 计算属性
  const availableActions = computed(() => {
    if (!petStore.currentPet) return []

    const actions = []
    const petId = petStore.currentPet.id

    // 喂食动作
    Object.entries(FEEDING_ACTIONS).forEach(([key, action]) => {
      const canPerform = canPerformAction(petId, key)
      actions.push({
        id: key,
        ...action,
        available: canPerform.canPerform,
        reason: canPerform.reason,
        cooldownRemaining: getCooldownRemaining(petId, key)
      })
    })

    // 训练动作
    Object.entries(TRAINING_TYPES).forEach(([key, training]) => {
      const canPerform = canPerformAction(petId, key)
      actions.push({
        id: key,
        type: 'train' as const,
        name: training.name,
        description: training.description,
        energyCost: training.energyCost,
        cooldown: training.cooldown,
        effects: {
          experience: training.experienceGain,
          stats: { [training.targetStat]: training.statGain }
        },
        available: canPerform.canPerform,
        reason: canPerform.reason,
        cooldownRemaining: getCooldownRemaining(petId, key)
      })
    })

    // 玩耍动作
    Object.entries(PLAY_ACTIONS).forEach(([key, action]) => {
      const canPerform = canPerformAction(petId, key)
      actions.push({
        id: key,
        ...action,
        available: canPerform.canPerform,
        reason: canPerform.reason,
        cooldownRemaining: getCooldownRemaining(petId, key)
      })
    })

    return actions
  })

  return {
    // 状态
    isProcessing: computed(() => isProcessing.value),
    actionHistory: computed(() => actionHistory.value),
    availableActions,

    // 方法
    feedPet,
    trainPet,
    playWithPet,
    restPet,
    canPerformAction,
    getCooldownRemaining,
    startAutoGrowth,
    updatePetMood,

    // 配置
    FEEDING_ACTIONS,
    TRAINING_TYPES,
    PLAY_ACTIONS
  }
}
