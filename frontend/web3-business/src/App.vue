<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'
import HelpButton from './components/HelpButton.vue'

// 应用加载状态
const isLoading = ref(true)

// 模拟应用初始化
onMounted(async () => {
  try {
    // 这里可以放置应用初始化逻辑，如加载配置、检查网络等
    await new Promise(resolve => setTimeout(resolve, 1000))
    isLoading.value = false
  } catch (error) {
    console.error('应用初始化失败:', error)
    showToast('应用加载失败，请刷新页面重试')
  }
})
</script>

<template>
  <div class="app-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">萌宠世界加载中...</p>
    </div>

    <!-- 主应用内容 -->
    <div v-else class="app-content">
      <RouterView v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </RouterView>

      <!-- 全局帮助系统 -->
      <HelpButton />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  @apply min-h-screen w-full;
}

.loading-container {
  @apply fixed inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50;
}

.loading-spinner {
  @apply w-16 h-16 border-4 border-primary-200 border-t-primary-500 rounded-full animate-spin;
}

.loading-text {
  @apply mt-4 text-lg font-medium text-primary-700;
}

.app-content {
  @apply min-h-screen w-full;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
