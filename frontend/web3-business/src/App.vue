<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'
import { useGlobalTheme } from './composables/useTheme'
import LoadingSpinner from './components/common/LoadingSpinner.vue'
import HelpButton from './components/HelpButton.vue'
import PerformanceMonitor from './components/common/PerformanceMonitor.vue'

const theme = useGlobalTheme()

// 应用加载状态
const isLoading = ref(true)

// 开发模式检测
const isDevelopment = import.meta.env.DEV

// 模拟应用初始化
onMounted(async () => {
  try {
    // 初始化主题系统
    theme.initTheme()

    // 这里可以放置应用初始化逻辑，如加载配置、检查网络等
    await new Promise(resolve => setTimeout(resolve, 1000))
    isLoading.value = false
  } catch (error) {
    console.error('应用初始化失败:', error)
    showToast('应用加载失败，请刷新页面重试')
  }
})
</script>

<template>
  <div class="app-container" :class="theme.themeClass.value">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <LoadingSpinner
        type="pulse"
        size="large"
        text="萌宠世界加载中..."
        color="primary"
      />
    </div>

    <!-- 主应用内容 -->
    <div v-else class="app-content">
      <RouterView v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </RouterView>

      <!-- 全局帮助系统 -->
      <HelpButton />

      <!-- 性能监控 (仅开发模式) -->
      <PerformanceMonitor v-if="isDevelopment" :show="false" />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  @apply min-h-screen w-full;
  background: var(--bg-gradient, linear-gradient(135deg, #faf5ff 0%, #f0f9ff 100%));
  transition: background 0.3s ease;
}

.loading-container {
  @apply fixed inset-0 flex flex-col items-center justify-center;
  background: var(--bg-gradient, linear-gradient(135deg, #faf5ff 0%, #f0f9ff 100%));
}

.app-content {
  @apply min-h-screen w-full;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 深色主题适配 */
:global(.theme-dark) .app-container,
:global(.theme-dark) .loading-container {
  background: var(--bg-gradient-dark, linear-gradient(135度, #1f2937 0%, #111827 100%));
}

/* 无动画模式 */
:global(.no-animations) .fade-enter-active,
:global(.no-animations) .fade-leave-active {
  transition: none !important;
}
</style>
