import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 首页直接导入，因为是最常访问的页面
import HomeView from '../views/HomeView.vue'

// 懒加载组件工厂函数，支持加载状态和错误处理
const lazyLoad = (componentPath: string) => {
  return () => {
    const component = import(`../views/${componentPath}.vue`)

    // 添加加载超时处理
    const timeout = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Component loading timeout')), 10000)
    })

    return Promise.race([component, timeout])
  }
}

// 预加载关键路由组件
const preloadComponents = () => {
  // 预加载首页相关的关键组件
  import('../views/PetsView.vue')
  import('../views/ShopView.vue')
}

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: '首页',
      keepAlive: true, // 缓存首页
      preload: true    // 预加载
    }
  },

  // 介绍和教程相关路由
  {
    path: '/introduction',
    name: 'introduction',
    component: lazyLoad('IntroductionView'),
    meta: {
      title: '游戏介绍',
      group: 'intro'
    }
  },
  {
    path: '/tutorial',
    name: 'tutorial',
    component: lazyLoad('TutorialView'),
    meta: {
      title: '游戏教程',
      group: 'intro'
    }
  },
  {
    path: '/test-tutorial',
    name: 'test-tutorial',
    component: lazyLoad('TestTutorial'),
    meta: {
      title: '教程测试',
      group: 'intro'
    }
  },
  {
    path: '/game-rules',
    name: 'game-rules',
    component: lazyLoad('GameRulesView'),
    meta: {
      title: '游戏规则',
      group: 'intro'
    }
  },

  // 萌宠相关路由
  {
    path: '/pets',
    name: 'pets',
    component: lazyLoad('PetsView'),
    meta: {
      title: '我的萌宠',
      group: 'pet',
      requiresAuth: true
    }
  },
  {
    path: '/pet/create',
    name: 'pet-create',
    component: lazyLoad('PetCreateView'),
    meta: {
      title: '创建萌宠',
      group: 'pet',
      requiresAuth: true
    }
  },
  {
    path: '/pet/:id',
    name: 'pet-details',
    component: lazyLoad('PetDetailsView'),
    meta: {
      title: '萌宠详情',
      group: 'pet',
      requiresAuth: true
    }
  },

  // 代币和交易相关路由
  {
    path: '/token-management',
    name: 'token-management',
    component: lazyLoad('TokenManagementView'),
    meta: {
      title: '代币管理',
      group: 'token',
      requiresAuth: true
    }
  },
  {
    path: '/token-exchange',
    name: 'token-exchange',
    component: lazyLoad('TokenExchangeView'),
    meta: {
      title: '代币兑换',
      group: 'token',
      requiresAuth: true
    }
  },

  // 商店相关路由
  {
    path: '/shop',
    name: 'shop',
    component: lazyLoad('ShopView'),
    meta: {
      title: '游戏商店',
      group: 'shop'
    }
  },

  // 其他页面
  {
    path: '/about',
    name: 'about',
    component: lazyLoad('AboutView'),
    meta: {
      title: '关于我们',
      group: 'misc'
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: lazyLoad('SettingsView'),
    meta: {
      title: '设置',
      group: 'misc'
    }
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: lazyLoad('NotFoundView'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 处理认证和预加载
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 萌宠养成游戏`
  }

  // 预加载相关组件
  if (to.name === 'home' && from.name === undefined) {
    // 首次访问首页时预加载关键组件
    preloadComponents()
  }

  // 检查是否需要钱包连接
  if (to.meta.requiresAuth) {
    // 这里可以添加钱包连接检查逻辑
    // const walletStore = useWalletStore()
    // if (!walletStore.isConnected) {
    //   return next({ name: 'home' })
    // }
  }

  next()
})

// 路由加载错误处理
router.onError((error) => {
  console.error('Router error:', error)
  // 可以添加错误上报逻辑
})

export default router
