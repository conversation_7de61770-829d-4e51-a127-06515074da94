import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 首页直接导入，因为是最常访问的页面
import HomeView from '../views/HomeView.vue'

// 懒加载组件工厂函数，支持加载状态和错误处理
const lazyLoad = (componentPath: string, chunkName?: string) => {
  return () => {
    const actualChunkName = chunkName || componentPath.toLowerCase().replace(/([a-z])([A-Z])/g, '$1-$2')

    const component = import(
      /* webpackChunkName: "[request]" */
      /* webpackPreload: true */
      `../views/${componentPath}.vue`
    )

    // 添加加载超时处理
    const timeout = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Component loading timeout')), 15000)
    })

    return Promise.race([component, timeout]).catch((error) => {
      console.error(`Failed to load component ${componentPath}:`, error)
      // 返回错误组件
      return import('../views/NotFoundView.vue')
    })
  }
}

// 高优先级懒加载（用于关键路由）
const lazyLoadCritical = (componentPath: string, chunkName?: string) => {
  return () => {
    const actualChunkName = chunkName || `critical-${componentPath.toLowerCase()}`

    return import(
      /* webpackChunkName: "[request]" */
      /* webpackPrefetch: true */
      `../views/${componentPath}.vue`
    ).catch((error) => {
      console.error(`Failed to load critical component ${componentPath}:`, error)
      return import('../views/NotFoundView.vue')
    })
  }
}

// 低优先级懒加载（用于次要路由）
const lazyLoadDeferred = (componentPath: string, chunkName?: string) => {
  return () => {
    const actualChunkName = chunkName || `deferred-${componentPath.toLowerCase()}`

    return new Promise((resolve) => {
      // 延迟加载，避免阻塞关键资源
      requestIdleCallback(() => {
        import(`../views/${componentPath}.vue`)
          .then(resolve)
          .catch((error) => {
            console.error(`Failed to load deferred component ${componentPath}:`, error)
            import('../views/NotFoundView.vue').then(resolve)
          })
      }, { timeout: 5000 })
    })
  }
}

// 预加载组件缓存
const preloadedComponents = new Set<string>()

// 智能预加载函数
const smartPreload = (componentPath: string) => {
  if (preloadedComponents.has(componentPath)) return

  preloadedComponents.add(componentPath)

  // 在空闲时间预加载
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      import(`../views/${componentPath}.vue`).catch(() => {
        // 预加载失败时从缓存中移除
        preloadedComponents.delete(componentPath)
      })
    })
  } else {
    // 降级处理
    setTimeout(() => {
      import(`../views/${componentPath}.vue`).catch(() => {
        preloadedComponents.delete(componentPath)
      })
    }, 100)
  }
}

// 预加载关键路由组件
const preloadComponents = () => {
  // 预加载首页相关的关键组件
  smartPreload('PetsView')
  smartPreload('TokenManagementView')
  smartPreload('TokenExchangeView')
  smartPreload('PetDetailsView')

  // 预加载常用组件（使用 requestIdleCallback 避免阻塞）
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      Promise.all([
        import('../components/pet/PetDisplay.vue'),
        import('../components/WalletConnect.vue'),
        import('../components/TokenBalanceDisplay.vue'),
        import('../components/pet/PetBasicInfoCard.vue'),
        import('../components/pet/PetStatsPanel.vue'),
        import('../components/common/LoadingSpinner.vue')
      ]).catch(error => {
        console.warn('Failed to preload components:', error)
      })
    })
  } else {
    // 降级处理
    setTimeout(() => {
      import('../components/pet/PetDisplay.vue')
      import('../components/WalletConnect.vue')
      import('../components/TokenBalanceDisplay.vue')
    }, 100)
  }
}

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: '首页',
      keepAlive: true, // 缓存首页
      preload: true    // 预加载
    }
  },

  // 介绍和教程相关路由（低优先级）
  {
    path: '/introduction',
    name: 'introduction',
    component: lazyLoadDeferred('IntroductionView', 'intro-introduction'),
    meta: {
      title: '游戏介绍',
      group: 'intro',
      priority: 'low'
    }
  },
  {
    path: '/tutorial',
    name: 'tutorial',
    component: lazyLoadDeferred('TutorialView', 'intro-tutorial'),
    meta: {
      title: '游戏教程',
      group: 'intro',
      priority: 'low'
    }
  },
  {
    path: '/test-tutorial',
    name: 'test-tutorial',
    component: lazyLoadDeferred('TestTutorial', 'intro-test'),
    meta: {
      title: '教程测试',
      group: 'intro',
      priority: 'low'
    }
  },
  {
    path: '/game-rules',
    name: 'game-rules',
    component: lazyLoadDeferred('GameRulesView', 'intro-rules'),
    meta: {
      title: '游戏规则',
      group: 'intro',
      priority: 'low'
    }
  },

  // 萌宠相关路由（高优先级）
  {
    path: '/pets',
    name: 'pets',
    component: lazyLoadCritical('PetsView', 'pet-list'),
    meta: {
      title: '我的萌宠',
      group: 'pet',
      requiresAuth: true,
      priority: 'high',
      preload: true
    }
  },
  {
    path: '/pet/create',
    name: 'pet-create',
    component: lazyLoad('PetCreateView', 'pet-create'),
    meta: {
      title: '创建萌宠',
      group: 'pet',
      requiresAuth: true,
      priority: 'medium'
    }
  },
  {
    path: '/pet/:id',
    name: 'pet-details',
    component: lazyLoadCritical('PetDetailsView', 'pet-details'),
    meta: {
      title: '萌宠详情',
      group: 'pet',
      requiresAuth: true,
      priority: 'high',
      preload: true
    }
  },

  // 代币和交易相关路由（高优先级）
  {
    path: '/token-management',
    name: 'token-management',
    component: lazyLoadCritical('TokenManagementView', 'token-management'),
    meta: {
      title: '代币管理',
      group: 'token',
      requiresAuth: true,
      priority: 'high',
      preload: true
    }
  },
  {
    path: '/token-exchange',
    name: 'token-exchange',
    component: lazyLoadCritical('TokenExchangeView', 'token-exchange'),
    meta: {
      title: '代币兑换',
      group: 'token',
      requiresAuth: true,
      priority: 'high',
      preload: true
    }
  },

  // 商店相关路由（中等优先级）
  {
    path: '/shop',
    name: 'shop',
    component: lazyLoad('ShopView', 'shop-main'),
    meta: {
      title: '游戏商店',
      group: 'shop',
      priority: 'medium'
    }
  },

  // 其他页面（低优先级）
  {
    path: '/about',
    name: 'about',
    component: lazyLoadDeferred('AboutView', 'misc-about'),
    meta: {
      title: '关于我们',
      group: 'misc',
      priority: 'low'
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: lazyLoad('SettingsView', 'misc-settings'),
    meta: {
      title: '设置',
      group: 'misc',
      priority: 'medium'
    }
  },

  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: lazyLoad('NotFoundView'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 处理认证和预加载
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 萌宠养成游戏`
  }

  // 预加载相关组件
  if (to.name === 'home' && from.name === undefined) {
    // 首次访问首页时预加载关键组件
    preloadComponents()
  }

  // 智能预加载相关页面（基于优先级）
  if (to.meta.group) {
    const relatedPages = routes
      .filter(route =>
        route.meta?.group === to.meta.group &&
        route.name !== to.name &&
        route.meta?.priority !== 'low' // 不预加载低优先级页面
      )
      .sort((a, b) => {
        // 按优先级排序
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        return (priorityOrder[b.meta?.priority as keyof typeof priorityOrder] || 1) -
               (priorityOrder[a.meta?.priority as keyof typeof priorityOrder] || 1)
      })

    // 预加载同组的高优先级页面
    relatedPages.slice(0, 2).forEach(route => {
      if (route.name && typeof route.component === 'function') {
        const componentName = String(route.name)
          .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
          .replace(/^([a-z])/, (_, letter) => letter.toUpperCase()) + 'View'
        smartPreload(componentName)
      }
    })
  }

  // 基于路由优先级的预加载策略
  if (to.meta.preload && to.meta.priority === 'high') {
    // 预加载高优先级路由的相关组件
    const preloadPromises = []

    if (to.name === 'pets' || to.name === 'pet-details') {
      preloadPromises.push(
        import('../components/pet/PetAnimation.vue'),
        import('../components/pet/PetInteraction.vue'),
        import('../components/pet/PetEquipmentManager.vue')
      )
    }

    if (to.name === 'token-management' || to.name === 'token-exchange') {
      preloadPromises.push(
        import('../components/pet/PetTokenExchange.vue'),
        import('../components/pet/PetValueAssessment.vue')
      )
    }

    // 非阻塞预加载
    Promise.allSettled(preloadPromises).catch(() => {
      // 预加载失败不影响路由导航
    })
  }

  // 检查是否需要钱包连接
  if (to.meta.requiresAuth) {
    // 这里可以添加钱包连接检查逻辑
    // const walletStore = useWalletStore()
    // if (!walletStore.isConnected) {
    //   return next({ name: 'home' })
    // }
  }

  next()
})

// 路由加载错误处理
router.onError((error) => {
  console.error('Router error:', error)
  // 可以添加错误上报逻辑
})

export default router
