import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/introduction',
      name: 'introduction',
      component: () => import('../views/IntroductionView.vue'),
      meta: {
        title: '游戏介绍'
      }
    },
    {
      path: '/tutorial',
      name: 'tutorial',
      component: () => import('../views/TutorialView.vue'),
      meta: {
        title: '游戏教程'
      }
    },
    {
      path: '/test-tutorial',
      name: 'test-tutorial',
      component: () => import('../views/TestTutorial.vue'),
      meta: {
        title: '教程测试'
      }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/shop',
      name: 'shop',
      component: () => import('../views/ShopView.vue'),
      meta: {
        title: '游戏商店'
      }
    },
    {
      path: '/game-rules',
      name: 'game-rules',
      component: () => import('../views/GameRulesView.vue'),
      meta: {
        title: '游戏规则'
      }
    },
    {
      path: '/pet/:id',
      name: 'pet-details',
      component: () => import('../views/PetDetailsView.vue'),
      meta: {
        title: '萌宠详情'
      }
    },
    {
      path: '/pets',
      name: 'pets',
      component: () => import('../views/PetsView.vue'),
      meta: {
        title: '我的萌宠'
      }
    },
    {
      path: '/pet/create',
      name: 'pet-create',
      component: () => import('../views/PetCreateView.vue'),
      meta: {
        title: '创建萌宠'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: {
        title: '设置'
      }
    },
  ],
})

export default router
