# Testing Documentation

This document provides comprehensive information about the testing strategy and implementation for the Pet Token Game frontend application.

## Testing Strategy

### Test Types

1. **Unit Tests** - Test individual components, functions, and modules in isolation
2. **Integration Tests** - Test interactions between different modules and services
3. **End-to-End Tests** - Test complete user workflows and scenarios
4. **Performance Tests** - Test application performance and memory usage
5. **Contract Integration Tests** - Test blockchain contract interactions

### Test Structure

```
src/test/
├── setup.ts                 # Test environment setup
├── utils.ts                 # Test utilities and helpers
├── e2e/                     # End-to-end tests
│   └── pet-nurturing-flow.test.ts
├── performance/             # Performance tests
│   └── performance-monitoring.test.ts
└── README.md               # This file

src/components/__tests__/    # Component tests
src/stores/__tests__/        # Store tests
src/services/__tests__/      # Service tests
src/utils/__tests__/         # Utility tests
src/types/__tests__/         # Type tests
src/composables/__tests__/   # Composable tests
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Specific Test Categories

```bash
# Unit tests only
npm run test:unit

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e

# Performance tests
npm run test:performance

# Component tests
npm run test:components

# Store tests
npm run test:stores

# Service tests
npm run test:services
```

### Development Commands

```bash
# Run tests for changed files only
npm run test:changed

# Debug tests with verbose output
npm run test:debug

# Clear test cache
npm run test:clear-cache

# CI/CD optimized test run
npm run test:ci
```

## Test Configuration

### Coverage Thresholds

- **Global**: 80% coverage for branches, functions, lines, and statements
- **Stores**: 90% coverage (critical business logic)
- **Services**: 85% coverage (important integrations)
- **Utils**: 85% coverage (shared functionality)

### Test Environment

- **Test Runner**: Vitest
- **DOM Environment**: jsdom
- **Mocking**: Built-in Vitest mocking
- **Component Testing**: Vue Test Utils

## Writing Tests

### Component Tests

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mountComponent, createMockPet } from '@/test/utils'
import PetDisplay from '@/components/pet/PetDisplay.vue'

describe('PetDisplay', () => {
  it('renders pet information correctly', () => {
    const mockPet = createMockPet({ name: 'Fluffy' })
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet }
    })

    expect(wrapper.find('[data-testid="pet-name"]').text()).toBe('Fluffy')
  })
})
```

### Store Tests

```typescript
import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { usePetStore } from '@/stores/pet'

describe('Pet Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('creates a new pet correctly', async () => {
    const store = usePetStore()
    const newPet = await store.createPet('Fluffy', 'cat')

    expect(store.pets).toHaveLength(1)
    expect(newPet.name).toBe('Fluffy')
  })
})
```

### Service Tests

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ContractService } from '@/services/contract.service'
import { createMockProvider, createMockContract } from '@/test/utils'

describe('Contract Service', () => {
  let service: ContractService
  let mockContract: any

  beforeEach(() => {
    mockContract = createMockContract()
    service = new ContractService(createMockProvider(), mockContract)
  })

  it('retrieves balance correctly', async () => {
    mockContract.balanceOf.mockResolvedValue('1000000000000000000')
    
    const balance = await service.getBalance('0x1234...')
    expect(balance).toBe('1.0')
  })
})
```

## Test Utilities

### Mock Data Creators

- `createMockPet(overrides)` - Create mock pet data
- `createMockItem(overrides)` - Create mock item data
- `createMockEquipment(overrides)` - Create mock equipment data
- `createMockProvider()` - Create mock wallet provider
- `createMockContract()` - Create mock contract instance

### Component Testing Helpers

- `mountComponent(component, options)` - Mount Vue component with testing setup
- `expectElementToBeVisible(wrapper, selector)` - Assert element visibility
- `expectElementToHaveText(wrapper, selector, text)` - Assert element text content

### Performance Testing

- `measurePerformance(fn)` - Measure function execution time
- `expectPerformanceUnder(fn, maxTime)` - Assert performance threshold
- `expectNoMemoryLeak(fn, iterations)` - Test for memory leaks

### Async Testing

- `waitForNextTick()` - Wait for Vue's next tick
- `waitForCondition(condition, timeout)` - Wait for condition to be true

## Best Practices

### Test Organization

1. **Group related tests** using `describe` blocks
2. **Use descriptive test names** that explain the expected behavior
3. **Follow AAA pattern** (Arrange, Act, Assert)
4. **Clean up after tests** using `beforeEach` and `afterEach`

### Mocking Guidelines

1. **Mock external dependencies** (APIs, contracts, providers)
2. **Use real implementations** for internal modules when possible
3. **Mock time-dependent functions** for consistent results
4. **Reset mocks** between tests

### Data-Testid Convention

Use `data-testid` attributes for test selectors:

```html
<button data-testid="feed-button">Feed Pet</button>
<div data-testid="pet-health">Health: 100%</div>
```

### Accessibility Testing

Include accessibility checks in component tests:

```typescript
it('is accessible with proper ARIA attributes', () => {
  const wrapper = mountComponent(Component)
  
  expect(wrapper.find('button').attributes('aria-label')).toBeDefined()
  expect(wrapper.find('[role="dialog"]').exists()).toBe(true)
})
```

## Continuous Integration

### GitHub Actions

Tests run automatically on:
- Pull requests
- Pushes to main branch
- Scheduled daily runs

### Coverage Reports

- Coverage reports are generated for each test run
- Reports are uploaded to coverage services
- Pull requests show coverage changes

### Performance Monitoring

- Performance tests run on CI
- Performance regressions are flagged
- Memory usage is monitored

## Debugging Tests

### Common Issues

1. **Async timing issues** - Use proper async/await and Vue's nextTick
2. **Mock not working** - Ensure mocks are set up before imports
3. **Component not rendering** - Check for missing global plugins/stubs
4. **Test isolation** - Ensure tests don't affect each other

### Debug Tools

```bash
# Run single test file
npx vitest run src/components/pet/PetDisplay.test.ts

# Run with debug output
npm run test:debug

# Run with UI for interactive debugging
npm run test:ui
```

### VS Code Integration

Install the Vitest extension for:
- Inline test results
- Debug test execution
- Test coverage visualization

## Contributing

When adding new features:

1. **Write tests first** (TDD approach when possible)
2. **Maintain coverage thresholds**
3. **Add integration tests** for new services
4. **Update test documentation** for new patterns
5. **Run full test suite** before submitting PR

## Performance Considerations

### Test Performance

- Use `vi.mock()` for heavy dependencies
- Avoid real network calls in tests
- Use `beforeEach` for common setup
- Clean up resources after tests

### Memory Management

- Unmount components after testing
- Clear timers and intervals
- Reset global state between tests
- Monitor memory usage in performance tests

## Troubleshooting

### Common Errors

1. **"Cannot find module"** - Check import paths and mock setup
2. **"ReferenceError: window is not defined"** - Ensure jsdom environment
3. **"TypeError: Cannot read property"** - Check mock implementations
4. **"Timeout exceeded"** - Increase timeout or fix async handling

### Getting Help

- Check Vitest documentation
- Review existing test examples
- Ask team members for guidance
- Use debugging tools and console logs