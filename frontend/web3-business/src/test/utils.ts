import { mount, VueWrapper } from '@vue/test-utils'
import { createP<PERSON> } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { vi } from 'vitest'
import type { Pet, PetRarity } from '@/types/pet'
import type { Equipment, EquipmentType } from '@/types/equipment'
import type { Item, ItemType } from '@/types/item'

// Test utilities for creating mock data
export const createMockPet = (overrides: Partial<Pet> = {}): Pet => ({
  id: 'test-pet-1',
  name: 'Test Pet',
  level: 1,
  experience: 0,
  maxExperience: 100,
  rarity: 'common' as PetRarity,
  health: 100,
  maxHealth: 100,
  happiness: 100,
  maxHappiness: 100,
  strength: 10,
  intelligence: 10,
  agility: 10,
  species: 'cat',
  color: 'orange',
  pattern: 'solid',
  accessories: [],
  equipment: [],
  createdAt: new Date('2024-01-01'),
  lastFeedTime: new Date('2024-01-01'),
  lastTrainTime: new Date('2024-01-01'),
  lastPlayTime: new Date('2024-01-01'),
  ...overrides,
})

export const createMockEquipment = (overrides: Partial<Equipment> = {}): Equipment => ({
  id: 'test-equipment-1',
  name: 'Test Equipment',
  type: 'accessory' as EquipmentType,
  rarity: 'common' as PetRarity,
  description: 'Test equipment description',
  statBonus: {
    strength: 5,
    intelligence: 0,
    agility: 0,
    health: 0,
    happiness: 0,
  },
  price: 100,
  isEquipped: false,
  ...overrides,
})

export const createMockItem = (overrides: Partial<Item> = {}): Item => ({
  id: 'test-item-1',
  name: 'Test Item',
  type: 'food' as ItemType,
  description: 'Test item description',
  quantity: 1,
  price: 50,
  effect: {
    health: 10,
    happiness: 5,
    experience: 0,
  },
  ...overrides,
})

// Mock wallet provider
export const createMockProvider = () => ({
  request: vi.fn(),
  on: vi.fn(),
  removeListener: vi.fn(),
  isMetaMask: true,
  selectedAddress: '******************************************',
})

// Mock contract
export const createMockContract = () => ({
  balanceOf: vi.fn().mockResolvedValue('1000000000000000000'),
  transfer: vi.fn().mockResolvedValue({
    hash: '0xtest-hash',
    wait: vi.fn().mockResolvedValue({ status: 1 }),
  }),
  mint: vi.fn().mockResolvedValue({
    hash: '0xtest-mint-hash',
    wait: vi.fn().mockResolvedValue({ status: 1 }),
  }),
  on: vi.fn(),
  off: vi.fn(),
})

// Component mounting utilities
export const createTestingPinia = () => {
  return createPinia()
}

export const createTestRouter = (routes: any[] = []) => {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/pet', component: { template: '<div>Pet</div>' } },
      { path: '/shop', component: { template: '<div>Shop</div>' } },
      ...routes,
    ],
  })
}

export const mountComponent = (component: any, options: any = {}) => {
  const pinia = createTestingPinia()
  const router = createTestRouter()

  return mount(component, {
    global: {
      plugins: [pinia, router],
      stubs: {
        'router-link': true,
        'router-view': true,
        'van-button': true,
        'van-cell': true,
        'van-field': true,
        'van-popup': true,
        'van-dialog': true,
        'van-loading': true,
        'van-progress': true,
        'van-image': true,
        'van-icon': true,
        'van-tab': true,
        'van-tabs': true,
        'van-card': true,
        'van-grid': true,
        'van-grid-item': true,
        'van-nav-bar': true,
        'van-tabbar': true,
        'van-tabbar-item': true,
        'vue3-lottie': true,
      },
      mocks: {
        $t: (key: string) => key,
      },
    },
    ...options,
  })
}

// Async utilities
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0))

export const waitForCondition = async (
  condition: () => boolean,
  timeout = 1000,
  interval = 10
): Promise<void> => {
  const start = Date.now()
  while (!condition() && Date.now() - start < timeout) {
    await new Promise(resolve => setTimeout(resolve, interval))
  }
  if (!condition()) {
    throw new Error('Condition not met within timeout')
  }
}

// Mock data generators
export const generateMockPets = (count: number): Pet[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockPet({
      id: `pet-${index + 1}`,
      name: `Pet ${index + 1}`,
      level: Math.floor(Math.random() * 10) + 1,
    })
  )
}

export const generateMockEquipment = (count: number): Equipment[] => {
  const types: EquipmentType[] = ['accessory', 'toy', 'collar']
  const rarities: PetRarity[] = ['common', 'uncommon', 'rare', 'epic', 'legendary']

  return Array.from({ length: count }, (_, index) =>
    createMockEquipment({
      id: `equipment-${index + 1}`,
      name: `Equipment ${index + 1}`,
      type: types[index % types.length],
      rarity: rarities[index % rarities.length],
    })
  )
}

export const generateMockItems = (count: number): Item[] => {
  const types: ItemType[] = ['food', 'toy', 'medicine', 'accessory']

  return Array.from({ length: count }, (_, index) =>
    createMockItem({
      id: `item-${index + 1}`,
      name: `Item ${index + 1}`,
      type: types[index % types.length],
      quantity: Math.floor(Math.random() * 10) + 1,
    })
  )
}

// Test assertion helpers
export const expectElementToBeVisible = (wrapper: VueWrapper<any>, selector: string) => {
  const element = wrapper.find(selector)
  expect(element.exists()).toBe(true)
  expect(element.isVisible()).toBe(true)
}

export const expectElementToHaveText = (wrapper: VueWrapper<any>, selector: string, text: string) => {
  const element = wrapper.find(selector)
  expect(element.exists()).toBe(true)
  expect(element.text()).toContain(text)
}

export const expectElementToHaveClass = (wrapper: VueWrapper<any>, selector: string, className: string) => {
  const element = wrapper.find(selector)
  expect(element.exists()).toBe(true)
  expect(element.classes()).toContain(className)
}

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void): Promise<number> => {
  const start = performance.now()
  await fn()
  const end = performance.now()
  return end - start
}

export const expectPerformanceUnder = async (fn: () => Promise<void> | void, maxTime: number) => {
  const time = await measurePerformance(fn)
  expect(time).toBeLessThan(maxTime)
}

// Memory testing utilities
export const measureMemoryUsage = (): number => {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize
  }
  return 0
}

export const expectNoMemoryLeak = async (fn: () => Promise<void> | void, iterations = 100) => {
  const initialMemory = measureMemoryUsage()

  for (let i = 0; i < iterations; i++) {
    await fn()
  }

  // Force garbage collection if available
  if (global.gc) {
    global.gc()
  }

  const finalMemory = measureMemoryUsage()
  const memoryIncrease = finalMemory - initialMemory

  // Allow for some memory increase but not excessive
  expect(memoryIncrease).toBeLessThan(1024 * 1024) // 1MB threshold
}
