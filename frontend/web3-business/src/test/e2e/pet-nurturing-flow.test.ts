import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from '@/App.vue'
import { useWalletStore } from '@/stores/wallet'
import { usePetStore } from '@/stores/pet'
import { useGameStore } from '@/stores/game'
import { createMockProvider, createMockContract, waitForCondition } from '@/test/utils'

describe('Pet Nurturing Flow E2E Tests', () => {
  let app: any
  let router: any
  let pinia: any
  let mockProvider: any
  let mockContract: any

  beforeEach(async () => {
    mockProvider = createMockProvider()
    mockContract = createMockContract()

    // Setup router
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/pet', component: { template: '<div>Pet</div>' } },
        { path: '/shop', component: { template: '<div>Shop</div>' } },
        { path: '/tutorial', component: { template: '<div>Tutorial</div>' } },
      ],
    })

    // Setup Pinia
    pinia = createPinia()

    // Mock window.ethereum
    Object.defineProperty(window, 'ethereum', {
      value: mockProvider,
      writable: true,
    })

    vi.clearAllMocks()
  })

  describe('Complete Pet Nurturing Journey', () => {
    it('completes full pet creation and nurturing flow', async () => {
      // Mount the app
      app = mount(App, {
        global: {
          plugins: [pinia, router],
          stubs: {
            'router-view': false,
            'van-button': false,
            'van-popup': false,
          },
        },
      })

      const walletStore = useWalletStore()
      const petStore = usePetStore()
      const gameStore = useGameStore()

      // Step 1: Connect wallet
      mockProvider.request
        .mockResolvedValueOnce(['******************************************'])
        .mockResolvedValueOnce('0xaa36a7') // Sepolia
        .mockResolvedValueOnce('0xde0b6b3a7640000') // 1 ETH

      await walletStore.connectWallet('metamask')

      expect(walletStore.isConnected).toBe(true)
      expect(walletStore.address).toBe('******************************************')

      // Step 2: Create first pet
      const newPet = await petStore.createPet('Buddy', 'dog')

      expect(petStore.pets).toHaveLength(1)
      expect(petStore.currentPet?.name).toBe('Buddy')
      expect(petStore.currentPet?.species).toBe('dog')
      expect(petStore.currentPet?.level).toBe(1)

      // Step 3: Navigate to pet page
      await router.push('/pet')
      await app.vm.$nextTick()

      // Step 4: Feed the pet
      const initialHealth = petStore.currentPet!.health
      await petStore.feedPet(newPet.id)

      expect(petStore.currentPet!.health).toBeGreaterThan(initialHealth)
      expect(petStore.currentPet!.lastFeedTime).toBeInstanceOf(Date)

      // Step 5: Train the pet
      const initialExperience = petStore.currentPet!.experience
      const initialStrength = petStore.currentPet!.strength

      await petStore.trainPet(newPet.id)

      expect(petStore.currentPet!.experience).toBeGreaterThan(initialExperience)
      expect(petStore.currentPet!.strength).toBeGreaterThan(initialStrength)

      // Step 6: Play with the pet
      const initialHappiness = petStore.currentPet!.happiness
      await petStore.playWithPet(newPet.id)

      expect(petStore.currentPet!.happiness).toBeGreaterThan(initialHappiness)

      // Step 7: Level up the pet through training
      let currentLevel = petStore.currentPet!.level

      // Train multiple times to level up
      for (let i = 0; i < 10; i++) {
        await petStore.trainPet(newPet.id)
        if (petStore.currentPet!.level > currentLevel) {
          break
        }
      }

      expect(petStore.currentPet!.level).toBeGreaterThan(currentLevel)

      // Step 8: Navigate to shop and buy items
      await router.push('/shop')
      await app.vm.$nextTick()

      const shopStore = useGameStore()
      const foodItem = {
        id: 'premium-food',
        name: 'Premium Pet Food',
        type: 'food',
        price: 50,
        effect: { health: 30, happiness: 15, experience: 10 }
      }

      await shopStore.purchaseItem(foodItem.id, 1)
      expect(petStore.inventory.some(item => item.id === foodItem.id)).toBe(true)

      // Step 9: Use purchased item on pet
      const healthBeforeItem = petStore.currentPet!.health
      await petStore.useItemOnPet(newPet.id, foodItem.id)

      expect(petStore.currentPet!.health).toBeGreaterThan(healthBeforeItem)

      // Step 10: Exchange pet for tokens
      mockContract.mint.mockResolvedValue({
        hash: '0xmint-hash',
        wait: vi.fn().mockResolvedValue({ status: 1 })
      })

      const petValue = petStore.calculatePetValue(petStore.currentPet!)
      const exchangeResult = await gameStore.exchangePetForTokens(newPet.id)

      expect(exchangeResult.success).toBe(true)
      expect(mockContract.mint).toHaveBeenCalled()
    })

    it('handles pet evolution flow correctly', async () => {
      const petStore = usePetStore()

      // Create a pet close to evolution
      const pet = await petStore.createPet('Evolvy', 'cat')

      // Set pet to high level for evolution
      pet.level = 9
      pet.experience = 950
      pet.maxExperience = 1000

      petStore.pets = [pet]
      petStore.currentPet = pet

      // Train to trigger evolution
      await petStore.trainPet(pet.id)

      // Check if pet evolved
      expect(petStore.currentPet!.level).toBe(10)
      expect(petStore.currentPet!.maxExperience).toBeGreaterThan(1000)

      // Evolution should improve stats
      expect(petStore.currentPet!.maxHealth).toBeGreaterThan(100)
      expect(petStore.currentPet!.strength).toBeGreaterThan(pet.strength)
    })

    it('handles equipment management flow', async () => {
      const petStore = usePetStore()

      const pet = await petStore.createPet('Geared', 'dog')
      const equipment = {
        id: 'power-collar',
        name: 'Power Collar',
        type: 'accessory',
        rarity: 'rare',
        statBonus: { strength: 10, intelligence: 5, agility: 3, health: 0, happiness: 0 },
        price: 200,
        isEquipped: false
      }

      // Add equipment to inventory
      petStore.addItemToInventory(equipment)

      const initialStrength = pet.strength

      // Equip the item
      await petStore.equipItem(pet.id, equipment.id)

      expect(petStore.currentPet!.equipment).toContain(equipment)
      expect(petStore.currentPet!.strength).toBe(initialStrength + 10)

      // Unequip the item
      await petStore.unequipItem(pet.id, equipment.id)

      expect(petStore.currentPet!.equipment).not.toContain(equipment)
      expect(petStore.currentPet!.strength).toBe(initialStrength)
    })
  })

  describe('Error Handling in E2E Flow', () => {
    it('handles wallet connection failure gracefully', async () => {
      const walletStore = useWalletStore()

      mockProvider.request.mockRejectedValue(new Error('User rejected'))

      await expect(walletStore.connectWallet('metamask')).rejects.toThrow('User rejected')
      expect(walletStore.isConnected).toBe(false)
    })

    it('handles contract interaction failures', async () => {
      const walletStore = useWalletStore()
      const gameStore = useGameStore()

      // Connect wallet first
      mockProvider.request
        .mockResolvedValueOnce(['******************************************'])
        .mockResolvedValueOnce('0xaa36a7')
        .mockResolvedValueOnce('0xde0b6b3a7640000')

      await walletStore.connectWallet('metamask')

      // Mock contract failure
      mockContract.mint.mockRejectedValue(new Error('Contract error'))

      const result = await gameStore.exchangePetForTokens('pet-id')
      expect(result.success).toBe(false)
      expect(result.error).toContain('Contract error')
    })

    it('handles network switching during gameplay', async () => {
      const walletStore = useWalletStore()

      // Connect to wrong network
      mockProvider.request
        .mockResolvedValueOnce(['******************************************'])
        .mockResolvedValueOnce('0x1') // Mainnet
        .mockResolvedValueOnce('0xde0b6b3a7640000')

      await walletStore.connectWallet('metamask')
      expect(walletStore.chainId).toBe(1)
      expect(walletStore.isCorrectNetwork(walletStore.chainId)).toBe(false)

      // Switch to correct network
      mockProvider.request.mockResolvedValueOnce(null)
      await walletStore.switchNetwork(11155111) // Sepolia

      expect(mockProvider.request).toHaveBeenCalledWith({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: '0xaa36a7' }],
      })
    })
  })

  describe('Performance in E2E Scenarios', () => {
    it('maintains good performance during intensive pet interactions', async () => {
      const petStore = usePetStore()
      const pet = await petStore.createPet('Speedy', 'rabbit')

      const startTime = performance.now()

      // Perform many rapid actions
      for (let i = 0; i < 100; i++) {
        await petStore.feedPet(pet.id)
        await petStore.playWithPet(pet.id)
        await petStore.trainPet(pet.id)
      }

      const endTime = performance.now()
      const duration = endTime - startTime

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(1000) // 1 second
    })

    it('handles large inventory efficiently', async () => {
      const petStore = usePetStore()

      const startTime = performance.now()

      // Add many items to inventory
      for (let i = 0; i < 1000; i++) {
        petStore.addItemToInventory({
          id: `item-${i}`,
          name: `Item ${i}`,
          type: 'food',
          quantity: 1,
          price: 10,
          effect: { health: 5, happiness: 0, experience: 0 }
        })
      }

      const endTime = performance.now()
      const duration = endTime - startTime

      expect(petStore.inventory).toHaveLength(1000)
      expect(duration).toBeLessThan(100) // Should be very fast
    })
  })

  describe('Data Persistence in E2E Flow', () => {
    it('persists game state across sessions', async () => {
      const petStore = usePetStore()
      const walletStore = useWalletStore()

      // Create initial state
      const pet = await petStore.createPet('Persistent', 'cat')
      await petStore.feedPet(pet.id)

      // Mock wallet connection
      walletStore.isConnected = true
      walletStore.address = '******************************************'

      // Save state
      await petStore.savePetData()
      await walletStore.saveWalletState()

      // Clear current state
      petStore.$reset()
      walletStore.$reset()

      expect(petStore.pets).toHaveLength(0)
      expect(walletStore.isConnected).toBe(false)

      // Load state
      await petStore.loadPetData()
      await walletStore.loadWalletState()

      expect(petStore.pets).toHaveLength(1)
      expect(petStore.pets[0].name).toBe('Persistent')
      expect(walletStore.address).toBe('******************************************')
    })

    it('handles data corruption gracefully', async () => {
      const petStore = usePetStore()

      // Mock corrupted data
      vi.spyOn(localStorage, 'getItem').mockReturnValue('corrupted json data')

      await petStore.loadPetData()

      // Should not crash and maintain clean state
      expect(petStore.pets).toEqual([])
      expect(petStore.currentPet).toBeNull()
    })
  })
})