import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock global objects
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock crypto
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-123'),
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    })
  }
})

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url')
global.URL.revokeObjectURL = vi.fn()

// Mock fetch
global.fetch = vi.fn()

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Configure Vue Test Utils
config.global.mocks = {
  $t: (key: string) => key, // Mock i18n
}

// Mock ethers
vi.mock('ethers', () => ({
  ethers: {
    BrowserProvider: vi.fn(),
    Contract: vi.fn(),
    parseEther: vi.fn((value) => `${value}000000000000000000`),
    formatEther: vi.fn((value) => (parseFloat(value) / 1e18).toString()),
    isAddress: vi.fn((address) => /^0x[a-fA-F0-9]{40}$/.test(address)),
    getDefaultProvider: vi.fn(),
    JsonRpcProvider: vi.fn(),
  },
  Contract: vi.fn(),
  BrowserProvider: vi.fn(),
}))

// Mock WalletConnect
vi.mock('@walletconnect/ethereum-provider', () => ({
  default: {
    init: vi.fn(),
  },
}))

// Mock Vant components
vi.mock('vant', () => ({
  showToast: vi.fn(),
  showDialog: vi.fn(),
  showConfirmDialog: vi.fn(),
  showLoadingToast: vi.fn(),
  closeToast: vi.fn(),
}))

// Mock Vue3 Lottie
vi.mock('vue3-lottie', () => ({
  Vue3Lottie: {
    name: 'Vue3Lottie',
    template: '<div class="lottie-mock"></div>',
  },
}))

// Mock @vueuse/motion
vi.mock('@vueuse/motion', () => ({
  useMotion: vi.fn(() => ({
    apply: vi.fn(),
    variant: vi.fn(),
  })),
}))