import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  measurePerformance,
  expectPerformanceUnder,
  measureMemoryUsage,
  expectNoMemoryLeak
} from '@/test/utils'
import { usePetStore } from '@/stores/pet'
import { useWalletStore } from '@/stores/wallet'
import { createPinia, setActivePinia } from 'pinia'
import { createMockPet, createMockItem, generateMockPets } from '@/test/utils'

describe('Performance Monitoring Tests', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('Store Performance', () => {
    it('pet creation should be fast', async () => {
      const petStore = usePetStore()

      await expectPerformanceUnder(async () => {
        await petStore.createPet('Speed Test', 'cat')
      }, 50) // Should complete within 50ms
    })

    it('pet feeding should be performant', async () => {
      const petStore = usePetStore()
      const pet = await petStore.createPet('Hungry', 'dog')

      await expectPerformanceUnder(async () => {
        await petStore.feedPet(pet.id)
      }, 20) // Should complete within 20ms
    })

    it('large pet collection should load quickly', async () => {
      const petStore = usePetStore()
      const largePetCollection = generateMockPets(1000)

      await expectPerformanceUnder(async () => {
        petStore.pets = largePetCollection
        petStore.selectPet(largePetCollection[500].id)
      }, 100) // Should handle 1000 pets within 100ms
    })

    it('inventory operations should be efficient', async () => {
      const petStore = usePetStore()

      await expectPerformanceUnder(async () => {
        for (let i = 0; i < 100; i++) {
          petStore.addItemToInventory(createMockItem({ id: `item-${i}` }))
        }
      }, 50) // Adding 100 items should be under 50ms
    })

    it('pet value calculation should be fast', async () => {
      const petStore = usePetStore()
      const complexPet = createMockPet({
        level: 50,
        rarity: 'legendary',
        equipment: Array.from({ length: 10 }, (_, i) => ({
          id: `eq-${i}`,
          name: `Equipment ${i}`,
          type: 'accessory',
          statBonus: { strength: 5, intelligence: 3, agility: 2, health: 0, happiness: 0 }
        }))
      })

      await expectPerformanceUnder(async () => {
        petStore.calculatePetValue(complexPet)
      }, 10) // Complex calculation should be under 10ms
    })
  })

  describe('Memory Management', () => {
    it('pet creation should not leak memory', async () => {
      const petStore = usePetStore()

      await expectNoMemoryLeak(async () => {
        const pet = await petStore.createPet('Temp Pet', 'cat')
        petStore.removePet(pet.id)
      }, 50) // Test 50 iterations
    })

    it('inventory management should not leak memory', async () => {
      const petStore = usePetStore()

      await expectNoMemoryLeak(async () => {
        const item = createMockItem()
        petStore.addItemToInventory(item)
        petStore.removeItemFromInventory(item.id)
      }, 100) // Test 100 iterations
    })

    it('wallet operations should not leak memory', async () => {
      const walletStore = useWalletStore()

      // Mock provider
      const mockProvider = {
        request: vi.fn().mockResolvedValue(['******************************************']),
        on: vi.fn(),
        removeListener: vi.fn(),
      }

      Object.defineProperty(window, 'ethereum', {
        value: mockProvider,
        writable: true,
      })

      await expectNoMemoryLeak(async () => {
        await walletStore.connectWallet('metamask')
        await walletStore.disconnect()
      }, 20) // Test 20 iterations
    })
  })

  describe('Component Rendering Performance', () => {
    it('pet display component should render quickly', async () => {
      const { mountComponent, createMockPet } = await import('@/test/utils')
      const PetDisplay = await import('@/components/pet/PetDisplay.vue')

      const mockPet = createMockPet()

      await expectPerformanceUnder(async () => {
        const wrapper = mountComponent(PetDisplay.default, {
          props: { pet: mockPet }
        })
        wrapper.unmount()
      }, 100) // Component mounting should be under 100ms
    })

    it('large pet list should render efficiently', async () => {
      const { mountComponent } = await import('@/test/utils')
      const PetsView = await import('@/views/PetsView.vue')

      const largePetList = generateMockPets(100)
      const petStore = usePetStore()
      petStore.pets = largePetList

      await expectPerformanceUnder(async () => {
        const wrapper = mountComponent(PetsView.default)
        wrapper.unmount()
      }, 500) // Rendering 100 pets should be under 500ms
    })
  })

  describe('Animation Performance', () => {
    it('pet animation should not impact performance significantly', async () => {
      const { mountComponent, createMockPet } = await import('@/test/utils')
      const PetAnimation = await import('@/components/pet/PetAnimation.vue')

      const mockPet = createMockPet()

      const animationTime = await measurePerformance(async () => {
        const wrapper = mountComponent(PetAnimation.default, {
          props: { pet: mockPet, animation: 'idle' }
        })

        // Simulate animation running
        await new Promise(resolve => setTimeout(resolve, 100))

        wrapper.unmount()
      })

      expect(animationTime).toBeLessThan(200) // Animation setup should be quick
    })

    it('multiple simultaneous animations should be manageable', async () => {
      const { mountComponent, createMockPet } = await import('@/test/utils')
      const PetAnimation = await import('@/components/pet/PetAnimation.vue')

      const pets = generateMockPets(10)

      await expectPerformanceUnder(async () => {
        const wrappers = pets.map(pet =>
          mountComponent(PetAnimation.default, {
            props: { pet, animation: 'play' }
          })
        )

        // Clean up
        wrappers.forEach(wrapper => wrapper.unmount())
      }, 300) // 10 simultaneous animations should be under 300ms
    })
  })

  describe('Data Processing Performance', () => {
    it('pet statistics calculation should be efficient', async () => {
      const petStore = usePetStore()
      const complexPets = generateMockPets(100).map(pet => ({
        ...pet,
        equipment: Array.from({ length: 5 }, (_, i) => ({
          id: `eq-${i}`,
          statBonus: { strength: 2, intelligence: 1, agility: 1, health: 0, happiness: 0 }
        }))
      }))

      await expectPerformanceUnder(async () => {
        complexPets.forEach(pet => {
          petStore.calculateTotalStats(pet)
        })
      }, 100) // Processing 100 complex pets should be under 100ms
    })

    it('inventory filtering should be fast', async () => {
      const petStore = usePetStore()
      const largeInventory = Array.from({ length: 1000 }, (_, i) =>
        createMockItem({
          id: `item-${i}`,
          type: i % 4 === 0 ? 'food' : 'toy',
          rarity: i % 10 === 0 ? 'rare' : 'common'
        })
      )

      petStore.inventory = largeInventory

      await expectPerformanceUnder(async () => {
        const foodItems = petStore.inventory.filter(item => item.type === 'food')
        const rareItems = petStore.inventory.filter(item => item.rarity === 'rare')
      }, 50) // Filtering 1000 items should be under 50ms
    })
  })

  describe('Network Request Performance', () => {
    it('contract calls should have reasonable timeout', async () => {
      const { createMockContract } = await import('@/test/utils')
      const mockContract = createMockContract()

      // Simulate slow network
      mockContract.balanceOf.mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve('1000000000000000000'), 100))
      )

      const startTime = performance.now()
      const balance = await mockContract.balanceOf('******************************************')
      const endTime = performance.now()

      expect(balance).toBe('1000000000000000000')
      expect(endTime - startTime).toBeGreaterThan(90) // Should respect the delay
      expect(endTime - startTime).toBeLessThan(200) // But not be excessively slow
    })

    it('batch contract calls should be efficient', async () => {
      const { createMockContract } = await import('@/test/utils')
      const mockContract = createMockContract()

      const addresses = Array.from({ length: 10 }, (_, i) =>
        `0x${i.toString().padStart(40, '0')}`
      )

      await expectPerformanceUnder(async () => {
        const balancePromises = addresses.map(addr => mockContract.balanceOf(addr))
        await Promise.all(balancePromises)
      }, 100) // 10 parallel calls should complete within 100ms
    })
  })

  describe('Storage Performance', () => {
    it('local storage operations should be fast', async () => {
      const petStore = usePetStore()
      const largePetCollection = generateMockPets(100)
      petStore.pets = largePetCollection

      await expectPerformanceUnder(async () => {
        await petStore.savePetData()
      }, 100) // Saving 100 pets should be under 100ms

      await expectPerformanceUnder(async () => {
        await petStore.loadPetData()
      }, 100) // Loading 100 pets should be under 100ms
    })

    it('storage compression should improve performance', async () => {
      const petStore = usePetStore()
      const hugePetCollection = generateMockPets(1000)
      petStore.pets = hugePetCollection

      const uncompressedTime = await measurePerformance(async () => {
        localStorage.setItem('test_uncompressed', JSON.stringify(hugePetCollection))
      })

      // Test with compression (if implemented)
      const compressedTime = await measurePerformance(async () => {
        await petStore.savePetData() // Assumes compression is implemented
      })

      // Compressed save should be faster or at least not significantly slower
      expect(compressedTime).toBeLessThan(uncompressedTime * 2)
    })
  })

  describe('Real-time Performance Monitoring', () => {
    it('should track frame rate during animations', async () => {
      let frameCount = 0
      const startTime = performance.now()

      // Simulate animation loop
      const animationLoop = () => {
        frameCount++
        if (performance.now() - startTime < 1000) { // Run for 1 second
          requestAnimationFrame(animationLoop)
        }
      }

      requestAnimationFrame(animationLoop)

      // Wait for animation to complete
      await new Promise(resolve => setTimeout(resolve, 1100))

      const fps = frameCount / 1 // frames per second
      expect(fps).toBeGreaterThan(30) // Should maintain at least 30 FPS
    })

    it('should monitor memory usage during gameplay', async () => {
      const initialMemory = measureMemoryUsage()
      const petStore = usePetStore()

      // Simulate intensive gameplay
      for (let i = 0; i < 50; i++) {
        const pet = await petStore.createPet(`Pet ${i}`, 'cat')
        await petStore.feedPet(pet.id)
        await petStore.trainPet(pet.id)
        await petStore.playWithPet(pet.id)
      }

      const finalMemory = measureMemoryUsage()
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be reasonable
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024) // Less than 10MB
    })
  })
})
