import { STORAGE_KEYS } from '../config/constants'
import type { WalletInfo } from '../types'

export interface WalletPersistenceData {
  address: string
  chainId: number
  balance: string
  walletType: 'metamask' | 'walletconnect'
  lastConnectedTime: number
  sessionId: string
}

export class WalletPersistenceService {
  private readonly STORAGE_VERSION = '1.0'
  private readonly SESSION_DURATION = 24 * 60 * 60 * 1000 // 24小时

  /**
   * 保存钱包状态到本地存储
   */
  saveWalletState(walletInfo: WalletInfo, walletType: 'metamask' | 'walletconnect'): void {
    try {
      const data: WalletPersistenceData = {
        address: walletInfo.address,
        chainId: walletInfo.chainId,
        balance: walletInfo.balance,
        walletType,
        lastConnectedTime: Date.now(),
        sessionId: this.generateSessionId()
      }

      const storageData = {
        version: this.STORAGE_VERSION,
        data,
        timestamp: Date.now()
      }

      localStorage.setItem(STORAGE_KEYS.WALLET_STATE, JSON.stringify(storageData))
      localStorage.setItem('wallet_connected', 'true')
      localStorage.setItem('wallet_type', walletType)

      console.log('钱包状态已保存:', data.address)
    } catch (error) {
      console.error('保存钱包状态失败:', error)
    }
  }

  /**
   * 从本地存储加载钱包状态
   */
  loadWalletState(): WalletPersistenceData | null {
    try {
      const savedData = localStorage.getItem(STORAGE_KEYS.WALLET_STATE)
      if (!savedData) return null

      const storageData = JSON.parse(savedData)

      // 检查数据版本
      if (storageData.version !== this.STORAGE_VERSION) {
        console.log('钱包状态版本不匹配，清除旧数据')
        this.clearWalletState()
        return null
      }

      const data = storageData.data as WalletPersistenceData

      // 验证数据完整性
      if (!this.validateWalletData(data)) {
        console.log('钱包状态数据无效，清除数据')
        this.clearWalletState()
        return null
      }

      // 检查会话是否过期
      if (this.isSessionExpired(data.lastConnectedTime)) {
        console.log('钱包会话已过期，清除数据')
        this.clearWalletState()
        return null
      }

      return data
    } catch (error) {
      console.error('加载钱包状态失败:', error)
      this.clearWalletState()
      return null
    }
  }

  /**
   * 清除钱包状态
   */
  clearWalletState(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.WALLET_STATE)
      localStorage.removeItem('wallet_connected')
      localStorage.removeItem('wallet_type')
      console.log('钱包状态已清除')
    } catch (error) {
      console.error('清除钱包状态失败:', error)
    }
  }

  /**
   * 更新钱包余额
   */
  updateBalance(balance: string): void {
    try {
      const savedData = localStorage.getItem(STORAGE_KEYS.WALLET_STATE)
      if (!savedData) return

      const storageData = JSON.parse(savedData)
      if (storageData.data) {
        storageData.data.balance = balance
        storageData.timestamp = Date.now()
        localStorage.setItem(STORAGE_KEYS.WALLET_STATE, JSON.stringify(storageData))
      }
    } catch (error) {
      console.error('更新余额失败:', error)
    }
  }

  /**
   * 检查是否有有效的钱包会话
   */
  hasValidSession(): boolean {
    const data = this.loadWalletState()
    return data !== null
  }

  /**
   * 获取上次连接的钱包类型
   */
  getLastWalletType(): 'metamask' | 'walletconnect' | null {
    try {
      const walletType = localStorage.getItem('wallet_type')
      return walletType as 'metamask' | 'walletconnect' | null
    } catch {
      return null
    }
  }

  /**
   * 验证钱包数据的有效性
   */
  private validateWalletData(data: any): data is WalletPersistenceData {
    if (!data || typeof data !== 'object') return false

    const requiredFields = ['address', 'chainId', 'balance', 'walletType', 'lastConnectedTime']
    for (const field of requiredFields) {
      if (!(field in data)) return false
    }

    // 验证地址格式
    if (typeof data.address !== 'string' || !data.address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return false
    }

    // 验证链ID
    if (typeof data.chainId !== 'number' || data.chainId <= 0) {
      return false
    }

    // 验证钱包类型
    if (!['metamask', 'walletconnect'].includes(data.walletType)) {
      return false
    }

    // 验证时间戳
    if (typeof data.lastConnectedTime !== 'number' || data.lastConnectedTime <= 0) {
      return false
    }

    return true
  }

  /**
   * 检查会话是否过期
   */
  private isSessionExpired(lastConnectedTime: number): boolean {
    return Date.now() - lastConnectedTime > this.SESSION_DURATION
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats() {
    try {
      const data = this.loadWalletState()
      if (!data) return null

      return {
        address: data.address,
        walletType: data.walletType,
        lastConnected: new Date(data.lastConnectedTime).toLocaleString(),
        sessionAge: Date.now() - data.lastConnectedTime,
        isExpired: this.isSessionExpired(data.lastConnectedTime)
      }
    } catch {
      return null
    }
  }
}

// 导出单例实例
export const walletPersistenceService = new WalletPersistenceService()
