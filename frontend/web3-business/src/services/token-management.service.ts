import { ethers } from 'ethers'
import { contractService } from './contract.service'
import type { TransactionResponse, TaxInfo, TransferPreview } from './contract.service'

export interface TokenBalance {
  balance: string
  balanceFormatted: string
  symbol: string
  decimals: number
  lastUpdated: number
}

export interface TransferRequest {
  to: string
  amount: string
  useV2Contract?: boolean
}

export interface TransactionHistory {
  hash: string
  from: string
  to: string
  amount: string
  amountFormatted: string
  timestamp: number
  blockNumber?: number
  gasUsed?: string
  gasPrice?: string
  status: 'pending' | 'confirmed' | 'failed'
  type: 'send' | 'receive' | 'mint' | 'burn'
  taxAmount?: string
  netAmount?: string
}

export interface TokenStats {
  totalTransactions: number
  totalSent: string
  totalReceived: string
  totalTaxPaid: string
  averageTransactionAmount: string
  lastTransactionTime: number
}

export class TokenManagementService {
  private balanceCache: Map<string, TokenBalance> = new Map()
  private transactionHistory: TransactionHistory[] = []
  private readonly STORAGE_KEY = 'token_transaction_history'
  private readonly BALANCE_CACHE_DURATION = 30000 // 30 seconds
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.loadTransactionHistory()
    this.setupEventListeners()
    this.eventListeners.set('balanceUpdated', [])
    this.eventListeners.set('transactionAdded', [])
    this.eventListeners.set('transferCompleted', [])
  }

  /**
   * 获取代币余额
   */
  async getBalance(address: string, forceRefresh = false): Promise<TokenBalance> {
    const cacheKey = address.toLowerCase()
    const cached = this.balanceCache.get(cacheKey)

    // 检查缓存是否有效
    if (!forceRefresh && cached && (Date.now() - cached.lastUpdated) < this.BALANCE_CACHE_DURATION) {
      return cached
    }

    try {
      // 获取合约信息
      const contractInfo = await contractService.getContractInfo()

      // 获取余额
      const balanceWei = await contractService.getBalance(address)
      const balanceFormatted = ethers.formatEther(balanceWei)

      const tokenBalance: TokenBalance = {
        balance: balanceWei,
        balanceFormatted: parseFloat(balanceFormatted).toFixed(6),
        symbol: contractInfo.symbol,
        decimals: contractInfo.decimals,
        lastUpdated: Date.now()
      }

      // 更新缓存
      this.balanceCache.set(cacheKey, tokenBalance)

      // 触发余额更新事件
      this.emitEvent('balanceUpdated', { address, balance: tokenBalance })

      return tokenBalance
    } catch (error) {
      console.error('获取余额失败:', error)
      throw new Error('获取余额失败')
    }
  }

  /**
   * 刷新余额
   */
  async refreshBalance(address: string): Promise<TokenBalance> {
    return this.getBalance(address, true)
  }

  /**
   * 转账代币
   */
  async transfer(request: TransferRequest, fromAddress: string): Promise<TransactionHistory> {
    // 验证输入
    if (!ethers.isAddress(request.to)) {
      throw new Error('无效的接收地址')
    }

    if (!ethers.isAddress(fromAddress)) {
      throw new Error('无效的发送地址')
    }

    if (request.to.toLowerCase() === fromAddress.toLowerCase()) {
      throw new Error('不能转账给自己')
    }

    // 验证金额
    let amountWei: bigint
    try {
      amountWei = ethers.parseEther(request.amount)
      if (amountWei <= 0n) {
        throw new Error('转账金额必须大于0')
      }
    } catch (error) {
      throw new Error('无效的转账金额')
    }

    // 检查余额
    const balance = await this.getBalance(fromAddress)
    const balanceWei = BigInt(balance.balance)

    if (balanceWei < amountWei) {
      throw new Error('余额不足')
    }

    // 创建交易记录
    const transaction: TransactionHistory = {
      hash: '',
      from: fromAddress,
      to: request.to,
      amount: amountWei.toString(),
      amountFormatted: request.amount,
      timestamp: Date.now(),
      status: 'pending',
      type: 'send'
    }

    try {
      let txResponse: TransactionResponse

      if (request.useV2Contract) {
        // 使用V2合约转账（包含税收）
        const preview = await this.previewV2Transfer(fromAddress, request.to, amountWei.toString())

        transaction.taxAmount = preview.taxAmount.toString()
        transaction.netAmount = preview.netAmount.toString()

        // 执行V2转账
        txResponse = await contractService.transfer(request.to, amountWei.toString())
      } else {
        // 使用普通转账
        txResponse = await contractService.transfer(request.to, amountWei.toString())
      }

      transaction.hash = txResponse.hash

      // 添加到历史记录
      this.addTransactionToHistory(transaction)

      // 等待交易确认
      const receipt = await txResponse.wait()

      if (receipt.status !== 1) {
        transaction.status = 'failed'
        this.updateTransactionInHistory(transaction)
        throw new Error('交易执行失败')
      }

      // 更新交易状态
      transaction.status = 'confirmed'
      transaction.blockNumber = receipt.blockNumber
      transaction.gasUsed = receipt.gasUsed?.toString()
      transaction.gasPrice = receipt.gasPrice?.toString()

      this.updateTransactionInHistory(transaction)

      // 清除余额缓存，强制刷新
      this.balanceCache.delete(fromAddress.toLowerCase())
      this.balanceCache.delete(request.to.toLowerCase())

      // 触发转账完成事件
      this.emitEvent('transferCompleted', {
        transaction,
        success: true
      })

      return transaction

    } catch (error) {
      transaction.status = 'failed'
      this.updateTransactionInHistory(transaction)

      // 触发转账失败事件
      this.emitEvent('transferCompleted', {
        transaction,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      })

      // 处理不同类型的错误
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase()

        if (errorMessage.includes('user rejected') || errorMessage.includes('user denied')) {
          throw new Error('用户取消了交易')
        } else if (errorMessage.includes('insufficient funds')) {
          throw new Error('余额不足支付Gas费用')
        } else if (errorMessage.includes('nonce too high')) {
          throw new Error('交易nonce错误，请重试')
        } else if (errorMessage.includes('replacement transaction underpriced')) {
          throw new Error('交易费用过低，请提高Gas价格')
        } else if (errorMessage.includes('network')) {
          throw new Error('网络连接异常，请检查网络设置')
        } else {
          throw new Error(`转账失败: ${error.message}`)
        }
      }

      throw new Error('转账失败，请稍后重试')
    }
  }

  /**
   * 安全转账代币
   */
  async safeTransfer(request: TransferRequest, fromAddress: string): Promise<TransactionHistory> {
    // 验证输入
    if (!ethers.isAddress(request.to)) {
      throw new Error('无效的接收地址')
    }

    if (!ethers.isAddress(fromAddress)) {
      throw new Error('无效的发送地址')
    }

    // 验证金额
    let amountWei: bigint
    try {
      amountWei = ethers.parseEther(request.amount)
      if (amountWei <= 0n) {
        throw new Error('转账金额必须大于0')
      }
    } catch (error) {
      throw new Error('无效的转账金额')
    }

    // 创建交易记录
    const transaction: TransactionHistory = {
      hash: '',
      from: fromAddress,
      to: request.to,
      amount: amountWei.toString(),
      amountFormatted: request.amount,
      timestamp: Date.now(),
      status: 'pending',
      type: 'send'
    }

    try {
      // 使用安全转账
      const txResponse = await contractService.safeTransfer(request.to, amountWei.toString())
      transaction.hash = txResponse.hash

      // 添加到历史记录
      this.addTransactionToHistory(transaction)

      // 等待交易确认
      const receipt = await txResponse.wait()

      if (receipt.status !== 1) {
        transaction.status = 'failed'
        this.updateTransactionInHistory(transaction)
        throw new Error('交易执行失败')
      }

      // 更新交易状态
      transaction.status = 'confirmed'
      transaction.blockNumber = receipt.blockNumber
      transaction.gasUsed = receipt.gasUsed?.toString()
      transaction.gasPrice = receipt.gasPrice?.toString()

      this.updateTransactionInHistory(transaction)

      // 清除余额缓存
      this.balanceCache.delete(fromAddress.toLowerCase())
      this.balanceCache.delete(request.to.toLowerCase())

      return transaction

    } catch (error) {
      transaction.status = 'failed'
      this.updateTransactionInHistory(transaction)
      throw error
    }
  }

  /**
   * 批量转账
   */
  async batchTransfer(recipients: string[], amounts: string[], fromAddress: string): Promise<TransactionHistory> {
    if (recipients.length !== amounts.length) {
      throw new Error('接收地址和金额数组长度不匹配')
    }

    if (recipients.length === 0) {
      throw new Error('至少需要一个接收地址')
    }

    // 验证所有地址
    for (const recipient of recipients) {
      if (!ethers.isAddress(recipient)) {
        throw new Error(`无效的接收地址: ${recipient}`)
      }
    }

    // 计算总金额
    let totalAmountWei = 0n
    const amountsWei: string[] = []

    for (const amount of amounts) {
      try {
        const amountWei = ethers.parseEther(amount)
        if (amountWei <= 0n) {
          throw new Error(`无效的转账金额: ${amount}`)
        }
        amountsWei.push(amountWei.toString())
        totalAmountWei += amountWei
      } catch (error) {
        throw new Error(`无效的转账金额: ${amount}`)
      }
    }

    // 检查余额
    const balance = await this.getBalance(fromAddress)
    const balanceWei = BigInt(balance.balance)

    if (balanceWei < totalAmountWei) {
      throw new Error('余额不足')
    }

    // 创建交易记录
    const transaction: TransactionHistory = {
      hash: '',
      from: fromAddress,
      to: `批量转账 (${recipients.length}个地址)`,
      amount: totalAmountWei.toString(),
      amountFormatted: ethers.formatEther(totalAmountWei),
      timestamp: Date.now(),
      status: 'pending',
      type: 'send'
    }

    try {
      // 执行批量转账
      const txResponse = await contractService.batchTransfer(recipients, amountsWei)
      transaction.hash = txResponse.hash

      // 添加到历史记录
      this.addTransactionToHistory(transaction)

      // 等待交易确认
      const receipt = await txResponse.wait()

      if (receipt.status !== 1) {
        transaction.status = 'failed'
        this.updateTransactionInHistory(transaction)
        throw new Error('交易执行失败')
      }

      // 更新交易状态
      transaction.status = 'confirmed'
      transaction.blockNumber = receipt.blockNumber
      transaction.gasUsed = receipt.gasUsed?.toString()
      transaction.gasPrice = receipt.gasPrice?.toString()

      this.updateTransactionInHistory(transaction)

      // 清除余额缓存
      this.balanceCache.delete(fromAddress.toLowerCase())
      for (const recipient of recipients) {
        this.balanceCache.delete(recipient.toLowerCase())
      }

      return transaction

    } catch (error) {
      transaction.status = 'failed'
      this.updateTransactionInHistory(transaction)
      throw error
    }
  }

  /**
   * 获取V2合约税收信息
   */
  async getTaxInfo(): Promise<TaxInfo> {
    try {
      return await contractService.getTaxInfo()
    } catch (error) {
      console.error('获取税收信息失败:', error)
      throw new Error('获取税收信息失败')
    }
  }

  /**
   * 预览V2转账（包含税收计算）
   */
  async previewV2Transfer(from: string, to: string, amount: string): Promise<TransferPreview> {
    try {
      return await contractService.previewTransfer(from, to, amount)
    } catch (error) {
      console.error('预览转账失败:', error)
      throw new Error('预览转账失败')
    }
  }

  /**
   * 计算税收
   */
  async calculateTax(amount: string): Promise<{ netAmount: string, taxAmount: string }> {
    try {
      return await contractService.calculateTax(amount)
    } catch (error) {
      console.error('计算税收失败:', error)
      throw new Error('计算税收失败')
    }
  }

  /**
   * 获取交易历史
   */
  getTransactionHistory(address?: string): TransactionHistory[] {
    if (!address) {
      return [...this.transactionHistory].reverse() // 最新的在前
    }

    const addressLower = address.toLowerCase()
    return this.transactionHistory
      .filter(tx =>
        tx.from.toLowerCase() === addressLower ||
        tx.to.toLowerCase() === addressLower
      )
      .reverse()
  }

  /**
   * 获取代币统计信息
   */
  getTokenStats(address: string): TokenStats {
    const addressLower = address.toLowerCase()
    const userTransactions = this.transactionHistory.filter(tx =>
      tx.from.toLowerCase() === addressLower ||
      tx.to.toLowerCase() === addressLower
    )

    const confirmedTxs = userTransactions.filter(tx => tx.status === 'confirmed')

    let totalSent = 0n
    let totalReceived = 0n
    let totalTaxPaid = 0n

    for (const tx of confirmedTxs) {
      const amount = BigInt(tx.amount)

      if (tx.from.toLowerCase() === addressLower) {
        // 发送的交易
        totalSent += amount
        if (tx.taxAmount) {
          totalTaxPaid += BigInt(tx.taxAmount)
        }
      } else {
        // 接收的交易
        totalReceived += amount
      }
    }

    const averageAmount = confirmedTxs.length > 0
      ? (totalSent + totalReceived) / BigInt(confirmedTxs.length)
      : 0n

    const lastTransactionTime = userTransactions.length > 0
      ? Math.max(...userTransactions.map(tx => tx.timestamp))
      : 0

    return {
      totalTransactions: userTransactions.length,
      totalSent: totalSent.toString(),
      totalReceived: totalReceived.toString(),
      totalTaxPaid: totalTaxPaid.toString(),
      averageTransactionAmount: averageAmount.toString(),
      lastTransactionTime
    }
  }

  /**
   * 清除交易历史
   */
  clearTransactionHistory(): void {
    this.transactionHistory = []
    this.saveTransactionHistory()
  }

  /**
   * 清除余额缓存
   */
  clearBalanceCache(): void {
    this.balanceCache.clear()
  }

  // 私有方法

  private setupEventListeners(): void {
    // 监听合约事件
    contractService.onEvent('Transfer', (event) => {
      this.handleTransferEvent(event)
    })

    contractService.onEvent('TaxCollected', (event) => {
      this.handleTaxEvent(event)
    })

    contractService.onEvent('MintCompleted', (event) => {
      this.handleMintEvent(event)
    })
  }

  private handleTransferEvent(event: any): void {
    const { from, to, amount } = event

    // 创建交易记录
    const transaction: TransactionHistory = {
      hash: event.transactionHash || '',
      from,
      to,
      amount: amount.toString(),
      amountFormatted: ethers.formatEther(amount),
      timestamp: Date.now(),
      status: 'confirmed',
      type: from === '******************************************' ? 'mint' : 'receive',
      blockNumber: event.blockNumber
    }

    // 检查是否已存在
    const exists = this.transactionHistory.some(tx =>
      tx.hash === transaction.hash &&
      tx.from === transaction.from &&
      tx.to === transaction.to
    )

    if (!exists) {
      this.addTransactionToHistory(transaction)
    }

    // 清除相关地址的余额缓存
    this.balanceCache.delete(from.toLowerCase())
    this.balanceCache.delete(to.toLowerCase())
  }

  private handleTaxEvent(event: any): void {
    const { from, to, taxAmount, netAmount } = event

    // 查找对应的转账记录并更新税收信息
    const transaction = this.transactionHistory.find(tx =>
      tx.from.toLowerCase() === from.toLowerCase() &&
      tx.to.toLowerCase() === to.toLowerCase() &&
      tx.status === 'pending'
    )

    if (transaction) {
      transaction.taxAmount = taxAmount.toString()
      transaction.netAmount = netAmount.toString()
      this.updateTransactionInHistory(transaction)
    }
  }

  private handleMintEvent(event: any): void {
    const { to, amount } = event

    // 创建铸造记录
    const transaction: TransactionHistory = {
      hash: event.transactionHash || '',
      from: '******************************************',
      to,
      amount: amount.toString(),
      amountFormatted: ethers.formatEther(amount),
      timestamp: Date.now(),
      status: 'confirmed',
      type: 'mint',
      blockNumber: event.blockNumber
    }

    this.addTransactionToHistory(transaction)

    // 清除余额缓存
    this.balanceCache.delete(to.toLowerCase())
  }

  private addTransactionToHistory(transaction: TransactionHistory): void {
    this.transactionHistory.push(transaction)
    this.saveTransactionHistory()
    this.emitEvent('transactionAdded', transaction)
  }

  private updateTransactionInHistory(transaction: TransactionHistory): void {
    const index = this.transactionHistory.findIndex(tx =>
      tx.hash === transaction.hash &&
      tx.from === transaction.from &&
      tx.to === transaction.to
    )

    if (index !== -1) {
      this.transactionHistory[index] = transaction
      this.saveTransactionHistory()
    }
  }

  private loadTransactionHistory(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.transactionHistory = JSON.parse(stored)
      }
    } catch (error) {
      console.error('加载交易历史失败:', error)
      this.transactionHistory = []
    }
  }

  private saveTransactionHistory(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.transactionHistory))
    } catch (error) {
      console.error('保存交易历史失败:', error)
    }
  }

  private emitEvent(eventName: string, data: any): void {
    const listeners = this.eventListeners.get(eventName) || []
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`事件监听器执行失败 (${eventName}):`, error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    const listeners = this.eventListeners.get(eventName) || []
    listeners.push(callback)
    this.eventListeners.set(eventName, listeners)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) return

    const listeners = this.eventListeners.get(eventName) || []
    const index = listeners.indexOf(callback)
    if (index !== -1) {
      listeners.splice(index, 1)
      this.eventListeners.set(eventName, listeners)
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.eventListeners.clear()
    this.balanceCache.clear()
  }
}

// 导出单例实例
export const tokenManagementService = new TokenManagementService()
