/**
 * 安全审计和监控服务
 * Security audit and monitoring service
 */

import { ethers } from 'ethers'

export interface SecurityEvent {
  id: string
  timestamp: Date
  type: 'transaction' | 'wallet_connection' | 'contract_interaction' | 'suspicious_activity'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  details: Record<string, any>
  userAddress?: string
  transactionHash?: string
}

export interface SecurityMetrics {
  totalTransactions: number
  failedTransactions: number
  suspiciousActivities: number
  walletConnections: number
  averageTransactionValue: string
  lastAuditTime: Date
}

export class SecurityAuditService {
  private static instance: SecurityAuditService
  private events: SecurityEvent[] = []
  private maxEvents = 1000
  private auditInterval: NodeJS.Timeout | null = null

  static getInstance(): SecurityAuditService {
    if (!SecurityAuditService.instance) {
      SecurityAuditService.instance = new SecurityAuditService()
    }
    return SecurityAuditService.instance
  }

  /**
   * 启动安全监控
   */
  startMonitoring() {
    // 每5分钟执行一次安全审计
    this.auditInterval = setInterval(() => {
      this.performSecurityAudit()
    }, 5 * 60 * 1000)

    // 立即执行一次审计
    this.performSecurityAudit()
  }

  /**
   * 停止安全监控
   */
  stopMonitoring() {
    if (this.auditInterval) {
      clearInterval(this.auditInterval)
      this.auditInterval = null
    }
  }

  /**
   * 记录安全事件
   */
  logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      ...event
    }

    this.events.push(securityEvent)

    // 保持事件数量在限制内
    if (this.events.length > this.maxEvents) {
      this.events.splice(0, this.events.length - this.maxEvents)
    }

    // 持久化到本地存储
    this.persistEvents()

    // 如果是高危事件，立即处理
    if (event.severity === 'critical' || event.severity === 'high') {
      this.handleHighSeverityEvent(securityEvent)
    }
  }

  /**
   * 执行安全审计
   */
  private performSecurityAudit() {
    try {
      // 检查异常交易模式
      this.auditTransactionPatterns()

      // 检查存储安全
      this.auditStorageSecurity()

      // 检查网络安全
      this.auditNetworkSecurity()

      // 生成审计报告
      const metrics = this.generateSecurityMetrics()

      this.logSecurityEvent({
        type: 'transaction',
        severity: 'low',
        description: '定期安全审计完成',
        details: { metrics }
      })
    } catch (error) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'medium',
        description: '安全审计执行失败',
        details: { error: error instanceof Error ? error.message : String(error) }
      })
    }
  }

  /**
   * 审计交易模式
   */
  private auditTransactionPatterns() {
    const recentEvents = this.getRecentEvents(24 * 60 * 60 * 1000) // 24小时内
    const transactionEvents = recentEvents.filter(e => e.type === 'transaction')

    // 检查频繁交易
    if (transactionEvents.length > 100) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'medium',
        description: '检测到异常频繁的交易活动',
        details: { transactionCount: transactionEvents.length }
      })
    }

    // 检查大额交易
    const largeTransactions = transactionEvents.filter(e => {
      const value = e.details.value
      if (value) {
        try {
          const valueWei = ethers.parseEther(value)
          const limitWei = ethers.parseEther('100') // 100 ETH
          return valueWei > limitWei
        } catch {
          return false
        }
      }
      return false
    })

    if (largeTransactions.length > 0) {
      this.logSecurityEvent({
        type: 'transaction',
        severity: 'high',
        description: '检测到大额交易',
        details: { largeTransactionCount: largeTransactions.length }
      })
    }
  }

  /**
   * 审计存储安全
   */
  private auditStorageSecurity() {
    try {
      // 检查本地存储大小
      const storageSize = this.calculateStorageSize()
      if (storageSize > 5 * 1024 * 1024) { // 5MB
        this.logSecurityEvent({
          type: 'suspicious_activity',
          severity: 'medium',
          description: '本地存储使用量过大',
          details: { storageSize }
        })
      }

      // 检查敏感数据
      this.checkSensitiveDataInStorage()
    } catch (error) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        severity: 'low',
        description: '存储安全审计失败',
        details: { error: error instanceof Error ? error.message : String(error) }
      })
    }
  }

  /**
   * 审计网络安全
   */
  private auditNetworkSecurity() {
    // 检查当前网络
    const expectedChainId = import.meta.env.VITE_CHAIN_ID
    const currentChainId = localStorage.getItem('current_chain_id')

    if (currentChainId && currentChainId !== expectedChainId) {
      this.logSecurityEvent({
        type: 'wallet_connection',
        severity: 'medium',
        description: '检测到网络切换',
        details: {
          expected: expectedChainId,
          current: currentChainId
        }
      })
    }
  }

  /**
   * 处理高危事件
   */
  private handleHighSeverityEvent(event: SecurityEvent) {
    console.warn('High severity security event:', event)

    // 可以在这里添加更多处理逻辑，比如：
    // - 发送警报通知
    // - 暂停某些功能
    // - 记录到远程日志系统

    // 暂时只在控制台输出警告
    if (event.severity === 'critical') {
      console.error('CRITICAL SECURITY EVENT:', event)
    }
  }

  /**
   * 生成安全指标
   */
  generateSecurityMetrics(): SecurityMetrics {
    const recentEvents = this.getRecentEvents(24 * 60 * 60 * 1000) // 24小时内
    const transactionEvents = recentEvents.filter(e => e.type === 'transaction')
    const failedEvents = recentEvents.filter(e => e.details.failed === true)
    const suspiciousEvents = recentEvents.filter(e => e.type === 'suspicious_activity')
    const walletEvents = recentEvents.filter(e => e.type === 'wallet_connection')

    // 计算平均交易金额
    let totalValue = 0n
    let valueCount = 0

    transactionEvents.forEach(event => {
      if (event.details.value) {
        try {
          totalValue += ethers.parseEther(event.details.value)
          valueCount++
        } catch {
          // 忽略无效值
        }
      }
    })

    const averageValue = valueCount > 0 ?
      ethers.formatEther(totalValue / BigInt(valueCount)) : '0'

    return {
      totalTransactions: transactionEvents.length,
      failedTransactions: failedEvents.length,
      suspiciousActivities: suspiciousEvents.length,
      walletConnections: walletEvents.length,
      averageTransactionValue: averageValue,
      lastAuditTime: new Date()
    }
  }

  /**
   * 获取最近的事件
   */
  private getRecentEvents(timeRangeMs: number): SecurityEvent[] {
    const cutoffTime = new Date(Date.now() - timeRangeMs)
    return this.events.filter(event => event.timestamp > cutoffTime)
  }

  /**
   * 计算存储大小
   */
  private calculateStorageSize(): number {
    let totalSize = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalSize += localStorage[key].length + key.length
      }
    }
    return totalSize
  }

  /**
   * 检查存储中的敏感数据
   */
  private checkSensitiveDataInStorage() {
    const sensitiveKeys = ['private_key', 'mnemonic', 'seed', 'password']

    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        const lowerKey = key.toLowerCase()
        const value = localStorage[key]

        // 检查键名是否包含敏感词
        if (sensitiveKeys.some(sensitive => lowerKey.includes(sensitive))) {
          this.logSecurityEvent({
            type: 'suspicious_activity',
            severity: 'critical',
            description: '检测到本地存储中可能包含敏感数据',
            details: { suspiciousKey: key }
          })
        }

        // 检查值是否像私钥
        if (value && typeof value === 'string' && /^0x[a-fA-F0-9]{64}$/.test(value)) {
          this.logSecurityEvent({
            type: 'suspicious_activity',
            severity: 'high',
            description: '检测到可能的私钥存储',
            details: { key }
          })
        }
      }
    }
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 持久化事件到本地存储
   */
  private persistEvents() {
    try {
      const eventsToStore = this.events.slice(-100) // 只存储最近100个事件
      localStorage.setItem('security_events', JSON.stringify(eventsToStore))
    } catch (error) {
      console.warn('Failed to persist security events:', error)
    }
  }

  /**
   * 从本地存储加载事件
   */
  loadPersistedEvents() {
    try {
      const stored = localStorage.getItem('security_events')
      if (stored) {
        const events = JSON.parse(stored)
        this.events = events.map((e: any) => ({
          ...e,
          timestamp: new Date(e.timestamp)
        }))
      }
    } catch (error) {
      console.warn('Failed to load persisted security events:', error)
    }
  }

  /**
   * 获取所有事件
   */
  getAllEvents(): SecurityEvent[] {
    return [...this.events]
  }

  /**
   * 获取特定类型的事件
   */
  getEventsByType(type: SecurityEvent['type']): SecurityEvent[] {
    return this.events.filter(event => event.type === type)
  }

  /**
   * 获取特定严重程度的事件
   */
  getEventsBySeverity(severity: SecurityEvent['severity']): SecurityEvent[] {
    return this.events.filter(event => event.severity === severity)
  }

  /**
   * 清除所有事件
   */
  clearEvents() {
    this.events = []
    localStorage.removeItem('security_events')
  }

  /**
   * 导出安全报告
   */
  exportSecurityReport(): {
    metrics: SecurityMetrics
    recentEvents: SecurityEvent[]
    summary: string
  } {
    const metrics = this.generateSecurityMetrics()
    const recentEvents = this.getRecentEvents(24 * 60 * 60 * 1000)

    const summary = `
安全报告摘要：
- 总交易数：${metrics.totalTransactions}
- 失败交易数：${metrics.failedTransactions}
- 可疑活动：${metrics.suspiciousActivities}
- 钱包连接次数：${metrics.walletConnections}
- 平均交易金额：${metrics.averageTransactionValue} ETH
- 最后审计时间：${metrics.lastAuditTime.toLocaleString()}
    `.trim()

    return {
      metrics,
      recentEvents,
      summary
    }
  }
}

export const securityAuditService = SecurityAuditService.getInstance()
