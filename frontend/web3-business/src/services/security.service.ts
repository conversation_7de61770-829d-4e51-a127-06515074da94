/**
 * 安全服务 - 处理交易安全确认和监控
 * Security Service - Handle transaction security confirmation and monitoring
 */

import { ethers } from 'ethers'
import { showConfirmDialog, showNotify } from 'vant'
import { InputValidator, SecurityValidator } from '../utils/validation'

export interface TransactionSecurityCheck {
  isSecure: boolean
  warnings: string[]
  errors: string[]
  riskLevel: 'low' | 'medium' | 'high'
}

export interface SecureTransactionParams {
  to: string
  value?: string
  data?: string
  gasLimit?: string
  description: string
  confirmationMessage?: string
}

export class SecurityService {
  private static instance: SecurityService
  private provider: ethers.Provider | null = null
  private securityConfig = {
    maxTransactionAmount: '100', // ETH
    requireConfirmationAbove: '10', // ETH
    suspiciousGasLimit: 1000000,
    maxDataLength: 50000
  }

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService()
    }
    return SecurityService.instance
  }

  setProvider(provider: ethers.Provider) {
    this.provider = provider
  }

  /**
   * 执行安全交易
   */
  async executeSecureTransaction(
    contractMethod: () => Promise<ethers.TransactionResponse>,
    params: SecureTransactionParams
  ): Promise<ethers.TransactionReceipt> {
    try {
      // 1. 安全检查
      const securityCheck = await this.performSecurityCheck(params)

      if (!securityCheck.isSecure) {
        throw new Error(`安全检查失败: ${securityCheck.errors.join(', ')}`)
      }

      // 2. 显示警告（如果有）
      if (securityCheck.warnings.length > 0) {
        const warningMessage = securityCheck.warnings.join('\n')
        const continueTransaction = await showConfirmDialog({
          title: '安全警告',
          message: `${warningMessage}\n\n是否继续执行交易？`,
          confirmButtonText: '继续',
          cancelButtonText: '取消'
        }).catch(() => false)

        if (!continueTransaction) {
          throw new Error('用户取消交易')
        }
      }

      // 3. 用户确认
      const confirmMessage = params.confirmationMessage ||
        `确认执行以下操作：\n${params.description}\n\n接收地址：${params.to}\n${params.value ? `金额：${params.value} ETH` : ''}`

      const confirmed = await showConfirmDialog({
        title: '交易确认',
        message: confirmMessage,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).catch(() => false)

      if (!confirmed) {
        throw new Error('用户取消交易')
      }

      // 4. 执行交易
      showNotify({
        type: 'loading',
        message: '正在执行交易...',
        duration: 0
      })

      const tx = await contractMethod()

      // 5. 等待确认
      showNotify({
        type: 'loading',
        message: '等待交易确认...',
        duration: 0
      })

      const receipt = await tx.wait()

      if (receipt?.status !== 1) {
        throw new Error('交易执行失败')
      }

      // 6. 成功通知
      showNotify({
        type: 'success',
        message: '交易执行成功！'
      })

      // 7. 记录交易
      this.logTransaction(tx.hash, params)

      return receipt
    } catch (error: any) {
      showNotify({
        type: 'danger',
        message: this.formatErrorMessage(error)
      })
      throw error
    }
  }

  /**
   * 执行安全检查
   */
  private async performSecurityCheck(params: SecureTransactionParams): Promise<TransactionSecurityCheck> {
    const warnings: string[] = []
    const errors: string[] = []
    let riskLevel: 'low' | 'medium' | 'high' = 'low'

    // 1. 基础参数验证
    const validationResult = SecurityValidator.validateTransactionSecurity(params)
    if (!validationResult.isValid) {
      errors.push(...validationResult.errors)
      riskLevel = 'high'
    }

    // 2. 检查交易金额
    if (params.value) {
      if (!SecurityValidator.validateAmountSafety(params.value, this.securityConfig.maxTransactionAmount)) {
        errors.push(`交易金额超过安全限制 (${this.securityConfig.maxTransactionAmount} ETH)`)
        riskLevel = 'high'
      } else if (!SecurityValidator.validateAmountSafety(params.value, this.securityConfig.requireConfirmationAbove)) {
        warnings.push(`大额交易警告：金额为 ${params.value} ETH`)
        riskLevel = riskLevel === 'low' ? 'medium' : riskLevel
      }
    }

    // 3. 检查Gas限制
    if (params.gasLimit) {
      const gasLimit = parseInt(params.gasLimit)
      if (gasLimit > this.securityConfig.suspiciousGasLimit) {
        warnings.push(`Gas限制较高：${gasLimit}`)
        riskLevel = riskLevel === 'low' ? 'medium' : riskLevel
      }
    }

    // 4. 检查合约地址
    if (this.provider) {
      try {
        const isSuspicious = await SecurityValidator.checkSuspiciousContract(params.to, this.provider)
        if (isSuspicious) {
          warnings.push('目标地址可能存在风险，请仔细确认')
          riskLevel = 'medium'
        }
      } catch (error) {
        warnings.push('无法验证目标地址安全性')
      }
    }

    // 5. 检查数据长度
    if (params.data && params.data.length > this.securityConfig.maxDataLength) {
      warnings.push('交易数据较长，请确认操作内容')
      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel
    }

    return {
      isSecure: errors.length === 0,
      warnings,
      errors,
      riskLevel
    }
  }

  /**
   * 格式化错误信息
   */
  private formatErrorMessage(error: any): string {
    if (error.code === 'INSUFFICIENT_FUNDS') {
      return '余额不足，请检查您的钱包余额'
    }

    if (error.code === 'USER_REJECTED') {
      return '用户取消了交易'
    }

    if (error.message?.includes('TradingNotEnabled')) {
      return '代币交易尚未开启'
    }

    if (error.message?.includes('execution reverted')) {
      return '合约执行失败，请检查交易参数'
    }

    if (error.message?.includes('network')) {
      return '网络连接异常，请检查网络设置'
    }

    if (error.message?.includes('gas')) {
      return 'Gas费用不足或设置错误'
    }

    return error.message || '交易失败，请稍后重试'
  }

  /**
   * 记录交易日志
   */
  private logTransaction(hash: string, params: SecureTransactionParams) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      hash,
      to: params.to,
      value: params.value,
      description: params.description
    }

    try {
      const logs = JSON.parse(localStorage.getItem('transaction_logs') || '[]')
      logs.push(logEntry)

      // 只保留最近100条记录
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100)
      }

      localStorage.setItem('transaction_logs', JSON.stringify(logs))
    } catch (error) {
      console.warn('Failed to log transaction:', error)
    }
  }

  /**
   * 获取交易日志
   */
  getTransactionLogs(): any[] {
    try {
      return JSON.parse(localStorage.getItem('transaction_logs') || '[]')
    } catch {
      return []
    }
  }

  /**
   * 清除交易日志
   */
  clearTransactionLogs() {
    localStorage.removeItem('transaction_logs')
  }

  /**
   * 检查钱包连接安全性
   */
  async validateWalletConnection(provider: ethers.BrowserProvider): Promise<{
    isSecure: boolean
    warnings: string[]
  }> {
    const warnings: string[] = []

    try {
      // 检查网络
      const network = await provider.getNetwork()
      const expectedChainId = parseInt(import.meta.env.VITE_CHAIN_ID || '********')

      if (Number(network.chainId) !== expectedChainId) {
        warnings.push(`当前网络 (${network.chainId}) 与预期网络 (${expectedChainId}) 不匹配`)
      }

      // 检查账户
      const accounts = await provider.listAccounts()
      if (accounts.length === 0) {
        warnings.push('未检测到钱包账户')
      }

      return {
        isSecure: warnings.length === 0,
        warnings
      }
    } catch (error) {
      return {
        isSecure: false,
        warnings: ['钱包连接验证失败']
      }
    }
  }

  /**
   * 更新安全配置
   */
  updateSecurityConfig(config: Partial<typeof this.securityConfig>) {
    this.securityConfig = { ...this.securityConfig, ...config }
  }

  /**
   * 获取当前安全配置
   */
  getSecurityConfig() {
    return { ...this.securityConfig }
  }
}

export const securityService = SecurityService.getInstance()
