/**
 * 数据持久化服务
 * 负责游戏数据的自动保存、加载和同步
 */

import { storageUtil } from '../utils/storage'
import { backupManager, performAutoBackup } from '../utils/backup'
import { STORAGE_KEYS } from '../config/constants'
import type { Pet } from '../types/pet'
import type { GameSettings } from '../stores/game'

/**
 * 持久化配置
 */
export interface PersistenceConfig {
  autoSave: boolean
  autoSaveInterval: number // 毫秒
  autoBackup: boolean
  autoBackupInterval: number // 毫秒
  encryptSensitiveData: boolean
}

/**
 * 持久化状态
 */
export interface PersistenceState {
  lastSaved: number
  lastBackup: number
  saveCount: number
  errorCount: number
}

/**
 * 数据持久化服务类
 */
export class DataPersistenceService {
  private config: PersistenceConfig = {
    autoSave: true,
    autoSaveInterval: 30000, // 30秒
    autoBackup: true,
    autoBackupInterval: 24 * 60 * 60 * 1000, // 24小时
    encryptSensitiveData: true
  }

  private state: PersistenceState = {
    lastSaved: 0,
    lastBackup: 0,
    saveCount: 0,
    errorCount: 0
  }

  private autoSaveTimer: number | null = null
  private autoBackupTimer: number | null = null
  private isInitialized = false

  /**
   * 初始化持久化服务
   */
  async initialize(walletAddress?: string): Promise<boolean> {
    try {
      // 加载配置
      await this.loadConfig()

      // 启动自动保存
      if (this.config.autoSave) {
        this.startAutoSave()
      }

      // 启动自动备份
      if (this.config.autoBackup) {
        this.startAutoBackup(walletAddress)
      }

      this.isInitialized = true
      console.log('数据持久化服务初始化完成')
      return true
    } catch (error) {
      console.error('数据持久化服务初始化失败:', error)
      return false
    }
  }

  /**
   * 销毁持久化服务
   */
  destroy(): void {
    this.stopAutoSave()
    this.stopAutoBackup()
    this.isInitialized = false
    console.log('数据持久化服务已销毁')
  }

  /**
   * 保存萌宠数据
   */
  async savePetData(pets: Pet[], currentPetId: string | null): Promise<boolean> {
    try {
      const petData = {
        pets,
        currentPetId,
        lastModified: Date.now()
      }

      const success = await storageUtil.setItem(
        STORAGE_KEYS.PET_STATE,
        petData,
        {
          encrypt: this.config.encryptSensitiveData,
          compress: true
        }
      )

      if (success) {
        this.state.lastSaved = Date.now()
        this.state.saveCount++
        console.log('萌宠数据保存成功')
      } else {
        this.state.errorCount++
        console.error('萌宠数据保存失败')
      }

      return success
    } catch (error) {
      this.state.errorCount++
      console.error('保存萌宠数据时发生错误:', error)
      return false
    }
  }

  /**
   * 加载萌宠数据
   */
  async loadPetData(): Promise<{ pets: Pet[], currentPetId: string | null } | null> {
    try {
      const petData = await storageUtil.getItem(
        STORAGE_KEYS.PET_STATE,
        {
          encrypt: this.config.encryptSensitiveData,
          compress: true
        }
      )

      if (petData && petData.pets) {
        console.log('萌宠数据加载成功')
        return {
          pets: petData.pets,
          currentPetId: petData.currentPetId || null
        }
      }

      return null
    } catch (error) {
      console.error('加载萌宠数据时发生错误:', error)
      return null
    }
  }

  /**
   * 保存游戏数据
   */
  async saveGameData(gameData: any): Promise<boolean> {
    try {
      const dataToSave = {
        ...gameData,
        lastModified: Date.now()
      }

      const success = await storageUtil.setItem(
        STORAGE_KEYS.GAME_STATE,
        dataToSave,
        {
          encrypt: this.config.encryptSensitiveData,
          compress: true
        }
      )

      if (success) {
        this.state.lastSaved = Date.now()
        this.state.saveCount++
        console.log('游戏数据保存成功')
      } else {
        this.state.errorCount++
        console.error('游戏数据保存失败')
      }

      return success
    } catch (error) {
      this.state.errorCount++
      console.error('保存游戏数据时发生错误:', error)
      return false
    }
  }

  /**
   * 加载游戏数据
   */
  async loadGameData(): Promise<any | null> {
    try {
      const gameData = await storageUtil.getItem(
        STORAGE_KEYS.GAME_STATE,
        {
          encrypt: this.config.encryptSensitiveData,
          compress: true
        }
      )

      if (gameData) {
        console.log('游戏数据加载成功')
        return gameData
      }

      return null
    } catch (error) {
      console.error('加载游戏数据时发生错误:', error)
      return null
    }
  }

  /**
   * 保存用户设置
   */
  async saveUserSettings(settings: GameSettings): Promise<boolean> {
    try {
      const success = await storageUtil.setItem(
        STORAGE_KEYS.USER_SETTINGS,
        settings
      )

      if (success) {
        console.log('用户设置保存成功')
      } else {
        console.error('用户设置保存失败')
      }

      return success
    } catch (error) {
      console.error('保存用户设置时发生错误:', error)
      return false
    }
  }

  /**
   * 加载用户设置
   */
  async loadUserSettings(): Promise<GameSettings | null> {
    try {
      const settings = await storageUtil.getItem<GameSettings>(
        STORAGE_KEYS.USER_SETTINGS
      )

      if (settings) {
        console.log('用户设置加载成功')
        return settings
      }

      return null
    } catch (error) {
      console.error('加载用户设置时发生错误:', error)
      return null
    }
  }

  /**
   * 清除所有数据
   */
  async clearAllData(): Promise<boolean> {
    try {
      const success = await storageUtil.clear()

      if (success) {
        this.state = {
          lastSaved: 0,
          lastBackup: 0,
          saveCount: 0,
          errorCount: 0
        }
        console.log('所有数据已清除')
      }

      return success
    } catch (error) {
      console.error('清除数据时发生错误:', error)
      return false
    }
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats() {
    return {
      ...storageUtil.getStorageStats(),
      persistence: { ...this.state },
      config: { ...this.config }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PersistenceConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // 重新启动定时器
    if (this.isInitialized) {
      this.stopAutoSave()
      this.stopAutoBackup()

      if (this.config.autoSave) {
        this.startAutoSave()
      }

      if (this.config.autoBackup) {
        this.startAutoBackup()
      }
    }

    // 保存配置
    this.saveConfig()
  }

  /**
   * 手动触发备份
   */
  async triggerBackup(walletAddress?: string): Promise<boolean> {
    try {
      const success = await performAutoBackup(walletAddress)

      if (success) {
        this.state.lastBackup = Date.now()
        console.log('手动备份完成')
      }

      return success
    } catch (error) {
      console.error('手动备份失败:', error)
      return false
    }
  }

  /**
   * 启动自动保存
   */
  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }

    this.autoSaveTimer = window.setInterval(() => {
      // 这里可以触发全局保存事件
      // 或者直接调用各个store的保存方法
      console.log('执行自动保存...')
    }, this.config.autoSaveInterval)

    console.log(`自动保存已启动，间隔: ${this.config.autoSaveInterval}ms`)
  }

  /**
   * 停止自动保存
   */
  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
      console.log('自动保存已停止')
    }
  }

  /**
   * 启动自动备份
   */
  private startAutoBackup(walletAddress?: string): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer)
    }

    this.autoBackupTimer = window.setInterval(async () => {
      console.log('执行自动备份...')
      const success = await performAutoBackup(walletAddress)

      if (success) {
        this.state.lastBackup = Date.now()
      }
    }, this.config.autoBackupInterval)

    console.log(`自动备份已启动，间隔: ${this.config.autoBackupInterval}ms`)
  }

  /**
   * 停止自动备份
   */
  private stopAutoBackup(): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer)
      this.autoBackupTimer = null
      console.log('自动备份已停止')
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    try {
      await storageUtil.setItem('persistence_config', this.config)
    } catch (error) {
      console.error('保存持久化配置失败:', error)
    }
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const savedConfig = await storageUtil.getItem<PersistenceConfig>('persistence_config')

      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig }
        console.log('持久化配置加载成功')
      }
    } catch (error) {
      console.error('加载持久化配置失败:', error)
    }
  }
}

// 创建全局持久化服务实例
export const dataPersistenceService = new DataPersistenceService()

/**
 * 便捷函数
 */

/**
 * 初始化数据持久化
 */
export async function initializeDataPersistence(walletAddress?: string): Promise<boolean> {
  return await dataPersistenceService.initialize(walletAddress)
}

/**
 * 保存所有游戏数据
 */
export async function saveAllGameData(
  pets: Pet[],
  currentPetId: string | null,
  gameData: any,
  settings: GameSettings
): Promise<boolean> {
  const results = await Promise.all([
    dataPersistenceService.savePetData(pets, currentPetId),
    dataPersistenceService.saveGameData(gameData),
    dataPersistenceService.saveUserSettings(settings)
  ])

  return results.every(result => result)
}

/**
 * 加载所有游戏数据
 */
export async function loadAllGameData(): Promise<{
  petData: { pets: Pet[], currentPetId: string | null } | null
  gameData: any | null
  settings: GameSettings | null
}> {
  const [petData, gameData, settings] = await Promise.all([
    dataPersistenceService.loadPetData(),
    dataPersistenceService.loadGameData(),
    dataPersistenceService.loadUserSettings()
  ])

  return {
    petData,
    gameData,
    settings
  }
}