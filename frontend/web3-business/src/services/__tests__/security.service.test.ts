import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { SecurityService } from '../security.service'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock
})

// Mock vant
vi.mock('vant', () => ({
  showConfirmDialog: vi.fn(),
  showNotify: vi.fn()
}))

// Mock ethers
vi.mock('ethers', () => ({
  ethers: {
    parseEther: vi.fn(),
    formatEther: vi.fn(),
    isAddress: vi.fn()
  }
}))

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_CHAIN_ID: '********'
  }
})

describe('SecurityService', () => {
  let securityService: SecurityService
  let mockProvider: any

  beforeEach(() => {
    securityService = SecurityService.getInstance()
    mockProvider = {
      getCode: vi.fn(),
      getNetwork: vi.fn().mockResolvedValue({ chainId: ********n }),
      listAccounts: vi.fn().mockResolvedValue(['******************************************'])
    }
    securityService.setProvider(mockProvider)

    // Clear localStorage mock
    localStorageMock.clear()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('security configuration', () => {
    it('should update security configuration', () => {
      const newConfig = {
        maxTransactionAmount: '50',
        requireConfirmationAbove: '5'
      }

      securityService.updateSecurityConfig(newConfig)
      const config = securityService.getSecurityConfig()

      expect(config.maxTransactionAmount).toBe('50')
      expect(config.requireConfirmationAbove).toBe('5')
    })

    it('should return current security configuration', () => {
      const config = securityService.getSecurityConfig()

      expect(config).toHaveProperty('maxTransactionAmount')
      expect(config).toHaveProperty('requireConfirmationAbove')
      expect(config).toHaveProperty('suspiciousGasLimit')
      expect(config).toHaveProperty('maxDataLength')
    })
  })

  describe('transaction logging', () => {
    it('should clear transaction logs', () => {
      securityService.clearTransactionLogs()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('transaction_logs')
    })

    it('should get empty transaction logs when none exist', () => {
      localStorageMock.getItem.mockReturnValue(null)
      const logs = securityService.getTransactionLogs()
      expect(logs).toEqual([])
    })

    it('should get transaction logs from localStorage', () => {
      const mockLogs = [
        { hash: '0x123', timestamp: new Date().toISOString() }
      ]
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockLogs))

      const logs = securityService.getTransactionLogs()
      expect(logs).toEqual(mockLogs)
    })
  })

  describe('validateWalletConnection', () => {
    it('should validate wallet connection structure', async () => {
      const result = await securityService.validateWalletConnection(mockProvider)

      expect(result).toHaveProperty('isSecure')
      expect(result).toHaveProperty('warnings')
      expect(Array.isArray(result.warnings)).toBe(true)
    })

    it('should detect network mismatch', async () => {
      mockProvider.getNetwork.mockResolvedValue({ chainId: 1n }) // Mainnet instead of expected testnet

      const result = await securityService.validateWalletConnection(mockProvider)

      expect(result.isSecure).toBe(false)
      expect(result.warnings.length).toBeGreaterThan(0)
      expect(result.warnings[0]).toContain('网络')
    })

    it('should detect missing accounts', async () => {
      mockProvider.listAccounts.mockResolvedValue([])

      const result = await securityService.validateWalletConnection(mockProvider)

      expect(result.isSecure).toBe(false)
      expect(result.warnings).toContain('未检测到钱包账户')
    })

    it('should handle provider errors', async () => {
      mockProvider.getNetwork.mockRejectedValue(new Error('Network error'))

      const result = await securityService.validateWalletConnection(mockProvider)

      expect(result.isSecure).toBe(false)
      expect(result.warnings).toContain('钱包连接验证失败')
    })
  })
})
