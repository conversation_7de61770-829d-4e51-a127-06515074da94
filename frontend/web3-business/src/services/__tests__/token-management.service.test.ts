import { describe, it, expect, beforeEach, vi } from 'vitest'
import { TokenManagementService } from '../token-management.service'
import { contractService } from '../contract.service'
import type { ContractInfo } from '../../types'

// Mock the contract service
vi.mock('../contract.service', () => ({
  contractService: {
    getContractInfo: vi.fn(),
    getBalance: vi.fn(),
    transfer: vi.fn(),
    safeTransfer: vi.fn(),
    batchTransfer: vi.fn(),
    getTaxInfo: vi.fn(),
    previewTransfer: vi.fn(),
    calculateTax: vi.fn(),
    onEvent: vi.fn()
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

// Mock global objects
Object.defineProperty(globalThis, 'localStorage', {
  value: localStorageMock,
  writable: true
})

describe('TokenManagementService', () => {
  let service: TokenManagementService
  const mockAddress = '0x1234567890123456789012345678901234567890'
  const mockContractInfo: ContractInfo = {
    address: '0xabcd',
    abi: [],
    name: 'TestToken',
    symbol: 'TEST',
    decimals: 18,
    totalSupply: '1000000000000000000000000',
    version: '1.0.0'
  }

  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    service = new TokenManagementService()
  })

  describe('getBalance', () => {
    it('should get token balance successfully', async () => {
      const mockBalance = '1000000000000000000' // 1 token

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)

      const result = await service.getBalance(mockAddress)

      expect(result).toEqual({
        balance: mockBalance,
        balanceFormatted: '1.000000',
        symbol: 'TEST',
        decimals: 18,
        lastUpdated: expect.any(Number)
      })
    })

    it('should use cached balance when available', async () => {
      const mockBalance = '1000000000000000000'

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)

      // First call
      await service.getBalance(mockAddress)

      // Second call should use cache
      const result = await service.getBalance(mockAddress)

      expect(contractService.getBalance).toHaveBeenCalledTimes(1)
      expect(result.balance).toBe(mockBalance)
    })

    it('should refresh balance when forceRefresh is true', async () => {
      const mockBalance = '1000000000000000000'

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)

      // First call
      await service.getBalance(mockAddress)

      // Second call with forceRefresh
      await service.getBalance(mockAddress, true)

      expect(contractService.getBalance).toHaveBeenCalledTimes(2)
    })
  })

  describe('transfer', () => {
    it('should transfer tokens successfully', async () => {
      const transferRequest = {
        to: '0x9876543210987654321098765432109876543210',
        amount: '1.0'
      }
      const mockBalance = '2000000000000000000' // 2 tokens
      const mockTxResponse = {
        hash: '0xabcd1234',
        wait: vi.fn().mockResolvedValue({
          status: 1,
          blockNumber: 12345,
          gasUsed: BigInt('21000'),
          gasPrice: BigInt('20000000000')
        })
      }

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)
      vi.mocked(contractService.transfer).mockResolvedValue(mockTxResponse)

      const result = await service.transfer(transferRequest, mockAddress)

      expect(result.status).toBe('confirmed')
      expect(result.hash).toBe('0xabcd1234')
      expect(result.from).toBe(mockAddress)
      expect(result.to).toBe(transferRequest.to)
    })

    it('should throw error for invalid address', async () => {
      const transferRequest = {
        to: 'invalid-address',
        amount: '1.0'
      }

      await expect(service.transfer(transferRequest, mockAddress))
        .rejects.toThrow('无效的接收地址')
    })

    it('should throw error for insufficient balance', async () => {
      const transferRequest = {
        to: '0x9876543210987654321098765432109876543210',
        amount: '10.0'
      }
      const mockBalance = '1000000000000000000' // 1 token

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)

      await expect(service.transfer(transferRequest, mockAddress))
        .rejects.toThrow('余额不足')
    })

    it('should throw error for self transfer', async () => {
      const transferRequest = {
        to: mockAddress,
        amount: '1.0'
      }

      await expect(service.transfer(transferRequest, mockAddress))
        .rejects.toThrow('不能转账给自己')
    })
  })

  describe('batchTransfer', () => {
    it('should perform batch transfer successfully', async () => {
      const recipients = [
        '0x1111111111111111111111111111111111111111',
        '0x2222222222222222222222222222222222222222'
      ]
      const amounts = ['1.0', '2.0']
      const mockBalance = '5000000000000000000' // 5 tokens
      const mockTxResponse = {
        hash: '0xbatch123',
        wait: vi.fn().mockResolvedValue({
          status: 1,
          blockNumber: 12346,
          gasUsed: BigInt('42000'),
          gasPrice: BigInt('20000000000')
        })
      }

      vi.mocked(contractService.getContractInfo).mockResolvedValue(mockContractInfo)
      vi.mocked(contractService.getBalance).mockResolvedValue(mockBalance)
      vi.mocked(contractService.batchTransfer).mockResolvedValue(mockTxResponse)

      const result = await service.batchTransfer(recipients, amounts, mockAddress)

      expect(result.status).toBe('confirmed')
      expect(result.hash).toBe('0xbatch123')
      expect(result.to).toBe('批量转账 (2个地址)')
    })

    it('should throw error for mismatched arrays', async () => {
      const recipients = ['0x1111111111111111111111111111111111111111']
      const amounts = ['1.0', '2.0']

      await expect(service.batchTransfer(recipients, amounts, mockAddress))
        .rejects.toThrow('接收地址和金额数组长度不匹配')
    })
  })

  describe('getTaxInfo', () => {
    it('should get tax info successfully', async () => {
      const mockTaxInfo = {
        currentTaxRate: BigInt('500'), // 5%
        currentTaxCollector: '0x1234567890123456789012345678901234567890',
        totalCollected: BigInt('1000000000000000000'),
        taxEnabled: true
      }

      vi.mocked(contractService.getTaxInfo).mockResolvedValue(mockTaxInfo)

      const result = await service.getTaxInfo()

      expect(result).toEqual(mockTaxInfo)
    })
  })

  describe('calculateTax', () => {
    it('should calculate tax correctly', async () => {
      const amount = '1000000000000000000' // 1 token
      const mockTaxResult = {
        netAmount: '950000000000000000', // 0.95 tokens
        taxAmount: '50000000000000000'   // 0.05 tokens
      }

      vi.mocked(contractService.calculateTax).mockResolvedValue(mockTaxResult)

      const result = await service.calculateTax(amount)

      expect(result).toEqual(mockTaxResult)
    })
  })

  describe('getTransactionHistory', () => {
    it('should return empty history for new service', () => {
      const history = service.getTransactionHistory()
      expect(history).toEqual([])
    })

    it('should filter history by address', () => {
      // This would require setting up some mock transaction history
      // For now, just test the basic functionality
      const history = service.getTransactionHistory(mockAddress)
      expect(Array.isArray(history)).toBe(true)
    })
  })

  describe('getTokenStats', () => {
    it('should return default stats for new address', () => {
      const stats = service.getTokenStats(mockAddress)

      expect(stats).toEqual({
        totalTransactions: 0,
        totalSent: '0',
        totalReceived: '0',
        totalTaxPaid: '0',
        averageTransactionAmount: '0',
        lastTransactionTime: 0
      })
    })
  })

  describe('event handling', () => {
    it('should add and remove event listeners', () => {
      const callback = vi.fn()

      service.addEventListener('balanceUpdated', callback)
      service.removeEventListener('balanceUpdated', callback)

      // Should not throw
      expect(true).toBe(true)
    })

    it('should cleanup resources', () => {
      service.cleanup()

      // Should not throw
      expect(true).toBe(true)
    })
  })
})
