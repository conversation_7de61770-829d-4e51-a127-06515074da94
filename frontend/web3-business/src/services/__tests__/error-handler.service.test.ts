import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { errorHandler, ErrorType } from '../error-handler.service'

// Mock Vant components
const mockShowToast = vi.fn()
const mockShowDialog = vi.fn()
const mockShowNotify = vi.fn()

vi.mock('vant', () => ({
  showToast: mockShowToast,
  showDialog: mockShowDialog,
  showNotify: mockShowNotify
}))

describe('ErrorHandlerService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockShowToast.mockClear()
    mockShowDialog.mockClear()
    mockShowNotify.mockClear()
    errorHandler.clearErrorHistory()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('parseError', () => {
    it('should identify network errors correctly', () => {
      const networkError = { code: 'NETWORK_ERROR', message: 'Network connection failed' }
      const appError = errorHandler.handleError(networkError)

      expect(appError.type).toBe(ErrorType.NETWORK)
      expect(appError.message).toContain('网络连接异常')
    })

    it('should identify contract errors correctly', () => {
      const contractError = {
        code: 'CALL_EXCEPTION',
        reason: 'TradingNotEnabled',
        message: 'execution reverted: TradingNotEnabled'
      }
      const appError = errorHandler.handleError(contractError)

      expect(appError.type).toBe(ErrorType.CONTRACT)
      expect(appError.message).toContain('代币交易尚未开启')
    })

    it('should identify wallet errors correctly', () => {
      const walletError = { code: 4001, message: 'User rejected the request' }
      const appError = errorHandler.handleError(walletError)

      expect(appError.type).toBe(ErrorType.WALLET)
      expect(appError.message).toContain('用户取消了操作')
    })

    it('should identify transaction errors correctly', () => {
      const txError = { code: 'INSUFFICIENT_FUNDS', message: 'insufficient funds' }
      const appError = errorHandler.handleError(txError)

      expect(appError.type).toBe(ErrorType.TRANSACTION)
      expect(appError.message).toContain('余额不足')
    })

    it('should handle unknown errors', () => {
      const unknownError = { message: 'Something went wrong' }
      const appError = errorHandler.handleError(unknownError)

      expect(appError.type).toBe(ErrorType.UNKNOWN)
      expect(appError.message).toBe('Something went wrong')
    })
  })

  describe('executeWithRetry', () => {
    it('should retry failed operations', async () => {
      let attempts = 0
      const operation = vi.fn().mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          throw new Error('Network error')
        }
        return Promise.resolve('success')
      })

      const result = await errorHandler.executeWithRetry(
        operation,
        { maxAttempts: 3, delay: 10 }
      )

      expect(result).toBe('success')
      expect(operation).toHaveBeenCalledTimes(3)
    })

    it('should not retry user cancelled operations', async () => {
      const operation = vi.fn().mockRejectedValue({
        code: 4001,
        message: 'User rejected'
      })

      await expect(
        errorHandler.executeWithRetry(operation, { maxAttempts: 3, delay: 10 })
      ).rejects.toThrow()

      expect(operation).toHaveBeenCalledTimes(1)
    })

    it('should use exponential backoff when configured', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Network error'))
      const startTime = Date.now()

      try {
        await errorHandler.executeWithRetry(
          operation,
          { maxAttempts: 3, delay: 100, backoff: true }
        )
      } catch (error) {
        // Expected to fail
      }

      const duration = Date.now() - startTime
      // Should take at least 100 + 200 = 300ms with backoff
      expect(duration).toBeGreaterThan(250)
    })
  })

  describe('error history', () => {
    it('should maintain error history', () => {
      const error1 = new Error('First error')
      const error2 = new Error('Second error')

      errorHandler.handleError(error1)
      errorHandler.handleError(error2)

      const history = errorHandler.getErrorHistory()
      expect(history).toHaveLength(2)
      expect(history[0].message).toBe('Second error') // Most recent first
      expect(history[1].message).toBe('First error')
    })

    it('should limit error history size', () => {
      // Add more than max history size errors
      for (let i = 0; i < 150; i++) {
        errorHandler.handleError(new Error(`Error ${i}`))
      }

      const history = errorHandler.getErrorHistory()
      expect(history.length).toBeLessThanOrEqual(100)
    })

    it('should provide error statistics', () => {
      errorHandler.handleError({ code: 'NETWORK_ERROR' })
      errorHandler.handleError({ code: 'CALL_EXCEPTION' })
      errorHandler.handleError({ code: 4001 })

      const stats = errorHandler.getErrorStats()
      expect(stats[ErrorType.NETWORK]).toBe(1)
      expect(stats[ErrorType.CONTRACT]).toBe(1)
      expect(stats[ErrorType.WALLET]).toBe(1)
    })
  })

  describe('message display', () => {
    it('should show success messages', () => {
      errorHandler.showSuccess('Operation completed')

      expect(mockShowToast).toHaveBeenCalledWith({
        message: 'Operation completed',
        type: 'success',
        duration: 3000,
        position: 'top'
      })
    })

    it('should show warning messages', () => {
      errorHandler.showWarning('Be careful')

      expect(mockShowNotify).toHaveBeenCalledWith({
        message: 'Be careful',
        type: 'warning',
        duration: 4000
      })
    })

    it('should show loading messages', () => {
      const mockToast = { close: vi.fn() }
      mockShowToast.mockReturnValue(mockToast)

      const closeLoading = errorHandler.showLoading('Processing...')

      expect(mockShowToast).toHaveBeenCalledWith({
        message: 'Processing...',
        type: 'loading',
        duration: 0,
        forbidClick: true
      })

      closeLoading()
      expect(mockToast.close).toHaveBeenCalled()
    })
  })

  describe('contract error messages', () => {
    it('should provide user-friendly contract error messages', () => {
      const testCases = [
        {
          error: { reason: 'TradingNotEnabled' },
          expected: '代币交易尚未开启'
        },
        {
          error: { reason: 'InsufficientBalance' },
          expected: '余额不足'
        },
        {
          error: { reason: 'InvalidAmount' },
          expected: '无效的金额'
        },
        {
          error: { reason: 'Unauthorized' },
          expected: '没有权限执行此操作'
        },
        {
          error: { reason: 'Paused' },
          expected: '合约已暂停，请稍后重试'
        }
      ]

      testCases.forEach(({ error, expected }) => {
        const appError = errorHandler.handleError(error)
        expect(appError.message).toBe(expected)
      })
    })

    it('should handle gas estimation errors', () => {
      const gasError = { code: 'UNPREDICTABLE_GAS_LIMIT' }
      const appError = errorHandler.handleError(gasError)

      expect(appError.type).toBe(ErrorType.CONTRACT)
      expect(appError.message).toBe('交易可能失败，请检查参数')
    })
  })

  describe('network error messages', () => {
    it('should provide appropriate network error messages', () => {
      const testCases = [
        {
          error: { code: 'NETWORK_ERROR' },
          expected: '网络连接异常，请检查网络设置'
        },
        {
          error: { code: 'TIMEOUT' },
          expected: '网络请求超时，请稍后重试'
        }
      ]

      testCases.forEach(({ error, expected }) => {
        const appError = errorHandler.handleError(error)
        expect(appError.message).toBe(expected)
      })
    })
  })

  describe('transaction error messages', () => {
    it('should provide clear transaction error messages', () => {
      const testCases = [
        {
          error: { code: 'INSUFFICIENT_FUNDS' },
          expected: '余额不足，请检查您的钱包余额'
        },
        {
          error: { code: 'NONCE_EXPIRED' },
          expected: '交易已过期，请重新发起'
        },
        {
          error: { code: 'REPLACEMENT_UNDERPRICED' },
          expected: 'Gas费用过低，请提高Gas费用'
        }
      ]

      testCases.forEach(({ error, expected }) => {
        const appError = errorHandler.handleError(error)
        expect(appError.message).toBe(expected)
      })
    })
  })
})
