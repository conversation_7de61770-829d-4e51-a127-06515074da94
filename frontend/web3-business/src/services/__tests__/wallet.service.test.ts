import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { WalletService } from '../wallet.service'
import { contractService } from '../contract.service'

// Mock the contract service
vi.mock('../contract.service', () => ({
  contractService: {
    initialize: vi.fn(),
    cleanup: vi.fn(),
    updateSigner: vi.fn(),
    getBalance: vi.fn().mockResolvedValue('100')
  }
}))

// Mock window.ethereum
const mockEthereum = {
  isMetaMask: true,
  request: vi.fn(),
  on: vi.fn(),
  removeListener: vi.fn(),
  selectedAddress: '0x123',
  chainId: '0x1',
  networkVersion: '1'
}

// Mock BrowserProvider and Signer
const mockSigner = {
  getAddress: vi.fn().mockResolvedValue('0x123')
}

const mockProvider = {
  getSigner: vi.fn().mockResolvedValue(mockSigner),
  getNetwork: vi.fn().mockResolvedValue({ chainId: 1n }),
  getBalance: vi.fn().mockResolvedValue(100n)
}

// Mock ethers
vi.mock('ethers', () => ({
  ethers: {
    BrowserProvider: vi.fn().mockImplementation(() => mockProvider)
  }
}))

describe('WalletService', () => {
  let walletService: WalletService

  beforeEach(() => {
    walletService = new WalletService()

    // Mock window.ethereum
    global.window = {
      ...global.window,
      ethereum: mockEthereum,
      location: { reload: vi.fn() } as any,
      dispatchEvent: vi.fn()
    }

    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('should check if MetaMask is installed', () => {
    expect(walletService.isMetaMaskInstalled()).toBe(true)

    // Test when ethereum is not available
    delete (global.window as any).ethereum
    expect(walletService.isMetaMaskInstalled()).toBe(false)
  })

  it('should connect to MetaMask', async () => {
    // Restore ethereum
    (global.window as any).ethereum = mockEthereum

    mockEthereum.request.mockResolvedValueOnce(['0x123'])

    const result = await walletService.connectMetaMask()

    expect(mockEthereum.request).toHaveBeenCalledWith({ method: 'eth_requestAccounts' })
    expect(contractService.initialize).toHaveBeenCalled()
    expect(mockEthereum.on).toHaveBeenCalledTimes(2) // accountsChanged and chainChanged

    expect(result).toEqual({
      isConnected: true,
      address: '0x123',
      balance: '100',
      chainId: 1,
      provider: mockProvider
    })
  })

  it('should disconnect wallet', () => {
    walletService.disconnect()
    expect(contractService.cleanup).toHaveBeenCalled()
  })

  it('should switch network', async () => {
    mockEthereum.request.mockResolvedValueOnce(null)

    await walletService.switchNetwork(1)

    expect(mockEthereum.request).toHaveBeenCalledWith({
      method: 'wallet_switchEthereumChain',
      params: [{ chainId: '0x1' }]
    })
  })

  it('should handle network switch error and add network', async () => {
    const error = { code: 4902 }
    mockEthereum.request.mockRejectedValueOnce(error)
    mockEthereum.request.mockResolvedValueOnce(null)

    await walletService.switchNetwork(1)

    expect(mockEthereum.request).toHaveBeenCalledTimes(2)
    expect(mockEthereum.request).toHaveBeenNthCalledWith(2, {
      method: 'wallet_addEthereumChain',
      params: [expect.objectContaining({ chainId: '0x1' })]
    })
  })
})