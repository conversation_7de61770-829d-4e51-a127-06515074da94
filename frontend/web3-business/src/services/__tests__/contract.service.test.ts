import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ContractService } from '../contract.service'
import { ethers } from 'ethers'

// 模拟合约
const mockContract = {
  target: '******************************************',
  name: vi.fn().mockResolvedValue('Test Token'),
  symbol: vi.fn().mockResolvedValue('TEST'),
  decimals: vi.fn().mockResolvedValue(18),
  totalSupply: vi.fn().mockResolvedValue(ethers.parseEther('1000000')),
  version: vi.fn().mockResolvedValue('1.0.0'),
  balanceOf: vi.fn().mockResolvedValue(ethers.parseEther('100')),
  transfer: vi.fn().mockResolvedValue({
    hash: '0xabcdef',
    wait: vi.fn().mockResolvedValue({ status: 1 })
  }),
  safeTransfer: vi.fn().mockResolvedValue({
    hash: '0xabcdef',
    wait: vi.fn().mockResolvedValue({ status: 1 })
  }),
  mint: vi.fn().mockResolvedValue({
    hash: '0xabcdef',
    wait: vi.fn().mockResolvedValue({ status: 1 })
  }),
  on: vi.fn(),
  removeAllListeners: vi.fn()
}

// 模拟V2合约
const mockV2Contract = {
  ...mockContract,
  getTaxInfo: vi.fn().mockResolvedValue({
    currentTaxRate: ethers.parseEther('0.05'),
    currentTaxCollector: '******************************************',
    totalCollected: ethers.parseEther('1000'),
    taxEnabled: true
  }),
  calculateTax: vi.fn().mockResolvedValue({
    netAmount: ethers.parseEther('95'),
    taxAmount: ethers.parseEther('5')
  }),
  calculateTaxForTransfer: vi.fn().mockResolvedValue({
    netAmount: ethers.parseEther('95'),
    taxAmount: ethers.parseEther('5')
  })
}

// 模拟ethers
vi.mock('ethers', () => {
  return {
    ethers: {
      Contract: vi.fn().mockImplementation(() => mockContract),
      parseEther: vi.fn().mockImplementation((value) => BigInt(value) * BigInt(10 ** 18))
    }
  }
})

describe('ContractService', () => {
  let contractService: ContractService
  let mockProvider: any
  let mockSigner: any

  beforeEach(() => {
    mockProvider = {
      getNetwork: vi.fn().mockResolvedValue({ chainId: 31337 })
    }
    mockSigner = {
      getAddress: vi.fn().mockResolvedValue('******************************************')
    }
    contractService = new ContractService()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize contracts', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    expect(ethers.Contract).toHaveBeenCalledTimes(2)
  })

  it('should get contract info', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    const info = await contractService.getContractInfo()
    expect(info.name).toBe('Test Token')
    expect(info.symbol).toBe('TEST')
    expect(info.decimals).toBe(18)
    expect(info.totalSupply).toBe(ethers.parseEther('1000000').toString())
    expect(info.version).toBe('1.0.0')
  })

  it('should get balance', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    const balance = await contractService.getBalance('******************************************')
    expect(balance).toBe(ethers.parseEther('100').toString())
  })

  it('should transfer tokens', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    const tx = await contractService.transfer('******************************************', '100')
    expect(tx.hash).toBe('0xabcdef')
    const receipt = await tx.wait()
    expect(receipt.status).toBe(1)
  })

  it('should mint tokens', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    const tx = await contractService.mint('******************************************', '100')
    expect(tx.hash).toBe('0xabcdef')
    const receipt = await tx.wait()
    expect(receipt.status).toBe(1)
  })

  it('should get tax info', async () => {
    // 替换为V2合约
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockContract)
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockV2Contract)

    await contractService.initialize(mockProvider as any, mockSigner as any)
    const taxInfo = await contractService.getTaxInfo()
    expect(taxInfo.currentTaxRate).toBe(ethers.parseEther('0.05'))
    expect(taxInfo.currentTaxCollector).toBe('******************************************')
    expect(taxInfo.totalCollected).toBe(ethers.parseEther('1000'))
    expect(taxInfo.taxEnabled).toBe(true)
  })

  it('should calculate tax', async () => {
    // 替换为V2合约
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockContract)
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockV2Contract)

    await contractService.initialize(mockProvider as any, mockSigner as any)
    const result = await contractService.calculateTax('100')
    expect(result.netAmount).toBe(ethers.parseEther('95').toString())
    expect(result.taxAmount).toBe(ethers.parseEther('5').toString())
  })

  it('should preview transfer', async () => {
    // 替换为V2合约
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockContract)
    vi.mocked(ethers.Contract).mockImplementationOnce(() => mockV2Contract)

    await contractService.initialize(mockProvider as any, mockSigner as any)
    const result = await contractService.previewTransfer(
      '******************************************',
      '******************************************',
      '100'
    )
    expect(result.netAmount).toBe(ethers.parseEther('95'))
    expect(result.taxAmount).toBe(ethers.parseEther('5'))
    expect(result.willApplyTax).toBe(true)
  })

  it('should cleanup resources', async () => {
    await contractService.initialize(mockProvider as any, mockSigner as any)
    contractService.cleanup()
    expect(mockContract.removeAllListeners).toHaveBeenCalled()
  })
})
