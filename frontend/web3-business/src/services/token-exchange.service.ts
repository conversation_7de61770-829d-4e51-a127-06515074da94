import { ethers } from 'ethers'
import { contractService } from './contract.service'
import { PetStatsCalculator } from '../types/pet'
import type { Pet } from '../types/typesWithoutCircular'


export interface ExchangeCalculation {
  petId: string
  petName: string
  petLevel: number
  petRarity: string
  tokenAmount: string
  tokenAmountFormatted: string
  baseValue: number
  rarityMultiplier: number
  healthBonus: number
  happinessBonus: number
  equipmentBonus: number
  traitBonus: number
  skillBonus: number
  statsBonus: number
  ageBonus: number
  achievementBonus: number
  totalMultiplier: number
}

export interface ExchangeTransaction {
  id: string
  petId: string
  petName: string
  petLevel: number
  petRarity: string
  tokenAmount: string
  transactionHash: string
  timestamp: number
  status: 'pending' | 'confirmed' | 'failed'
  blockNumber?: number
  gasUsed?: string
  gasPrice?: string
}

export interface ExchangeHistory {
  transactions: ExchangeTransaction[]
  totalExchanged: string
  totalPetsExchanged: number
  lastExchangeTime: number
}

export class TokenExchangeService {
  private calculator = new PetStatsCalculator()
  private exchangeHistory: ExchangeTransaction[] = []
  private readonly STORAGE_KEY = 'pet_token_exchange_history'
  private readonly MIN_EXCHANGE_LEVEL = 5
  private readonly MIN_HEALTH_PERCENTAGE = 0.5
  private readonly MIN_HAPPINESS_PERCENTAGE = 0.3
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.loadExchangeHistory()
    this.eventListeners.set('success', [])
    this.eventListeners.set('failed', [])
    this.eventListeners.set('progress', [])
  }

  /**
   * 计算萌宠兑换价值 - 改进的算法
   */
  calculateExchangeValue(pet: Pet): ExchangeCalculation {
    // 基础价值计算 - 基于等级的指数增长
    const baseValue = this.getBaseTokenValue(pet.level)

    // 稀有度加成 - 不同稀有度有不同的倍数
    const rarityMultiplier = this.getRarityMultiplier(pet.rarity)

    // 状态加成 - 健康度和快乐度影响最终价值
    const healthBonus = Math.max(0.3, pet.health / pet.maxHealth) // 最低30%
    const happinessBonus = Math.max(0.2, pet.happiness / pet.maxHappiness) // 最低20%

    // 装备加成 - 考虑装备数量、稀有度和等级
    const equipmentBonus = this.calculateEquipmentBonus(pet.equipment)

    // 特质加成 - 考虑特质数量、类型和等级
    const traitBonus = this.calculateTraitBonus(pet.traits)

    // 技能加成 - 考虑技能数量和等级
    const skillBonus = this.calculateSkillBonus(pet.skills)

    // 属性加成 - 基于萌宠的总属性值
    const statsBonus = this.calculateStatsBonus(pet.stats)

    // 年龄加成 - 基于萌宠的年龄（存在时间）
    const ageBonus = this.calculateAgeBonus(pet.birthTime)

    // 成就加成 - 基于萌宠获得的成就
    const achievementBonus = this.calculateAchievementBonus(pet.achievements)

    // 计算总倍数 - 使用加法而不是乘法避免过度膨胀
    const totalMultiplier = rarityMultiplier *
      (1 + (healthBonus - 1) * 0.5) *
      (1 + (happinessBonus - 1) * 0.3) *
      (1 + equipmentBonus * 0.2) *
      (1 + traitBonus * 0.15) *
      (1 + skillBonus * 0.1) *
      (1 + statsBonus * 0.05) *
      (1 + ageBonus * 0.03) *
      (1 + achievementBonus * 0.02)

    const finalValue = baseValue * totalMultiplier

    // 转换为wei单位，确保精度
    const tokenAmount = ethers.parseEther(Math.max(0.000001, finalValue).toFixed(6)).toString()

    return {
      petId: pet.id,
      petName: pet.name,
      petLevel: pet.level,
      petRarity: pet.rarity,
      tokenAmount,
      tokenAmountFormatted: finalValue.toFixed(6),
      baseValue,
      rarityMultiplier,
      healthBonus,
      happinessBonus,
      equipmentBonus,
      traitBonus,
      skillBonus,
      statsBonus,
      ageBonus,
      achievementBonus,
      totalMultiplier
    }
  }

  /**
   * 检查萌宠是否可以兑换
   */
  canExchangePet(pet: Pet): { canExchange: boolean; reason?: string } {
    if (pet.level < this.MIN_EXCHANGE_LEVEL) {
      return {
        canExchange: false,
        reason: `萌宠等级需要达到${this.MIN_EXCHANGE_LEVEL}级才能兑换`
      }
    }

    const healthPercentage = pet.health / pet.maxHealth
    if (healthPercentage < this.MIN_HEALTH_PERCENTAGE) {
      return {
        canExchange: false,
        reason: `萌宠健康度需要达到${Math.round(this.MIN_HEALTH_PERCENTAGE * 100)}%以上才能兑换`
      }
    }

    const happinessPercentage = pet.happiness / pet.maxHappiness
    if (happinessPercentage < this.MIN_HAPPINESS_PERCENTAGE) {
      return {
        canExchange: false,
        reason: `萌宠快乐度需要达到${Math.round(this.MIN_HAPPINESS_PERCENTAGE * 100)}%以上才能兑换`
      }
    }

    return { canExchange: true }
  }

  /**
   * 执行代币兑换
   */
  async exchangePetForTokens(
    pet: Pet,
    userAddress: string,
    onProgress?: (step: string) => void
  ): Promise<ExchangeTransaction> {
    // 检查兑换条件
    const canExchange = this.canExchangePet(pet)
    if (!canExchange.canExchange) {
      throw new Error(canExchange.reason)
    }

    // 计算兑换价值
    const calculation = this.calculateExchangeValue(pet)

    onProgress?.('正在准备交易...')

    // 验证地址格式
    if (!ethers.isAddress(userAddress)) {
      throw new Error('无效的钱包地址')
    }

    // 验证代币数量
    const tokenAmountBigInt = BigInt(calculation.tokenAmount)
    if (tokenAmountBigInt <= 0n) {
      throw new Error('代币数量必须大于0')
    }

    // 创建交易记录
    const transaction: ExchangeTransaction = {
      id: this.generateTransactionId(),
      petId: pet.id,
      petName: pet.name,
      petLevel: pet.level,
      petRarity: pet.rarity,
      tokenAmount: calculation.tokenAmount,
      transactionHash: '',
      timestamp: Date.now(),
      status: 'pending'
    }

    try {
      onProgress?.('正在检查合约状态...')

      // 检查合约是否已初始化
      try {
        await contractService.getContractInfo()
      } catch (error) {
        throw new Error('合约服务未初始化，请先连接钱包')
      }

      onProgress?.('正在估算Gas费用...')

      // 预估Gas费用（可选）
      try {
        // 这里可以添加Gas估算逻辑
        console.log('估算Gas费用...')
      } catch (error) {
        console.warn('Gas估算失败，继续执行交易:', error)
      }

      onProgress?.('正在调用智能合约铸造代币...')

      // 调用智能合约铸造代币
      const txResponse = await contractService.mint(userAddress, calculation.tokenAmount)

      transaction.transactionHash = txResponse.hash
      onProgress?.('交易已提交到区块链，等待确认...')

      // 等待交易确认
      const receipt = await txResponse.wait()

      if (receipt.status !== 1) {
        throw new Error('交易执行失败')
      }

      transaction.status = 'confirmed'
      transaction.blockNumber = receipt.blockNumber
      transaction.gasUsed = receipt.gasUsed?.toString()
      transaction.gasPrice = receipt.gasPrice?.toString()

      onProgress?.('交易确认成功！代币已转入您的钱包')

      // 保存交易记录
      this.addTransactionToHistory(transaction)

      // 触发成功事件
      this.emitExchangeEvent('success', {
        transaction,
        pet,
        calculation
      })

      return transaction

    } catch (error) {
      transaction.status = 'failed'
      this.addTransactionToHistory(transaction)

      // 触发失败事件
      this.emitExchangeEvent('failed', {
        transaction,
        pet,
        error: error instanceof Error ? error.message : '未知错误'
      })

      // 处理不同类型的错误
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase()

        if (errorMessage.includes('user rejected') || errorMessage.includes('user denied')) {
          throw new Error('用户取消了交易')
        } else if (errorMessage.includes('insufficient funds')) {
          throw new Error('钱包余额不足支付Gas费用')
        } else if (errorMessage.includes('tradingnotenable')) {
          throw new Error('代币交易尚未开启')
        } else if (errorMessage.includes('nonce too high')) {
          throw new Error('交易nonce错误，请重试')
        } else if (errorMessage.includes('replacement transaction underpriced')) {
          throw new Error('交易费用过低，请提高Gas价格')
        } else if (errorMessage.includes('network')) {
          throw new Error('网络连接异常，请检查网络设置')
        } else if (errorMessage.includes('timeout')) {
          throw new Error('交易超时，请稍后重试')
        } else {
          throw new Error(`兑换失败: ${error.message}`)
        }
      }

      throw new Error('兑换失败，请稍后重试')
    }
  }

  /**
   * 获取兑换历史
   */
  getExchangeHistory(): ExchangeHistory {
    try {
      const confirmedTxs = this.exchangeHistory.filter(tx => tx.status === 'confirmed')

      const totalExchanged = confirmedTxs.reduce((total, tx) => {
        try {
          return total + BigInt(tx.tokenAmount)
        } catch (error) {
          console.warn('无效的代币数量:', tx.tokenAmount)
          return total
        }
      }, 0n)

      const totalPetsExchanged = confirmedTxs.length

      const lastExchangeTime = this.exchangeHistory.length > 0
        ? Math.max(...this.exchangeHistory.map(tx => tx.timestamp))
        : 0

      return {
        transactions: [...this.exchangeHistory].reverse(), // 最新的在前
        totalExchanged: totalExchanged.toString(),
        totalPetsExchanged,
        lastExchangeTime
      }
    } catch (error) {
      console.error('获取兑换历史失败:', error)
      return {
        transactions: [],
        totalExchanged: '0',
        totalPetsExchanged: 0,
        lastExchangeTime: 0
      }
    }
  }

  /**
   * 获取特定萌宠的兑换记录
   */
  getPetExchangeHistory(petId: string): ExchangeTransaction[] {
    return this.exchangeHistory
      .filter(tx => tx.petId === petId)
      .reverse()
  }

  /**
   * 清除兑换历史
   */
  clearExchangeHistory(): void {
    this.exchangeHistory = []
    this.saveExchangeHistory()
  }

  /**
   * 获取兑换统计信息
   */
  getExchangeStats() {
    try {
      const confirmedTxs = this.exchangeHistory.filter(tx => tx.status === 'confirmed')
      const failedTxs = this.exchangeHistory.filter(tx => tx.status === 'failed')
      const pendingTxs = this.exchangeHistory.filter(tx => tx.status === 'pending')

      const totalTokens = confirmedTxs.reduce((total, tx) => {
        try {
          return total + BigInt(tx.tokenAmount)
        } catch (error) {
          console.warn('无效的代币数量:', tx.tokenAmount)
          return total
        }
      }, 0n)

      const averageTokensPerPet = confirmedTxs.length > 0
        ? totalTokens / BigInt(confirmedTxs.length)
        : 0n

      // 按稀有度统计
      const rarityStats = confirmedTxs.reduce((stats, tx) => {
        if (!stats[tx.petRarity]) {
          stats[tx.petRarity] = { count: 0, totalTokens: 0n }
        }
        stats[tx.petRarity].count++
        try {
          stats[tx.petRarity].totalTokens += BigInt(tx.tokenAmount)
        } catch (error) {
          console.warn('无效的代币数量:', tx.tokenAmount)
        }
        return stats
      }, {} as Record<string, { count: number; totalTokens: bigint }>)

      // 按时间统计（最近30天）
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)
      const recentTxs = confirmedTxs.filter(tx => tx.timestamp > thirtyDaysAgo)

      return {
        totalTransactions: this.exchangeHistory.length,
        confirmedTransactions: confirmedTxs.length,
        failedTransactions: failedTxs.length,
        pendingTransactions: pendingTxs.length,
        totalTokensEarned: totalTokens.toString(),
        averageTokensPerPet: averageTokensPerPet.toString(),
        rarityStats,
        recentTransactions: recentTxs.length,
        successRate: this.exchangeHistory.length > 0
          ? (confirmedTxs.length / this.exchangeHistory.length * 100).toFixed(2)
          : '0',
        // 新增统计
        mostValuablePet: this.getMostValuablePet(confirmedTxs),
        averageExchangeTime: this.calculateAverageExchangeTime(confirmedTxs),
        topRarity: this.getTopRarity(rarityStats)
      }
    } catch (error) {
      console.error('获取兑换统计失败:', error)
      return {
        totalTransactions: 0,
        totalTokensExchanged: '0',
        averageTokensPerExchange: '0',
        successRate: 0,
        topRarity: 'common'
      }
    }
  }

  /**
   * 预览兑换（不执行实际交易）
   */
  previewExchange(pet: Pet): {
    canExchange: boolean
    reason?: string
    calculation?: ExchangeCalculation
    estimatedGas?: string
  } {
    const canExchange = this.canExchangePet(pet)

    if (!canExchange.canExchange) {
      return canExchange
    }

    const calculation = this.calculateExchangeValue(pet)

    return {
      canExchange: true,
      calculation,
      estimatedGas: '0.001' // 估算的Gas费用，实际应该调用合约估算
    }
  }

  // 私有方法

  private getBaseTokenValue(level: number): number {
    return Math.floor(10 * Math.pow(1.2, level - 1))
  }

  private getRarityMultiplier(rarity: string): number {
    const multipliers: Record<string, number> = {
      'common': 1.0,
      'uncommon': 1.5,
      'rare': 2.0,
      'epic': 3.0,
      'legendary': 5.0,
      'mythical': 10.0
    }
    return multipliers[rarity] || 1.0
  }

  private calculateEquipmentBonus(equipment: any[]): number {
    if (!equipment || equipment.length === 0) return 0

    return equipment.reduce((bonus, item) => {
      const rarityBonus = this.getRarityMultiplier(item.rarity) * 0.1
      const levelBonus = (item.level || 1) * 0.05
      const durabilityBonus = item.durability ? (item.durability / item.maxDurability) * 0.02 : 0
      return bonus + rarityBonus + levelBonus + durabilityBonus
    }, 0)
  }

  private calculateTraitBonus(traits: any[]): number {
    if (!traits || traits.length === 0) return 0

    return traits.reduce((bonus, trait) => {
      const rarityBonus = this.getRarityMultiplier(trait.rarity) * 0.05
      const levelBonus = (trait.level || 1) * 0.02
      const typeBonus = this.getTraitTypeBonus(trait.type)
      return bonus + rarityBonus + levelBonus + typeBonus
    }, 0)
  }

  private calculateSkillBonus(skills: any[]): number {
    if (!skills || skills.length === 0) return 0

    return skills.reduce((bonus, skill) => {
      const levelBonus = (skill.level || 1) * 0.03
      const typeBonus = this.getSkillTypeBonus(skill.type)
      const experienceBonus = skill.experience ? (skill.experience / skill.maxExperience) * 0.01 : 0
      return bonus + levelBonus + typeBonus + experienceBonus
    }, 0)
  }

  private calculateStatsBonus(stats: any): number {
    if (!stats) return 0

    const totalStats = Object.values(stats).reduce((sum: number, stat: any) => sum + (stat || 0), 0)
    const averageStats = totalStats / 6 // 6个属性

    // 属性越高，加成越大，但有上限
    return Math.min(0.5, averageStats / 100)
  }

  private calculateAgeBonus(birthTime: number): number {
    if (!birthTime) return 0

    const ageInDays = (Date.now() - birthTime) / (1000 * 60 * 60 * 24)

    // 年龄加成：前30天线性增长，之后趋于平缓
    if (ageInDays <= 30) {
      return ageInDays / 30 * 0.2 // 最多20%加成
    } else {
      return 0.2 + Math.log(ageInDays - 30) / 100 // 对数增长
    }
  }

  private calculateAchievementBonus(achievements: string[]): number {
    if (!achievements || achievements.length === 0) return 0

    // 每个成就提供固定加成，但有上限
    return Math.min(0.3, achievements.length * 0.05)
  }

  private getTraitTypeBonus(traitType: string): number {
    const bonuses: Record<string, number> = {
      'physical': 0.03,
      'mental': 0.04,
      'magical': 0.05,
      'social': 0.02,
      'special': 0.06
    }
    return bonuses[traitType] || 0.01
  }

  private getSkillTypeBonus(skillType: string): number {
    const bonuses: Record<string, number> = {
      'combat': 0.04,
      'magic': 0.05,
      'crafting': 0.03,
      'social': 0.02,
      'survival': 0.03,
      'special': 0.06
    }
    return bonuses[skillType] || 0.01
  }

  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private addTransactionToHistory(transaction: ExchangeTransaction): void {
    this.exchangeHistory.push(transaction)
    this.saveExchangeHistory()
  }

  private loadExchangeHistory(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.exchangeHistory = JSON.parse(stored)
      }
    } catch (error) {
      console.error('加载兑换历史失败:', error)
      this.exchangeHistory = []
    }
  }

  private saveExchangeHistory(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.exchangeHistory))
    } catch (error) {
      console.error('保存兑换历史失败:', error)
    }
  }

  /**
   * 触发事件
   */
  private emitExchangeEvent(eventName: string, data: any): void {
    const listeners = this.eventListeners.get(eventName) || []
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`事件监听器执行失败 (${eventName}):`, error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    const listeners = this.eventListeners.get(eventName) || []
    listeners.push(callback)
    this.eventListeners.set(eventName, listeners)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) return

    const listeners = this.eventListeners.get(eventName) || []
    const index = listeners.indexOf(callback)
    if (index !== -1) {
      listeners.splice(index, 1)
      this.eventListeners.set(eventName, listeners)
    }
  }

  /**
   * 清理所有事件监听器
   */
  cleanup(): void {
    this.eventListeners.clear()
  }

  /**
   * 获取最有价值的萌宠
   */
  private getMostValuablePet(transactions: ExchangeTransaction[]): string {
    if (transactions.length === 0) return 'N/A'

    const mostValuable = transactions.reduce((max, tx) => {
      const currentAmount = BigInt(tx.tokenAmount)
      const maxAmount = BigInt(max.tokenAmount)
      return currentAmount > maxAmount ? tx : max
    })

    return `${mostValuable.petName} (${formatTokenAmount(mostValuable.tokenAmount)})`
  }

  /**
   * 计算平均兑换时间
   */
  private calculateAverageExchangeTime(transactions: ExchangeTransaction[]): string {
    if (transactions.length < 2) return 'N/A'

    const sortedTxs = transactions.sort((a, b) => a.timestamp - b.timestamp)
    let totalInterval = 0

    for (let i = 1; i < sortedTxs.length; i++) {
      totalInterval += sortedTxs[i].timestamp - sortedTxs[i - 1].timestamp
    }

    const averageMs = totalInterval / (sortedTxs.length - 1)
    const averageHours = averageMs / (1000 * 60 * 60)

    if (averageHours < 1) {
      return `${Math.round(averageMs / (1000 * 60))}分钟`
    } else if (averageHours < 24) {
      return `${averageHours.toFixed(1)}小时`
    } else {
      return `${(averageHours / 24).toFixed(1)}天`
    }
  }

  /**
   * 获取最常见的稀有度
   */
  private getTopRarity(rarityStats: Record<string, { count: number; totalTokens: bigint }>): string {
    if (Object.keys(rarityStats).length === 0) return 'common'

    return Object.entries(rarityStats).reduce((top, [rarity, stats]) => {
      return stats.count > rarityStats[top].count ? rarity : top
    }, Object.keys(rarityStats)[0])
  }
}

// 辅助函数
function formatTokenAmount(amount: string): string {
  try {
    const formatted = ethers.formatEther(amount)
    const num = parseFloat(formatted)
    if (num === 0) return '0'
    if (num < 0.000001) return '<0.000001'
    return num.toFixed(6)
  } catch {
    return '0'
  }
}

// 导出单例实例
export const tokenExchangeService = new TokenExchangeService()
