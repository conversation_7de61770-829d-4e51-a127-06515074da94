/**
 * 性能优化服务
 * 统一管理所有性能优化功能
 */

import { ref, computed, nextTick } from 'vue'
import { memoryManager } from '@/utils/memoryManager'
import { requestCache } from '@/utils/requestCache'
import { imageOptimizer } from '@/utils/imageOptimization'

interface PerformanceConfig {
  enableAutoOptimization: boolean
  memoryThreshold: number
  cacheCleanupInterval: number
  imagePreloadLimit: number
  animationQuality: 'high' | 'medium' | 'low'
  enableDebugMode: boolean
}

interface OptimizationResult {
  success: boolean
  message: string
  metrics: {
    memoryFreed: number
    cacheCleared: number
    resourcesCleaned: number
  }
}

class PerformanceOptimizationService {
  private static instance: PerformanceOptimizationService
  private config: PerformanceConfig
  private isOptimizing = ref(false)
  private lastOptimization = ref(0)
  private optimizationHistory: OptimizationResult[] = []

  // 性能监控定时器
  private monitoringTimer: number | null = null
  private cleanupTimer: number | null = null

  static getInstance(): PerformanceOptimizationService {
    if (!PerformanceOptimizationService.instance) {
      PerformanceOptimizationService.instance = new PerformanceOptimizationService()
    }
    return PerformanceOptimizationService.instance
  }

  constructor() {
    this.config = {
      enableAutoOptimization: true,
      memoryThreshold: 80,
      cacheCleanupInterval: 5 * 60 * 1000, // 5分钟
      imagePreloadLimit: 10,
      animationQuality: 'high',
      enableDebugMode: false
    }

    this.startMonitoring()
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig }

    if (this.config.enableDebugMode) {
      console.log('Performance config updated:', this.config)
    }

    // 重启监控以应用新配置
    this.stopMonitoring()
    this.startMonitoring()
  }

  /**
   * 开始性能监控
   */
  private startMonitoring(): void {
    if (!this.config.enableAutoOptimization) return

    // 内存监控
    this.monitoringTimer = setInterval(() => {
      this.checkPerformance()
    }, 10000) // 每10秒检查一次

    // 定期清理
    this.cleanupTimer = setInterval(() => {
      this.performScheduledCleanup()
    }, this.config.cacheCleanupInterval)

    if (this.config.enableDebugMode) {
      console.log('Performance monitoring started')
    }
  }

  /**
   * 停止性能监控
   */
  private stopMonitoring(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
      this.monitoringTimer = null
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }

    if (this.config.enableDebugMode) {
      console.log('Performance monitoring stopped')
    }
  }

  /**
   * 检查性能状态
   */
  private async checkPerformance(): Promise<void> {
    const memoryInfo = memoryManager.getMemoryInfo()

    if (!memoryInfo) return

    // 内存使用率过高时自动优化
    if (memoryInfo.usage > this.config.memoryThreshold) {
      if (this.config.enableDebugMode) {
        console.warn(`High memory usage detected: ${memoryInfo.usage.toFixed(2)}%`)
      }

      await this.optimizeMemory()
    }

    // 检查缓存状态
    const cacheStats = requestCache.getStats()
    if (cacheStats.errors > cacheStats.totalRequests * 0.1) {
      if (this.config.enableDebugMode) {
        console.warn('High error rate detected, clearing cache')
      }

      requestCache.clearCache()
    }
  }

  /**
   * 执行定期清理
   */
  private async performScheduledCleanup(): Promise<void> {
    if (this.isOptimizing.value) return

    try {
      // 清理过期缓存
      requestCache.cleanupExpired()

      // 清理过期资源
      memoryManager.cleanupExpiredResources()

      // 清理图片缓存（如果内存使用率较高）
      const memoryInfo = memoryManager.getMemoryInfo()
      if (memoryInfo && memoryInfo.usage > 60) {
        imageOptimizer.clearCache()
      }

      if (this.config.enableDebugMode) {
        console.log('Scheduled cleanup completed')
      }
    } catch (error) {
      console.error('Scheduled cleanup failed:', error)
    }
  }

  /**
   * 优化内存使用
   */
  async optimizeMemory(): Promise<OptimizationResult> {
    if (this.isOptimizing.value) {
      return {
        success: false,
        message: 'Optimization already in progress',
        metrics: { memoryFreed: 0, cacheCleared: 0, resourcesCleaned: 0 }
      }
    }

    this.isOptimizing.value = true
    const startTime = performance.now()
    const initialMemory = memoryManager.getMemoryInfo()

    try {
      let resourcesCleaned = 0
      let cacheCleared = 0

      // 1. 清理过期资源
      const resourcesBefore = memoryManager.getResources().length
      memoryManager.cleanupExpiredResources()
      const resourcesAfter = memoryManager.getResources().length
      resourcesCleaned = resourcesBefore - resourcesAfter

      // 2. 清理请求缓存
      const cacheSizeBefore = requestCache.getCacheSize()
      requestCache.clearCache()
      cacheCleared = cacheSizeBefore

      // 3. 清理图片缓存
      imageOptimizer.clearCache()

      // 4. 强制垃圾回收（通过创建临时对象）
      await this.forceGarbageCollection()

      // 等待一帧以获取准确的内存信息
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      const finalMemory = memoryManager.getMemoryInfo()
      const memoryFreed = initialMemory && finalMemory
        ? initialMemory.usedJSHeapSize - finalMemory.usedJSHeapSize
        : 0

      const result: OptimizationResult = {
        success: true,
        message: `Memory optimization completed in ${(performance.now() - startTime).toFixed(2)}ms`,
        metrics: {
          memoryFreed,
          cacheCleared,
          resourcesCleaned
        }
      }

      this.optimizationHistory.push(result)
      this.lastOptimization.value = Date.now()

      if (this.config.enableDebugMode) {
        console.log('Memory optimization result:', result)
      }

      return result
    } catch (error) {
      const result: OptimizationResult = {
        success: false,
        message: `Optimization failed: ${error}`,
        metrics: { memoryFreed: 0, cacheCleared: 0, resourcesCleaned: 0 }
      }

      this.optimizationHistory.push(result)
      return result
    } finally {
      this.isOptimizing.value = false
    }
  }

  /**
   * 强制垃圾回收
   */
  private async forceGarbageCollection(): Promise<void> {
    // 创建大量临时对象来触发垃圾回收
    const temp = []
    for (let i = 0; i < 1000; i++) {
      temp.push(new Array(1000).fill(null))
    }

    // 清空数组
    temp.length = 0

    // 等待微任务队列执行
    await Promise.resolve()
  }

  /**
   * 优化图片加载
   */
  async optimizeImages(imageUrls: string[]): Promise<void> {
    if (imageUrls.length === 0) return

    // 限制预加载数量
    const urlsToPreload = imageUrls.slice(0, this.config.imagePreloadLimit)

    try {
      await imageOptimizer.preloadImages(urlsToPreload, {
        priority: 'high',
        as: 'image'
      })

      if (this.config.enableDebugMode) {
        console.log(`Preloaded ${urlsToPreload.length} images`)
      }
    } catch (error) {
      console.warn('Image preloading failed:', error)
    }
  }

  /**
   * 优化动画性能
   */
  optimizeAnimations(): void {
    // 根据性能模式调整动画
    const memoryInfo = memoryManager.getMemoryInfo()

    if (!memoryInfo) return

    let newQuality: 'high' | 'medium' | 'low' = 'high'

    if (memoryInfo.usage > 80) {
      newQuality = 'low'
    } else if (memoryInfo.usage > 60) {
      newQuality = 'medium'
    }

    if (newQuality !== this.config.animationQuality) {
      this.config.animationQuality = newQuality

      // 触发动画质量变更事件
      window.dispatchEvent(new CustomEvent('animationQualityChange', {
        detail: { quality: newQuality }
      }))

      if (this.config.enableDebugMode) {
        console.log(`Animation quality changed to: ${newQuality}`)
      }
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const memoryStats = memoryManager.getStats()
    const cacheStats = requestCache.getStats()

    return {
      memory: memoryStats,
      cache: cacheStats,
      optimization: {
        isOptimizing: this.isOptimizing.value,
        lastOptimization: this.lastOptimization.value,
        optimizationCount: this.optimizationHistory.length,
        successRate: this.optimizationHistory.length > 0
          ? (this.optimizationHistory.filter(r => r.success).length / this.optimizationHistory.length) * 100
          : 100
      }
    }
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []
    const stats = this.getPerformanceStats()

    // 内存建议
    if (stats.memory.memoryInfo && stats.memory.memoryInfo.usage > 70) {
      suggestions.push('内存使用率较高，建议清理不必要的资源')
    }

    // 缓存建议
    const hitRate = stats.cache.totalRequests > 0
      ? (stats.cache.cacheHits / stats.cache.totalRequests) * 100
      : 0

    if (hitRate < 60) {
      suggestions.push('缓存命中率较低，建议优化缓存策略')
    }

    // 错误率建议
    const errorRate = stats.cache.totalRequests > 0
      ? (stats.cache.errors / stats.cache.totalRequests) * 100
      : 0

    if (errorRate > 5) {
      suggestions.push('请求错误率较高，建议检查网络连接')
    }

    // 资源建议
    if (stats.memory.resourceCount > 50) {
      suggestions.push('活动资源过多，建议及时清理')
    }

    return suggestions
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopMonitoring()
    memoryManager.destroy()

    if (this.config.enableDebugMode) {
      console.log('Performance optimization service destroyed')
    }
  }
}

// 导出单例实例
export const performanceOptimizationService = PerformanceOptimizationService.getInstance()

// Vue 组合式函数
export function usePerformanceOptimization() {
  const service = performanceOptimizationService

  const isOptimizing = computed(() => service['isOptimizing'].value)
  const lastOptimization = computed(() => service['lastOptimization'].value)

  const optimize = () => service.optimizeMemory()
  const optimizeImages = (urls: string[]) => service.optimizeImages(urls)
  const optimizeAnimations = () => service.optimizeAnimations()
  const getStats = () => service.getPerformanceStats()
  const getSuggestions = () => service.getOptimizationSuggestions()
  const updateConfig = (config: Partial<PerformanceConfig>) => service.updateConfig(config)

  return {
    isOptimizing,
    lastOptimization,
    optimize,
    optimizeImages,
    optimizeAnimations,
    getStats,
    getSuggestions,
    updateConfig
  }
}
