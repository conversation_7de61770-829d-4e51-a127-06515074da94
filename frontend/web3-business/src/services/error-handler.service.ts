import { showToast, showDialog, showNotify } from 'vant'
import type { ToastOptions, DialogOptions, NotifyOptions } from 'vant'

export enum ErrorType {
  NETWORK = 'network',
  CONTRACT = 'contract',
  WALLET = 'wallet',
  VALIDATION = 'validation',
  TRANSACTION = 'transaction',
  UNKNOWN = 'unknown'
}

export interface AppError {
  type: ErrorType
  code?: string
  message: string
  originalError?: any
  timestamp: Date
  context?: Record<string, any>
}

export interface RetryConfig {
  maxAttempts: number
  delay: number
  backoff?: boolean
}

class ErrorHandlerService {
  private errorHistory: AppError[] = []
  private maxHistorySize = 100

  /**
   * 处理错误并显示用户友好的消息
   */
  handleError(error: any, context?: Record<string, any>): AppError {
    const appError = this.parseError(error, context)
    this.logError(appError)
    this.showErrorToUser(appError)
    return appError
  }

  /**
   * 解析错误类型和消息
   */
  private parseError(error: any, context?: Record<string, any>): AppError {
    const timestamp = new Date()

    // 网络错误
    if (this.isNetworkError(error)) {
      return {
        type: ErrorType.NETWORK,
        code: error.code,
        message: this.getNetworkErrorMessage(error),
        originalError: error,
        timestamp,
        context
      }
    }

    // 合约错误
    if (this.isContractError(error)) {
      return {
        type: ErrorType.CONTRACT,
        code: error.code || error.reason,
        message: this.getContractErrorMessage(error),
        originalError: error,
        timestamp,
        context
      }
    }

    // 钱包错误
    if (this.isWalletError(error)) {
      return {
        type: ErrorType.WALLET,
        code: error.code,
        message: this.getWalletErrorMessage(error),
        originalError: error,
        timestamp,
        context
      }
    }

    // 交易错误
    if (this.isTransactionError(error)) {
      return {
        type: ErrorType.TRANSACTION,
        code: error.code,
        message: this.getTransactionErrorMessage(error),
        originalError: error,
        timestamp,
        context
      }
    }

    // 验证错误
    if (this.isValidationError(error)) {
      return {
        type: ErrorType.VALIDATION,
        message: error.message || '输入验证失败',
        originalError: error,
        timestamp,
        context
      }
    }

    // 未知错误
    return {
      type: ErrorType.UNKNOWN,
      message: error.message || '发生未知错误，请稍后重试',
      originalError: error,
      timestamp,
      context
    }
  }

  /**
   * 检查是否为网络错误
   */
  private isNetworkError(error: any): boolean {
    return error.code === 'NETWORK_ERROR' ||
           error.code === 'TIMEOUT' ||
           error.message?.includes('network') ||
           error.message?.includes('timeout') ||
           error.name === 'NetworkError'
  }

  /**
   * 检查是否为合约错误
   */
  private isContractError(error: any): boolean {
    return error.code === 'CALL_EXCEPTION' ||
           error.code === 'UNPREDICTABLE_GAS_LIMIT' ||
           error.reason ||
           error.message?.includes('revert') ||
           error.message?.includes('execution reverted')
  }

  /**
   * 检查是否为钱包错误
   */
  private isWalletError(error: any): boolean {
    return error.code === 'ACTION_REJECTED' ||
           error.code === 'UNAUTHORIZED' ||
           error.code === 4001 ||
           error.message?.includes('User rejected') ||
           error.message?.includes('user rejected')
  }

  /**
   * 检查是否为交易错误
   */
  private isTransactionError(error: any): boolean {
    return error.code === 'INSUFFICIENT_FUNDS' ||
           error.code === 'NONCE_EXPIRED' ||
           error.code === 'REPLACEMENT_UNDERPRICED' ||
           error.message?.includes('insufficient funds') ||
           error.message?.includes('nonce')
  }

  /**
   * 检查是否为验证错误
   */
  private isValidationError(error: any): boolean {
    return error.name === 'ValidationError' ||
           error.type === 'validation'
  }

  /**
   * 获取网络错误消息
   */
  private getNetworkErrorMessage(error: any): string {
    if (error.code === 'NETWORK_ERROR') {
      return '网络连接异常，请检查网络设置'
    }
    if (error.code === 'TIMEOUT') {
      return '网络请求超时，请稍后重试'
    }
    return '网络错误，请检查网络连接'
  }

  /**
   * 获取合约错误消息
   */
  private getContractErrorMessage(error: any): string {
    const reason = error.reason || error.message

    if (reason?.includes('TradingNotEnabled')) {
      return '代币交易尚未开启'
    }
    if (reason?.includes('InsufficientBalance')) {
      return '余额不足'
    }
    if (reason?.includes('InvalidAmount')) {
      return '无效的金额'
    }
    if (reason?.includes('Unauthorized')) {
      return '没有权限执行此操作'
    }
    if (reason?.includes('Paused')) {
      return '合约已暂停，请稍后重试'
    }
    if (error.code === 'UNPREDICTABLE_GAS_LIMIT') {
      return '交易可能失败，请检查参数'
    }

    return '合约调用失败，请稍后重试'
  }

  /**
   * 获取钱包错误消息
   */
  private getWalletErrorMessage(error: any): string {
    if (error.code === 'ACTION_REJECTED' || error.code === 4001) {
      return '用户取消了操作'
    }
    if (error.code === 'UNAUTHORIZED') {
      return '请先连接钱包'
    }
    return '钱包操作失败，请重试'
  }

  /**
   * 获取交易错误消息
   */
  private getTransactionErrorMessage(error: any): string {
    if (error.code === 'INSUFFICIENT_FUNDS') {
      return '余额不足，请检查您的钱包余额'
    }
    if (error.code === 'NONCE_EXPIRED') {
      return '交易已过期，请重新发起'
    }
    if (error.code === 'REPLACEMENT_UNDERPRICED') {
      return 'Gas费用过低，请提高Gas费用'
    }
    return '交易失败，请稍后重试'
  }

  /**
   * 记录错误
   */
  private logError(error: AppError): void {
    console.error('Application Error:', error)

    // 添加到错误历史
    this.errorHistory.unshift(error)

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize)
    }
  }

  /**
   * 向用户显示错误
   */
  private showErrorToUser(error: AppError): void {
    const options: ToastOptions = {
      message: error.message,
      type: 'fail',
      duration: 4000,
      position: 'top'
    }

    // 对于严重错误，使用对话框
    if (error.type === ErrorType.CONTRACT || error.type === ErrorType.TRANSACTION) {
      showDialog({
        title: '操作失败',
        message: error.message,
        confirmButtonText: '确定'
      })
    } else {
      showToast(options)
    }
  }

  /**
   * 显示成功消息
   */
  showSuccess(message: string, options?: Partial<ToastOptions>): void {
    showToast({
      message,
      type: 'success',
      duration: 3000,
      position: 'top',
      ...options
    })
  }

  /**
   * 显示警告消息
   */
  showWarning(message: string, options?: Partial<NotifyOptions>): void {
    showNotify({
      message,
      type: 'warning',
      duration: 4000,
      ...options
    })
  }

  /**
   * 显示信息消息
   */
  showInfo(message: string, options?: Partial<ToastOptions>): void {
    showToast({
      message,
      type: 'text',
      duration: 3000,
      position: 'top',
      ...options
    })
  }

  /**
   * 显示加载消息
   */
  showLoading(message: string = '加载中...'): () => void {
    const toast = showToast({
      message,
      type: 'loading',
      duration: 0,
      forbidClick: true
    })

    return () => toast.close()
  }

  /**
   * 带重试机制的操作执行
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = { maxAttempts: 3, delay: 1000 },
    context?: Record<string, any>
  ): Promise<T> {
    let lastError: any

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error

        // 如果是用户取消的操作，不重试
        if (this.isWalletError(error) && (error.code === 4001 || error.code === 'ACTION_REJECTED')) {
          throw error
        }

        // 最后一次尝试失败
        if (attempt === config.maxAttempts) {
          break
        }

        // 计算延迟时间
        const delay = config.backoff ? config.delay * Math.pow(2, attempt - 1) : config.delay

        // 显示重试提示
        this.showWarning(`操作失败，${delay / 1000}秒后重试 (${attempt}/${config.maxAttempts})`)

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    // 所有重试都失败了
    throw this.handleError(lastError, { ...context, attempts: config.maxAttempts })
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): AppError[] {
    return [...this.errorHistory]
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = []
  }

  /**
   * 获取特定类型的错误统计
   */
  getErrorStats(): Record<ErrorType, number> {
    const stats = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = 0
      return acc
    }, {} as Record<ErrorType, number>)

    this.errorHistory.forEach(error => {
      stats[error.type]++
    })

    return stats
  }
}

export const errorHandler = new ErrorHandlerService()
export default errorHandler
