import { ItemType, PetRarity, EquipmentType } from '../types/typesWithoutCircular'
import type { Item, Equipment, Pet } from '../types/typesWithoutCircular'
import { ItemFactory, ItemManager } from '../types/item'
import { EquipmentFactory } from '../types/equipment'

export interface ShopTransaction {
  id: string
  itemId: string
  itemName: string
  quantity: number
  unitPrice: number
  totalPrice: number
  currency: 'coins' | 'tokens'
  timestamp: Date
  status: 'pending' | 'completed' | 'failed' | 'refunded'
}

export interface ShopDiscount {
  id: string
  name: string
  description: string
  discountType: 'percentage' | 'fixed'
  discountValue: number
  minPurchase?: number
  maxDiscount?: number
  validFrom: Date
  validTo: Date
  applicableCategories?: string[]
  applicableItems?: string[]
  usageLimit?: number
  usedCount: number
}

export interface ShopRestock {
  itemId: string
  newStock: number
  restockTime: Date
  reason: 'scheduled' | 'manual' | 'event'
}

export class ShopService {
  private static instance: ShopService
  private transactions: ShopTransaction[] = []
  private discounts: ShopDiscount[] = []
  private restockSchedule: ShopRestock[] = []

  static getInstance(): ShopService {
    if (!ShopService.instance) {
      ShopService.instance = new ShopService()
    }
    return ShopService.instance
  }

  // 处理购买交易
  async processPurchase(
    itemId: string,
    quantity: number,
    currency: 'coins' | 'tokens',
    unitPrice: number,
    userBalance: number
  ): Promise<{
    success: boolean
    transaction?: ShopTransaction
    error?: string
  }> {
    try {
      // 计算总价（包含折扣）
      const { finalPrice, appliedDiscounts } = this.calculateFinalPrice(
        itemId,
        quantity,
        unitPrice
      )

      // 验证余额
      if (userBalance < finalPrice) {
        return {
          success: false,
          error: `余额不足。需要 ${finalPrice} ${currency}，当前余额 ${userBalance} ${currency}`
        }
      }

      // 创建交易记录
      const transaction: ShopTransaction = {
        id: this.generateTransactionId(),
        itemId,
        itemName: `Item-${itemId}`, // 实际应该从商店数据获取
        quantity,
        unitPrice,
        totalPrice: finalPrice,
        currency,
        timestamp: new Date(),
        status: 'pending'
      }

      // 模拟交易处理
      await this.simulateTransactionProcessing()

      // 更新交易状态
      transaction.status = 'completed'
      this.transactions.push(transaction)

      // 应用使用的折扣
      appliedDiscounts.forEach(discount => {
        discount.usedCount++
      })

      return {
        success: true,
        transaction
      }
    } catch (error) {
      console.error('处理购买失败:', error)
      return {
        success: false,
        error: '交易处理失败，请重试'
      }
    }
  }

  // 计算最终价格（包含折扣）
  private calculateFinalPrice(
    itemId: string,
    quantity: number,
    unitPrice: number
  ): {
    finalPrice: number
    appliedDiscounts: ShopDiscount[]
  } {
    const basePrice = unitPrice * quantity
    let finalPrice = basePrice
    const appliedDiscounts: ShopDiscount[] = []

    // 查找适用的折扣
    const applicableDiscounts = this.getApplicableDiscounts(itemId, basePrice)

    applicableDiscounts.forEach(discount => {
      if (this.canUseDiscount(discount)) {
        let discountAmount = 0

        if (discount.discountType === 'percentage') {
          discountAmount = basePrice * (discount.discountValue / 100)
          if (discount.maxDiscount) {
            discountAmount = Math.min(discountAmount, discount.maxDiscount)
          }
        } else {
          discountAmount = discount.discountValue
        }

        finalPrice -= discountAmount
        appliedDiscounts.push(discount)
      }
    })

    // 确保价格不为负数
    finalPrice = Math.max(0, Math.floor(finalPrice))

    return { finalPrice, appliedDiscounts }
  }

  // 获取适用的折扣
  private getApplicableDiscounts(itemId: string, totalPrice: number): ShopDiscount[] {
    const now = new Date()

    return this.discounts.filter(discount => {
      // 检查时间有效性
      if (now < discount.validFrom || now > discount.validTo) {
        return false
      }

      // 检查最小购买金额
      if (discount.minPurchase && totalPrice < discount.minPurchase) {
        return false
      }

      // 检查使用次数限制
      if (discount.usageLimit && discount.usedCount >= discount.usageLimit) {
        return false
      }

      // 检查适用商品
      if (discount.applicableItems && !discount.applicableItems.includes(itemId)) {
        return false
      }

      return true
    })
  }

  // 检查是否可以使用折扣
  private canUseDiscount(discount: ShopDiscount): boolean {
    const now = new Date()
    return (
      now >= discount.validFrom &&
      now <= discount.validTo &&
      (!discount.usageLimit || discount.usedCount < discount.usageLimit)
    )
  }

  // 模拟交易处理延迟
  private async simulateTransactionProcessing(): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, Math.random() * 1000 + 500) // 0.5-1.5秒延迟
    })
  }

  // 生成交易ID
  private generateTransactionId(): string {
    return `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 获取交易历史
  getTransactionHistory(limit?: number): ShopTransaction[] {
    const sorted = [...this.transactions].sort((a, b) =>
      b.timestamp.getTime() - a.timestamp.getTime()
    )
    return limit ? sorted.slice(0, limit) : sorted
  }

  // 获取用户统计
  getUserStats(): {
    totalTransactions: number
    totalSpent: { coins: number; tokens: number }
    favoriteCategory: string
    averageTransactionValue: number
  } {
    const coinTransactions = this.transactions.filter(t => t.currency === 'coins')
    const tokenTransactions = this.transactions.filter(t => t.currency === 'tokens')

    const totalCoinsSpent = coinTransactions.reduce((sum, t) => sum + t.totalPrice, 0)
    const totalTokensSpent = tokenTransactions.reduce((sum, t) => sum + t.totalPrice, 0)

    return {
      totalTransactions: this.transactions.length,
      totalSpent: {
        coins: totalCoinsSpent,
        tokens: totalTokensSpent
      },
      favoriteCategory: 'food', // 简化实现
      averageTransactionValue: this.transactions.length > 0
        ? (totalCoinsSpent + totalTokensSpent) / this.transactions.length
        : 0
    }
  }

  // 创建折扣
  createDiscount(discount: Omit<ShopDiscount, 'id' | 'usedCount'>): ShopDiscount {
    const newDiscount: ShopDiscount = {
      ...discount,
      id: this.generateDiscountId(),
      usedCount: 0
    }

    this.discounts.push(newDiscount)
    return newDiscount
  }

  // 生成折扣ID
  private generateDiscountId(): string {
    return `DISC_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }

  // 获取当前有效折扣
  getActiveDiscounts(): ShopDiscount[] {
    const now = new Date()
    return this.discounts.filter(discount =>
      now >= discount.validFrom &&
      now <= discount.validTo &&
      (!discount.usageLimit || discount.usedCount < discount.usageLimit)
    )
  }

  // 验证购买条件
  validatePurchase(
    itemId: string,
    quantity: number,
    pet: Pet | null,
    userLevel: number = 1
  ): {
    valid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 基础验证
    if (quantity <= 0) {
      errors.push('购买数量必须大于0')
    }

    if (quantity > 99) {
      errors.push('单次购买数量不能超过99')
    }

    // 等级验证（示例）
    if (userLevel < 5 && itemId.includes('premium')) {
      errors.push('需要达到5级才能购买高级物品')
    }

    // 萌宠相关验证
    if (pet) {
      if (pet.health < 20 && itemId.includes('toy')) {
        warnings.push('萌宠健康度较低，建议先购买食物或药品')
      }

      if (pet.happiness > 80 && itemId.includes('toy')) {
        warnings.push('萌宠已经很快乐了，可能不需要更多玩具')
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  // 推荐购买物品
  recommendItems(
    pet: Pet | null,
    userCoins: number,
    userTokens: number,
    purchaseHistory: any[]
  ): {
    itemId: string
    reason: string
    priority: number
  }[] {
    const recommendations: { itemId: string; reason: string; priority: number }[] = []

    if (pet) {
      // 基于萌宠状态推荐
      if (pet.health < 50) {
        recommendations.push({
          itemId: 'health_potion',
          reason: '萌宠健康度较低，建议购买治疗药水',
          priority: 10
        })
      }

      if (pet.happiness < 40) {
        recommendations.push({
          itemId: 'basic_toy',
          reason: '萌宠心情不佳，玩具可以提升快乐度',
          priority: 8
        })
      }

      if (pet.energy < 30) {
        recommendations.push({
          itemId: 'energy_drink',
          reason: '萌宠能量不足，需要恢复体力',
          priority: 9
        })
      }

      // 基于等级推荐装备
      if (pet.level >= 10 && pet.equipment.length < 3) {
        recommendations.push({
          itemId: 'steel_armor',
          reason: '萌宠等级足够，可以装备更好的护甲',
          priority: 7
        })
      }
    }

    // 基于预算推荐
    if (userCoins >= 100) {
      recommendations.push({
        itemId: 'premium_food',
        reason: '金币充足，可以购买高级食物',
        priority: 6
      })
    }

    // 基于购买历史推荐
    const recentCategories = purchaseHistory
      .slice(0, 5)
      .map(p => p.category)
      .filter(Boolean)

    if (recentCategories.includes('food')) {
      recommendations.push({
        itemId: 'deluxe_food',
        reason: '您经常购买食物，推荐尝试豪华大餐',
        priority: 5
      })
    }

    // 按优先级排序
    return recommendations.sort((a, b) => b.priority - a.priority)
  }

  // 计算库存补充
  scheduleRestock(itemId: string, currentStock: number, maxStock: number): ShopRestock | null {
    if (currentStock >= maxStock * 0.3) {
      return null // 库存充足，不需要补充
    }

    const restockAmount = maxStock - currentStock
    const restockTime = new Date(Date.now() + 6 * 60 * 60 * 1000) // 6小时后补充

    const restock: ShopRestock = {
      itemId,
      newStock: currentStock + restockAmount,
      restockTime,
      reason: 'scheduled'
    }

    this.restockSchedule.push(restock)
    return restock
  }

  // 执行库存补充
  executeRestocks(): ShopRestock[] {
    const now = new Date()
    const dueRestocks = this.restockSchedule.filter(r => r.restockTime <= now)

    // 移除已执行的补充计划
    this.restockSchedule = this.restockSchedule.filter(r => r.restockTime > now)

    return dueRestocks
  }

  // 获取商店统计
  getShopStats(): {
    totalSales: number
    popularItems: { itemId: string; salesCount: number }[]
    revenueByCategory: Record<string, number>
    averageOrderValue: number
  } {
    const itemSales = new Map<string, number>()
    const categoryRevenue = new Map<string, number>()
    let totalRevenue = 0

    this.transactions.forEach(transaction => {
      if (transaction.status === 'completed') {
        // 统计商品销量
        const currentSales = itemSales.get(transaction.itemId) || 0
        itemSales.set(transaction.itemId, currentSales + transaction.quantity)

        // 统计收入（简化分类逻辑）
        const category = this.getCategoryByItemId(transaction.itemId)
        const currentRevenue = categoryRevenue.get(category) || 0
        categoryRevenue.set(category, currentRevenue + transaction.totalPrice)

        totalRevenue += transaction.totalPrice
      }
    })

    // 转换为数组并排序
    const popularItems = Array.from(itemSales.entries())
      .map(([itemId, salesCount]) => ({ itemId, salesCount }))
      .sort((a, b) => b.salesCount - a.salesCount)
      .slice(0, 10)

    const revenueByCategory = Object.fromEntries(categoryRevenue)

    return {
      totalSales: this.transactions.filter(t => t.status === 'completed').length,
      popularItems,
      revenueByCategory,
      averageOrderValue: this.transactions.length > 0 ? totalRevenue / this.transactions.length : 0
    }
  }

  // 根据商品ID获取分类（简化实现）
  private getCategoryByItemId(itemId: string): string {
    if (itemId.includes('food')) return 'food'
    if (itemId.includes('toy')) return 'toy'
    if (itemId.includes('equipment') || itemId.includes('weapon') || itemId.includes('armor')) return 'equipment'
    if (itemId.includes('potion') || itemId.includes('medicine')) return 'medicine'
    if (itemId.includes('training') || itemId.includes('manual')) return 'training'
    return 'other'
  }

  // 清理过期数据
  cleanup(): void {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    // 清理过期交易记录
    this.transactions = this.transactions.filter(t => t.timestamp > thirtyDaysAgo)

    // 清理过期折扣
    const now = new Date()
    this.discounts = this.discounts.filter(d => d.validTo > now)

    // 清理过期的补充计划
    this.restockSchedule = this.restockSchedule.filter(r => r.restockTime > now)
  }
}

// 导出单例实例
export const shopService = ShopService.getInstance()
