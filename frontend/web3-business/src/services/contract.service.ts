import { ethers, type Contract, type ContractTransactionResponse, type TransactionReceipt } from 'ethers'
import { TOKEN_ABI, TOKEN_V2_ABI } from '../config/abis'
import { CONTRACT_CONFIG } from '../config/constants'
import type { ContractInfo } from '../types'

export interface TransactionResponse {
  hash: string
  wait: () => Promise<TransactionReceipt>
}

export interface TaxInfo {
  currentTaxRate: bigint
  currentTaxCollector: string
  totalCollected: bigint
  taxEnabled: boolean
}

export interface TransferPreview {
  netAmount: bigint
  taxAmount: bigint
  willApplyTax: boolean
}

export class ContractService {
  private tokenContract: any = null
  private tokenV2Contract: any = null
  private signer: ethers.Signer | null = null
  private provider: ethers.BrowserProvider | null = null
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.eventListeners.set('Transfer', [])
    this.eventListeners.set('TaxCollected', [])
    this.eventListeners.set('MintCompleted', [])
  }

  /**
   * 初始化合约服务
   * @param provider 以太坊提供者
   * @param signer 签名者
   */
  async initialize(provider: ethers.BrowserProvider, signer?: ethers.Signer): Promise<void> {
    try {
      this.provider = provider

      // 如果提供了签名者，使用签名者连接合约
      if (signer) {
        this.signer = signer
        this.tokenContract = new ethers.Contract(CONTRACT_CONFIG.TOKEN_ADDRESS, TOKEN_ABI, signer)
        this.tokenV2Contract = new ethers.Contract(CONTRACT_CONFIG.TOKEN_V2_ADDRESS, TOKEN_V2_ABI, signer)
      } else {
        // 否则使用只读模式连接合约
        this.tokenContract = new ethers.Contract(CONTRACT_CONFIG.TOKEN_ADDRESS, TOKEN_ABI, provider)
        this.tokenV2Contract = new ethers.Contract(CONTRACT_CONFIG.TOKEN_V2_ADDRESS, TOKEN_V2_ABI, provider)
      }

      // 设置事件监听器
      this.setupEventListeners()
    } catch (error) {
      console.error('初始化合约服务失败:', error)
      throw new Error('初始化合约服务失败')
    }
  }

  /**
   * 更新签名者
   * @param signer 新的签名者
   */
  async updateSigner(signer: ethers.Signer): Promise<void> {
    this.signer = signer
    if (this.tokenContract) {
      this.tokenContract = this.tokenContract.connect(signer)
    }
    if (this.tokenV2Contract) {
      this.tokenV2Contract = this.tokenV2Contract.connect(signer)
    }
  }

  /**
   * 获取合约信息
   * @returns 合约信息
   */
  async getContractInfo(): Promise<ContractInfo> {
    if (!this.tokenContract) {
      throw new Error('合约未初始化')
    }

    try {
      const [name, symbol, decimals, totalSupply, version] = await Promise.all([
        this.tokenContract.name(),
        this.tokenContract.symbol(),
        this.tokenContract.decimals(),
        this.tokenContract.totalSupply(),
        this.tokenContract.version()
      ])

      return {
        address: this.tokenContract.target as string,
        abi: TOKEN_ABI,
        name,
        symbol,
        decimals,
        totalSupply: totalSupply.toString(),
        version
      }
    } catch (error) {
      console.error('获取合约信息失败:', error)
      throw new Error('获取合约信息失败')
    }
  }

  /**
   * 获取V2合约信息
   * @returns V2合约信息
   */
  async getV2ContractInfo(): Promise<ContractInfo> {
    if (!this.tokenV2Contract) {
      throw new Error('V2合约未初始化')
    }

    try {
      const [name, symbol, decimals, totalSupply, version] = await Promise.all([
        this.tokenV2Contract.name(),
        this.tokenV2Contract.symbol(),
        this.tokenV2Contract.decimals(),
        this.tokenV2Contract.totalSupply(),
        this.tokenV2Contract.version()
      ])

      return {
        address: this.tokenV2Contract.target as string,
        abi: TOKEN_V2_ABI,
        name,
        symbol,
        decimals,
        totalSupply: totalSupply.toString(),
        version
      }
    } catch (error) {
      console.error('获取V2合约信息失败:', error)
      throw new Error('获取V2合约信息失败')
    }
  }

  /**
   * 获取账户余额
   * @param address 账户地址
   * @returns 余额
   */
  async getBalance(address: string): Promise<string> {
    if (!this.tokenContract) {
      throw new Error('合约未初始化')
    }

    try {
      const balance = await this.tokenContract.balanceOf(address)
      return balance.toString()
    } catch (error) {
      console.error('获取余额失败:', error)
      throw new Error('获取余额失败')
    }
  }

  /**
   * 转账代币
   * @param to 接收地址
   * @param amount 金额
   * @returns 交易响应
   */
  async transfer(to: string, amount: string): Promise<TransactionResponse> {
    if (!this.tokenContract || !this.signer) {
      throw new Error('合约未初始化或未连接钱包')
    }

    try {
      const tx = await this.tokenContract.transfer(to, amount) as ContractTransactionResponse
      return {
        hash: tx.hash,
        wait: async () => await tx.wait()
      }
    } catch (error) {
      console.error('转账失败:', error)
      throw this.handleContractError(error)
    }
  }

  /**
   * 安全转账代币
   * @param to 接收地址
   * @param amount 金额
   * @returns 交易响应
   */
  async safeTransfer(to: string, amount: string): Promise<TransactionResponse> {
    if (!this.tokenContract || !this.signer) {
      throw new Error('合约未初始化或未连接钱包')
    }

    try {
      const tx = await this.tokenContract.safeTransfer(to, amount) as ContractTransactionResponse
      return {
        hash: tx.hash,
        wait: async () => await tx.wait()
      }
    } catch (error) {
      console.error('安全转账失败:', error)
      throw this.handleContractError(error)
    }
  }

  /**
   * 批量转账代币
   * @param recipients 接收地址数组
   * @param amounts 金额数组
   * @returns 交易响应
   */
  async batchTransfer(recipients: string[], amounts: string[]): Promise<TransactionResponse> {
    if (!this.tokenContract || !this.signer) {
      throw new Error('合约未初始化或未连接钱包')
    }

    if (recipients.length !== amounts.length) {
      throw new Error('接收地址和金额数组长度不匹配')
    }

    try {
      const tx = await this.tokenContract.batchTransfer(recipients, amounts) as ContractTransactionResponse
      return {
        hash: tx.hash,
        wait: async () => await tx.wait()
      }
    } catch (error) {
      console.error('批量转账失败:', error)
      throw this.handleContractError(error)
    }
  }

  /**
   * 铸造代币
   * @param to 接收地址
   * @param amount 金额
   * @returns 交易响应
   */
  async mint(to: string, amount: string): Promise<TransactionResponse> {
    if (!this.tokenContract || !this.signer) {
      throw new Error('合约未初始化或未连接钱包')
    }

    try {
      const tx = await this.tokenContract.mint(to, amount) as ContractTransactionResponse
      return {
        hash: tx.hash,
        wait: async () => await tx.wait()
      }
    } catch (error) {
      console.error('铸造代币失败:', error)
      throw this.handleContractError(error)
    }
  }

  /**
   * 获取税收信息 (V2合约)
   * @returns 税收信息
   */
  async getTaxInfo(): Promise<TaxInfo> {
    if (!this.tokenV2Contract) {
      throw new Error('V2合约未初始化')
    }

    try {
      const taxInfo = await this.tokenV2Contract.getTaxInfo()
      return {
        currentTaxRate: taxInfo.currentTaxRate,
        currentTaxCollector: taxInfo.currentTaxCollector,
        totalCollected: taxInfo.totalCollected,
        taxEnabled: taxInfo.taxEnabled
      }
    } catch (error) {
      console.error('获取税收信息失败:', error)
      throw new Error('获取税收信息失败')
    }
  }

  /**
   * 计算税收 (V2合约)
   * @param amount 金额
   * @returns 净额和税额
   */
  async calculateTax(amount: string): Promise<{ netAmount: string, taxAmount: string }> {
    if (!this.tokenV2Contract) {
      throw new Error('V2合约未初始化')
    }

    try {
      const result = await this.tokenV2Contract.calculateTax(amount)
      return {
        netAmount: result.netAmount.toString(),
        taxAmount: result.taxAmount.toString()
      }
    } catch (error) {
      console.error('计算税收失败:', error)
      throw new Error('计算税收失败')
    }
  }

  /**
   * 预览转账 (V2合约)
   * @param from 发送地址
   * @param to 接收地址
   * @param amount 金额
   * @returns 转账预览
   */
  async previewTransfer(from: string, to: string, amount: string): Promise<TransferPreview> {
    if (!this.tokenV2Contract) {
      throw new Error('V2合约未初始化')
    }

    try {
      const result = await this.tokenV2Contract.calculateTaxForTransfer(from, to, amount)
      return {
        netAmount: result.netAmount,
        taxAmount: result.taxAmount,
        willApplyTax: result.taxAmount > 0n
      }
    } catch (error) {
      console.error('预览转账失败:', error)
      throw new Error('预览转账失败')
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.tokenContract || !this.tokenV2Contract) {
      return
    }

    // 监听转账事件
    this.tokenContract.on('Transfer', (from, to, amount, event) => {
      const listeners = this.eventListeners.get('Transfer') || []
      listeners.forEach(callback => callback({ from, to, amount: amount.toString(), event }))
    })

    // 监听铸造完成事件
    this.tokenContract.on('MintCompleted', (to, amount, event) => {
      const listeners = this.eventListeners.get('MintCompleted') || []
      listeners.forEach(callback => callback({ to, amount: amount.toString(), event }))
    })

    // 监听税收事件 (V2合约)
    this.tokenV2Contract.on('TaxCollected', (from, to, taxAmount, netAmount, event) => {
      const listeners = this.eventListeners.get('TaxCollected') || []
      listeners.forEach(callback => callback({
        from,
        to,
        taxAmount: taxAmount.toString(),
        netAmount: netAmount.toString(),
        event
      }))
    })
  }

  /**
   * 添加事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  onEvent(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    const listeners = this.eventListeners.get(eventName) || []
    listeners.push(callback)
    this.eventListeners.set(eventName, listeners)
  }

  /**
   * 移除事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  offEvent(eventName: string, callback: Function): void {
    if (!this.eventListeners.has(eventName)) {
      return
    }
    const listeners = this.eventListeners.get(eventName) || []
    const index = listeners.indexOf(callback)
    if (index !== -1) {
      listeners.splice(index, 1)
      this.eventListeners.set(eventName, listeners)
    }
  }

  /**
   * 处理合约错误
   * @param error 错误对象
   * @returns 格式化的错误
   */
  private handleContractError(error: any): Error {
    // 检查常见错误类型
    if (error.code === 'ACTION_REJECTED') {
      return new Error('用户取消了交易')
    }

    if (error.code === 'INSUFFICIENT_FUNDS') {
      return new Error('余额不足')
    }

    // 检查自定义错误
    if (error.message?.includes('TradingNotEnabled')) {
      return new Error('交易尚未开启')
    }

    if (error.message?.includes('AccountBlacklisted')) {
      return new Error('账户已被列入黑名单')
    }

    if (error.message?.includes('ExceedsMaxTransaction')) {
      return new Error('超过最大交易金额')
    }

    if (error.message?.includes('ExceedsMaxWallet')) {
      return new Error('超过最大钱包金额')
    }

    return new Error('交易失败: ' + (error.message || '未知错误'))
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.tokenContract) {
      this.tokenContract.removeAllListeners()
    }
    if (this.tokenV2Contract) {
      this.tokenV2Contract.removeAllListeners()
    }
    this.eventListeners.clear()
  }
}

// 导出单例实例
export const contractService = new ContractService()
