import { contractService } from './contract.service'
import { useWalletStore } from '@/stores/wallet'
import { usePetStore } from '@/stores/pet'
import { useGameStore } from '@/stores/game'

export interface ContractEvent {
  type: string
  data: any
  timestamp: number
}

export class ContractEventsService {
  private walletStore = useWalletStore()
  private petStore = usePetStore()
  private gameStore = useGameStore()
  private events: ContractEvent[] = []
  private maxEvents = 100

  /**
   * 初始化事件监听
   */
  initialize(): void {
    this.setupTransferEventListener()
    this.setupMintEventListener()
    this.setupTaxEventListener()
  }

  /**
   * 获取所有事件
   * @returns 事件列表
   */
  getEvents(): ContractEvent[] {
    return [...this.events]
  }

  /**
   * 清除所有事件
   */
  clearEvents(): void {
    this.events = []
  }

  /**
   * 设置转账事件监听器
   */
  private setupTransferEventListener(): void {
    contractService.onEvent('Transfer', (event: any) => {
      const { from, to, amount } = event
      const currentAddress = this.walletStore.address

      // 只处理与当前用户相关的转账
      if (from === currentAddress || to === currentAddress) {
        this.addEvent('transfer', {
          from,
          to,
          amount,
          isIncoming: to === currentAddress,
          isOutgoing: from === currentAddress
        })

        // 更新用户余额
        if (from === currentAddress || to === currentAddress) {
          this.updateUserBalance()
        }
      }
    })
  }

  /**
   * 设置铸造事件监听器
   */
  private setupMintEventListener(): void {
    contractService.onEvent('MintCompleted', (event: any) => {
      const { to, amount } = event
      const currentAddress = this.walletStore.address

      // 只处理与当前用户相关的铸造
      if (to === currentAddress) {
        this.addEvent('mint', {
          to,
          amount
        })

        // 更新用户余额
        this.updateUserBalance()

        // 更新游戏状态
        this.gameStore.addTokens(amount)
      }
    })
  }

  /**
   * 设置税收事件监听器
   */
  private setupTaxEventListener(): void {
    contractService.onEvent('TaxCollected', (event: any) => {
      const { from, to, taxAmount, netAmount } = event
      const currentAddress = this.walletStore.address

      // 只处理与当前用户相关的税收
      if (from === currentAddress || to === currentAddress) {
        this.addEvent('tax', {
          from,
          to,
          taxAmount,
          netAmount,
          isFromCurrentUser: from === currentAddress
        })
      }
    })
  }

  /**
   * 添加事件
   * @param type 事件类型
   * @param data 事件数据
   */
  private addEvent(type: string, data: any): void {
    const event: ContractEvent = {
      type,
      data,
      timestamp: Date.now()
    }

    this.events.unshift(event)

    // 限制事件数量
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents)
    }

    // 触发事件通知
    window.dispatchEvent(new CustomEvent('contract-event', {
      detail: event
    }))
  }

  /**
   * 更新用户余额
   */
  private async updateUserBalance(): Promise<void> {
    const address = this.walletStore.address
    if (!address) return

    try {
      const balance = await contractService.getBalance(address)
      this.walletStore.setWalletInfo({ balance })
    } catch (error) {
      console.error('更新余额失败:', error)
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 移除所有事件监听器
    contractService.offEvent('Transfer', this.setupTransferEventListener)
    contractService.offEvent('MintCompleted', this.setupMintEventListener)
    contractService.offEvent('TaxCollected', this.setupTaxEventListener)
  }
}

// 导出单例实例
export const contractEventsService = new ContractEventsService()