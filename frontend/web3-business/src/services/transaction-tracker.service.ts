import { ref, reactive } from 'vue'
import { showNotify, showDialog } from 'vant'
import type { TransactionResponse, TransactionReceipt } from 'ethers'
import { errorHandler } from './error-handler.service'

export enum TransactionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface TransactionInfo {
  id: string
  hash?: string
  type: string
  description: string
  status: TransactionStatus
  timestamp: Date
  confirmations?: number
  gasUsed?: string
  gasPrice?: string
  blockNumber?: number
  error?: string
  metadata?: Record<string, any>
}

export interface TransactionNotification {
  title: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  actions?: Array<{
    text: string
    action: () => void
  }>
}

class TransactionTrackerService {
  private transactions = reactive<Map<string, TransactionInfo>>(new Map())
  private activeTransactions = ref<string[]>([])
  private notificationQueue: TransactionNotification[] = []

  /**
   * 开始跟踪交易
   */
  async trackTransaction(
    txPromise: Promise<TransactionResponse>,
    type: string,
    description: string,
    metadata?: Record<string, any>
  ): Promise<TransactionReceipt> {
    const id = this.generateTransactionId()

    // 创建初始交易信息
    const txInfo: TransactionInfo = {
      id,
      type,
      description,
      status: TransactionStatus.PENDING,
      timestamp: new Date(),
      metadata
    }

    this.addTransaction(txInfo)
    this.showTransactionStarted(txInfo)

    try {
      // 等待交易响应
      const tx = await txPromise

      // 更新交易哈希
      txInfo.hash = tx.hash
      this.updateTransaction(id, txInfo)

      this.showTransactionSubmitted(txInfo)

      // 等待交易确认
      const receipt = await this.waitForConfirmation(tx, id)

      // 更新交易状态为已确认
      txInfo.status = TransactionStatus.CONFIRMED
      txInfo.confirmations = receipt.confirmations
      txInfo.gasUsed = receipt.gasUsed.toString()
      txInfo.gasPrice = receipt.gasPrice?.toString()
      txInfo.blockNumber = receipt.blockNumber

      this.updateTransaction(id, txInfo)
      this.showTransactionConfirmed(txInfo)

      return receipt

    } catch (error: any) {
      // 处理交易失败
      const isUserCancelled = error.code === 4001 || error.code === 'ACTION_REJECTED'

      txInfo.status = isUserCancelled ? TransactionStatus.CANCELLED : TransactionStatus.FAILED
      txInfo.error = error.message

      this.updateTransaction(id, txInfo)

      if (isUserCancelled) {
        this.showTransactionCancelled(txInfo)
      } else {
        this.showTransactionFailed(txInfo, error)
      }

      throw error
    } finally {
      // 从活跃交易列表中移除
      this.removeActiveTransaction(id)
    }
  }

  /**
   * 等待交易确认
   */
  private async waitForConfirmation(
    tx: TransactionResponse,
    transactionId: string
  ): Promise<TransactionReceipt> {
    const confirmationsNeeded = 1
    let confirmations = 0

    return new Promise((resolve, reject) => {
      const checkConfirmation = async () => {
        try {
          const receipt = await tx.wait(1)

          if (receipt.status === 0) {
            reject(new Error('交易执行失败'))
            return
          }

          confirmations = receipt.confirmations || 1

          // 更新确认数
          const txInfo = this.transactions.get(transactionId)
          if (txInfo) {
            txInfo.confirmations = confirmations
            this.updateTransaction(transactionId, txInfo)
          }

          if (confirmations >= confirmationsNeeded) {
            resolve(receipt)
          } else {
            // 继续等待更多确认
            setTimeout(checkConfirmation, 2000)
          }
        } catch (error) {
          reject(error)
        }
      }

      checkConfirmation()
    })
  }

  /**
   * 添加交易
   */
  private addTransaction(txInfo: TransactionInfo): void {
    this.transactions.set(txInfo.id, txInfo)
    this.activeTransactions.value.push(txInfo.id)
  }

  /**
   * 更新交易
   */
  private updateTransaction(id: string, txInfo: TransactionInfo): void {
    this.transactions.set(id, { ...txInfo })
  }

  /**
   * 移除活跃交易
   */
  private removeActiveTransaction(id: string): void {
    const index = this.activeTransactions.value.indexOf(id)
    if (index > -1) {
      this.activeTransactions.value.splice(index, 1)
    }
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 显示交易开始通知
   */
  private showTransactionStarted(txInfo: TransactionInfo): void {
    errorHandler.showInfo(`正在准备交易: ${txInfo.description}`)
  }

  /**
   * 显示交易已提交通知
   */
  private showTransactionSubmitted(txInfo: TransactionInfo): void {
    const notification: TransactionNotification = {
      title: '交易已提交',
      message: `${txInfo.description} - 等待确认中...`,
      type: 'info',
      duration: 0,
      actions: [
        {
          text: '查看详情',
          action: () => this.showTransactionDetails(txInfo.id)
        }
      ]
    }

    this.showNotification(notification)
  }

  /**
   * 显示交易确认通知
   */
  private showTransactionConfirmed(txInfo: TransactionInfo): void {
    const notification: TransactionNotification = {
      title: '交易成功',
      message: `${txInfo.description} 已完成`,
      type: 'success',
      duration: 5000,
      actions: [
        {
          text: '查看详情',
          action: () => this.showTransactionDetails(txInfo.id)
        }
      ]
    }

    this.showNotification(notification)
  }

  /**
   * 显示交易失败通知
   */
  private showTransactionFailed(txInfo: TransactionInfo, error: any): void {
    const notification: TransactionNotification = {
      title: '交易失败',
      message: `${txInfo.description} 失败: ${error.message}`,
      type: 'error',
      duration: 0,
      actions: [
        {
          text: '重试',
          action: () => this.retryTransaction(txInfo.id)
        },
        {
          text: '查看详情',
          action: () => this.showTransactionDetails(txInfo.id)
        }
      ]
    }

    this.showNotification(notification)
  }

  /**
   * 显示交易取消通知
   */
  private showTransactionCancelled(txInfo: TransactionInfo): void {
    errorHandler.showWarning(`交易已取消: ${txInfo.description}`)
  }

  /**
   * 显示通知
   */
  private showNotification(notification: TransactionNotification): void {
    const options: any = {
      type: notification.type,
      message: notification.message,
      duration: notification.duration || 4000
    }

    if (notification.actions && notification.actions.length > 0) {
      options.onClick = () => {
        // 显示操作选择对话框
        showDialog({
          title: notification.title,
          message: notification.message,
          showCancelButton: true,
          confirmButtonText: notification.actions![0].text,
          cancelButtonText: notification.actions!.length > 1 ? notification.actions![1].text : '关闭'
        }).then(() => {
          notification.actions![0].action()
        }).catch(() => {
          if (notification.actions!.length > 1) {
            notification.actions![1].action()
          }
        })
      }
    }

    showNotify(options)
  }

  /**
   * 显示交易详情
   */
  private showTransactionDetails(transactionId: string): void {
    const txInfo = this.transactions.get(transactionId)
    if (!txInfo) return

    const details = [
      `类型: ${txInfo.type}`,
      `状态: ${this.getStatusText(txInfo.status)}`,
      `时间: ${txInfo.timestamp.toLocaleString()}`,
      txInfo.hash ? `哈希: ${txInfo.hash.slice(0, 10)}...${txInfo.hash.slice(-8)}` : '',
      txInfo.confirmations ? `确认数: ${txInfo.confirmations}` : '',
      txInfo.gasUsed ? `Gas使用: ${txInfo.gasUsed}` : '',
      txInfo.blockNumber ? `区块: ${txInfo.blockNumber}` : '',
      txInfo.error ? `错误: ${txInfo.error}` : ''
    ].filter(Boolean).join('\n')

    showDialog({
      title: '交易详情',
      message: details,
      confirmButtonText: '确定'
    })
  }

  /**
   * 重试交易
   */
  private retryTransaction(transactionId: string): void {
    const txInfo = this.transactions.get(transactionId)
    if (!txInfo) return

    // 这里应该触发重新执行交易的逻辑
    // 具体实现取决于业务需求
    errorHandler.showInfo('请重新执行交易操作')
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: TransactionStatus): string {
    const statusMap = {
      [TransactionStatus.PENDING]: '等待中',
      [TransactionStatus.CONFIRMED]: '已确认',
      [TransactionStatus.FAILED]: '失败',
      [TransactionStatus.CANCELLED]: '已取消'
    }
    return statusMap[status]
  }

  /**
   * 获取所有交易
   */
  getAllTransactions(): TransactionInfo[] {
    return Array.from(this.transactions.values()).sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    )
  }

  /**
   * 获取活跃交易
   */
  getActiveTransactions(): TransactionInfo[] {
    return this.activeTransactions.value
      .map(id => this.transactions.get(id))
      .filter(Boolean) as TransactionInfo[]
  }

  /**
   * 获取特定类型的交易
   */
  getTransactionsByType(type: string): TransactionInfo[] {
    return this.getAllTransactions().filter(tx => tx.type === type)
  }

  /**
   * 获取交易统计
   */
  getTransactionStats(): Record<TransactionStatus, number> {
    const stats = Object.values(TransactionStatus).reduce((acc, status) => {
      acc[status] = 0
      return acc
    }, {} as Record<TransactionStatus, number>)

    Array.from(this.transactions.values()).forEach(tx => {
      stats[tx.status]++
    })

    return stats
  }

  /**
   * 清除旧交易记录
   */
  clearOldTransactions(olderThanDays: number = 7): void {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    for (const [id, tx] of this.transactions.entries()) {
      if (tx.timestamp < cutoffDate && tx.status !== TransactionStatus.PENDING) {
        this.transactions.delete(id)
      }
    }
  }

  /**
   * 检查是否有待处理的交易
   */
  hasPendingTransactions(): boolean {
    return this.activeTransactions.value.length > 0
  }
}

export const transactionTracker = new TransactionTrackerService()
export default transactionTracker
