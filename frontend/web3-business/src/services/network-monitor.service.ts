import { ref, computed } from 'vue'
import { errorHandler } from './error-handler.service'

export enum NetworkStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  SLOW = 'slow',
  UNSTABLE = 'unstable'
}

export interface NetworkInfo {
  status: NetworkStatus
  rtt: number // Round Trip Time in ms
  downlink: number // Connection speed in Mbps
  effectiveType: string // Connection type
  lastChecked: Date
}

export interface RetryPolicy {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
  retryCondition?: (error: any) => boolean
}

class NetworkMonitorService {
  private networkStatus = ref<NetworkStatus>(NetworkStatus.ONLINE)
  private networkInfo = ref<NetworkInfo>({
    status: NetworkStatus.ONLINE,
    rtt: 0,
    downlink: 0,
    effectiveType: 'unknown',
    lastChecked: new Date()
  })

  private isMonitoring = ref(false)
  private monitoringInterval: number | null = null
  private connectionCheckUrl = 'https://httpbin.org/get'

  // 默认重试策略
  private defaultRetryPolicy: RetryPolicy = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryCondition: (error: any) => {
      // 只对网络相关错误重试
      return error.code === 'NETWORK_ERROR' ||
             error.code === 'TIMEOUT' ||
             error.message?.includes('network') ||
             error.message?.includes('timeout')
    }
  }

  constructor() {
    this.initializeNetworkMonitoring()
  }

  /**
   * 初始化网络监控
   */
  private initializeNetworkMonitoring(): void {
    // 监听浏览器网络状态变化
    window.addEventListener('online', this.handleOnline.bind(this))
    window.addEventListener('offline', this.handleOffline.bind(this))

    // 初始检查网络状态
    this.checkNetworkStatus()
  }

  /**
   * 处理网络连接
   */
  private handleOnline(): void {
    this.networkStatus.value = NetworkStatus.ONLINE
    this.checkNetworkQuality()
    errorHandler.showSuccess('网络连接已恢复')
  }

  /**
   * 处理网络断开
   */
  private handleOffline(): void {
    this.networkStatus.value = NetworkStatus.OFFLINE
    errorHandler.showWarning('网络连接已断开，请检查网络设置')
  }

  /**
   * 检查网络状态
   */
  async checkNetworkStatus(): Promise<NetworkInfo> {
    try {
      const startTime = Date.now()

      // 使用 fetch 检查网络连接
      const response = await fetch(this.connectionCheckUrl, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      })

      const rtt = Date.now() - startTime

      // 获取连接信息（如果支持）
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection

      const networkInfo: NetworkInfo = {
        status: this.determineNetworkStatus(rtt, connection),
        rtt,
        downlink: connection?.downlink || 0,
        effectiveType: connection?.effectiveType || 'unknown',
        lastChecked: new Date()
      }

      this.networkInfo.value = networkInfo
      this.networkStatus.value = networkInfo.status

      return networkInfo

    } catch (error) {
      const networkInfo: NetworkInfo = {
        status: NetworkStatus.OFFLINE,
        rtt: 0,
        downlink: 0,
        effectiveType: 'unknown',
        lastChecked: new Date()
      }

      this.networkInfo.value = networkInfo
      this.networkStatus.value = NetworkStatus.OFFLINE

      return networkInfo
    }
  }

  /**
   * 确定网络状态
   */
  private determineNetworkStatus(rtt: number, connection?: any): NetworkStatus {
    if (!navigator.onLine) {
      return NetworkStatus.OFFLINE
    }

    // 基于RTT判断网络质量
    if (rtt > 2000) {
      return NetworkStatus.SLOW
    }

    if (rtt > 1000 || (connection && connection.effectiveType === 'slow-2g')) {
      return NetworkStatus.UNSTABLE
    }

    return NetworkStatus.ONLINE
  }

  /**
   * 检查网络质量
   */
  private async checkNetworkQuality(): Promise<void> {
    try {
      const startTime = Date.now()

      // 下载一个小文件来测试网络速度
      const response = await fetch('data:text/plain;base64,SGVsbG8gV29ybGQ=', {
        cache: 'no-cache'
      })

      await response.text()
      const rtt = Date.now() - startTime

      if (rtt > 2000) {
        this.networkStatus.value = NetworkStatus.SLOW
        errorHandler.showWarning('网络连接较慢，可能影响使用体验')
      } else if (rtt > 1000) {
        this.networkStatus.value = NetworkStatus.UNSTABLE
      } else {
        this.networkStatus.value = NetworkStatus.ONLINE
      }

    } catch (error) {
      this.networkStatus.value = NetworkStatus.UNSTABLE
    }
  }

  /**
   * 开始监控网络状态
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.isMonitoring.value) {
      return
    }

    this.isMonitoring.value = true

    this.monitoringInterval = window.setInterval(() => {
      this.checkNetworkStatus()
    }, intervalMs)
  }

  /**
   * 停止监控网络状态
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    this.isMonitoring.value = false
  }

  /**
   * 带网络重试的请求执行
   */
  async executeWithNetworkRetry<T>(
    operation: () => Promise<T>,
    customPolicy?: Partial<RetryPolicy>
  ): Promise<T> {
    const policy = { ...this.defaultRetryPolicy, ...customPolicy }
    let lastError: any

    for (let attempt = 0; attempt < policy.maxRetries; attempt++) {
      try {
        // 检查网络状态
        if (this.networkStatus.value === NetworkStatus.OFFLINE) {
          throw new Error('网络连接不可用')
        }

        return await operation()

      } catch (error: any) {
        lastError = error

        // 检查是否应该重试
        if (!policy.retryCondition || !policy.retryCondition(error)) {
          throw error
        }

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === policy.maxRetries - 1) {
          break
        }

        // 计算延迟时间（指数退避）
        const delay = Math.min(
          policy.baseDelay * Math.pow(policy.backoffFactor, attempt),
          policy.maxDelay
        )

        // 显示重试提示
        errorHandler.showWarning(
          `网络请求失败，${delay / 1000}秒后重试 (${attempt + 1}/${policy.maxRetries})`
        )

        // 等待后重试
        await this.delay(delay)

        // 重试前检查网络状态
        await this.checkNetworkStatus()
      }
    }

    throw lastError
  }

  /**
   * 等待指定时间
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 检查网络连接是否可用
   */
  async isNetworkAvailable(): Promise<boolean> {
    try {
      const info = await this.checkNetworkStatus()
      return info.status !== NetworkStatus.OFFLINE
    } catch {
      return false
    }
  }

  /**
   * 等待网络恢复
   */
  async waitForNetwork(timeoutMs: number = 30000): Promise<boolean> {
    const startTime = Date.now()

    while (Date.now() - startTime < timeoutMs) {
      if (await this.isNetworkAvailable()) {
        return true
      }

      await this.delay(1000)
    }

    return false
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus(): NetworkStatus {
    return this.networkStatus.value
  }

  /**
   * 获取网络信息
   */
  getNetworkInfo(): NetworkInfo {
    return this.networkInfo.value
  }

  /**
   * 检查是否为良好的网络连接
   */
  isGoodConnection(): boolean {
    return this.networkStatus.value === NetworkStatus.ONLINE
  }

  /**
   * 检查是否为慢速连接
   */
  isSlowConnection(): boolean {
    return this.networkStatus.value === NetworkStatus.SLOW ||
           this.networkStatus.value === NetworkStatus.UNSTABLE
  }

  /**
   * 获取网络状态的响应式引用
   */
  get status() {
    return computed(() => this.networkStatus.value)
  }

  /**
   * 获取网络信息的响应式引用
   */
  get info() {
    return computed(() => this.networkInfo.value)
  }

  /**
   * 获取监控状态的响应式引用
   */
  get monitoring() {
    return computed(() => this.isMonitoring.value)
  }

  /**
   * 销毁监控服务
   */
  destroy(): void {
    this.stopMonitoring()
    window.removeEventListener('online', this.handleOnline.bind(this))
    window.removeEventListener('offline', this.handleOffline.bind(this))
  }
}

export const networkMonitor = new NetworkMonitorService()
export default networkMonitor
