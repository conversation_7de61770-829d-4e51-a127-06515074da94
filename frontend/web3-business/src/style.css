@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义样式 */
@layer base {
  html {
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Microsoft YaHei', sans-serif;
  }

  body {
    @apply bg-gradient-to-br from-purple-50 to-blue-50 min-h-screen;
  }
}

@layer components {
  .pet-card {
    @apply bg-white rounded-2xl shadow-lg p-4 border border-gray-100 hover:shadow-xl transition-all duration-300;
  }

  .game-button {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .stat-card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/20;
  }

  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2.5 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full transition-all duration-500;
  }

  .floating-element {
    @apply animate-float;
  }

  .glow-effect {
    @apply animate-glow;
  }

  .pet-avatar {
    @apply w-24 h-24 rounded-full border-4 border-white shadow-lg bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center text-4xl;
  }

  .token-display {
    @apply bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold py-2 px-4 rounded-full shadow-lg;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #ee56ff, #0ea5e9);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #d934e8, #0284c7);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .pet-card {
    @apply p-3;
  }

  .game-button {
    @apply py-2 px-4 text-sm;
  }

  .pet-avatar {
    @apply w-20 h-20 text-3xl;
  }
}
