@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义样式 */
@layer base {
  html {
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Microsoft YaHei', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-purple-50 to-blue-50 min-h-screen;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 响应式字体大小 */
  .font-small {
    font-size: var(--font-size-base, 0.875rem);
  }

  .font-medium {
    font-size: var(--font-size-base, 1rem);
  }

  .font-large {
    font-size: var(--font-size-base, 1.125rem);
  }

  /* 主题变量 */
  :root {
    --bg-gradient: linear-gradient(135deg, #faf5ff 0%, #f0f9ff 100%);
    --bg-gradient-dark: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  .theme-dark {
    --bg-gradient: var(--bg-gradient-dark);
  }
}

@layer components {
  /* 响应式卡片组件 */
  .pet-card {
    @apply bg-white rounded-2xl shadow-lg p-3 sm:p-4 lg:p-6 border border-gray-100 hover:shadow-xl transition-all duration-300;
  }

  .game-button {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold py-2 px-4 sm:py-3 sm:px-6 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 text-sm sm:text-base;
  }

  .stat-card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 lg:p-6 shadow-md border border-white/20;
  }

  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 sm:h-2.5 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full transition-all duration-500;
  }

  .floating-element {
    @apply animate-float;
  }

  .glow-effect {
    @apply animate-glow;
  }

  .pet-avatar {
    @apply w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-full border-2 sm:border-4 border-white shadow-lg bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center text-2xl sm:text-3xl lg:text-4xl;
  }

  .token-display {
    @apply bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold py-1.5 px-3 sm:py-2 sm:px-4 rounded-full shadow-lg text-sm sm:text-base;
  }

  /* 响应式网格系统 */
  .responsive-grid {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .responsive-grid-2 {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2;
  }

  .responsive-grid-3 {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  /* 响应式弹性布局 */
  .responsive-flex {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  .responsive-flex-center {
    @apply flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6;
  }

  /* 响应式文本 */
  .responsive-title {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold;
  }

  .responsive-subtitle {
    @apply text-lg sm:text-xl lg:text-2xl font-semibold;
  }

  .responsive-body {
    @apply text-sm sm:text-base lg:text-lg;
  }

  /* 响应式间距 */
  .responsive-padding {
    @apply p-4 sm:p-6 lg:p-8 xl:p-12;
  }

  .responsive-margin {
    @apply m-4 sm:m-6 lg:m-8 xl:m-12;
  }

  .responsive-gap {
    @apply gap-2 sm:gap-4 lg:gap-6;
  }

  /* 交互状态 */
  .interactive-card {
    @apply cursor-pointer transform transition-all duration-200 hover:scale-105 hover:shadow-lg active:scale-95;
  }

  .interactive-button {
    @apply cursor-pointer transform transition-all duration-150 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
  }

  /* 加载状态 */
  .loading-skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .loading-shimmer {
    @apply relative overflow-hidden bg-gray-200 rounded;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent;
    content: '';
    animation: shimmer 2s infinite;
  }

  /* 深色主题适配 */
  .theme-dark .pet-card {
    @apply bg-gray-800 border-gray-700;
  }

  .theme-dark .stat-card {
    @apply bg-gray-800/80 border-gray-700/20;
  }

  .theme-dark .loading-skeleton {
    @apply bg-gray-700;
  }

  .theme-dark .loading-shimmer {
    @apply bg-gray-700;
  }

  .theme-dark .loading-shimmer::after {
    @apply bg-gradient-to-r from-transparent via-gray-600 to-transparent;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #ee56ff, #0ea5e9);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #d934e8, #0284c7);
}

/* 动画定义 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计增强 */
@media (max-width: 640px) {
  /* 移动端优化 */
  .responsive-title {
    line-height: 1.2;
  }

  .responsive-grid {
    @apply gap-3;
  }

  .responsive-padding {
    @apply p-3;
  }

  /* 触摸优化 */
  .interactive-button,
  .game-button {
    min-height: 44px; /* iOS 推荐的最小触摸目标 */
    min-width: 44px;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* 平板端优化 */
  .responsive-grid {
    @apply grid-cols-2 gap-4;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 小桌面端优化 */
  .responsive-grid {
    @apply grid-cols-3 gap-5;
  }
}

@media (min-width: 1025px) {
  /* 大桌面端优化 */
  .responsive-grid {
    @apply grid-cols-4 gap-6;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .pet-avatar,
  .token-display {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
  .responsive-padding {
    @apply py-2 px-4;
  }

  .responsive-title {
    @apply text-xl;
  }
}

/* 打印样式 */
@media print {
  .game-button,
  .interactive-button {
    @apply shadow-none border border-gray-300;
  }

  .pet-card,
  .stat-card {
    @apply shadow-none border border-gray-300;
  }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .glow-effect,
  .interactive-card,
  .interactive-button {
    animation: none !important;
    transition: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .pet-card,
  .stat-card {
    @apply border-2 border-gray-800;
  }

  .game-button {
    @apply border-2 border-white;
  }
}
