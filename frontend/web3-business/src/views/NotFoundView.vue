<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-pink-50">
    <div class="text-center px-4">
      <div class="mb-8">
        <div class="text-8xl mb-4">🐾</div>
        <h1 class="text-6xl font-bold text-gray-800 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-600 mb-4">页面走丢了</h2>
        <p class="text-gray-500 mb-8 max-w-md mx-auto">
          看起来你要找的页面不存在，或许它和萌宠一起去玩耍了？
        </p>
      </div>

      <div class="space-y-4">
        <van-button
          type="primary"
          size="large"
          @click="goHome"
          class="w-full max-w-xs"
        >
          回到首页
        </van-button>

        <van-button
          type="default"
          size="large"
          @click="goBack"
          class="w-full max-w-xs"
        >
          返回上页
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push({ name: 'home' })
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}
</script>
