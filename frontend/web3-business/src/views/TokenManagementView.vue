<template>
  <AppLayoutWithSidebar>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">代币管理</h1>
        <p class="page-desc">管理你的游戏代币和区块链资产</p>
      </div>
    </section>

    <!-- 代币管理内容区域 -->
    <section class="token-content-section">
      <div class="container">
        <!-- 钱包连接状态 -->
        <div v-if="!walletStore.isConnected" class="wallet-connect-card">
          <div class="connect-animation">
            <div class="wallet-icon">💳</div>
            <h3 class="connect-title">连接你的钱包</h3>
            <p class="connect-desc">开始管理你的萌宠代币</p>
            <van-button
              type="primary"
              @click="connectWallet"
              class="connect-button"
              size="large"
            >
              <van-icon name="plus" />
              连接钱包
            </van-button>
          </div>
        </div>

        <!-- 主要内容 -->
        <div v-else class="token-main-content">
          <!-- 余额卡片 -->
          <div class="balance-section">
            <div class="balance-card">
              <div class="balance-header">
                <div class="balance-icon">💎</div>
                <h3 class="balance-title">我的代币</h3>
                <van-button
                  size="small"
                  @click="refreshBalance"
                  :loading="balanceLoading"
                  class="refresh-btn"
                  round
                >
                  <van-icon name="replay" />
                </van-button>
              </div>

              <div class="balance-amount">
                <div class="amount-value">
                  {{ tokenBalance?.balanceFormatted || '0.000000' }}
                </div>
                <div class="amount-symbol">
                  {{ tokenBalance?.symbol || 'TOKEN' }}
                </div>
              </div>

              <div class="balance-info">
                <div class="wallet-address">
                  <span class="address-label">钱包地址:</span>
                  <span class="address-value">{{ walletStore.shortAddress }}</span>
                </div>
                <span class="usd-value">最后更新: {{ formatTime(tokenBalance?.lastUpdated) }}</span>
              </div>
            </div>
          </div>

          <!-- 功能按钮区域 -->
          <div class="action-buttons">
            <div class="action-grid">
              <div class="action-item" @click="showTransferDialog = true">
                <div class="action-icon transfer">💸</div>
                <span class="action-label">转账</span>
              </div>
              <div class="action-item" @click="showReceiveDialog = true">
                <div class="action-icon receive">📥</div>
                <span class="action-label">收款</span>
              </div>
              <div class="action-item" @click="showHistoryDialog = true">
                <div class="action-icon history">📊</div>
                <span class="action-label">历史</span>
              </div>
              <div class="action-item" @click="showStatsDialog = true">
                <div class="action-icon stats">📈</div>
                <span class="action-label">统计</span>
              </div>
            </div>
          </div>

          <!-- 统计卡片区域 -->
          <div class="stats-section">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats?.totalTransactions || 0 }}</div>
                  <div class="stat-label">总交易数</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatTokenAmount(stats?.totalSent) }}</div>
                  <div class="stat-label">总发送量</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">⚡</div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatTokenAmount(stats?.averageTransactionAmount) }}</div>
                  <div class="stat-label">平均交易额</div>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-content">
                  <div class="stat-value">{{ formatTokenAmount(stats?.totalReceived) }}</div>
                  <div class="stat-label">总接收量</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 交易历史 -->
          <div class="px-4">
            <van-card title="交易历史" class="history-card">
              <template #tags>
                <van-button
                  size="mini"
                  type="danger"
                  plain
                  @click="clearHistory"
                  v-if="transactionHistory.length > 0"
                >
                  清除历史
                </van-button>
              </template>
              <template #desc>
                <div v-if="transactionHistory.length === 0" class="text-center py-8 text-gray-500">
                  暂无交易记录
                </div>
                <div v-else class="transaction-list">
                  <div
                    v-for="tx in displayedTransactions"
                    :key="tx.hash + tx.timestamp"
                    class="transaction-item"
                    @click="showTransactionDetail(tx)"
                  >
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <van-icon
                          :name="getTransactionIcon(tx)"
                          :color="getTransactionColor(tx)"
                          size="20"
                          class="mr-3"
                        />
                        <div>
                          <div class="font-medium">
                            {{ getTransactionTitle(tx) }}
                          </div>
                          <div class="text-sm text-gray-500">
                            {{ formatTime(tx.timestamp) }}
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div
                          class="font-medium"
                          :class="getTransactionAmountClass(tx)"
                        >
                          {{ getTransactionAmountDisplay(tx) }}
                        </div>
                        <div class="text-xs">
                          <van-tag
                            :type="getStatusTagType(tx.status)"
                            size="mini"
                          >
                            {{ getStatusText(tx.status) }}
                          </van-tag>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 加载更多 -->
                  <div v-if="transactionHistory.length > displayLimit" class="text-center mt-4">
                    <van-button
                      size="small"
                      type="primary"
                      plain
                      @click="loadMoreTransactions"
                    >
                      加载更多 ({{ transactionHistory.length - displayLimit }} 条)
                    </van-button>
                  </div>
                </div>
              </template>
            </van-card>
          </div>
        </div>
      </div>
    </section>

    <!-- 转账对话框 -->
    <van-dialog
      v-model:show="showTransferDialog"
      title="转账代币"
      show-cancel-button
      @confirm="handleTransfer"
      :confirm-button-loading="transferLoading"
    >
      <div class="p-4">
        <van-form @submit="handleTransfer">
          <van-field
            v-model="transferForm.to"
            label="接收地址"
            placeholder="请输入接收地址"
            required
            :rules="[{ required: true, message: '请输入接收地址' }]"
          />
          <van-field
            v-model="transferForm.amount"
            label="转账金额"
            placeholder="请输入转账金额"
            type="number"
            required
            :rules="[
              { required: true, message: '请输入转账金额' },
              { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字' }
            ]"
          />
          <van-field label="使用V2合约">
            <template #input>
              <van-switch v-model="transferForm.useV2Contract" />
            </template>
          </van-field>

          <!-- V2合约税收预览 -->
          <div v-if="transferForm.useV2Contract && transferPreview" class="mt-4 p-3 bg-gray-50 rounded">
            <div class="text-sm text-gray-600 mb-2">税收预览:</div>
            <div class="text-xs space-y-1">
              <div>实际到账: {{ formatTokenAmount(transferPreview.netAmount.toString()) }}</div>
              <div>税收金额: {{ formatTokenAmount(transferPreview.taxAmount.toString()) }}</div>
              <div>税率: {{ taxInfo?.currentTaxRate ? Number(taxInfo.currentTaxRate) / 100 : 0 }}%</div>
            </div>
          </div>
        </van-form>
      </div>
    </van-dialog>

    <!-- 批量转账对话框 -->
    <van-dialog
      v-model:show="showBatchTransferDialog"
      title="批量转账"
      show-cancel-button
      @confirm="handleBatchTransfer"
      :confirm-button-loading="batchTransferLoading"
    >
      <div class="p-4">
        <div class="mb-4">
          <van-button
            size="small"
            type="primary"
            @click="addBatchRecipient"
            icon="plus"
          >
            添加接收者
          </van-button>
        </div>

        <div v-for="(recipient, index) in batchTransferForm.recipients" :key="index" class="mb-4 p-3 border rounded">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium">接收者 {{ index + 1 }}</span>
            <van-button
              size="mini"
              type="danger"
              plain
              @click="removeBatchRecipient(index)"
              v-if="batchTransferForm.recipients.length > 1"
            >
              删除
            </van-button>
          </div>
          <van-field
            v-model="recipient.address"
            placeholder="接收地址"
            required
          />
          <van-field
            v-model="recipient.amount"
            placeholder="转账金额"
            type="number"
            required
          />
        </div>

        <div class="mt-4 p-3 bg-gray-50 rounded">
          <div class="text-sm text-gray-600">
            总金额: {{ calculateBatchTotal() }} {{ tokenBalance?.symbol || 'TOKEN' }}
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 税收信息对话框 -->
    <van-dialog
      v-model:show="showTaxInfoDialog"
      title="V2合约税收信息"
      show-cancel-button
      cancel-button-text="关闭"
      :show-confirm-button="false"
    >
      <div class="p-4" v-if="taxInfo">
        <van-cell-group>
          <van-cell title="当前税率" :value="`${Number(taxInfo.currentTaxRate) / 100}%`" />
          <van-cell title="税收收集者" :value="taxInfo.currentTaxCollector" />
          <van-cell title="总收集税费" :value="formatTokenAmount(taxInfo.totalCollected.toString())" />
          <van-cell title="税收状态" :value="taxInfo.taxEnabled ? '已启用' : '已禁用'" />
        </van-cell-group>
      </div>
      <div v-else class="p-4 text-center text-gray-500">
        加载税收信息失败
      </div>
    </van-dialog>

    <!-- 交易详情对话框 -->
    <van-dialog
      v-model:show="showTransactionDetailDialog"
      title="交易详情"
      show-cancel-button
      cancel-button-text="关闭"
      :show-confirm-button="false"
    >
      <div class="p-4" v-if="selectedTransaction">
        <van-cell-group>
          <van-cell title="交易哈希" :value="selectedTransaction.hash" />
          <van-cell title="发送地址" :value="selectedTransaction.from" />
          <van-cell title="接收地址" :value="selectedTransaction.to" />
          <van-cell title="金额" :value="`${selectedTransaction.amountFormatted} ${tokenBalance?.symbol || 'TOKEN'}`" />
          <van-cell title="状态" :value="getStatusText(selectedTransaction.status)" />
          <van-cell title="时间" :value="formatFullTime(selectedTransaction.timestamp)" />
          <van-cell v-if="selectedTransaction.blockNumber" title="区块号" :value="selectedTransaction.blockNumber.toString()" />
          <van-cell v-if="selectedTransaction.gasUsed" title="Gas使用" :value="selectedTransaction.gasUsed" />
          <van-cell v-if="selectedTransaction.taxAmount" title="税收金额" :value="formatTokenAmount(selectedTransaction.taxAmount)" />
          <van-cell v-if="selectedTransaction.netAmount" title="实际到账" :value="formatTokenAmount(selectedTransaction.netAmount)" />
        </van-cell-group>
      </div>
    </van-dialog>
  </AppLayoutWithSidebar>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useWalletStore } from '../stores/wallet'
import { tokenManagementService } from '../services/token-management.service'
import AppLayoutWithSidebar from '../components/layout/AppLayoutWithSidebar.vue'
import type {
  TokenBalance,
  TransferRequest,
  TransactionHistory,
  TokenStats
} from '../services/token-management.service'
import type { TaxInfo, TransferPreview } from '../services/contract.service'
import { showToast, showDialog } from 'vant'
import { ethers } from 'ethers'

const router = useRouter()
const walletStore = useWalletStore()

// 响应式数据
const tokenBalance = ref<TokenBalance | null>(null)
const transactionHistory = ref<TransactionHistory[]>([])
const stats = ref<TokenStats>({
  totalTransactions: 0,
  totalSent: '0',
  totalReceived: '0',
  totalTaxPaid: '0',
  averageTransactionAmount: '0',
  lastTransactionTime: 0
})
const taxInfo = ref<TaxInfo | null>(null)
const transferPreview = ref<TransferPreview | null>(null)

// 加载状态
const balanceLoading = ref(false)
const transferLoading = ref(false)
const batchTransferLoading = ref(false)

// 对话框状态
const showTransferDialog = ref(false)
const showBatchTransferDialog = ref(false)
const showTaxInfoDialog = ref(false)
const showTransactionDetailDialog = ref(false)
const showReceiveDialog = ref(false)
const showHistoryDialog = ref(false)
const showStatsDialog = ref(false)

// 表单数据
const transferForm = ref<TransferRequest & { useV2Contract: boolean }>({
  to: '',
  amount: '',
  useV2Contract: false
})

const batchTransferForm = ref({
  recipients: [{ address: '', amount: '' }]
})

const selectedTransaction = ref<TransactionHistory | null>(null)

// 显示控制
const displayLimit = ref(10)

// 计算属性
const displayedTransactions = computed(() => {
  return transactionHistory.value.slice(0, displayLimit.value)
})

// 方法
const connectWallet = async () => {
  // 这里应该调用钱包连接服务
  showToast('请使用钱包连接组件')
}

const refreshBalance = async () => {
  if (!walletStore.address) return

  balanceLoading.value = true
  try {
    tokenBalance.value = await tokenManagementService.refreshBalance(walletStore.address)
    showToast('余额已更新')
  } catch (error) {
    console.error('刷新余额失败:', error)
    showToast('刷新余额失败')
  } finally {
    balanceLoading.value = false
  }
}

const loadBalance = async () => {
  if (!walletStore.address) return

  try {
    tokenBalance.value = await tokenManagementService.getBalance(walletStore.address)
  } catch (error) {
    console.error('加载余额失败:', error)
  }
}

const loadTransactionHistory = () => {
  if (!walletStore.address) return

  transactionHistory.value = tokenManagementService.getTransactionHistory(walletStore.address)
  stats.value = tokenManagementService.getTokenStats(walletStore.address)
}

const loadTaxInfo = async () => {
  try {
    taxInfo.value = await tokenManagementService.getTaxInfo()
  } catch (error) {
    console.error('加载税收信息失败:', error)
  }
}

const handleTransfer = async () => {
  if (!walletStore.address) {
    showToast('请先连接钱包')
    return
  }

  if (!transferForm.value.to || !transferForm.value.amount) {
    showToast('请填写完整信息')
    return
  }

  transferLoading.value = true
  try {
    await tokenManagementService.transfer(
      transferForm.value,
      walletStore.address
    )

    showToast('转账成功')
    showTransferDialog.value = false

    // 重置表单
    transferForm.value = {
      to: '',
      amount: '',
      useV2Contract: false
    }

    // 刷新数据
    await loadBalance()
    loadTransactionHistory()

  } catch (error) {
    console.error('转账失败:', error)
    showToast(error instanceof Error ? error.message : '转账失败')
  } finally {
    transferLoading.value = false
  }
}

const handleBatchTransfer = async () => {
  if (!walletStore.address) {
    showToast('请先连接钱包')
    return
  }

  const recipients = batchTransferForm.value.recipients
    .filter(r => r.address && r.amount)

  if (recipients.length === 0) {
    showToast('请至少添加一个有效的接收者')
    return
  }

  batchTransferLoading.value = true
  try {
    const addresses = recipients.map(r => r.address)
    const amounts = recipients.map(r => r.amount)

    await tokenManagementService.batchTransfer(addresses, amounts, walletStore.address)

    showToast('批量转账成功')
    showBatchTransferDialog.value = false

    // 重置表单
    batchTransferForm.value = {
      recipients: [{ address: '', amount: '' }]
    }

    // 刷新数据
    await loadBalance()
    loadTransactionHistory()

  } catch (error) {
    console.error('批量转账失败:', error)
    showToast(error instanceof Error ? error.message : '批量转账失败')
  } finally {
    batchTransferLoading.value = false
  }
}

const addBatchRecipient = () => {
  batchTransferForm.value.recipients.push({ address: '', amount: '' })
}

const removeBatchRecipient = (index: number) => {
  batchTransferForm.value.recipients.splice(index, 1)
}

const calculateBatchTotal = () => {
  return batchTransferForm.value.recipients
    .reduce((total, recipient) => {
      const amount = parseFloat(recipient.amount) || 0
      return total + amount
    }, 0)
    .toFixed(6)
}

const clearHistory = async () => {
  try {
    await showDialog({
      title: '确认清除',
      message: '确定要清除所有交易历史吗？此操作不可恢复。',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    tokenManagementService.clearTransactionHistory()
    loadTransactionHistory()
    showToast('交易历史已清除')
  } catch {
    // 用户取消了操作
  }
}

const loadMoreTransactions = () => {
  displayLimit.value += 10
}

const showTransactionDetail = (transaction: TransactionHistory) => {
  selectedTransaction.value = transaction
  showTransactionDetailDialog.value = true
}

// 工具函数
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '未知'

  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const formatFullTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatTokenAmount = (amount: string | undefined) => {
  if (!amount) return '0'
  try {
    const formatted = ethers.formatEther(amount)
    const num = parseFloat(formatted)
    if (num === 0) return '0'
    if (num < 0.000001) return '<0.000001'
    return num.toFixed(6)
  } catch {
    return '0'
  }
}

const getTransactionIcon = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return 'arrow-up'
    case 'receive': return 'arrow-down'
    case 'mint': return 'plus'
    case 'burn': return 'minus'
    default: return 'exchange'
  }
}

const getTransactionColor = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return '#ff4444'
    case 'receive': return '#00aa00'
    case 'mint': return '#0066ff'
    case 'burn': return '#ff6600'
    default: return '#666666'
  }
}

const getTransactionTitle = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return '发送代币'
    case 'receive': return '接收代币'
    case 'mint': return '铸造代币'
    case 'burn': return '销毁代币'
    default: return '代币交易'
  }
}

const getTransactionAmountClass = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return 'text-red-500'
    case 'receive': return 'text-green-500'
    case 'mint': return 'text-blue-500'
    case 'burn': return 'text-orange-500'
    default: return 'text-gray-500'
  }
}

const getTransactionAmountDisplay = (tx: TransactionHistory) => {
  const prefix = tx.type === 'send' ? '-' : '+'
  return `${prefix}${tx.amountFormatted}`
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'confirmed': return 'success'
    case 'pending': return 'warning'
    case 'failed': return 'danger'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'confirmed': return '已确认'
    case 'pending': return '待确认'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 监听转账表单变化，更新税收预览
watch(
  () => [transferForm.value.amount, transferForm.value.useV2Contract],
  async ([amount, useV2]) => {
    if (useV2 && amount && typeof amount === 'string' && walletStore.address) {
      try {
        const amountWei = ethers.parseEther(amount).toString()
        transferPreview.value = await tokenManagementService.previewV2Transfer(
          walletStore.address,
          transferForm.value.to || '******************************************',
          amountWei
        )
      } catch (error) {
        console.error('预览转账失败:', error)
        transferPreview.value = null
      }
    } else {
      transferPreview.value = null
    }
  }
)

// 生命周期
onMounted(async () => {
  if (walletStore.isConnected && walletStore.address) {
    await loadBalance()
    loadTransactionHistory()
    await loadTaxInfo()
  }

  // 监听事件
  tokenManagementService.addEventListener('balanceUpdated', () => {
    loadBalance()
  })

  tokenManagementService.addEventListener('transactionAdded', () => {
    loadTransactionHistory()
  })

  tokenManagementService.addEventListener('transferCompleted', () => {
    loadBalance()
    loadTransactionHistory()
  })
})

onUnmounted(() => {
  // 清理事件监听器
  tokenManagementService.cleanup()
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* 代币内容区域样式 */
.token-content-section {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 钱包连接卡片样式 */
.wallet-connect-card {
  background: white;
  border-radius: 18px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.connect-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.wallet-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.connect-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.connect-desc {
  color: #6b7280;
  margin-bottom: 2rem;
}

.connect-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.connect-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 主要内容区域样式 */
.token-main-content {
  padding-bottom: 2rem;
}

/* 余额卡片样式 */
.balance-section {
  margin-bottom: 2rem;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 18px;
  padding: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.balance-icon {
  font-size: 2rem;
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.balance-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.balance-amount {
  text-align: center;
  margin-bottom: 1.5rem;
}

.amount-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.amount-symbol {
  font-size: 1rem;
  opacity: 0.8;
  font-weight: 500;
}

.balance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.9;
}

.wallet-address {
  display: flex;
  flex-direction: column;
}

.address-label {
  opacity: 0.7;
  margin-bottom: 0.25rem;
}

.address-value {
  font-family: monospace;
  font-weight: 600;
}

/* 功能按钮样式 */
.action-buttons {
  margin-bottom: 2rem;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.action-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.action-item:hover .action-icon {
  transform: scale(1.2);
}

.action-icon.transfer { animation: bounce 2s infinite; }
.action-icon.receive { animation: pulse 2s infinite; }
.action-icon.history { animation: fadeInOut 2s infinite; }
.action-icon.stats { animation: wiggle 2s infinite; }

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

.action-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2rem;
  margin-right: 1rem;
  animation: float 3s ease-in-out infinite;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .balance-card {
    padding: 1.5rem;
    margin: 0 0.5rem 1.5rem;
  }

  .amount-value {
    font-size: 2rem;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .action-item {
    padding: 1rem 0.75rem;
  }

  .action-icon {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 0.5rem;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 2rem 0 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .balance-card {
    padding: 1rem;
  }

  .amount-value {
    font-size: 1.75rem;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .action-item {
    padding: 0.75rem 0.5rem;
  }

  .balance-info {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
</style>
