<template>
  <div class="token-management-view">
    <!-- 页面标题 -->
    <van-nav-bar
      title="代币管理"
      left-arrow
      @click-left="$router.back()"
      class="bg-gradient-to-r from-purple-500 to-pink-500 text-white"
    />

    <!-- 钱包连接状态 -->
    <div v-if="!walletStore.isConnected" class="p-4">
      <van-empty
        image="network"
        description="请先连接钱包"
      >
        <van-button
          type="primary"
          @click="connectWallet"
          class="mt-4"
        >
          连接钱包
        </van-button>
      </van-empty>
    </div>

    <!-- 主要内容 -->
    <div v-else class="pb-20">
      <!-- 余额卡片 -->
      <div class="p-4">
        <van-card class="balance-card bg-gradient-to-br from-blue-500 to-purple-600 text-white">
          <template #title>
            <div class="flex items-center justify-between">
              <span class="text-lg font-bold">代币余额</span>
              <van-button
                size="small"
                type="primary"
                plain
                @click="refreshBalance"
                :loading="balanceLoading"
                class="text-white border-white"
              >
                刷新
              </van-button>
            </div>
          </template>
          <template #desc>
            <div class="mt-2">
              <div class="text-2xl font-bold mb-1">
                {{ tokenBalance?.balanceFormatted || '0.000000' }} {{ tokenBalance?.symbol || 'TOKEN' }}
              </div>
              <div class="text-sm opacity-80">
                钱包地址: {{ walletStore.shortAddress }}
              </div>
              <div class="text-xs opacity-60 mt-1">
                最后更新: {{ formatTime(tokenBalance?.lastUpdated) }}
              </div>
            </div>
          </template>
        </van-card>
      </div>

      <!-- 功能按钮 -->
      <div class="px-4 mb-4">
        <van-row gutter="12">
          <van-col span="8">
            <van-button
              type="primary"
              block
              @click="showTransferDialog = true"
              icon="exchange"
            >
              转账
            </van-button>
          </van-col>
          <van-col span="8">
            <van-button
              type="success"
              block
              @click="showBatchTransferDialog = true"
              icon="cluster-o"
            >
              批量转账
            </van-button>
          </van-col>
          <van-col span="8">
            <van-button
              type="warning"
              block
              @click="showTaxInfoDialog = true"
              icon="info-o"
            >
              税收信息
            </van-button>
          </van-col>
        </van-row>
      </div>

      <!-- 统计信息 -->
      <div class="px-4 mb-4">
        <van-card title="交易统计" class="stats-card">
          <template #desc>
            <van-row gutter="12" class="mt-2">
              <van-col span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ stats.totalTransactions }}</div>
                  <div class="stat-label">总交易数</div>
                </div>
              </van-col>
              <van-col span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ formatTokenAmount(stats.totalSent) }}</div>
                  <div class="stat-label">总发送</div>
                </div>
              </van-col>
              <van-col span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ formatTokenAmount(stats.totalReceived) }}</div>
                  <div class="stat-label">总接收</div>
                </div>
              </van-col>
              <van-col span="12">
                <div class="stat-item">
                  <div class="stat-value">{{ formatTokenAmount(stats.totalTaxPaid) }}</div>
                  <div class="stat-label">总税费</div>
                </div>
              </van-col>
            </van-row>
          </template>
        </van-card>
      </div>

      <!-- 交易历史 -->
      <div class="px-4">
        <van-card title="交易历史" class="history-card">
          <template #tags>
            <van-button
              size="mini"
              type="danger"
              plain
              @click="clearHistory"
              v-if="transactionHistory.length > 0"
            >
              清除历史
            </van-button>
          </template>
          <template #desc>
            <div v-if="transactionHistory.length === 0" class="text-center py-8 text-gray-500">
              暂无交易记录
            </div>
            <div v-else class="transaction-list">
              <div
                v-for="tx in displayedTransactions"
                :key="tx.hash + tx.timestamp"
                class="transaction-item"
                @click="showTransactionDetail(tx)"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <van-icon
                      :name="getTransactionIcon(tx)"
                      :color="getTransactionColor(tx)"
                      size="20"
                      class="mr-3"
                    />
                    <div>
                      <div class="font-medium">
                        {{ getTransactionTitle(tx) }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ formatTime(tx.timestamp) }}
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div
                      class="font-medium"
                      :class="getTransactionAmountClass(tx)"
                    >
                      {{ getTransactionAmountDisplay(tx) }}
                    </div>
                    <div class="text-xs">
                      <van-tag
                        :type="getStatusTagType(tx.status)"
                        size="mini"
                      >
                        {{ getStatusText(tx.status) }}
                      </van-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 加载更多 -->
              <div v-if="transactionHistory.length > displayLimit" class="text-center mt-4">
                <van-button
                  size="small"
                  type="primary"
                  plain
                  @click="loadMoreTransactions"
                >
                  加载更多 ({{ transactionHistory.length - displayLimit }} 条)
                </van-button>
              </div>
            </div>
          </template>
        </van-card>
      </div>
    </div>

    <!-- 转账对话框 -->
    <van-dialog
      v-model:show="showTransferDialog"
      title="转账代币"
      show-cancel-button
      @confirm="handleTransfer"
      :confirm-button-loading="transferLoading"
    >
      <div class="p-4">
        <van-form @submit="handleTransfer">
          <van-field
            v-model="transferForm.to"
            label="接收地址"
            placeholder="请输入接收地址"
            required
            :rules="[{ required: true, message: '请输入接收地址' }]"
          />
          <van-field
            v-model="transferForm.amount"
            label="转账金额"
            placeholder="请输入转账金额"
            type="number"
            required
            :rules="[
              { required: true, message: '请输入转账金额' },
              { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字' }
            ]"
          />
          <van-field label="使用V2合约">
            <template #input>
              <van-switch v-model="transferForm.useV2Contract" />
            </template>
          </van-field>

          <!-- V2合约税收预览 -->
          <div v-if="transferForm.useV2Contract && transferPreview" class="mt-4 p-3 bg-gray-50 rounded">
            <div class="text-sm text-gray-600 mb-2">税收预览:</div>
            <div class="text-xs space-y-1">
              <div>实际到账: {{ formatTokenAmount(transferPreview.netAmount.toString()) }}</div>
              <div>税收金额: {{ formatTokenAmount(transferPreview.taxAmount.toString()) }}</div>
              <div>税率: {{ taxInfo?.currentTaxRate ? Number(taxInfo.currentTaxRate) / 100 : 0 }}%</div>
            </div>
          </div>
        </van-form>
      </div>
    </van-dialog>

    <!-- 批量转账对话框 -->
    <van-dialog
      v-model:show="showBatchTransferDialog"
      title="批量转账"
      show-cancel-button
      @confirm="handleBatchTransfer"
      :confirm-button-loading="batchTransferLoading"
    >
      <div class="p-4">
        <div class="mb-4">
          <van-button
            size="small"
            type="primary"
            @click="addBatchRecipient"
            icon="plus"
          >
            添加接收者
          </van-button>
        </div>

        <div v-for="(recipient, index) in batchTransferForm.recipients" :key="index" class="mb-4 p-3 border rounded">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium">接收者 {{ index + 1 }}</span>
            <van-button
              size="mini"
              type="danger"
              plain
              @click="removeBatchRecipient(index)"
              v-if="batchTransferForm.recipients.length > 1"
            >
              删除
            </van-button>
          </div>
          <van-field
            v-model="recipient.address"
            placeholder="接收地址"
            required
          />
          <van-field
            v-model="recipient.amount"
            placeholder="转账金额"
            type="number"
            required
          />
        </div>

        <div class="mt-4 p-3 bg-gray-50 rounded">
          <div class="text-sm text-gray-600">
            总金额: {{ calculateBatchTotal() }} {{ tokenBalance?.symbol || 'TOKEN' }}
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 税收信息对话框 -->
    <van-dialog
      v-model:show="showTaxInfoDialog"
      title="V2合约税收信息"
      show-cancel-button
      cancel-button-text="关闭"
      :show-confirm-button="false"
    >
      <div class="p-4" v-if="taxInfo">
        <van-cell-group>
          <van-cell title="当前税率" :value="`${Number(taxInfo.currentTaxRate) / 100}%`" />
          <van-cell title="税收收集者" :value="taxInfo.currentTaxCollector" />
          <van-cell title="总收集税费" :value="formatTokenAmount(taxInfo.totalCollected.toString())" />
          <van-cell title="税收状态" :value="taxInfo.taxEnabled ? '已启用' : '已禁用'" />
        </van-cell-group>
      </div>
      <div v-else class="p-4 text-center text-gray-500">
        加载税收信息失败
      </div>
    </van-dialog>

    <!-- 交易详情对话框 -->
    <van-dialog
      v-model:show="showTransactionDetailDialog"
      title="交易详情"
      show-cancel-button
      cancel-button-text="关闭"
      :show-confirm-button="false"
    >
      <div class="p-4" v-if="selectedTransaction">
        <van-cell-group>
          <van-cell title="交易哈希" :value="selectedTransaction.hash" />
          <van-cell title="发送地址" :value="selectedTransaction.from" />
          <van-cell title="接收地址" :value="selectedTransaction.to" />
          <van-cell title="金额" :value="`${selectedTransaction.amountFormatted} ${tokenBalance?.symbol || 'TOKEN'}`" />
          <van-cell title="状态" :value="getStatusText(selectedTransaction.status)" />
          <van-cell title="时间" :value="formatFullTime(selectedTransaction.timestamp)" />
          <van-cell v-if="selectedTransaction.blockNumber" title="区块号" :value="selectedTransaction.blockNumber.toString()" />
          <van-cell v-if="selectedTransaction.gasUsed" title="Gas使用" :value="selectedTransaction.gasUsed" />
          <van-cell v-if="selectedTransaction.taxAmount" title="税收金额" :value="formatTokenAmount(selectedTransaction.taxAmount)" />
          <van-cell v-if="selectedTransaction.netAmount" title="实际到账" :value="formatTokenAmount(selectedTransaction.netAmount)" />
        </van-cell-group>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useWalletStore } from '../stores/wallet'
import { tokenManagementService } from '../services/token-management.service'
import type {
  TokenBalance,
  TransferRequest,
  TransactionHistory,
  TokenStats
} from '../services/token-management.service'
import type { TaxInfo, TransferPreview } from '../services/contract.service'
import { showToast, showDialog } from 'vant'
import { ethers } from 'ethers'

const router = useRouter()
const walletStore = useWalletStore()

// 响应式数据
const tokenBalance = ref<TokenBalance | null>(null)
const transactionHistory = ref<TransactionHistory[]>([])
const stats = ref<TokenStats>({
  totalTransactions: 0,
  totalSent: '0',
  totalReceived: '0',
  totalTaxPaid: '0',
  averageTransactionAmount: '0',
  lastTransactionTime: 0
})
const taxInfo = ref<TaxInfo | null>(null)
const transferPreview = ref<TransferPreview | null>(null)

// 加载状态
const balanceLoading = ref(false)
const transferLoading = ref(false)
const batchTransferLoading = ref(false)

// 对话框状态
const showTransferDialog = ref(false)
const showBatchTransferDialog = ref(false)
const showTaxInfoDialog = ref(false)
const showTransactionDetailDialog = ref(false)

// 表单数据
const transferForm = ref<TransferRequest & { useV2Contract: boolean }>({
  to: '',
  amount: '',
  useV2Contract: false
})

const batchTransferForm = ref({
  recipients: [{ address: '', amount: '' }]
})

const selectedTransaction = ref<TransactionHistory | null>(null)

// 显示控制
const displayLimit = ref(10)

// 计算属性
const displayedTransactions = computed(() => {
  return transactionHistory.value.slice(0, displayLimit.value)
})

// 方法
const connectWallet = async () => {
  // 这里应该调用钱包连接服务
  showToast('请使用钱包连接组件')
}

const refreshBalance = async () => {
  if (!walletStore.address) return

  balanceLoading.value = true
  try {
    tokenBalance.value = await tokenManagementService.refreshBalance(walletStore.address)
    showToast('余额已更新')
  } catch (error) {
    console.error('刷新余额失败:', error)
    showToast('刷新余额失败')
  } finally {
    balanceLoading.value = false
  }
}

const loadBalance = async () => {
  if (!walletStore.address) return

  try {
    tokenBalance.value = await tokenManagementService.getBalance(walletStore.address)
  } catch (error) {
    console.error('加载余额失败:', error)
  }
}

const loadTransactionHistory = () => {
  if (!walletStore.address) return

  transactionHistory.value = tokenManagementService.getTransactionHistory(walletStore.address)
  stats.value = tokenManagementService.getTokenStats(walletStore.address)
}

const loadTaxInfo = async () => {
  try {
    taxInfo.value = await tokenManagementService.getTaxInfo()
  } catch (error) {
    console.error('加载税收信息失败:', error)
  }
}

const handleTransfer = async () => {
  if (!walletStore.address) {
    showToast('请先连接钱包')
    return
  }

  if (!transferForm.value.to || !transferForm.value.amount) {
    showToast('请填写完整信息')
    return
  }

  transferLoading.value = true
  try {
    const transaction = await tokenManagementService.transfer(
      transferForm.value,
      walletStore.address
    )

    showToast('转账成功')
    showTransferDialog.value = false

    // 重置表单
    transferForm.value = {
      to: '',
      amount: '',
      useV2Contract: false
    }

    // 刷新数据
    await loadBalance()
    loadTransactionHistory()

  } catch (error) {
    console.error('转账失败:', error)
    showToast(error instanceof Error ? error.message : '转账失败')
  } finally {
    transferLoading.value = false
  }
}

const handleBatchTransfer = async () => {
  if (!walletStore.address) {
    showToast('请先连接钱包')
    return
  }

  const recipients = batchTransferForm.value.recipients
    .filter(r => r.address && r.amount)

  if (recipients.length === 0) {
    showToast('请至少添加一个有效的接收者')
    return
  }

  batchTransferLoading.value = true
  try {
    const addresses = recipients.map(r => r.address)
    const amounts = recipients.map(r => r.amount)

    await tokenManagementService.batchTransfer(addresses, amounts, walletStore.address)

    showToast('批量转账成功')
    showBatchTransferDialog.value = false

    // 重置表单
    batchTransferForm.value = {
      recipients: [{ address: '', amount: '' }]
    }

    // 刷新数据
    await loadBalance()
    loadTransactionHistory()

  } catch (error) {
    console.error('批量转账失败:', error)
    showToast(error instanceof Error ? error.message : '批量转账失败')
  } finally {
    batchTransferLoading.value = false
  }
}

const addBatchRecipient = () => {
  batchTransferForm.value.recipients.push({ address: '', amount: '' })
}

const removeBatchRecipient = (index: number) => {
  batchTransferForm.value.recipients.splice(index, 1)
}

const calculateBatchTotal = () => {
  return batchTransferForm.value.recipients
    .reduce((total, recipient) => {
      const amount = parseFloat(recipient.amount) || 0
      return total + amount
    }, 0)
    .toFixed(6)
}

const clearHistory = async () => {
  try {
    await showDialog({
      title: '确认清除',
      message: '确定要清除所有交易历史吗？此操作不可恢复。',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    tokenManagementService.clearTransactionHistory()
    loadTransactionHistory()
    showToast('交易历史已清除')
  } catch {
    // 用户取消了操作
  }
}

const loadMoreTransactions = () => {
  displayLimit.value += 10
}

const showTransactionDetail = (transaction: TransactionHistory) => {
  selectedTransaction.value = transaction
  showTransactionDetailDialog.value = true
}

// 工具函数
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '未知'

  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

const formatFullTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatTokenAmount = (amount: string) => {
  try {
    const formatted = ethers.formatEther(amount)
    const num = parseFloat(formatted)
    if (num === 0) return '0'
    if (num < 0.000001) return '<0.000001'
    return num.toFixed(6)
  } catch {
    return '0'
  }
}

const getTransactionIcon = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return 'arrow-up'
    case 'receive': return 'arrow-down'
    case 'mint': return 'plus'
    case 'burn': return 'minus'
    default: return 'exchange'
  }
}

const getTransactionColor = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return '#ff4444'
    case 'receive': return '#00aa00'
    case 'mint': return '#0066ff'
    case 'burn': return '#ff6600'
    default: return '#666666'
  }
}

const getTransactionTitle = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return '发送代币'
    case 'receive': return '接收代币'
    case 'mint': return '铸造代币'
    case 'burn': return '销毁代币'
    default: return '代币交易'
  }
}

const getTransactionAmountClass = (tx: TransactionHistory) => {
  switch (tx.type) {
    case 'send': return 'text-red-500'
    case 'receive': return 'text-green-500'
    case 'mint': return 'text-blue-500'
    case 'burn': return 'text-orange-500'
    default: return 'text-gray-500'
  }
}

const getTransactionAmountDisplay = (tx: TransactionHistory) => {
  const prefix = tx.type === 'send' ? '-' : '+'
  return `${prefix}${tx.amountFormatted}`
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'confirmed': return 'success'
    case 'pending': return 'warning'
    case 'failed': return 'danger'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'confirmed': return '已确认'
    case 'pending': return '待确认'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 监听转账表单变化，更新税收预览
watch(
  () => [transferForm.value.amount, transferForm.value.useV2Contract],
  async ([amount, useV2]) => {
    if (useV2 && amount && walletStore.address) {
      try {
        const amountWei = ethers.parseEther(amount).toString()
        transferPreview.value = await tokenManagementService.previewV2Transfer(
          walletStore.address,
          transferForm.value.to || '******************************************',
          amountWei
        )
      } catch (error) {
        console.error('预览转账失败:', error)
        transferPreview.value = null
      }
    } else {
      transferPreview.value = null
    }
  }
)

// 生命周期
onMounted(async () => {
  if (walletStore.isConnected && walletStore.address) {
    await loadBalance()
    loadTransactionHistory()
    await loadTaxInfo()
  }

  // 监听事件
  tokenManagementService.addEventListener('balanceUpdated', () => {
    loadBalance()
  })

  tokenManagementService.addEventListener('transactionAdded', () => {
    loadTransactionHistory()
  })

  tokenManagementService.addEventListener('transferCompleted', () => {
    loadBalance()
    loadTransactionHistory()
  })
})

onUnmounted(() => {
  // 清理事件监听器
  tokenManagementService.cleanup()
})
</script>

<style scoped>
.token-management-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.balance-card {
  border-radius: 12px;
  overflow: hidden;
}

.stats-card {
  border-radius: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.history-card {
  border-radius: 8px;
}

.transaction-list {
  max-height: 400px;
  overflow-y: auto;
}

.transaction-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.transaction-item:hover {
  background-color: #f9f9f9;
}

.transaction-item:last-child {
  border-bottom: none;
}
</style>
