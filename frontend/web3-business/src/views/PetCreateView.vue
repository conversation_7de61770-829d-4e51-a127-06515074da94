<template>
  <AppLayout>
    <!-- 创建表单 -->
    <section class="create-section">
      <div class="container">
        <div class="create-form">
          <!-- 萌宠预览 -->
        <div class="pet-preview text-center mb-6">
          <div class="preview-container bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl p-6 mb-4">
            <div class="pet-avatar w-32 h-32 mx-auto mb-4 bg-white rounded-full flex items-center justify-center text-6xl shadow-lg">
              {{ getTypeEmoji(selectedType) }}
            </div>
            <h3 class="text-lg font-bold text-gray-800">{{ petName || '未命名萌宠' }}</h3>
            <div class="text-sm text-gray-600 mt-1">{{ getTypeText(selectedType) }}</div>
          </div>
        </div>

        <!-- 萌宠名称 -->
        <div class="form-section mb-6">
          <van-field
            v-model="petName"
            label="萌宠名称"
            placeholder="给你的萌宠起个名字"
            maxlength="20"
            show-word-limit
            :rules="nameRules"
          />
        </div>

        <!-- 萌宠类型选择 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">选择萌宠类型</h4>
          <div class="type-grid grid grid-cols-4 gap-3">
            <div
              v-for="type in petTypes"
              :key="type.value"
              class="type-option bg-gray-50 rounded-lg p-3 text-center cursor-pointer transition-all duration-200"
              :class="{ 'bg-blue-100 border-2 border-blue-500': selectedType === type.value }"
              @click="selectedType = type.value"
            >
              <div class="text-2xl mb-1">{{ type.emoji }}</div>
              <div class="text-xs text-gray-600">{{ type.name }}</div>
            </div>
          </div>
        </div>

        <!-- 稀有度选择 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">稀有度</h4>
          <van-radio-group v-model="selectedRarity">
            <div class="space-y-2">
              <van-radio
                v-for="rarity in rarityOptions"
                :key="rarity.value"
                :name="rarity.value"
                :class="getRarityTextClass(rarity.value)"
              >
                <span class="flex items-center space-x-2">
                  <span>{{ rarity.name }}</span>
                  <span class="text-xs text-gray-500">({{ rarity.description }})</span>
                </span>
              </van-radio>
            </div>
          </van-radio-group>
        </div>

        <!-- 初始属性预览 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">初始属性预览</h4>
          <div class="stats-preview bg-gray-50 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div class="flex justify-between">
                <span>力量:</span>
                <span class="font-medium">{{ previewStats.strength }}</span>
              </div>
              <div class="flex justify-between">
                <span>智力:</span>
                <span class="font-medium">{{ previewStats.intelligence }}</span>
              </div>
              <div class="flex justify-between">
                <span>敏捷:</span>
                <span class="font-medium">{{ previewStats.agility }}</span>
              </div>
              <div class="flex justify-between">
                <span>魅力:</span>
                <span class="font-medium">{{ previewStats.charm }}</span>
              </div>
              <div class="flex justify-between">
                <span>体力:</span>
                <span class="font-medium">{{ previewStats.vitality }}</span>
              </div>
              <div class="flex justify-between">
                <span>幸运:</span>
                <span class="font-medium">{{ previewStats.luck }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 创建按钮 -->
        <div class="form-actions">
          <van-button
            type="primary"
            size="large"
            @click="handleCreatePet"
            :loading="creating"
            :disabled="!canCreate"
            class="w-full"
          >
            {{ creating ? '创建中...' : '创建萌宠' }}
          </van-button>
        </div>
      </div>
    </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { usePetStore } from '../stores/pet'
import type { PetType, PetRarity, PetStats } from '../types/typesWithoutCircular'
import AppLayout from '../components/layout/AppLayoutSimple.vue'

const router = useRouter()
const petStore = usePetStore()

// 响应式状态
const petName = ref('')
const selectedType = ref<PetType>('cat')
const selectedRarity = ref<PetRarity>('common')
const creating = ref(false)

// 萌宠类型选项
const petTypes = [
  { value: 'cat' as PetType, name: '猫咪', emoji: '🐱' },
  { value: 'dog' as PetType, name: '狗狗', emoji: '🐶' },
  { value: 'rabbit' as PetType, name: '兔子', emoji: '🐰' },
  { value: 'bird' as PetType, name: '小鸟', emoji: '🐦' },
  { value: 'fish' as PetType, name: '鱼儿', emoji: '🐠' },
  { value: 'dragon' as PetType, name: '龙', emoji: '🐉' },
  { value: 'unicorn' as PetType, name: '独角兽', emoji: '🦄' },
  { value: 'phoenix' as PetType, name: '凤凰', emoji: '🔥' }
]

// 稀有度选项
const rarityOptions = [
  { value: 'common' as PetRarity, name: '普通', description: '基础属性' },
  { value: 'uncommon' as PetRarity, name: '优秀', description: '属性提升20%' },
  { value: 'rare' as PetRarity, name: '稀有', description: '属性提升50%' },
  { value: 'epic' as PetRarity, name: '史诗', description: '属性提升100%' },
  { value: 'legendary' as PetRarity, name: '传说', description: '属性提升200%' },
  { value: 'mythical' as PetRarity, name: '神话', description: '属性提升400%' }
]

// 表单验证规则
const nameRules = [
  { required: true, message: '请输入萌宠名称' },
  { min: 1, max: 20, message: '名称长度应为1-20个字符' }
]

// 计算属性
const canCreate = computed(() => {
  return petName.value.trim().length > 0 && petName.value.trim().length <= 20
})

const previewStats = computed(() => {
  const baseStats = {
    strength: 10,
    intelligence: 10,
    agility: 10,
    charm: 10,
    vitality: 10,
    luck: 5
  }

  const rarityMultipliers = {
    common: 1.0,
    uncommon: 1.2,
    rare: 1.5,
    epic: 2.0,
    legendary: 3.0,
    mythical: 5.0
  }

  const multiplier = rarityMultipliers[selectedRarity.value]

  return {
    strength: Math.floor(baseStats.strength * multiplier),
    intelligence: Math.floor(baseStats.intelligence * multiplier),
    agility: Math.floor(baseStats.agility * multiplier),
    charm: Math.floor(baseStats.charm * multiplier),
    vitality: Math.floor(baseStats.vitality * multiplier),
    luck: Math.floor(baseStats.luck * multiplier)
  }
})

// 方法
const handleBack = () => {
  router.back()
}

const handleCreatePet = async () => {
  if (!canCreate.value) {
    showToast('请完善萌宠信息')
    return
  }

  creating.value = true

  try {
    const petOptions = {
      name: petName.value.trim(),
      type: selectedType.value,
      rarity: selectedRarity.value,
      customStats: previewStats.value
    }

    const newPet = await petStore.createCustomPet(petOptions)
    showToast('萌宠创建成功！')

    // 跳转到萌宠详情页
    router.push(`/pet/${newPet.id}`)
  } catch (error) {
    showToast('创建失败，请重试')
    console.error('Failed to create pet:', error)
  } finally {
    creating.value = false
  }
}

// 工具方法
const getTypeEmoji = (type: PetType): string => {
  const typeEmojis = {
    cat: '🐱',
    dog: '🐶',
    rabbit: '🐰',
    bird: '🐦',
    fish: '🐠',
    dragon: '🐉',
    unicorn: '🦄',
    phoenix: '🔥'
  }
  return typeEmojis[type] || '🐾'
}

const getTypeText = (type: PetType): string => {
  const typeTexts = {
    cat: '猫咪',
    dog: '狗狗',
    rabbit: '兔子',
    bird: '小鸟',
    fish: '鱼儿',
    dragon: '龙',
    unicorn: '独角兽',
    phoenix: '凤凰'
  }
  return typeTexts[type] || type
}

const getRarityTextClass = (rarity: PetRarity): string => {
  const rarityClasses = {
    common: 'text-gray-600',
    uncommon: 'text-green-600',
    rare: 'text-blue-600',
    epic: 'text-purple-600',
    legendary: 'text-yellow-600',
    mythical: 'text-red-600'
  }
  return rarityClasses[rarity]
}

// 监听稀有度变化，自动调整预览
watch(selectedRarity, () => {
  // 稀有度变化时可以添加一些动画效果
})
</script>

<style scoped>
/* 创建区域样式 */
.create-section {
  padding: 2rem 0;
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  min-height: calc(100vh - 200px);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.create-form {
  background: white;
  border-radius: 18px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

/* 萌宠预览样式 */
.pet-preview {
  text-align: center;
  margin-bottom: 2rem;
}

.preview-container {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border-radius: 18px;
  padding: 2rem;
  margin-bottom: 1rem;
}

.pet-avatar {
  width: 8rem;
  height: 8rem;
  margin: 0 auto 1rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.pet-avatar:hover {
  transform: scale(1.05);
}

/* 类型选择样式 */
.type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.type-option {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.type-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.type-option.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 稀有度选择样式 */
.rarity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.rarity-option {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  padding: 0.75rem 0.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.rarity-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rarity-option.selected {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 稀有度颜色 */
.rarity-option.common.selected { border-color: #6b7280; background: #f9fafb; }
.rarity-option.uncommon.selected { border-color: #059669; background: #ecfdf5; }
.rarity-option.rare.selected { border-color: #2563eb; background: #eff6ff; }
.rarity-option.epic.selected { border-color: #7c3aed; background: #f3e8ff; }
.rarity-option.legendary.selected { border-color: #d97706; background: #fffbeb; }
.rarity-option.mythical.selected { border-color: #dc2626; background: #fef2f2; }

/* 按钮样式 */
.create-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 1rem 2rem;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.create-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.create-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .create-form {
    padding: 1.5rem;
    margin: 0 0.5rem 1rem;
  }

  .type-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .rarity-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pet-avatar {
    width: 6rem;
    height: 6rem;
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .create-form {
    padding: 1rem;
  }

  .type-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
