<template>
  <div class="pet-create-view min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="创建萌宠"
      left-arrow
      @click-left="handleBack"
      class="bg-white shadow-sm"
    />

    <!-- 创建表单 -->
    <div class="container mx-auto px-4 py-6 max-w-md">
      <div class="create-form bg-white rounded-xl shadow-lg p-6">
        <!-- 萌宠预览 -->
        <div class="pet-preview text-center mb-6">
          <div class="preview-container bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl p-6 mb-4">
            <div class="pet-avatar w-32 h-32 mx-auto mb-4 bg-white rounded-full flex items-center justify-center text-6xl shadow-lg">
              {{ getTypeEmoji(selectedType) }}
            </div>
            <h3 class="text-lg font-bold text-gray-800">{{ petName || '未命名萌宠' }}</h3>
            <div class="text-sm text-gray-600 mt-1">{{ getTypeText(selectedType) }}</div>
          </div>
        </div>

        <!-- 萌宠名称 -->
        <div class="form-section mb-6">
          <van-field
            v-model="petName"
            label="萌宠名称"
            placeholder="给你的萌宠起个名字"
            maxlength="20"
            show-word-limit
            :rules="nameRules"
          />
        </div>

        <!-- 萌宠类型选择 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">选择萌宠类型</h4>
          <div class="type-grid grid grid-cols-4 gap-3">
            <div
              v-for="type in petTypes"
              :key="type.value"
              class="type-option bg-gray-50 rounded-lg p-3 text-center cursor-pointer transition-all duration-200"
              :class="{ 'bg-blue-100 border-2 border-blue-500': selectedType === type.value }"
              @click="selectedType = type.value"
            >
              <div class="text-2xl mb-1">{{ type.emoji }}</div>
              <div class="text-xs text-gray-600">{{ type.name }}</div>
            </div>
          </div>
        </div>

        <!-- 稀有度选择 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">稀有度</h4>
          <van-radio-group v-model="selectedRarity">
            <div class="space-y-2">
              <van-radio
                v-for="rarity in rarityOptions"
                :key="rarity.value"
                :name="rarity.value"
                :class="getRarityTextClass(rarity.value)"
              >
                <span class="flex items-center space-x-2">
                  <span>{{ rarity.name }}</span>
                  <span class="text-xs text-gray-500">({{ rarity.description }})</span>
                </span>
              </van-radio>
            </div>
          </van-radio-group>
        </div>

        <!-- 初始属性预览 -->
        <div class="form-section mb-6">
          <h4 class="text-md font-semibold text-gray-800 mb-3">初始属性预览</h4>
          <div class="stats-preview bg-gray-50 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div class="flex justify-between">
                <span>力量:</span>
                <span class="font-medium">{{ previewStats.strength }}</span>
              </div>
              <div class="flex justify-between">
                <span>智力:</span>
                <span class="font-medium">{{ previewStats.intelligence }}</span>
              </div>
              <div class="flex justify-between">
                <span>敏捷:</span>
                <span class="font-medium">{{ previewStats.agility }}</span>
              </div>
              <div class="flex justify-between">
                <span>魅力:</span>
                <span class="font-medium">{{ previewStats.charm }}</span>
              </div>
              <div class="flex justify-between">
                <span>体力:</span>
                <span class="font-medium">{{ previewStats.vitality }}</span>
              </div>
              <div class="flex justify-between">
                <span>幸运:</span>
                <span class="font-medium">{{ previewStats.luck }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 创建按钮 -->
        <div class="form-actions">
          <van-button
            type="primary"
            size="large"
            @click="handleCreatePet"
            :loading="creating"
            :disabled="!canCreate"
            class="w-full"
          >
            {{ creating ? '创建中...' : '创建萌宠' }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { usePetStore } from '../stores/pet'
import type { PetType, PetRarity, PetStats } from '../types/typesWithoutCircular'

const router = useRouter()
const petStore = usePetStore()

// 响应式状态
const petName = ref('')
const selectedType = ref<PetType>('cat')
const selectedRarity = ref<PetRarity>('common')
const creating = ref(false)

// 萌宠类型选项
const petTypes = [
  { value: 'cat' as PetType, name: '猫咪', emoji: '🐱' },
  { value: 'dog' as PetType, name: '狗狗', emoji: '🐶' },
  { value: 'rabbit' as PetType, name: '兔子', emoji: '🐰' },
  { value: 'bird' as PetType, name: '小鸟', emoji: '🐦' },
  { value: 'fish' as PetType, name: '鱼儿', emoji: '🐠' },
  { value: 'dragon' as PetType, name: '龙', emoji: '🐉' },
  { value: 'unicorn' as PetType, name: '独角兽', emoji: '🦄' },
  { value: 'phoenix' as PetType, name: '凤凰', emoji: '🔥' }
]

// 稀有度选项
const rarityOptions = [
  { value: 'common' as PetRarity, name: '普通', description: '基础属性' },
  { value: 'uncommon' as PetRarity, name: '优秀', description: '属性提升20%' },
  { value: 'rare' as PetRarity, name: '稀有', description: '属性提升50%' },
  { value: 'epic' as PetRarity, name: '史诗', description: '属性提升100%' },
  { value: 'legendary' as PetRarity, name: '传说', description: '属性提升200%' },
  { value: 'mythical' as PetRarity, name: '神话', description: '属性提升400%' }
]

// 表单验证规则
const nameRules = [
  { required: true, message: '请输入萌宠名称' },
  { min: 1, max: 20, message: '名称长度应为1-20个字符' }
]

// 计算属性
const canCreate = computed(() => {
  return petName.value.trim().length > 0 && petName.value.trim().length <= 20
})

const previewStats = computed(() => {
  const baseStats = {
    strength: 10,
    intelligence: 10,
    agility: 10,
    charm: 10,
    vitality: 10,
    luck: 5
  }

  const rarityMultipliers = {
    common: 1.0,
    uncommon: 1.2,
    rare: 1.5,
    epic: 2.0,
    legendary: 3.0,
    mythical: 5.0
  }

  const multiplier = rarityMultipliers[selectedRarity.value]

  return {
    strength: Math.floor(baseStats.strength * multiplier),
    intelligence: Math.floor(baseStats.intelligence * multiplier),
    agility: Math.floor(baseStats.agility * multiplier),
    charm: Math.floor(baseStats.charm * multiplier),
    vitality: Math.floor(baseStats.vitality * multiplier),
    luck: Math.floor(baseStats.luck * multiplier)
  }
})

// 方法
const handleBack = () => {
  router.back()
}

const handleCreatePet = async () => {
  if (!canCreate.value) {
    showToast('请完善萌宠信息')
    return
  }

  creating.value = true

  try {
    const petOptions = {
      name: petName.value.trim(),
      type: selectedType.value,
      rarity: selectedRarity.value,
      customStats: previewStats.value
    }

    const newPet = await petStore.createCustomPet(petOptions)
    showToast('萌宠创建成功！')

    // 跳转到萌宠详情页
    router.push(`/pet/${newPet.id}`)
  } catch (error) {
    showToast('创建失败，请重试')
    console.error('Failed to create pet:', error)
  } finally {
    creating.value = false
  }
}

// 工具方法
const getTypeEmoji = (type: PetType): string => {
  const typeEmojis = {
    cat: '🐱',
    dog: '🐶',
    rabbit: '🐰',
    bird: '🐦',
    fish: '🐠',
    dragon: '🐉',
    unicorn: '🦄',
    phoenix: '🔥'
  }
  return typeEmojis[type] || '🐾'
}

const getTypeText = (type: PetType): string => {
  const typeTexts = {
    cat: '猫咪',
    dog: '狗狗',
    rabbit: '兔子',
    bird: '小鸟',
    fish: '鱼儿',
    dragon: '龙',
    unicorn: '独角兽',
    phoenix: '凤凰'
  }
  return typeTexts[type] || type
}

const getRarityTextClass = (rarity: PetRarity): string => {
  const rarityClasses = {
    common: 'text-gray-600',
    uncommon: 'text-green-600',
    rare: 'text-blue-600',
    epic: 'text-purple-600',
    legendary: 'text-yellow-600',
    mythical: 'text-red-600'
  }
  return rarityClasses[rarity]
}

// 监听稀有度变化，自动调整预览
watch(selectedRarity, () => {
  // 稀有度变化时可以添加一些动画效果
})
</script>

<style scoped>
.pet-create-view {
  min-height: 100vh;
}

.type-option {
  transition: all 0.2s ease;
}

.type-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-preview {
  transition: all 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }

  .type-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
