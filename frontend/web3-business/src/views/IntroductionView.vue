<template>
  <AppLayout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏介绍</h1>
        <p class="page-desc">了解萌宠养成游戏的世界观和玩法</p>
      </div>
    </section>

    <!-- 游戏概览 -->
    <section class="overview-section">
      <div class="container">
        <div class="overview-wrapper">
          <div class="overview-content">
            <h2 class="section-title">游戏概览</h2>
            <p class="section-text">
              《萌宠养成》是一款基于区块链技术的养成游戏，你可以在游戏中收集、饲养、交易各种萌宠，
              并通过游戏获取真实的区块链代币奖励。
            </p>
            <p class="section-text">
              游戏结合了传统养成玩法与Web3创新机制，让你在享受养成乐趣的同时，
              也能体验区块链技术带来的所有权与价值保障。
            </p>
            <button class="primary-btn" @click="startTutorial">开始教程</button>
          </div>
          <div class="overview-image">
            <img src="/images/pets/cat/orange_striped.svg" alt="游戏概览" />
    </div>
        </div>
      </div>
    </section>

    <!-- 游戏特色 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title text-center">游戏特色</h2>
        <p class="section-desc text-center">
          探索萌宠养成游戏的独特魅力
        </p>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🌟</div>
            <h3 class="feature-title">稀有萌宠</h3>
            <p class="feature-desc">
              收集不同稀有度的萌宠，每一只都拥有独特的特性和能力。
              从普通到神话级别，总有适合你的萌宠伙伴。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📈</div>
            <h3 class="feature-title">养成系统</h3>
            <p class="feature-desc">
              精心照顾你的萌宠，满足它们的需求，提升等级和能力，
              解锁更多进化路线和特殊技能。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🛡️</div>
            <h3 class="feature-title">装备系统</h3>
            <p class="feature-desc">
              为萌宠搭配各种装备，提升属性和外观。收集稀有装备，
              打造最强萌宠阵容。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">💰</div>
            <h3 class="feature-title">代币奖励</h3>
            <p class="feature-desc">
              通过日常任务、挑战和成就获取代币奖励，代币可用于游戏内交易，
              也可以在区块链上自由交易。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 游戏流程 -->
    <section class="flow-section">
      <div class="container">
        <h2 class="section-title text-center">游戏流程</h2>
        <p class="section-desc text-center">
          了解萌宠养成的基本玩法流程
        </p>

        <div class="flow-steps">
          <div class="flow-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3 class="step-title">创建萌宠</h3>
              <p class="step-desc">选择类型、定制外观，创建属于你的第一只萌宠</p>
            </div>
          </div>

          <div class="flow-connector"></div>

          <div class="flow-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3 class="step-title">日常养成</h3>
              <p class="step-desc">喂食、玩耍、训练，照顾好萌宠的各种需求</p>
            </div>
          </div>

          <div class="flow-connector"></div>

          <div class="flow-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3 class="step-title">升级进化</h3>
              <p class="step-desc">提升等级，解锁进化，提高萌宠的能力与价值</p>
            </div>
          </div>

          <div class="flow-connector"></div>

          <div class="flow-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3 class="step-title">收集奖励</h3>
              <p class="step-desc">完成任务获取代币，购买装备和消耗品</p>
            </div>
          </div>
        </div>
    </div>
    </section>

    <!-- 代币系统 -->
    <section class="token-section">
      <div class="container">
        <div class="token-wrapper">
          <div class="token-image">
            <img src="/images/pets/cat/orange_striped.svg" alt="代币系统" />
          </div>
          <div class="token-content">
            <h2 class="section-title">代币系统</h2>
            <p class="section-text">
              游戏内使用真实的区块链代币，为你的游戏体验增添真实价值。
              通过参与游戏，你可以赚取代币，并用于以下场景：
            </p>
            <ul class="token-list">
              <li class="token-item">
                <span class="token-bullet">•</span>
                <span>购买游戏内的装备、道具和消耗品</span>
              </li>
              <li class="token-item">
                <span class="token-bullet">•</span>
                <span>与其他玩家交易稀有萌宠和装备</span>
              </li>
              <li class="token-item">
                <span class="token-bullet">•</span>
                <span>参与特殊活动和限时挑战</span>
              </li>
              <li class="token-item">
                <span class="token-bullet">•</span>
                <span>转换为其他加密货币或法币</span>
              </li>
            </ul>
            <router-link to="/tutorial" class="secondary-btn">了解更多</router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速开始 -->
    <section class="start-section">
      <div class="container">
        <div class="start-card">
          <h2 class="start-title">准备好开始冒险了吗？</h2>
          <p class="start-desc">
            立即创建你的第一只萌宠，探索区块链养成游戏的精彩世界！
          </p>
          <div class="start-actions">
            <button class="primary-btn" @click="createPet">创建萌宠</button>
            <button class="secondary-btn" @click="startTutorial">查看教程</button>
      </div>
    </div>
  </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import AppLayout from '../components/layout/AppLayout.vue'

const router = useRouter()

// 方法
const startTutorial = () => {
  router.push('/tutorial')
}

const createPet = () => {
  router.push('/pet/create')
}
</script>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.section-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  max-width: 600px;
  margin: 0 auto 3rem;
}

.section-text {
  font-size: 1.125rem;
  color: #424245;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.text-center {
  text-align: center;
}

.primary-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.secondary-btn {
  background-color: transparent;
  color: #0071e3;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.secondary-btn:hover {
  text-decoration: underline;
}

/* 页面头部 */
.page-header {
  padding: 4rem 0 2rem;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.page-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  max-width: 600px;
  margin: 0 auto;
}

/* 概览部分 */
.overview-section {
  padding: 4rem 0;
}

.overview-wrapper {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.overview-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.overview-image img {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
}

/* 特色部分 */
.features-section {
  padding: 4rem 0;
  background-color: #f5f5f7;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.feature-card {
  background-color: white;
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1rem;
}

.feature-desc {
  font-size: 1rem;
  color: #6e6e73;
  line-height: 1.6;
}

/* 游戏流程 */
.flow-section {
  padding: 4rem 0;
}

.flow-steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.flow-step {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 18px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.flow-step:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #0071e3;
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.step-desc {
  font-size: 1rem;
  color: #6e6e73;
  line-height: 1.6;
}

.flow-connector {
  display: none;
}

/* 代币系统 */
.token-section {
  padding: 4rem 0;
  background-color: #f5f5f7;
}

.token-wrapper {
  display: flex;
  flex-direction: column-reverse;
  gap: 3rem;
}

.token-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.token-image img {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
}

.token-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem;
}

.token-item {
  display: flex;
  margin-bottom: 1rem;
}

.token-bullet {
  color: #0071e3;
  font-size: 1.5rem;
  line-height: 1;
  margin-right: 1rem;
}

/* 快速开始 */
.start-section {
  padding: 4rem 0;
}

.start-card {
  background: linear-gradient(135deg, #0071e3 0%, #42a5f5 100%);
  border-radius: 18px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.start-title {
  font-size: 2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.start-desc {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.start-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.start-card .primary-btn {
  background-color: white;
  color: #0071e3;
}

.start-card .primary-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

.start-card .secondary-btn {
  color: white;
  border: 1px solid white;
}

/* 响应式调整 */
@media (min-width: 640px) {
  .page-title {
    font-size: 3.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .flow-steps {
    gap: 2rem;
  }
}

@media (min-width: 768px) {
  .overview-wrapper {
    flex-direction: row;
    align-items: center;
  }

  .overview-content,
  .overview-image {
    flex: 1;
  }

  .token-wrapper {
    flex-direction: row;
    align-items: center;
  }

  .token-content,
  .token-image {
    flex: 1;
  }

  .flow-steps {
    flex-direction: row;
    align-items: stretch;
  }

  .flow-step {
    flex-direction: column;
    flex: 1;
    text-align: center;
    align-items: center;
  }

  .step-number {
    margin-bottom: 1rem;
  }

  .flow-connector {
    display: block;
    height: 2px;
    background-color: #d2d2d7;
    margin-top: 2.5rem;
    flex: 0.5;
    position: relative;
  }

  .flow-connector:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 8px solid #d2d2d7;
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
