<template>
  <AppLayout>
    <!-- 关于内容区域 -->
    <section class="about-content-section">
      <div class="container">
        <div class="about-content">
        <div class="text-center mb-6">
          <div class="text-6xl mb-4">🐾</div>
          <h1 class="text-2xl font-bold text-gray-800 mb-2">萌宠养成代币游戏</h1>
          <p class="text-gray-600">版本 1.0.0</p>
        </div>

        <div class="space-y-6">
          <section>
            <h2 class="text-lg font-semibold text-gray-800 mb-3">游戏简介</h2>
            <p class="text-gray-600 leading-relaxed">
              这是一款基于区块链技术的萌宠养成游戏。玩家可以创建和养成自己的虚拟萌宠，
              通过喂食、训练、装备道具等方式提升萌宠的属性，最终将养成的萌宠兑换成有价值的代币。
            </p>
          </section>

          <section>
            <h2 class="text-lg font-semibold text-gray-800 mb-3">技术特色</h2>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-start space-x-2">
                <span class="text-green-500 mt-1">✓</span>
                <span>基于以太坊智能合约，保障资产安全</span>
              </li>
              <li class="flex items-start space-x-2">
                <span class="text-green-500 mt-1">✓</span>
                <span>本地数据存储，保护用户隐私</span>
              </li>
              <li class="flex items-start space-x-2">
                <span class="text-green-500 mt-1">✓</span>
                <span>响应式设计，支持多种设备</span>
              </li>
              <li class="flex items-start space-x-2">
                <span class="text-green-500 mt-1">✓</span>
                <span>实时数据同步，流畅游戏体验</span>
              </li>
            </ul>
          </section>

          <section>
            <h2 class="text-lg font-semibold text-gray-800 mb-3">开发团队</h2>
            <p class="text-gray-600">
              本游戏由专业的区块链开发团队打造，致力于为用户提供安全、有趣的游戏体验。
            </p>
          </section>

          <section>
            <h2 class="text-lg font-semibold text-gray-800 mb-3">联系我们</h2>
            <div class="space-y-2 text-gray-600">
              <p>如有问题或建议，请通过以下方式联系我们：</p>
              <p>邮箱: <EMAIL></p>
              <p>官网: https://petgame.com</p>
            </div>
          </section>
        </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '../components/layout/AppLayoutSimple.vue'
</script>

<style scoped>
/* 关于内容区域样式 */
.about-content-section {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.about-content {
  background: white;
  border-radius: 18px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

/* 游戏图标样式 */
.about-content .text-6xl {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 标题样式 */
.about-content h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.about-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

/* 段落样式 */
.about-content p {
  color: #6b7280;
  line-height: 1.7;
  margin-bottom: 1rem;
}

/* 列表样式 */
.about-content ul {
  margin-bottom: 1.5rem;
}

.about-content li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  color: #6b7280;
  line-height: 1.6;
}

.about-content li span:first-child {
  color: #10b981;
  margin-top: 0.125rem;
  margin-right: 0.5rem;
  font-weight: 600;
}

/* 版本信息样式 */
.about-content .text-gray-600 {
  color: #9ca3af;
  font-size: 0.9rem;
}

/* 分区样式 */
.about-content section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.about-content section:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .about-content {
    padding: 1.5rem;
    margin: 0 0.5rem;
  }

  .about-content .text-6xl {
    font-size: 3rem;
  }

  .about-content h1 {
    font-size: 1.75rem;
  }

  .about-content section {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .about-content {
    padding: 1rem;
  }

  .about-content .text-6xl {
    font-size: 2.5rem;
  }
}
</style>
