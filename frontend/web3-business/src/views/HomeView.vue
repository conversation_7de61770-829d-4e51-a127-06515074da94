<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useGameStore } from '../stores/game'
import { usePetStore } from '../stores/pet'
import { showDialog } from 'vant'
import IntroductionGuide from '../components/introduction/IntroductionGuide.vue'
import PetDisplay from '../components/pet/PetDisplay.vue'
import AppLayout from '../components/layout/AppLayout.vue'
import ResponsiveContainer from '../components/layout/ResponsiveContainer.vue'

const router = useRouter()
const gameStore = useGameStore()
const petStore = usePetStore()
const showIntroGuide = ref(false)

// 计算属性
const shouldShowTutorialPrompt = computed(() => {
  return gameStore.shouldShowTutorial
})

// 方法
const startTutorial = () => {
  router.push('/tutorial')
}

const skipTutorial = () => {
  showDialog({
    title: '跳过教程',
    message: '确定要跳过教程吗？你可以随时在设置中重新查看教程。',
    confirmButtonText: '跳过',
    cancelButtonText: '开始教程'
  }).then(() => {
    gameStore.setTutorialCompleted(true)
  }).catch(() => {
    startTutorial()
  })
}

const goToPets = () => {
  router.push('/pets')
}

const createPet = () => {
  router.push('/pet/create')
}

const startGame = () => {
  // 如果没有萌宠，先创建一个
  if (petStore.pets.length === 0) {
    createPet()
  } else {
    goToPets()
  }
}

// 计算属性
const averageLevel = computed(() => {
  if (petStore.petCount === 0) return 0
  const totalLevel = petStore.pets.reduce((sum, pet) => sum + pet.level, 0)
  return Math.round(totalLevel / petStore.petCount)
})

const rareCount = computed(() => {
  return petStore.pets.filter(pet =>
    ['rare', 'epic', 'legendary', 'mythical'].includes(pet.rarity)
  ).length
})

const totalValue = computed(() => {
  return petStore.pets.reduce((sum, pet) => {
    const rarityMultiplier = { common: 1, uncommon: 1.5, rare: 2, epic: 3, legendary: 5, mythical: 10 }
    return sum + (pet.level * (rarityMultiplier[pet.rarity] || 1))
  }, 0)
})

// 生命周期
onMounted(() => {
  // 如果是新用户且未完成教程，显示引导页面
  if (shouldShowTutorialPrompt.value) {
    setTimeout(() => {
      showIntroGuide.value = true
    }, 1000)
  }
})
</script>

<template>
  <AppLayout>
    <!-- Hero 区域 -->
    <section class="hero-section">
      <ResponsiveContainer max-width="xl" padding="lg">
        <div class="hero-content responsive-flex-center">
          <div class="hero-text">
            <h1 class="hero-title">萌宠养成</h1>
            <h2 class="hero-subtitle">在区块链世界中养育你专属的萌宠</h2>
            <p class="hero-desc">收集、养育、交易、赚取代币</p>

            <div class="hero-cta">
              <button class="primary-btn" @click="startGame">
                {{ petStore.pets.length > 0 ? '进入游戏' : '开始体验' }}
              </button>
              <button class="secondary-btn" @click="router.push('/introduction')">
                了解更多
              </button>
            </div>
          </div>

          <div class="hero-visual">
            <div v-if="petStore.currentPet" class="pet-display active-pet">
              <PetDisplay
                :pet="petStore.currentPet"
                size="huge"
                :interactive="true"
                :effects="['rarity', 'animation']"
                animation="happy"
              />
            </div>
            <div v-else class="pet-display empty-pet">
              <div class="pet-placeholder">
                <span>🐱</span>
                <p>创建你的第一只萌宠</p>
              </div>
            </div>
          </div>
        </div>
      </ResponsiveContainer>
    </section>

    <!-- 游戏特色 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">游戏特色</h2>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🌟</div>
            <h3 class="feature-title">稀有萌宠</h3>
            <p class="feature-desc">收集不同稀有度的萌宠，打造专属萌宠家族</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🛡️</div>
            <h3 class="feature-title">装备系统</h3>
            <p class="feature-desc">搭配各种装备，增强萌宠能力</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📈</div>
            <h3 class="feature-title">成长系统</h3>
            <p class="feature-desc">养育萌宠成长，解锁进化路线</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">💰</div>
            <h3 class="feature-title">代币奖励</h3>
            <p class="feature-desc">游戏获取真实区块链代币，可交易使用</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 萌宠展示 -->
    <section class="pets-showcase">
      <div class="container">
        <h2 class="section-title">萌宠世界</h2>
        <p class="section-desc">发现各种稀有、可爱且独特的萌宠</p>

        <div class="pets-grid">
          <!-- 常见萌宠 -->
          <div class="pet-category">
            <h3 class="category-title">常见萌宠</h3>
            <div class="pet-images">
              <img src="/images/pets/cat/orange_striped.svg" alt="橙色条纹猫" />
            </div>
            <router-link to="/pets" class="view-more">探索更多 →</router-link>
          </div>

          <!-- 稀有萌宠 -->
          <div class="pet-category">
            <h3 class="category-title">稀有萌宠</h3>
            <div class="pet-images">
              <img src="/images/pets/cat/orange_striped.svg" alt="稀有萌宠" />
            </div>
            <router-link to="/pets" class="view-more">探索更多 →</router-link>
          </div>

          <!-- 传说萌宠 -->
          <div class="pet-category">
            <h3 class="category-title">传说萌宠</h3>
            <div class="pet-images">
              <img src="/images/pets/cat/orange_striped.svg" alt="传说萌宠" />
            </div>
            <router-link to="/pets" class="view-more">探索更多 →</router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- 游戏数据 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-content">
          <h2 class="section-title">游戏数据</h2>
          <p class="section-desc">查看你的游戏进度和萌宠状态</p>

          <div class="stats-grid">
            <div class="stat-card">
              <span class="stat-value">{{ petStore.petCount }}</span>
              <span class="stat-label">萌宠总数</span>
            </div>

            <div class="stat-card">
              <span class="stat-value">{{ averageLevel }}</span>
              <span class="stat-label">平均等级</span>
            </div>

            <div class="stat-card">
              <span class="stat-value">{{ rareCount }}</span>
              <span class="stat-label">稀有萌宠</span>
            </div>

            <div class="stat-card">
              <span class="stat-value">{{ totalValue }}</span>
              <span class="stat-label">总价值</span>
            </div>
          </div>

          <div class="stats-actions">
            <button class="primary-btn" @click="router.push('/pets')">查看我的萌宠</button>
            <button class="secondary-btn" @click="router.push('/pet/create')">创建新萌宠</button>
          </div>
    </div>

        <div class="stats-image">
          <img src="/images/pets/cat/orange_striped.svg" alt="萌宠统计" />
        </div>
      </div>
    </section>

    <!-- 快速入口 -->
    <section class="quick-access">
      <div class="container">
        <h2 class="section-title">快速入口</h2>

        <div class="access-grid">
          <router-link to="/tutorial" class="access-card">
            <div class="access-icon">📚</div>
            <h3 class="access-title">游戏教程</h3>
            <p class="access-desc">学习游戏玩法与规则</p>
          </router-link>

          <router-link to="/shop" class="access-card">
            <div class="access-icon">🛒</div>
            <h3 class="access-title">游戏商店</h3>
            <p class="access-desc">购买装备与道具</p>
          </router-link>

          <router-link to="/pet/create" class="access-card">
            <div class="access-icon">✨</div>
            <h3 class="access-title">创建萌宠</h3>
            <p class="access-desc">创建专属萌宠伙伴</p>
          </router-link>

          <router-link to="/token-management" class="access-card">
            <div class="access-icon">💰</div>
            <h3 class="access-title">代币管理</h3>
            <p class="access-desc">查看余额和转账</p>
          </router-link>

          <router-link to="/settings" class="access-card">
            <div class="access-icon">⚙️</div>
            <h3 class="access-title">游戏设置</h3>
            <p class="access-desc">调整游戏配置</p>
          </router-link>
        </div>
      </div>
    </section>

    <!-- 教程提示 -->
    <div v-if="showIntroGuide" class="intro-modal">
    <IntroductionGuide
      :show="showIntroGuide"
      @close="showIntroGuide = false"
    />
    </div>
  </AppLayout>
</template>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1d1d1f;
  text-align: center;
  margin-bottom: 1rem;
}

.section-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.primary-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.secondary-btn {
  background-color: transparent;
  color: #0071e3;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  text-decoration: underline;
}

/* Hero 区域 */
.hero-section {
  padding: 6rem 0;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.hero-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  margin-bottom: 2rem;
}

.hero-cta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
}

.hero-visual {
  width: 100%;
  max-width: 300px;
  height: 300px;
  margin: 0 auto;
  position: relative;
}

.pet-display {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.active-pet {
  animation: float 6s ease-in-out infinite;
}

.empty-pet {
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  animation: float 6s ease-in-out infinite;
}

.pet-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.pet-placeholder span {
  font-size: 5rem;
  margin-bottom: 0.5rem;
}

.pet-placeholder p {
  font-size: 1rem;
  color: #6e6e73;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

/* 特色区域 */
.features-section {
  padding: 6rem 0;
  background-color: #f5f5f7;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

.feature-card {
  background-color: white;
  border-radius: 18px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1rem;
}

.feature-desc {
  font-size: 1rem;
  color: #6e6e73;
  line-height: 1.5;
}

/* 萌宠展示 */
.pets-showcase {
  padding: 6rem 0;
}

.pets-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

.pet-category {
  background-color: #f5f5f7;
  border-radius: 18px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.pet-category:hover {
  background-color: #eef1f5;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.pet-images {
  margin-bottom: 1.5rem;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-images img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.view-more {
  display: inline-block;
  color: #0071e3;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.view-more:hover {
  text-decoration: underline;
}

/* 游戏数据 */
.stats-section {
  padding: 6rem 0;
  background-color: #f5f5f7;
}

.stats-section .container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-content {
  width: 100%;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: white;
  border-radius: 18px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #0071e3;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6e6e73;
}

.stats-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
}

.stats-image {
  display: none;
}

/* 快速入口 */
.quick-access {
  padding: 6rem 0;
}

.access-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.access-card {
  background-color: #f5f5f7;
  border-radius: 18px;
  padding: 1.5rem;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.access-card:hover {
  background-color: #eef1f5;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.access-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.access-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.access-desc {
  font-size: 0.875rem;
  color: #6e6e73;
}

/* 教程模态框 */
.intro-modal {
  position: fixed;
  inset: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

/* 响应式设计 */
@media (min-width: 640px) {
  .hero-title {
    font-size: 4rem;
  }

  .hero-subtitle {
    font-size: 2rem;
  }

  .hero-visual {
    max-width: 400px;
    height: 400px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .access-grid {
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .hero-section {
    flex-direction: row;
    text-align: left;
    padding: 8rem 0;
    gap: 4rem;
  }

  .hero-cta {
    justify-content: flex-start;
  }

  .section-title, .section-desc {
    text-align: left;
  }

  .stats-section .container {
    flex-direction: row;
    align-items: center;
    gap: 4rem;
  }

  .stats-content {
    width: 60%;
    text-align: left;
  }

  .section-title, .section-desc {
    text-align: left;
    margin-left: 0;
  }

  .stats-actions {
    justify-content: flex-start;
  }

  .stats-image {
    display: block;
    width: 40%;
  }

  .stats-image img {
    width: 100%;
    max-width: 300px;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 5rem;
  }

  .hero-subtitle {
    font-size: 2.5rem;
  }

  .hero-desc {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .pets-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .access-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
</style>
