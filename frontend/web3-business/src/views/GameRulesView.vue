<template>
  <div class="game-rules-view">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="游戏规则"
      left-arrow
      @click-left="handleBack"
      class="rules-nav"
    />

    <!-- 导航菜单 -->
    <div class="px-4 mt-4">
      <IntroNavigation />
    </div>

    <!-- 主要内容 -->
    <div class="rules-content">
      <div class="rules-container">
        <GameRules />
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="rules-actions">
      <van-button
        type="primary"
        size="large"
        @click="startGame"
        class="start-button"
      >
        立即开始游戏
      </van-button>

      <div class="action-links">
        <van-button
          plain
          size="small"
          @click="viewTutorial"
        >
          查看完整教程
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useGameStore } from '../stores/game'
import { showDialog } from 'vant'
import GameRules from '../components/help/GameRules.vue'
import IntroNavigation from '../components/introduction/IntroNavigation.vue'

const router = useRouter()
const gameStore = useGameStore()

// 方法
const handleBack = () => {
  router.back()
}

const startGame = async () => {
  // 检查是否需要显示教程
  if (!gameStore.settings.tutorialCompleted) {
    const result = await showDialog({
      title: '开始游戏',
      message: '建议先完成新手教程，这样能更好地了解游戏玩法。是否现在开始教程？',
      confirmButtonText: '开始教程',
      cancelButtonText: '直接开始'
    }).catch(() => false)

    if (result) {
      router.push('/tutorial')
      return
    }
  }

  router.push('/')
}

const viewTutorial = () => {
  router.push('/tutorial')
}
</script>

<style scoped>
.game-rules-view {
  @apply min-h-screen bg-gradient-to-br from-purple-50 to-blue-50;
}

.rules-nav {
  @apply bg-white shadow-sm;
}

.rules-content {
  @apply p-4 pb-32;
}

.rules-container {
  @apply max-w-3xl mx-auto;
}

.rules-actions {
  @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 space-y-3;
}

.start-button {
  @apply w-full bg-gradient-to-r from-purple-500 to-blue-500;
}

.action-links {
  @apply flex justify-center space-x-4;
}
</style>