<template>
  <div class="shop-view">
    <van-nav-bar title="游戏商店" left-arrow @click-left="$router.back()">
      <template #right>
        <van-icon name="bag" @click="showInventory = true" />
      </template>
    </van-nav-bar>

    <!-- 用户资产显示 -->
    <div class="user-assets">
      <div class="asset-item">
        <span class="asset-icon">🪙</span>
        <span class="asset-label">金币</span>
        <span class="asset-value">{{ gameStore.coins }}</span>
      </div>
      <div class="asset-item">
        <span class="asset-icon">💎</span>
        <span class="asset-label">代币</span>
        <span class="asset-value">{{ formatTokens(gameStore.tokens) }}</span>
      </div>
      <div class="asset-item">
        <span class="asset-icon">🎒</span>
        <span class="asset-label">背包</span>
        <span class="asset-value">{{ inventoryCount }}/100</span>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="shop-filters">
      <van-search
        v-model="searchQuery"
        placeholder="搜索商品..."
        @search="handleSearch"
        @clear="handleClearSearch"
      />

      <div class="filter-row">
        <van-dropdown-menu>
          <van-dropdown-item v-model="selectedCategory" :options="categoryOptions" />
          <van-dropdown-item v-model="sortBy" :options="sortOptions" />
          <van-dropdown-item v-model="priceFilter" :options="priceFilterOptions" />
        </van-dropdown-menu>
      </div>
    </div>

    <!-- 推荐商品 -->
    <div v-if="recommendedItems.length > 0" class="recommended-section">
      <h3 class="section-title">
        <van-icon name="star" />
        为您推荐
      </h3>
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="item in recommendedItems" :key="item.id">
          <ShopItemCard :item="item" :is-recommended="true" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 限时特惠 -->
    <div v-if="limitedTimeItems.length > 0" class="limited-section">
      <h3 class="section-title">
        <van-icon name="clock" />
        限时特惠
        <van-count-down
          :time="timeUntilRefresh"
          format="HH:mm:ss"
          class="countdown"
        />
      </h3>
      <div class="limited-items">
        <ShopItemCard
          v-for="item in limitedTimeItems"
          :key="item.id"
          :item="item"
          class="limited-item"
        />
      </div>
    </div>

    <!-- 商品分类标签 -->
    <van-tabs v-model:active="activeTab" @change="handleTabChange" sticky>
      <van-tab title="全部" name="all" />
      <van-tab title="食物" name="food" />
      <van-tab title="玩具" name="toy" />
      <van-tab title="装备" name="equipment" />
      <van-tab title="药品" name="medicine" />
      <van-tab title="训练" name="training" />
    </van-tabs>

    <!-- 商品列表 -->
    <div class="shop-content">
      <van-loading v-if="shopStore.isLoading" class="loading-center">
        加载中...
      </van-loading>

      <van-empty v-else-if="filteredItems.length === 0" description="暂无商品" />

      <div v-else class="items-grid">
        <ShopItemCard
          v-for="item in paginatedItems"
          :key="item.id"
          :item="item"
        />
      </div>

      <!-- 分页 -->
      <van-pagination
        v-if="totalPages > 1"
        v-model="currentPage"
        :total-items="filteredItems.length"
        :items-per-page="itemsPerPage"
        :show-page-size="3"
        class="pagination"
      />
    </div>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <van-button
        type="primary"
        size="small"
        @click="refreshShop"
        :loading="shopStore.isLoading"
        class="refresh-btn"
      >
        <van-icon name="replay" />
        刷新商店
      </van-button>
    </div>

    <!-- 背包管理弹窗 -->
    <van-popup
      v-model:show="showInventory"
      position="right"
      :style="{ width: '100%', height: '100%' }"
    >
      <InventoryManager @close="showInventory = false" @use-item="handleUseItem" />
    </van-popup>

    <!-- 购买确认弹窗 -->
    <van-dialog
      v-model:show="showPurchaseDialog"
      title="购买确认"
      :message="purchaseDialogMessage"
      show-cancel-button
      @confirm="confirmPurchase"
      @cancel="cancelPurchase"
    />

    <!-- 购买成功提示 -->
    <van-notify
      v-model:show="showSuccessNotify"
      type="success"
      :message="successMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import { useShopStore } from '../stores/shop'
import { useGameStore } from '../stores/game'
import ShopItemCard from '../components/shop/ShopItemCard.vue'
import InventoryManager from '../components/shop/InventoryManager.vue'

const shopStore = useShopStore()
const gameStore = useGameStore()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const sortBy = ref('default')
const priceFilter = ref('all')
const activeTab = ref('all')
const currentPage = ref(1)
const itemsPerPage = 12
const showInventory = ref(false)
const showPurchaseDialog = ref(false)
const purchaseDialogMessage = ref('')
const showSuccessNotify = ref(false)
const successMessage = ref('')

// 选项配置
const categoryOptions = [
  { text: '全部分类', value: 'all' },
  { text: '食物', value: 'food' },
  { text: '玩具', value: 'toy' },
  { text: '装备', value: 'equipment' },
  { text: '药品', value: 'medicine' },
  { text: '训练用品', value: 'training' }
]

const sortOptions = [
  { text: '默认排序', value: 'default' },
  { text: '价格从低到高', value: 'price_asc' },
  { text: '价格从高到低', value: 'price_desc' },
  { text: '稀有度', value: 'rarity' },
  { text: '名称', value: 'name' }
]

const priceFilterOptions = [
  { text: '全部价格', value: 'all' },
  { text: '0-50金币', value: '0-50' },
  { text: '51-100金币', value: '51-100' },
  { text: '101-200金币', value: '101-200' },
  { text: '200+金币', value: '200+' }
]

// 计算属性
const inventoryCount = computed(() =>
  gameStore.inventory.reduce((sum, item) => sum + item.quantity, 0)
)

const recommendedItems = computed(() =>
  shopStore.getRecommendedItems(3)
)

const limitedTimeItems = computed(() =>
  shopStore.limitedTimeItems.slice(0, 3)
)

const timeUntilRefresh = computed(() => {
  // 计算到下次刷新的时间（简化实现）
  const now = Date.now()
  const nextRefresh = shopStore.lastRefreshTime + 24 * 60 * 60 * 1000 // 24小时后
  return Math.max(0, nextRefresh - now)
})

const filteredItems = computed(() => {
  let items = [...shopStore.shopItems]

  // 搜索筛选
  if (searchQuery.value) {
    items = shopStore.searchItems(searchQuery.value)
  }

  // 分类筛选
  if (activeTab.value !== 'all') {
    items = items.filter(item => item.category === activeTab.value)
  }

  if (selectedCategory.value !== 'all' && selectedCategory.value !== activeTab.value) {
    items = items.filter(item => item.category === selectedCategory.value)
  }

  // 价格筛选
  if (priceFilter.value !== 'all') {
    const [min, max] = priceFilter.value.split('-').map(Number)
    if (max) {
      items = items.filter(item => item.price >= min && item.price <= max)
    } else {
      items = items.filter(item => item.price >= min)
    }
  }

  // 排序
  switch (sortBy.value) {
    case 'price_asc':
      items = shopStore.sortItemsByPrice(true)
      break
    case 'price_desc':
      items = shopStore.sortItemsByPrice(false)
      break
    case 'rarity':
      items = shopStore.sortItemsByRarity()
      break
    case 'name':
      items.sort((a, b) => a.name.localeCompare(b.name))
      break
    default:
      // 保持默认顺序
      break
  }

  return items
})

const totalPages = computed(() =>
  Math.ceil(filteredItems.value.length / itemsPerPage)
)

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 方法
const formatTokens = (tokens: string): string => {
  const num = parseFloat(tokens)
  if (num === 0) return '0'
  if (num < 0.001) return '<0.001'
  return num.toFixed(3)
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleClearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

const handleTabChange = () => {
  currentPage.value = 1
  selectedCategory.value = activeTab.value
}

const refreshShop = async () => {
  try {
    shopStore.refreshShop()
    showToast({
      type: 'success',
      message: '商店已刷新'
    })
  } catch (error) {
    console.error('刷新商店失败:', error)
    showToast({
      type: 'fail',
      message: '刷新失败，请重试'
    })
  }
}

const handleUseItem = (item: any) => {
  showToast({
    type: 'success',
    message: `使用了 ${item.name}`
  })
}

const confirmPurchase = () => {
  showPurchaseDialog.value = false
  // 购买逻辑已在 ShopItemCard 中处理
}

const cancelPurchase = () => {
  showPurchaseDialog.value = false
}

// 监听筛选条件变化重置分页
watch([selectedCategory, sortBy, priceFilter, searchQuery], () => {
  currentPage.value = 1
})

// 组件挂载时初始化商店
onMounted(() => {
  shopStore.initializeShop()
})
</script>

<style scoped>
.shop-view {
  @apply bg-gray-50 min-h-screen pb-20;
}

.user-assets {
  @apply bg-white p-4 flex justify-around border-b shadow-sm;
}

.asset-item {
  @apply flex flex-col items-center;
}

.asset-icon {
  @apply text-2xl mb-1;
}

.asset-label {
  @apply text-xs text-gray-500 mb-1;
}

.asset-value {
  @apply font-bold text-blue-600;
}

.shop-filters {
  @apply bg-white border-b;
}

.filter-row {
  @apply px-4 pb-2;
}

.recommended-section,
.limited-section {
  @apply bg-white mb-2 p-4;
}

.section-title {
  @apply flex items-center text-lg font-bold mb-3 text-gray-800;
}

.section-title .van-icon {
  @apply mr-2 text-yellow-500;
}

.countdown {
  @apply ml-auto text-red-500 font-mono;
}

.limited-items {
  @apply flex gap-3 overflow-x-auto pb-2;
}

.limited-item {
  @apply flex-shrink-0 w-64;
}

.shop-content {
  @apply p-4;
}

.loading-center {
  @apply flex justify-center items-center h-32;
}

.items-grid {
  @apply grid grid-cols-1 gap-4;
}

@media (min-width: 640px) {
  .items-grid {
    @apply grid-cols-2;
  }
}

@media (min-width: 1024px) {
  .items-grid {
    @apply grid-cols-3;
  }
}

@media (min-width: 1280px) {
  .items-grid {
    @apply grid-cols-4;
  }
}

.pagination {
  @apply mt-6;
}

.quick-actions {
  @apply fixed bottom-4 right-4 z-10;
}

.refresh-btn {
  @apply shadow-lg;
}

.refresh-btn .van-icon {
  @apply mr-1;
}

/* 动画效果 */
.shop-view {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.recommended-section,
.limited-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
