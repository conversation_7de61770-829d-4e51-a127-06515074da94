<template>
  <AppLayout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏商店</h1>
        <p class="page-desc">为你的萌宠购买各种道具和装备</p>

        <!-- 用户资产显示 -->
        <div class="user-assets">
      <div class="asset-item">
        <span class="asset-icon">🪙</span>
        <span class="asset-label">金币</span>
        <span class="asset-value">{{ gameStore.coins }}</span>
      </div>
      <div class="asset-item">
        <span class="asset-icon">💎</span>
        <span class="asset-label">代币</span>
        <span class="asset-value">{{ formatTokens(gameStore.tokens) }}</span>
      </div>
      <div class="asset-item">
        <span class="asset-icon">🎒</span>
        <span class="asset-label">背包</span>
        <span class="asset-value">{{ inventoryCount }}/100</span>
      </div>
    </div>
    </section>

    <!-- 商店内容区域 -->
    <section class="shop-content-section">
      <div class="container">
        <!-- 搜索和筛选 -->
        <div class="shop-filters">
      <van-search
        v-model="searchQuery"
        placeholder="搜索商品..."
        @search="handleSearch"
        @clear="handleClearSearch"
      />

      <div class="filter-row">
        <van-dropdown-menu>
          <van-dropdown-item v-model="selectedCategory" :options="categoryOptions" />
          <van-dropdown-item v-model="sortBy" :options="sortOptions" />
          <van-dropdown-item v-model="priceFilter" :options="priceFilterOptions" />
        </van-dropdown-menu>
      </div>
    </div>

    <!-- 推荐商品 -->
    <div v-if="recommendedItems.length > 0" class="recommended-section">
      <h3 class="section-title">
        <van-icon name="star" />
        为您推荐
      </h3>
      <van-swipe :autoplay="3000" indicator-color="white">
        <van-swipe-item v-for="item in recommendedItems" :key="item.id">
          <ShopItemCardSimple :item="item" :is-recommended="true" />
        </van-swipe-item>
      </van-swipe>
    </div>

    <!-- 限时特惠 -->
    <div v-if="limitedTimeItems.length > 0" class="limited-section">
      <h3 class="section-title">
        <van-icon name="clock" />
        限时特惠
        <van-count-down
          :time="timeUntilRefresh"
          format="HH:mm:ss"
          class="countdown"
        />
      </h3>
      <div class="limited-items">
        <ShopItemCardSimple
          v-for="item in limitedTimeItems"
          :key="item.id"
          :item="item"
          class="limited-item"
        />
      </div>
    </div>

    <!-- 商品分类标签 -->
    <van-tabs v-model:active="activeTab" @change="handleTabChange" sticky>
      <van-tab title="全部" name="all" />
      <van-tab title="食物" name="food" />
      <van-tab title="玩具" name="toy" />
      <van-tab title="装备" name="equipment" />
      <van-tab title="药品" name="medicine" />
      <van-tab title="训练" name="training" />
    </van-tabs>

    <!-- 商品列表 -->
    <div class="shop-content">
      <van-loading v-if="shopStore.isLoading" class="loading-center">
        加载中...
      </van-loading>

      <van-empty v-else-if="filteredItems.length === 0" description="暂无商品" />

      <div v-else class="items-grid">
        <ShopItemCardSimple
          v-for="item in paginatedItems"
          :key="item.id"
          :item="item"
        />
      </div>

      <!-- 分页 -->
      <van-pagination
        v-if="totalPages > 1"
        v-model="currentPage"
        :total-items="filteredItems.length"
        :items-per-page="itemsPerPage"
        :show-page-size="3"
        class="pagination"
      />
    </div>

    <!-- 快速操作按钮 -->
    <div class="quick-actions">
      <van-button
        type="primary"
        size="small"
        @click="refreshShop"
        :loading="shopStore.isLoading"
        class="refresh-btn"
      >
        <van-icon name="replay" />
        刷新商店
      </van-button>
    </div>

    <!-- 背包管理弹窗 -->
    <van-popup
      v-model:show="showInventory"
      position="right"
      :style="{ width: '100%', height: '100%' }"
    >
      <div class="inventory-placeholder">
        <van-empty description="背包功能开发中..." />
        <van-button @click="showInventory = false" type="primary">关闭</van-button>
      </div>
    </van-popup>

    <!-- 购买确认弹窗 -->
    <van-dialog
      v-model:show="showPurchaseDialog"
      title="购买确认"
      :message="purchaseDialogMessage"
      show-cancel-button
      @confirm="confirmPurchase"
      @cancel="cancelPurchase"
    />

    <!-- 购买成功提示 -->
    <van-notify
      v-model:show="showSuccessNotify"
      type="success"
      :message="successMessage"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { showToast } from 'vant'
import { useShopStore } from '../stores/shop'
import { useGameStore } from '../stores/game'
import AppLayout from '../components/layout/AppLayoutSimple.vue'
import ShopItemCardSimple from '../components/shop/ShopItemCardSimple.vue'
// import InventoryManager from '../components/shop/InventoryManager.vue'

const shopStore = useShopStore()
const gameStore = useGameStore()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')
const sortBy = ref('default')
const priceFilter = ref('all')
const activeTab = ref('all')
const currentPage = ref(1)
const itemsPerPage = 12
const showInventory = ref(false)
const showPurchaseDialog = ref(false)
const purchaseDialogMessage = ref('')
const showSuccessNotify = ref(false)
const successMessage = ref('')

// 选项配置
const categoryOptions = [
  { text: '全部分类', value: 'all' },
  { text: '食物', value: 'food' },
  { text: '玩具', value: 'toy' },
  { text: '装备', value: 'equipment' },
  { text: '药品', value: 'medicine' },
  { text: '训练用品', value: 'training' }
]

const sortOptions = [
  { text: '默认排序', value: 'default' },
  { text: '价格从低到高', value: 'price_asc' },
  { text: '价格从高到低', value: 'price_desc' },
  { text: '稀有度', value: 'rarity' },
  { text: '名称', value: 'name' }
]

const priceFilterOptions = [
  { text: '全部价格', value: 'all' },
  { text: '0-50金币', value: '0-50' },
  { text: '51-100金币', value: '51-100' },
  { text: '101-200金币', value: '101-200' },
  { text: '200+金币', value: '200+' }
]

// 计算属性
const inventoryCount = computed(() =>
  gameStore.inventory.reduce((sum, item) => sum + item.quantity, 0)
)

const recommendedItems = computed(() =>
  shopStore.getRecommendedItems(3)
)

const limitedTimeItems = computed(() =>
  shopStore.limitedTimeItems.slice(0, 3)
)

const timeUntilRefresh = computed(() => {
  // 计算到下次刷新的时间（简化实现）
  const now = Date.now()
  const nextRefresh = shopStore.lastRefreshTime + 24 * 60 * 60 * 1000 // 24小时后
  return Math.max(0, nextRefresh - now)
})

const filteredItems = computed(() => {
  let items = [...shopStore.shopItems]

  // 搜索筛选
  if (searchQuery.value) {
    items = shopStore.searchItems(searchQuery.value)
  }

  // 分类筛选
  if (activeTab.value !== 'all') {
    items = items.filter(item => item.category === activeTab.value)
  }

  if (selectedCategory.value !== 'all' && selectedCategory.value !== activeTab.value) {
    items = items.filter(item => item.category === selectedCategory.value)
  }

  // 价格筛选
  if (priceFilter.value !== 'all') {
    const [min, max] = priceFilter.value.split('-').map(Number)
    if (max) {
      items = items.filter(item => item.price >= min && item.price <= max)
    } else {
      items = items.filter(item => item.price >= min)
    }
  }

  // 排序
  switch (sortBy.value) {
    case 'price_asc':
      items = shopStore.sortItemsByPrice(true)
      break
    case 'price_desc':
      items = shopStore.sortItemsByPrice(false)
      break
    case 'rarity':
      items = shopStore.sortItemsByRarity()
      break
    case 'name':
      items.sort((a, b) => a.name.localeCompare(b.name))
      break
    default:
      // 保持默认顺序
      break
  }

  return items
})

const totalPages = computed(() =>
  Math.ceil(filteredItems.value.length / itemsPerPage)
)

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 方法
const formatTokens = (tokens: string): string => {
  const num = parseFloat(tokens)
  if (num === 0) return '0'
  if (num < 0.001) return '<0.001'
  return num.toFixed(3)
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleClearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

const handleTabChange = () => {
  currentPage.value = 1
  selectedCategory.value = activeTab.value
}

const refreshShop = async () => {
  try {
    shopStore.refreshShop()
    showToast({
      type: 'success',
      message: '商店已刷新'
    })
  } catch (error) {
    console.error('刷新商店失败:', error)
    showToast({
      type: 'fail',
      message: '刷新失败，请重试'
    })
  }
}

const handleUseItem = (item: any) => {
  showToast({
    type: 'success',
    message: `使用了 ${item.name}`
  })
}

const confirmPurchase = () => {
  showPurchaseDialog.value = false
  // 购买逻辑已在 ShopItemCard 中处理
}

const cancelPurchase = () => {
  showPurchaseDialog.value = false
}

// 监听筛选条件变化重置分页
watch([selectedCategory, sortBy, priceFilter, searchQuery], () => {
  currentPage.value = 1
})

// 组件挂载时初始化商店
onMounted(() => {
  shopStore.initializeShop()
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 2rem 0;
}

/* 用户资产样式 */
.user-assets {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
  backdrop-filter: blur(10px);
}

.asset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.asset-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.asset-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.asset-value {
  font-size: 1.1rem;
  font-weight: 600;
}

/* 商店内容区域样式 */
.shop-content-section {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.shop-filters {
  background: white;
  border-radius: 18px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 1.5rem;
}

.filter-row {
  padding: 1rem 0;
}

.recommended-section,
.limited-section {
  background: white;
  border-radius: 18px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  padding: 2rem;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.section-title .van-icon {
  margin-right: 0.5rem;
  color: #f59e0b;
}

.countdown {
  margin-left: auto;
  color: #ef4444;
  font-family: monospace;
}

.limited-items {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.limited-item {
  flex-shrink: 0;
  width: 16rem;
}

.shop-content {
  padding: 1rem;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 8rem;
}

.items-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .items-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .items-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .items-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.pagination {
  margin-top: 2rem;
}

.quick-actions {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 10;
}

.refresh-btn {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50px;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.refresh-btn .van-icon {
  margin-right: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .user-assets {
    padding: 1rem;
  }

  .shop-filters,
  .recommended-section,
  .limited-section {
    margin: 0 0.5rem 1rem;
    padding: 1rem;
  }

  .container {
    padding: 0 0.5rem;
  }
}

/* 动画效果 */
.shop-view {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.recommended-section,
.limited-section {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
