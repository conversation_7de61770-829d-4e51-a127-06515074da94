<template>
  <AppLayout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">教程测试页面</h1>
        <p class="page-desc">测试各种教程组件的功能和交互</p>
      </div>
    </section>

    <!-- 测试区域 -->
    <section class="test-section">
      <div class="container">
        <div class="component-grid">
          <!-- 宠物介绍演示 -->
          <div class="component-card">
            <h2 class="component-title">宠物介绍演示</h2>
            <div class="component-content">
              <PetIntroDemo @demo-complete="handleDemoComplete" />
            </div>
          </div>

          <!-- 代币交换演示 -->
          <div class="component-card">
            <h2 class="component-title">代币交换演示</h2>
            <div class="component-content">
              <TokenExchangeDemo @demo-complete="handleDemoComplete" />
            </div>
          </div>

          <!-- 游戏流程演示 -->
          <div class="component-card">
            <h2 class="component-title">游戏流程演示</h2>
            <div class="component-content">
              <GameFlowDemo @demo-complete="handleDemoComplete" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 测试结果 -->
    <section class="results-section">
      <div class="container">
        <div class="results-card">
          <h2 class="results-title">演示完成情况</h2>
          <div class="results-content">
            <div class="result-item">
              <span class="result-label">已完成演示:</span>
              <span class="result-value">{{ completedDemos }}</span>
            </div>
            <div class="progress-container">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: `${(completedDemos / 3) * 100}%` }"
                ></div>
              </div>
              <div class="progress-text">{{ Math.round((completedDemos / 3) * 100) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import PetIntroDemo from '../components/tutorial/PetIntroDemo.vue'
import TokenExchangeDemo from '../components/tutorial/TokenExchangeDemo.vue'
import GameFlowDemo from '../components/tutorial/GameFlowDemo.vue'

const completedDemos = ref(0)

const handleDemoComplete = () => {
  completedDemos.value++
  console.log('Demo completed! Total:', completedDemos.value)
}
</script>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* 页面头部 */
.page-header {
  padding: 4rem 0 2rem;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.page-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  max-width: 600px;
  margin: 0 auto;
}

/* 测试区域 */
.test-section {
  padding: 2rem 0;
}

.component-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.component-card {
  background-color: white;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.component-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.component-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  padding: 1.5rem;
  margin: 0;
  border-bottom: 1px solid #f5f5f7;
}

.component-content {
  padding: 1.5rem;
}

/* 测试结果 */
.results-section {
  padding: 2rem 0 4rem;
}

.results-card {
  background-color: #f5f5f7;
  border-radius: 18px;
  padding: 2rem;
}

.results-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.result-label {
  font-size: 1rem;
  color: #6e6e73;
}

.result-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0071e3;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: #e5e5e7;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #0071e3;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6e6e73;
  min-width: 40px;
  text-align: right;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .component-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .component-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
