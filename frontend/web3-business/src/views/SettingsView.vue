<template>
  <AppLayoutWithSidebar>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏设置</h1>
        <p class="page-desc">个性化您的游戏体验</p>
      </div>
    </section>

    <ResponsiveContainer max-width="lg" padding="lg">
      <div class="settings-view">

        <!-- 设置分组 -->
        <div class="settings-groups space-responsive">

          <!-- 主题设置 -->
          <div class="settings-group">
            <h2 class="group-title">
              <span class="group-icon">🎨</span>
              主题外观
            </h2>

            <div class="settings-cards">
              <!-- 主题模式 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">主题模式</h3>
                  <p class="setting-desc">选择浅色、深色或跟随系统</p>
                </div>
                <div class="setting-control">
                  <div class="theme-selector">
                    <button
                      v-for="mode in themeModes"
                      :key="mode.value"
                      class="theme-option"
                      :class="{ active: themeConfig.mode === mode.value }"
                      @click="setThemeMode(mode.value)"
                    >
                      <span class="theme-icon">{{ mode.icon }}</span>
                      <span class="theme-label">{{ mode.label }}</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 颜色方案 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">颜色方案</h3>
                  <p class="setting-desc">选择您喜欢的主色调</p>
                </div>
                <div class="setting-control">
                  <div class="color-selector">
                    <button
                      v-for="scheme in colorSchemes"
                      :key="scheme.value"
                      class="color-option"
                      :class="{ active: themeConfig.colorScheme === scheme.value }"
                      :style="{ backgroundColor: scheme.color }"
                      @click="setColorScheme(scheme.value)"
                      :title="scheme.label"
                    >
                      <span v-if="themeConfig.colorScheme === scheme.value" class="color-check">✓</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 字体大小 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">字体大小</h3>
                  <p class="setting-desc">调整界面文字大小</p>
                </div>
                <div class="setting-control">
                  <div class="font-size-selector">
                    <button
                      v-for="size in fontSizes"
                      :key="size.value"
                      class="font-size-option"
                      :class="{ active: themeConfig.fontSize === size.value }"
                      @click="setFontSize(size.value)"
                    >
                      {{ size.label }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- 动画效果 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">动画效果</h3>
                  <p class="setting-desc">开启或关闭界面动画</p>
                </div>
                <div class="setting-control">
                  <label class="toggle-switch">
                    <input
                      type="checkbox"
                      :checked="themeConfig.animations"
                      @change="toggleAnimations"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 主题预设 -->
          <div class="settings-group">
            <h2 class="group-title">
              <span class="group-icon">🌈</span>
              主题预设
            </h2>

            <div class="preset-grid">
              <button
                v-for="preset in themePresets"
                :key="preset.name"
                class="preset-card"
                @click="applyPreset(preset)"
              >
                <div class="preset-preview">
                  <div class="preset-colors">
                    <div class="preset-color primary" :style="getPresetColor(preset, 'primary')"></div>
                    <div class="preset-color secondary" :style="getPresetColor(preset, 'secondary')"></div>
                  </div>
                  <div class="preset-mode">{{ getModeIcon(preset.mode) }}</div>
                </div>
                <div class="preset-name">{{ preset.name }}</div>
              </button>
            </div>
          </div>

          <!-- 游戏设置 -->
          <div class="settings-group">
            <h2 class="group-title">
              <span class="group-icon">🎮</span>
              游戏设置
            </h2>

            <div class="settings-cards">
              <!-- 音效设置 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">音效</h3>
                  <p class="setting-desc">开启或关闭游戏音效</p>
                </div>
                <div class="setting-control">
                  <label class="toggle-switch">
                    <input
                      type="checkbox"
                      :checked="gameSettings.soundEnabled"
                      @change="toggleSound"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>

              <!-- 通知设置 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">推送通知</h3>
                  <p class="setting-desc">接收游戏相关通知</p>
                </div>
                <div class="setting-control">
                  <label class="toggle-switch">
                    <input
                      type="checkbox"
                      :checked="gameSettings.notificationsEnabled"
                      @change="toggleNotifications"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>

              <!-- 自动保存 -->
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">自动保存</h3>
                  <p class="setting-desc">自动保存游戏进度</p>
                </div>
                <div class="setting-control">
                  <label class="toggle-switch">
                    <input
                      type="checkbox"
                      :checked="gameSettings.autoSave"
                      @change="toggleAutoSave"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据管理 -->
          <div class="settings-group">
            <h2 class="group-title">
              <span class="group-icon">💾</span>
              数据管理
            </h2>

            <div class="settings-cards">
              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">导出数据</h3>
                  <p class="setting-desc">备份您的游戏数据</p>
                </div>
                <div class="setting-control">
                  <button class="action-button secondary" @click="exportData">
                    导出
                  </button>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">导入数据</h3>
                  <p class="setting-desc">恢复之前的游戏数据</p>
                </div>
                <div class="setting-control">
                  <input
                    ref="fileInput"
                    type="file"
                    accept=".json"
                    style="display: none"
                    @change="importData"
                  />
                  <button class="action-button secondary" @click="$refs.fileInput?.click()">
                    导入
                  </button>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-info">
                  <h3 class="setting-title">重置数据</h3>
                  <p class="setting-desc">清除所有游戏数据</p>
                </div>
                <div class="setting-control">
                  <button class="action-button danger" @click="confirmReset">
                    重置
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 重置按钮 -->
          <div class="settings-actions">
            <button class="action-button secondary" @click="resetTheme">
              恢复默认主题
            </button>
            <button class="action-button primary" @click="saveSettings">
              保存设置
            </button>
          </div>
        </div>
      </div>
    </ResponsiveContainer>
  </AppLayoutWithSidebar>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showDialog, showToast } from 'vant'
import { useGlobalTheme } from '../composables/useTheme'
import type { ThemeMode, ColorScheme } from '../composables/useTheme'
import AppLayoutWithSidebar from '../components/layout/AppLayoutWithSidebar.vue'
import ResponsiveContainer from '../components/layout/ResponsiveContainer.vue'

const theme = useGlobalTheme()
const fileInput = ref<HTMLInputElement>()

// 游戏设置
const gameSettings = ref({
  soundEnabled: true,
  notificationsEnabled: true,
  autoSave: true
})

// 主题配置
const themeConfig = computed(() => theme.themeConfig.value)

// 主题模式选项
const themeModes = [
  { value: 'light' as ThemeMode, label: '浅色', icon: '☀️' },
  { value: 'dark' as ThemeMode, label: '深色', icon: '🌙' },
  { value: 'auto' as ThemeMode, label: '自动', icon: '🔄' }
]

// 颜色方案选项
const colorSchemes = [
  { value: 'purple' as ColorScheme, label: '紫色', color: '#a855f7' },
  { value: 'blue' as ColorScheme, label: '蓝色', color: '#3b82f6' },
  { value: 'green' as ColorScheme, label: '绿色', color: '#22c55e' },
  { value: 'orange' as ColorScheme, label: '橙色', color: '#f97316' },
  { value: 'pink' as ColorScheme, label: '粉色', color: '#ec4899' }
]

// 字体大小选项
const fontSizes = [
  { value: 'small' as const, label: '小' },
  { value: 'medium' as const, label: '中' },
  { value: 'large' as const, label: '大' }
]

// 主题预设
const themePresets = computed(() => theme.getThemePresets())

// 方法
const setThemeMode = (mode: ThemeMode) => {
  theme.setThemeMode(mode)
}

const setColorScheme = (scheme: ColorScheme) => {
  theme.setColorScheme(scheme)
}

const setFontSize = (size: 'small' | 'medium' | 'large') => {
  theme.setFontSize(size)
}

const toggleAnimations = () => {
  theme.toggleAnimations()
}

const applyPreset = (preset: any) => {
  theme.applyPreset(preset)
  showToast('主题预设已应用')
}

const resetTheme = () => {
  showDialog({
    title: '重置主题',
    message: '确定要恢复默认主题设置吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    theme.resetTheme()
    showToast('主题已重置')
  })
}

const getPresetColor = (preset: any, type: 'primary' | 'secondary') => {
  const colors = {
    purple: { primary: '#a855f7', secondary: '#0ea5e9' },
    blue: { primary: '#3b82f6', secondary: '#06b6d4' },
    green: { primary: '#22c55e', secondary: '#84cc16' },
    orange: { primary: '#f97316', secondary: '#eab308' },
    pink: { primary: '#ec4899', secondary: '#f43f5e' }
  }
  return { backgroundColor: colors[preset.colorScheme][type] }
}

const getModeIcon = (mode: ThemeMode) => {
  const icons = { light: '☀️', dark: '🌙', auto: '🔄' }
  return icons[mode]
}

// 游戏设置方法
const toggleSound = () => {
  gameSettings.value.soundEnabled = !gameSettings.value.soundEnabled
}

const toggleNotifications = () => {
  gameSettings.value.notificationsEnabled = !gameSettings.value.notificationsEnabled
}

const toggleAutoSave = () => {
  gameSettings.value.autoSave = !gameSettings.value.autoSave
}

// 数据管理方法
const exportData = () => {
  try {
    const data = {
      theme: themeConfig.value,
      game: gameSettings.value,
      timestamp: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `pet-game-settings-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    showToast('数据导出成功')
  } catch (error) {
    showToast('导出失败')
  }
}

const importData = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target?.result as string)

      if (data.theme) {
        Object.assign(theme.themeConfig.value, data.theme)
      }
      if (data.game) {
        Object.assign(gameSettings.value, data.game)
      }

      showToast('数据导入成功')
    } catch (error) {
      showToast('导入失败，文件格式错误')
    }
  }
  reader.readAsText(file)
}

const confirmReset = () => {
  showDialog({
    title: '重置数据',
    message: '确定要清除所有游戏数据吗？此操作不可恢复！',
    confirmButtonText: '确定重置',
    cancelButtonText: '取消'
  }).then(() => {
    // 重置所有数据
    theme.resetTheme()
    gameSettings.value = {
      soundEnabled: true,
      notificationsEnabled: true,
      autoSave: true
    }
    showToast('数据已重置')
  })
}

const saveSettings = () => {
  // 设置会自动保存，这里只是给用户反馈
  showToast('设置已保存')
}
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.settings-view {
  max-width: 100%;
}

.settings-header {
  text-align: center;
  margin-bottom: 3rem;
}

.settings-groups {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.settings-group {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.group-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.group-icon {
  font-size: 1.75rem;
}

.settings-cards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.setting-desc {
  font-size: 0.875rem;
  color: #6b7280;
}

.setting-control {
  margin-left: 1rem;
}

/* 主题选择器 */
.theme-selector {
  display: flex;
  gap: 0.5rem;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-option:hover {
  border-color: #a855f7;
}

.theme-option.active {
  border-color: #a855f7;
  background: #faf5ff;
}

.theme-icon {
  font-size: 1.25rem;
}

.theme-label {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 颜色选择器 */
.color-selector {
  display: flex;
  gap: 0.5rem;
}

.color-option {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #1f2937;
  box-shadow: 0 0 0 2px white, 0 0 0 4px #1f2937;
}

/* 字体大小选择器 */
.font-size-selector {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.font-size-option {
  padding: 0.5rem 1rem;
  border: none;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid #e5e7eb;
}

.font-size-option:last-child {
  border-right: none;
}

.font-size-option:hover {
  background: #f3f4f6;
}

.font-size-option.active {
  background: #a855f7;
  color: white;
}

/* 切换开关 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: 0.3s;
  border-radius: 1.5rem;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 1.125rem;
  width: 1.125rem;
  left: 0.1875rem;
  bottom: 0.1875rem;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #a855f7;
}

input:checked + .toggle-slider:before {
  transform: translateX(1.5rem);
}

/* 主题预设网格 */
.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.preset-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-card:hover {
  border-color: #a855f7;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(168, 85, 247, 0.2);
}

.preset-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preset-colors {
  display: flex;
  gap: 0.25rem;
}

.preset-color {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}

.preset-mode {
  font-size: 1.25rem;
}

.preset-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

/* 操作按钮 */
.action-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: #a855f7;
  color: white;
}

.action-button.primary:hover {
  background: #9333ea;
}

.action-button.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.action-button.secondary:hover {
  background: #e5e7eb;
}

.action-button.danger {
  background: #ef4444;
  color: white;
}

.action-button.danger:hover {
  background: #dc2626;
}

.settings-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding-top: 2rem;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .setting-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .setting-control {
    margin-left: 0;
    width: 100%;
  }

  .theme-selector,
  .color-selector {
    justify-content: center;
  }

  .settings-actions {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }
}

/* 深色主题适配 */
:global(.theme-dark) .settings-group {
  background: #1f2937;
}

:global(.theme-dark) .group-title {
  color: #f9fafb;
}

:global(.theme-dark) .setting-card {
  background: #374151;
  border-color: #4b5563;
}

:global(.theme-dark) .setting-title {
  color: #f9fafb;
}

:global(.theme-dark) .setting-desc {
  color: #d1d5db;
}

:global(.theme-dark) .theme-option {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

:global(.theme-dark) .theme-option.active {
  background: #581c87;
  border-color: #a855f7;
}

:global(.theme-dark) .preset-card {
  background: #374151;
  border-color: #4b5563;
}

:global(.theme-dark) .preset-name {
  color: #f9fafb;
}
</style>
