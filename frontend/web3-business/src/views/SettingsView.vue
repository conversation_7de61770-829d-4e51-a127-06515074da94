<template>
  <div class="settings-view min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="设置"
      left-arrow
      @click-left="handleBack"
      class="bg-white shadow-sm"
    />

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 游戏设置 -->
      <van-cell-group title="游戏设置" class="mb-4">
        <van-cell title="声音效果" is-link>
          <template #right-icon>
            <van-switch v-model="gameStore.settings.soundEnabled" />
          </template>
        </van-cell>
        <van-cell title="动画效果" is-link>
          <template #right-icon>
            <van-switch v-model="gameStore.settings.animationEnabled" />
          </template>
        </van-cell>
        <van-cell title="教程提示" is-link>
          <template #right-icon>
            <van-switch v-model="gameStore.settings.showTutorialHints" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 萌宠管理 -->
      <van-cell-group title="萌宠管理" class="mb-4">
        <van-cell title="我的萌宠" is-link @click="router.push('/pets')">
          <template #right-icon>
            <span class="text-gray-500">{{ petStore.petCount }} 只</span>
          </template>
        </van-cell>
        <van-cell title="创建萌宠" is-link @click="router.push('/pet/create')" />
        <van-cell title="导出数据" is-link @click="handleExportData" />
        <van-cell title="导入数据" is-link @click="handleImportData" />
      </van-cell-group>

      <!-- 帮助与支持 -->
      <van-cell-group title="帮助与支持" class="mb-4">
        <van-cell title="游戏教程" is-link @click="router.push('/tutorial')" />
        <van-cell title="游戏介绍" is-link @click="router.push('/introduction')" />
        <van-cell title="游戏规则" is-link @click="router.push('/game-rules')" />
        <van-cell title="关于游戏" is-link @click="router.push('/about')" />
      </van-cell-group>

      <!-- 数据管理 -->
      <van-cell-group title="数据管理" class="mb-4">
        <van-cell title="清除缓存" is-link @click="handleClearCache" />
        <van-cell title="重置游戏" is-link @click="handleResetGame" />
      </van-cell-group>
    </div>

    <!-- 版本信息 -->
    <div class="version-info text-center py-4 text-gray-500 text-sm">
      萌宠养成游戏 v1.0.0
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { showDialog, showToast, showConfirmDialog } from 'vant'
import { useGameStore } from '../stores/game'
import { usePetStore } from '../stores/pet'

const router = useRouter()
const gameStore = useGameStore()
const petStore = usePetStore()

// 方法
const handleBack = () => {
  router.back()
}

const handleExportData = async () => {
  try {
    const gameData = {
      pets: petStore.pets,
      gameSettings: gameStore.settings,
      exportTime: new Date().toISOString()
    }

    const dataStr = JSON.stringify(gameData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `pet-game-data-${Date.now()}.json`
    a.click()

    URL.revokeObjectURL(url)
    showToast('数据导出成功')
  } catch (error) {
    showToast('导出失败')
    console.error('Export failed:', error)
  }
}

const handleImportData = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)

        showConfirmDialog({
          title: '确认导入',
          message: '导入数据将覆盖当前游戏数据，是否继续？'
        }).then(() => {
          // 这里应该实现数据导入逻辑
          showToast('数据导入成功')
        }).catch(() => {
          // 用户取消
        })
      } catch (error) {
        showToast('文件格式错误')
      }
    }
    reader.readAsText(file)
  }

  input.click()
}

const handleClearCache = async () => {
  const confirmed = await showConfirmDialog({
    title: '清除缓存',
    message: '确定要清除游戏缓存吗？这不会影响你的萌宠数据。'
  })

  if (confirmed) {
    // 清除缓存逻辑
    localStorage.removeItem('game-cache')
    showToast('缓存清除成功')
  }
}

const handleResetGame = async () => {
  const confirmed = await showConfirmDialog({
    title: '重置游戏',
    message: '确定要重置游戏吗？这将删除所有萌宠和游戏数据，此操作不可恢复！'
  })

  if (confirmed) {
    const doubleConfirmed = await showConfirmDialog({
      title: '最终确认',
      message: '请再次确认：重置游戏将永久删除所有数据！'
    })

    if (doubleConfirmed) {
      gameStore.resetGame()
      petStore.clearPets()
      showToast('游戏已重置')
      router.push('/')
    }
  }
}
</script>

<style scoped>
.settings-view {
  min-height: 100vh;
}

.settings-content {
  padding: 1rem;
}

.version-info {
  border-top: 1px solid #eee;
  margin-top: 2rem;
}
</style>
