<template>
  <AppLayout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title">萌宠详情</h1>
          <div class="header-actions">
            <button class="icon-btn" @click="handleBack" aria-label="返回">
              <van-icon name="arrow-left" />
            </button>
            <button class="icon-btn" @click="showSettings = true" aria-label="设置">
              <van-icon name="setting-o" />
            </button>
          </div>
        </div>
      </div>
    </section>

    <div v-if="currentPet" class="pet-details-content">
      <div class="container">
        <!-- 萌宠基本信息和展示 -->
        <section class="pet-showcase">
          <div class="pet-display">
            <PetDisplay
              :pet="currentPet"
              size="huge"
              :interactive="true"
              :effects="['rarity', 'animation']"
              animation="idle"
            />
          </div>

          <div class="pet-info">
            <div class="pet-name-section">
              <h2 class="pet-name">{{ currentPet.name }}</h2>
              <button class="edit-name-btn" @click="handleEditName">
                <van-icon name="edit" />
              </button>
            </div>

            <div class="pet-meta">
              <span class="pet-type">{{ getPetTypeName(currentPet.type) }}</span>
              <span class="pet-rarity" :class="getRarityClass(currentPet.rarity)">
                {{ getRarityName(currentPet.rarity) }}
              </span>
              <span class="pet-level">等级 {{ currentPet.level }}</span>
            </div>

            <div class="pet-actions">
              <button class="primary-btn" @click="showEvolution = true">
                查看进化路线
              </button>
              <button class="secondary-btn" @click="handleCalculateValue">
                查看价值
              </button>
            </div>
          </div>
        </section>

        <!-- 萌宠属性面板 -->
        <section class="stats-section">
          <h3 class="section-title">属性面板</h3>
          <PetStatsPanel
            :pet="currentPet"
            :show-detailed="true"
            @stat-click="handleStatClick"
          />
        </section>

        <!-- 萌宠状态监控 -->
        <section class="status-section">
          <h3 class="section-title">状态监控</h3>
          <PetStatusMonitor
            :pet="currentPet"
            @feed="handleFeed"
            @play="handlePlay"
            @rest="handleRest"
            @train="handleTrain"
          />
        </section>

        <!-- 萌宠装备管理 -->
        <section class="equipment-section">
          <h3 class="section-title">装备管理</h3>
          <PetEquipmentManager
            :pet="currentPet"
            :inventory="inventory"
            @equip-item="handleEquipItem"
            @unequip-item="handleUnequipItem"
            @view-equipment="handleViewEquipment"
          />
        </section>

        <!-- 萌宠技能和特质 -->
        <section class="skills-section">
          <h3 class="section-title">技能与特质</h3>
          <PetSkillsTraits
            :pet="currentPet"
            @learn-skill="handleLearnSkill"
            @upgrade-trait="handleUpgradeTrait"
          />
        </section>

        <!-- 萌宠价值评估 -->
        <section class="value-section">
          <h3 class="section-title">价值评估</h3>
          <PetValueAssessment
            :pet="currentPet"
            @calculate-value="handleCalculateValue"
            @exchange-tokens="handleExchangeTokens"
          />
        </section>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="container">
        <div class="empty-icon">🔍</div>
        <h3 class="empty-title">未找到萌宠信息</h3>
        <p class="empty-desc">该萌宠可能不存在或已被删除</p>
        <button class="primary-btn" @click="handleBack">返回首页</button>
      </div>
    </div>

    <!-- 弹窗组件 -->
    <!-- 编辑名称弹窗 -->
    <van-dialog
      v-model:show="showNameEdit"
      title="编辑萌宠名称"
      show-cancel-button
      @confirm="confirmNameEdit"
      class="custom-dialog"
    >
      <div class="p-4">
        <van-field
          v-model="newPetName"
          placeholder="请输入新名称"
          maxlength="20"
          show-word-limit
        />
      </div>
    </van-dialog>

    <!-- 进化路径弹窗 -->
    <van-popup
      v-model:show="showEvolution"
      position="bottom"
      :style="{ height: '70%' }"
      class="custom-popup"
    >
      <PetEvolutionPath
        v-if="currentPet"
        :pet="currentPet"
        @close="showEvolution = false"
        @evolve="handleEvolve"
      />
    </van-popup>

    <!-- 装备详情弹窗 -->
    <van-popup
      v-model:show="showEquipmentDetail"
      position="center"
      :style="{ width: '90%', maxWidth: '400px' }"
      class="custom-popup"
    >
      <EquipmentDetailCard
        v-if="selectedEquipment"
        :equipment="selectedEquipment"
        @close="showEquipmentDetail = false"
        @enhance="handleEnhanceEquipment"
        @repair="handleRepairEquipment"
      />
    </van-popup>

    <!-- 设置弹窗 -->
    <van-popup
      v-model:show="showSettings"
      position="bottom"
      :style="{ height: '50%' }"
      class="custom-popup"
    >
      <PetSettingsPanel
        :pet="currentPet"
        @close="showSettings = false"
        @export="handleExportPet"
        @reset="handleResetPet"
      />
    </van-popup>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { usePetStore } from '../stores/pet'
import { useGameStore } from '../stores/game'
import type { Pet, Equipment, Item, PetRarity, PetType } from '../types/typesWithoutCircular'
import AppLayout from '../components/layout/AppLayout.vue'
import PetDisplay from '../components/pet/PetDisplay.vue'

// 导入组件
import PetBasicInfoCard from '../components/pet/PetBasicInfoCard.vue'
import PetStatsPanel from '../components/pet/PetStatsPanel.vue'
import PetEquipmentManager from '../components/pet/PetEquipmentManager.vue'
import PetStatusMonitor from '../components/pet/PetStatusMonitor.vue'
import PetSkillsTraits from '../components/pet/PetSkillsTraits.vue'
import PetValueAssessment from '../components/pet/PetValueAssessment.vue'
import PetEvolutionPath from '../components/pet/PetEvolutionPath.vue'
import EquipmentDetailCard from '../components/equipment/EquipmentDetailCard.vue'
import PetSettingsPanel from '../components/pet/PetSettingsPanel.vue'

const route = useRoute()
const router = useRouter()
const petStore = usePetStore()
const gameStore = useGameStore()

// 响应式状态
const loading = ref(false)
const showNameEdit = ref(false)
const showEvolution = ref(false)
const showEquipmentDetail = ref(false)
const showSettings = ref(false)
const newPetName = ref('')
const selectedEquipment = ref<Equipment | null>(null)

// 计算属性
const currentPet = computed(() => {
  const petId = route.params.id as string
  return petStore.getPetById(petId)
})

const inventory = computed(() => gameStore.inventory)

// 方法
const handleBack = () => {
  router.back()
}

const handleEditName = () => {
  if (currentPet.value) {
    newPetName.value = currentPet.value.name
    showNameEdit.value = true
  }
}

const confirmNameEdit = async () => {
  if (!currentPet.value || !newPetName.value.trim()) {
    showToast('请输入有效的名称')
    return
  }

  try {
    loading.value = true
    await petStore.updatePetName(currentPet.value.id, newPetName.value.trim())
    showToast('名称修改成功')
    showNameEdit.value = false
  } catch (error) {
    showToast('名称修改失败')
    console.error('Failed to update pet name:', error)
  } finally {
    loading.value = false
  }
}

const handleStatClick = (statName: string, value: number) => {
  showToast(`${statName}: ${value}`)
}

const handleEquipItem = async (equipment: Equipment) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.equipItem(currentPet.value.id, equipment)
    showToast(`已装备 ${equipment.name}`)
  } catch (error) {
    showToast('装备失败')
    console.error('Failed to equip item:', error)
  } finally {
    loading.value = false
  }
}

const handleUnequipItem = async (equipment: Equipment) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.unequipItem(currentPet.value.id, equipment.id)
    showToast(`已卸下 ${equipment.name}`)
  } catch (error) {
    showToast('卸下装备失败')
    console.error('Failed to unequip item:', error)
  } finally {
    loading.value = false
  }
}

const handleViewEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showEquipmentDetail.value = true
}

const handleFeed = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.feedPet(currentPet.value.id)
    showToast('喂食成功')
  } catch (error) {
    showToast('喂食失败')
    console.error('Failed to feed pet:', error)
  } finally {
    loading.value = false
  }
}

const handlePlay = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.playWithPet(currentPet.value.id)
    showToast('玩耍成功')
  } catch (error) {
    showToast('玩耍失败')
    console.error('Failed to play with pet:', error)
  } finally {
    loading.value = false
  }
}

const handleRest = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.restPet(currentPet.value.id)
    showToast('休息成功')
  } catch (error) {
    showToast('休息失败')
    console.error('Failed to rest pet:', error)
  } finally {
    loading.value = false
  }
}

const handleTrain = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.trainPet(currentPet.value.id, 'general')
    showToast('训练成功')
  } catch (error) {
    showToast('训练失败')
    console.error('Failed to train pet:', error)
  } finally {
    loading.value = false
  }
}

const handleLearnSkill = async (skillId: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.learnSkill(currentPet.value.id, skillId)
    showToast('技能学习成功')
  } catch (error) {
    showToast('技能学习失败')
    console.error('Failed to learn skill:', error)
  } finally {
    loading.value = false
  }
}

const handleUpgradeTrait = async (traitId: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.upgradeTrait(currentPet.value.id, traitId)
    showToast('特质升级成功')
  } catch (error) {
    showToast('特质升级失败')
    console.error('Failed to upgrade trait:', error)
  } finally {
    loading.value = false
  }
}

const handleCalculateValue = async () => {
  if (!currentPet.value) return

  try {
    loading.value = true
    const value = await petStore.calculatePetValue(currentPet.value.id)
    showToast(`萌宠价值: ${value} 代币`)
  } catch (error) {
    showToast('价值计算失败')
    console.error('Failed to calculate pet value:', error)
  } finally {
    loading.value = false
  }
}

const handleExchangeTokens = async () => {
  if (!currentPet.value) return

  const confirmed = await showDialog({
    title: '确认兑换',
    message: '兑换后萌宠将被销毁，是否继续？',
    confirmButtonText: '确认兑换',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (confirmed) {
    try {
      loading.value = true
      await petStore.exchangePetForTokens(currentPet.value.id)
      showToast('兑换成功')
      router.push('/')
    } catch (error) {
      showToast('兑换失败')
      console.error('Failed to exchange pet for tokens:', error)
    } finally {
      loading.value = false
    }
  }
}

const handleEvolve = async (evolutionPath: string) => {
  if (!currentPet.value) return

  try {
    loading.value = true
    await petStore.evolvePet(currentPet.value.id, evolutionPath)
    showToast('进化成功')
    showEvolution.value = false
  } catch (error) {
    showToast('进化失败')
    console.error('Failed to evolve pet:', error)
  } finally {
    loading.value = false
  }
}

const handleEnhanceEquipment = async (equipmentId: string, level: number) => {
  try {
    loading.value = true
    await gameStore.enhanceEquipment(equipmentId, level)
    showToast('强化成功')
  } catch (error) {
    showToast('强化失败')
    console.error('Failed to enhance equipment:', error)
  } finally {
    loading.value = false
  }
}

const handleRepairEquipment = async (equipmentId: string) => {
  try {
    loading.value = true
    await gameStore.repairEquipment(equipmentId)
    showToast('修理成功')
  } catch (error) {
    showToast('修理失败')
    console.error('Failed to repair equipment:', error)
  } finally {
    loading.value = false
  }
}

const handleExportPet = async () => {
  if (!currentPet.value) return

  try {
    const exportData = await petStore.exportPet(currentPet.value.id)
    // 创建下载链接
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${currentPet.value.name}_export.json`
    a.click()
    URL.revokeObjectURL(url)
    showToast('导出成功')
  } catch (error) {
    showToast('导出失败')
    console.error('Failed to export pet:', error)
  }
}

const handleResetPet = async () => {
  if (!currentPet.value) return

  const confirmed = await showDialog({
    title: '确认重置',
    message: '重置后萌宠将恢复到初始状态，是否继续？',
    confirmButtonText: '确认重置',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (confirmed) {
    try {
      loading.value = true
      await petStore.resetPet(currentPet.value.id)
      showToast('重置成功')
      showSettings.value = false
    } catch (error) {
      showToast('重置失败')
      console.error('Failed to reset pet:', error)
    } finally {
      loading.value = false
    }
  }
}

// 辅助函数
const getRarityName = (rarity: PetRarity): string => {
  const names = {
    common: '普通',
    uncommon: '不常见',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return names[rarity] || rarity
}

const getRarityClass = (rarity: PetRarity): string => {
  return `rarity-${rarity}`
}

const getPetTypeName = (type: PetType): string => {
  const names = {
    cat: '猫咪',
    dog: '狗狗',
    bird: '鸟类',
    other: '其他'
  }
  return names[type] || type
}

// 生命周期
onMounted(() => {
  if (!currentPet.value) {
    showToast('萌宠不存在')
    router.push('/')
  }
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId && !currentPet.value) {
    showToast('萌宠不存在')
    router.push('/')
  }
})
</script>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1.5rem;
}

.primary-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.secondary-btn {
  background-color: #f5f5f7;
  color: #1d1d1f;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  background-color: #e5e5e7;
}

.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #f5f5f7;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  background-color: #e5e5e7;
}

/* 页面头部 */
.page-header {
  padding: 2rem 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

/* 萌宠展示区域 */
.pet-showcase {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 3rem;
}

.pet-display {
  width: 100%;
  max-width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}

.pet-info {
  width: 100%;
  text-align: center;
}

.pet-name-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.pet-name {
  font-size: 2rem;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
  margin-right: 0.5rem;
}

.edit-name-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background-color: #f5f5f7;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-name-btn:hover {
  background-color: #e5e5e7;
}

.pet-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.pet-type,
.pet-rarity,
.pet-level {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 980px;
  background-color: #f5f5f7;
}

.rarity-common {
  color: #6b7280;
}

.rarity-uncommon {
  color: #10b981;
}

.rarity-rare {
  color: #3b82f6;
}

.rarity-epic {
  color: #8b5cf6;
}

.rarity-legendary {
  color: #f59e0b;
}

.rarity-mythical {
  color: #ef4444;
}

.pet-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

/* 内容区域 */
.pet-details-content {
  padding-bottom: 4rem;
}

.stats-section,
.status-section,
.equipment-section,
.skills-section,
.value-section {
  margin-bottom: 3rem;
  background-color: white;
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* 移动端优化：减小内边距，增加可用空间 */
@media (max-width: 767px) {
  .stats-section,
  .status-section,
  .equipment-section,
  .skills-section,
  .value-section {
    padding: 1.25rem;
  }

  .container {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  /* 确保组件内容在移动端能够完整显示 */
  :deep(.pet-stats-panel),
  :deep(.pet-status-monitor),
  :deep(.pet-equipment-manager),
  :deep(.pet-skills-traits),
  :deep(.pet-value-assessment) {
    width: 100%;
    overflow-x: auto;
  }

  /* 确保表格和列表在移动端可以水平滚动 */
  :deep(table),
  :deep(.equipment-list),
  :deep(.skills-list) {
    min-width: 100%;
    width: max-content;
  }

  /* 调整按钮大小和间距 */
  .primary-btn,
  .secondary-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .pet-actions {
    gap: 0.5rem;
  }

  /* 减小萌宠展示区域的大小 */
  .pet-display {
    height: 250px;
    margin-bottom: 1.5rem;
  }

  .pet-name {
    font-size: 1.75rem;
  }
}

/* 空状态 */
.empty-state {
  padding: 4rem 0;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.75rem;
}

.empty-desc {
  font-size: 1.125rem;
  color: #6e6e73;
  margin-bottom: 2rem;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: white;
  font-size: 1.125rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 自定义弹窗样式 */
:deep(.custom-dialog) {
  border-radius: 18px;
  overflow: hidden;
}

:deep(.custom-popup) {
  border-radius: 18px 18px 0 0;
  overflow: hidden;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .page-title {
    font-size: 3rem;
  }

  .pet-showcase {
    flex-direction: row;
    align-items: flex-start;
    gap: 3rem;
  }

  .pet-display {
    margin-bottom: 0;
  }

  .pet-info {
    text-align: left;
  }

  .pet-name-section,
  .pet-meta,
  .pet-actions {
    justify-content: flex-start;
  }
}

@media (min-width: 1024px) {
  .pet-details-content .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .pet-showcase {
    grid-column: span 2;
  }
}

/* 平板设备优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .pet-details-content .container {
    display: flex;
    flex-direction: column;
  }

  /* 在平板上使用单列布局，但保持较大的内容宽度 */
  .stats-section,
  .status-section,
  .equipment-section,
  .skills-section,
  .value-section {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
