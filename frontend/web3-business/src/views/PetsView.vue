<template>
  <AppLayout>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">萌宠世界</h1>
        <p class="page-desc">查看和管理你的萌宠伙伴</p>
      </div>
    </section>

    <!-- 萌宠统计信息 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card">
            <span class="stat-value">{{ petStore.petCount }}</span>
            <span class="stat-label">萌宠总数</span>
          </div>

          <div class="stat-card">
            <span class="stat-value">{{ averageLevel }}</span>
            <span class="stat-label">平均等级</span>
          </div>

          <div class="stat-card">
            <span class="stat-value">{{ rareCount }}</span>
            <span class="stat-label">稀有萌宠</span>
          </div>

          <div class="stat-card">
            <span class="stat-value">{{ totalValue }}</span>
            <span class="stat-label">总价值</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 筛选器和搜索 -->
    <section class="filters-section">
      <div class="container">
        <div class="filters-wrapper">
          <div class="search-box">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索萌宠..."
              class="search-input"
            />
            <button class="search-btn">
              <van-icon name="search" />
            </button>
          </div>

          <div class="filters-group">
            <div class="filter-item">
              <label class="filter-label">稀有度</label>
              <select v-model="filterRarity" class="filter-select">
                <option value="all">全部</option>
                <option value="common">普通</option>
                <option value="uncommon">不常见</option>
                <option value="rare">稀有</option>
                <option value="epic">史诗</option>
                <option value="legendary">传说</option>
                <option value="mythical">神话</option>
              </select>
            </div>

            <div class="filter-item">
              <label class="filter-label">类型</label>
              <select v-model="filterType" class="filter-select">
                <option value="all">全部</option>
                <option value="cat">猫咪</option>
                <option value="dog">狗狗</option>
                <option value="bird">鸟类</option>
                <option value="other">其他</option>
              </select>
            </div>

            <div class="filter-item">
              <label class="filter-label">排序</label>
              <select v-model="sortBy" class="filter-select">
                <option value="level">等级</option>
                <option value="rarity">稀有度</option>
                <option value="health">健康</option>
                <option value="happiness">快乐</option>
                <option value="energy">能量</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 萌宠列表 -->
    <section class="pets-section">
      <div class="container">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p class="loading-text">加载萌宠中...</p>
        </div>

        <div v-else-if="filteredPets.length > 0" class="pets-grid">
          <div
            v-for="pet in filteredPets"
            :key="pet.id"
            class="pet-card"
            @click="viewPetDetails(pet.id)"
          >
            <div class="pet-image-container">
              <PetDisplay
                :pet="pet"
                size="large"
                :effects="['rarity']"
                :interactive="true"
              />
            </div>

            <div class="pet-info">
              <h3 class="pet-name">{{ pet.name }}</h3>
              <div class="pet-details">
                <div class="pet-detail">
                  <span class="detail-label">类型:</span>
                  <span class="detail-value">{{ getPetTypeName(pet.type) }}</span>
                </div>
                <div class="pet-detail">
                  <span class="detail-label">等级:</span>
                  <span class="detail-value">{{ pet.level }}</span>
                </div>
                <div class="pet-detail">
                  <span class="detail-label">稀有度:</span>
                  <span class="detail-value" :class="getRarityClass(pet.rarity)">
                    {{ getRarityName(pet.rarity) }}
                  </span>
                </div>
              </div>

              <div class="pet-stats">
                <div class="stat-bar">
                  <div class="stat-label">
                    <van-icon name="like-o" />
                    <span>健康</span>
                  </div>
                  <div class="progress-bar">
                    <div
                      class="progress-fill health"
                      :style="{ width: `${(pet.health / pet.maxHealth) * 100}%` }"
                      :class="getHealthColor(pet)"
                    ></div>
                  </div>
                </div>

                <div class="stat-bar">
                  <div class="stat-label">
                    <van-icon name="smile-o" />
                    <span>快乐</span>
                  </div>
                  <div class="progress-bar">
                    <div
                      class="progress-fill happiness"
                      :style="{ width: `${(pet.happiness / pet.maxHappiness) * 100}%` }"
                      :class="getHappinessColor(pet)"
                    ></div>
                  </div>
                </div>

                <div class="stat-bar">
                  <div class="stat-label">
                    <van-icon name="fire-o" />
                    <span>能量</span>
                  </div>
                  <div class="progress-bar">
                    <div
                      class="progress-fill energy"
                      :style="{ width: `${(pet.energy / pet.maxEnergy) * 100}%` }"
                      :class="getEnergyColor(pet)"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="petStore.petCount === 0" class="empty-state">
          <div class="empty-icon">🐱</div>
          <h3 class="empty-title">还没有萌宠</h3>
          <p class="empty-desc">创建你的第一只萌宠，开始冒险吧！</p>
          <button class="primary-btn" @click="handleCreatePet">创建萌宠</button>
        </div>

        <div v-else class="empty-state">
          <div class="empty-icon">🔍</div>
          <h3 class="empty-title">没有找到符合条件的萌宠</h3>
          <p class="empty-desc">尝试调整筛选条件</p>
          <button class="secondary-btn" @click="resetFilters">重置筛选</button>
        </div>
      </div>
    </section>

    <!-- 创建萌宠浮动按钮 -->
    <button class="floating-btn" @click="handleCreatePet" aria-label="创建萌宠">
      <van-icon name="plus" />
    </button>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePetStore } from '../stores/pet'
import PetDisplay from '../components/pet/PetDisplay.vue'
import AppLayout from '../components/layout/AppLayout.vue'
import type { Pet, PetRarity, PetType } from '../types/typesWithoutCircular'

const router = useRouter()
const petStore = usePetStore()

// 响应式状态
const loading = ref(false)
const searchQuery = ref('')
const filterRarity = ref('all')
const filterType = ref('all')
const sortBy = ref('level')

// 计算属性
const filteredPets = computed(() => {
  let result = [...petStore.pets]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(pet =>
      pet.name.toLowerCase().includes(query) ||
      pet.type.toLowerCase().includes(query)
    )
  }

  // 稀有度过滤
  if (filterRarity.value !== 'all') {
    result = result.filter(pet => pet.rarity === filterRarity.value)
  }

  // 类型过滤
  if (filterType.value !== 'all') {
    result = result.filter(pet => pet.type === filterType.value)
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'level':
        return b.level - a.level
      case 'rarity':
        return getRarityValue(b.rarity) - getRarityValue(a.rarity)
      case 'health':
        return (b.health / b.maxHealth) - (a.health / a.maxHealth)
      case 'happiness':
        return (b.happiness / b.maxHappiness) - (a.happiness / a.maxHappiness)
      case 'energy':
        return (b.energy / b.maxEnergy) - (a.energy / a.maxEnergy)
      default:
        return 0
    }
  })

  return result
})

const averageLevel = computed(() => {
  if (petStore.petCount === 0) return 0
  const totalLevel = petStore.pets.reduce((sum, pet) => sum + pet.level, 0)
  return Math.round(totalLevel / petStore.petCount)
})

const rareCount = computed(() => {
  return petStore.pets.filter(pet =>
    ['rare', 'epic', 'legendary', 'mythical'].includes(pet.rarity)
  ).length
})

const totalValue = computed(() => {
  return petStore.pets.reduce((sum, pet) => {
    const rarityMultiplier = { common: 1, uncommon: 1.5, rare: 2, epic: 3, legendary: 5, mythical: 10 }
    return sum + (pet.level * (rarityMultiplier[pet.rarity] || 1))
  }, 0)
})

// 方法
const handleBack = () => {
  router.back()
}

const handleCreatePet = () => {
  router.push('/pet/create')
}

const viewPetDetails = (id: string) => {
  router.push(`/pet/${id}`)
}

const resetFilters = () => {
  searchQuery.value = ''
  filterRarity.value = 'all'
  filterType.value = 'all'
  sortBy.value = 'level'
}

// 辅助函数
const getRarityValue = (rarity: PetRarity): number => {
  const values = { common: 1, uncommon: 2, rare: 3, epic: 4, legendary: 5, mythical: 6 }
  return values[rarity] || 0
}

const getRarityName = (rarity: PetRarity): string => {
  const names = {
    common: '普通',
    uncommon: '不常见',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return names[rarity] || rarity
}

const getRarityClass = (rarity: PetRarity): string => {
  return `rarity-${rarity}`
}

const getPetTypeName = (type: PetType): string => {
  const names = {
    cat: '猫咪',
    dog: '狗狗',
    bird: '鸟类',
    other: '其他'
  }
  return names[type] || type
}

const getHealthColor = (pet: Pet): string => {
  const percentage = (pet.health / pet.maxHealth) * 100
  if (percentage >= 80) return 'good'
  if (percentage >= 60) return 'medium'
  return 'low'
}

const getHappinessColor = (pet: Pet): string => {
  const percentage = (pet.happiness / pet.maxHappiness) * 100
  if (percentage >= 80) return 'good'
  if (percentage >= 60) return 'medium'
  return 'low'
}

const getEnergyColor = (pet: Pet): string => {
  const percentage = (pet.energy / pet.maxEnergy) * 100
  if (percentage >= 80) return 'good'
  if (percentage >= 60) return 'medium'
  return 'low'
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    await petStore.loadPetData()
  } catch (error) {
    console.error('Failed to load pets:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.primary-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.secondary-btn {
  background-color: #f5f5f7;
  color: #1d1d1f;
  border: none;
  border-radius: 980px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover {
  background-color: #e5e5e7;
}

/* 页面头部 */
.page-header {
  padding: 4rem 0 2rem;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.5rem;
}

.page-desc {
  font-size: 1.25rem;
  color: #6e6e73;
  max-width: 600px;
  margin: 0 auto;
}

/* 统计部分 */
.stats-section {
  padding: 2rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-card {
  background-color: white;
  border-radius: 18px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #0071e3;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6e6e73;
}

/* 筛选部分 */
.filters-section {
  padding: 2rem 0;
}

.filters-wrapper {
  background-color: white;
  border-radius: 18px;
  padding: 1.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  margin-bottom: 1.5rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d2d2d7;
  border-radius: 8px 0 0 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #0071e3;
}

.search-btn {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 0 8px 8px 0;
  padding: 0 1rem;
  cursor: pointer;
}

.filters-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 0.875rem;
  color: #6e6e73;
  margin-bottom: 0.5rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #d2d2d7;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  outline: none;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  border-color: #0071e3;
}

/* 萌宠列表 */
.pets-section {
  padding: 2rem 0 6rem;
}

.pets-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.pet-card {
  background-color: white;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.pet-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.pet-image-container {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  background-color: #f5f5f7;
}

.pet-info {
  padding: 1.5rem;
}

.pet-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1rem;
}

.pet-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.pet-detail {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 0.875rem;
  color: #6e6e73;
  margin-right: 0.5rem;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1d1d1f;
}

.rarity-common {
  color: #6b7280;
}

.rarity-uncommon {
  color: #10b981;
}

.rarity-rare {
  color: #3b82f6;
}

.rarity-epic {
  color: #8b5cf6;
}

.rarity-legendary {
  color: #f59e0b;
}

.rarity-mythical {
  color: #ef4444;
}

.pet-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-bar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6e6e73;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f5f5f7;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.health.good { background-color: #34d399; }
.progress-fill.health.medium { background-color: #fbbf24; }
.progress-fill.health.low { background-color: #f87171; }

.progress-fill.happiness.good { background-color: #60a5fa; }
.progress-fill.happiness.medium { background-color: #fbbf24; }
.progress-fill.happiness.low { background-color: #f87171; }

.progress-fill.energy.good { background-color: #a78bfa; }
.progress-fill.energy.medium { background-color: #fbbf24; }
.progress-fill.energy.low { background-color: #f87171; }

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 0.75rem;
}

.empty-desc {
  font-size: 1.125rem;
  color: #6e6e73;
  margin-bottom: 2rem;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(0, 113, 227, 0.2);
  border-top-color: #0071e3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

.loading-text {
  font-size: 1.125rem;
  color: #6e6e73;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 浮动按钮 */
.floating-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background-color: #0071e3;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.floating-btn:hover {
  background-color: #0077ed;
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* 移动端隐藏浮动按钮 */
@media (max-width: 768px) {
  .floating-btn {
    bottom: 5rem; /* 防止和移动端底部导航重叠 */
  }
}

/* 响应式调整 */
@media (min-width: 640px) {
  .page-title {
    font-size: 3.5rem;
  }

  .filters-group {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .filter-item {
    flex: 1;
    min-width: 180px;
  }

  .pets-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .pets-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
