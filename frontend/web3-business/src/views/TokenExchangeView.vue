<template>
  <AppLayoutWithSidebar>
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">代币兑换</h1>
        <p class="page-desc">将你的萌宠兑换成珍贵的代币</p>
      </div>
    </section>

    <!-- 兑换内容区域 -->
    <section class="exchange-content-section">
      <div class="container">
        <!-- 兑换统计 -->
        <div class="exchange-stats">
          <div class="stat-card">
            <div class="stat-icon">💎</div>
            <div class="stat-content">
              <div class="stat-value">{{ totalTokensEarned }}</div>
              <div class="stat-label">总获得代币</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">🔄</div>
            <div class="stat-content">
              <div class="stat-value">{{ totalExchanges }}</div>
              <div class="stat-label">兑换次数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-value">{{ exchangeablePets.length }}</div>
              <div class="stat-label">可兑换萌宠</div>
            </div>
          </div>
        </div>

        <!-- 标签页 -->
        <div class="exchange-tabs">
          <div class="tab-buttons">
            <button
              class="tab-button"
              :class="{ active: activeTab === 'exchange' }"
              @click="activeTab = 'exchange'"
            >
              <span class="tab-icon">🔄</span>
              <span class="tab-text">兑换萌宠</span>
            </button>
            <button
              class="tab-button"
              :class="{ active: activeTab === 'history' }"
              @click="activeTab = 'history'"
            >
              <span class="tab-icon">📊</span>
              <span class="tab-text">兑换历史</span>
            </button>
          </div>
        </div>
        <!-- 兑换萌宠标签页内容 -->
        <div v-if="activeTab === 'exchange'" class="exchange-tab">
          <!-- 萌宠选择 -->
          <div v-if="!selectedPet" class="pet-selection">
            <div class="selection-header">
              <h3 class="selection-title">选择要兑换的萌宠</h3>
              <p class="selection-desc">选择一只萌宠来兑换代币</p>
            </div>

            <!-- 萌宠网格 -->
            <div v-if="exchangeablePets.length > 0" class="pets-grid">
              <div
                v-for="pet in exchangeablePets"
                :key="pet.id"
                class="pet-exchange-card"
                @click="selectPet(pet)"
              >
                <div class="pet-image">
                  <img :src="getPetImageUrl(pet)" :alt="pet.name" />
                  <div class="rarity-badge" :class="pet.rarity">
                    {{ getRarityText(pet.rarity) }}
                  </div>
                </div>

                <div class="pet-info">
                  <h4 class="pet-name">{{ pet.name }}</h4>
                  <div class="pet-stats">
                    <span class="stat">等级 {{ pet.level }}</span>
                    <span class="stat">{{ getRarityText(pet.rarity) }}</span>
                  </div>
                  <div class="exchange-value">
                    <span class="value">{{ calculateExchangeValue(pet) }}</span>
                    <span class="unit">代币</span>
                  </div>
                </div>

                <div class="exchange-button">
                  <button class="select-btn">选择兑换</button>
                </div>
              </div>
            </div>

            <!-- 无可兑换萌宠 -->
            <div v-else class="empty-state">
              <div class="empty-icon">🐾</div>
              <h3 class="empty-title">暂无可兑换的萌宠</h3>
              <p class="empty-desc">你需要拥有等级5以上的萌宠才能进行兑换</p>
              <router-link to="/pet/create" class="create-pet-btn">
                创建萌宠
              </router-link>
            </div>
          </div>

          <!-- 兑换确认界面 -->
          <div v-if="selectedPet" class="exchange-confirm">
            <div class="confirm-header">
              <h3 class="confirm-title">确认兑换 {{ selectedPet.name }}</h3>
              <button class="back-btn" @click="selectedPet = null">重新选择</button>
            </div>

            <div class="exchange-preview">
              <div class="pet-preview">
                <img :src="getPetImageUrl(selectedPet)" :alt="selectedPet.name" />
                <div class="pet-details">
                  <h4>{{ selectedPet.name }}</h4>
                  <p>等级 {{ selectedPet.level }} | {{ getRarityText(selectedPet.rarity) }}</p>
                </div>
              </div>

              <div class="exchange-arrow">→</div>

              <div class="token-preview">
                <div class="token-icon">💎</div>
                <div class="token-amount">{{ calculateExchangeValue(selectedPet) }}</div>
                <div class="token-label">代币</div>
              </div>
            </div>

            <div class="exchange-actions">
              <button
                class="confirm-exchange-btn"
                @click="handleExchange"
                :disabled="!walletStore.isConnected || exchanging"
              >
                <span v-if="exchanging">兑换中...</span>
                <span v-else-if="!walletStore.isConnected">请先连接钱包</span>
                <span v-else>确认兑换</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 兑换历史标签页内容 -->
        <div v-if="activeTab === 'history'" class="history-tab">
          <div class="history-header">
            <h3 class="history-title">兑换历史</h3>
            <p class="history-desc">查看你的所有兑换记录</p>
          </div>

          <div v-if="exchangeHistory.length > 0" class="history-list">
            <div
              v-for="record in exchangeHistory"
              :key="record.id"
              class="history-item"
            >
              <div class="history-pet">
                <img :src="getPetImageUrl(record.pet)" :alt="record.pet.name" />
                <div class="history-info">
                  <h4>{{ record.pet.name }}</h4>
                  <p>{{ formatDate(record.timestamp) }}</p>
                </div>
              </div>
              <div class="history-value">
                <span class="amount">+{{ record.tokenAmount }}</span>
                <span class="unit">代币</span>
              </div>
            </div>
          </div>

          <div v-else class="empty-history">
            <div class="empty-icon">📊</div>
            <h3 class="empty-title">暂无兑换记录</h3>
            <p class="empty-desc">开始兑换萌宠来获得代币吧！</p>
          </div>
        </div>
      </div>
    </section>
  </AppLayoutWithSidebar>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showToast, showNotify } from 'vant'
import { usePetStore } from '../stores/pet'
import { useWalletStore } from '../stores/wallet'
import AppLayoutWithSidebar from '../components/layout/AppLayoutWithSidebar.vue'
import type { Pet } from '../types/typesWithoutCircular'
import type { ExchangeTransaction } from '../services/token-exchange.service'
import { tokenExchangeService } from '../services/token-exchange.service'

// 存储
const petStore = usePetStore()
const walletStore = useWalletStore()

// 响应式数据
const activeTab = ref('exchange')
const selectedPet = ref<Pet | null>(null)
const exchanging = ref(false)
const exchangeHistory = ref<ExchangeTransaction[]>([])
const totalTokensEarned = ref(0)
const totalExchanges = ref(0)

// 计算属性
const exchangeablePets = computed(() => {
  return petStore.pets.filter(pet => {
    // 简化的兑换条件检查
    return pet.level >= 5 && pet.health > 50 && pet.happiness > 30
  })
})

const petPickerColumns = computed(() => {
  return exchangeablePets.value.map(pet => ({
    text: `${pet.name} (等级 ${pet.level})`,
    value: pet.id
  }))
})

// 方法
const getRarityText = (rarity: string): string => {
  const rarityMap: Record<string, string> = {
    'common': '普通',
    'uncommon': '稀有',
    'rare': '珍稀',
    'epic': '史诗',
    'legendary': '传说',
    'mythical': '神话'
  }
  return rarityMap[rarity] || rarity
}

const getRarityTagType = (rarity: string): string => {
  const typeMap: Record<string, string> = {
    'common': 'default',
    'uncommon': 'primary',
    'rare': 'success',
    'epic': 'warning',
    'legendary': 'danger',
    'mythical': 'primary'
  }
  return typeMap[rarity] || 'default'
}

const getPetImageUrl = (pet: Pet): string => {
  return `/images/pets/${pet.type}/${pet.rarity}.png`
}

const selectPet = (pet: any) => {
  // Create a mutable copy of the pet to avoid readonly issues
  selectedPet.value = JSON.parse(JSON.stringify(pet)) as Pet
}

const calculateExchangeValue = (pet: any): number => {
  // 基础价值计算：等级 * 稀有度倍数
  const rarityMultiplier = {
    common: 1,
    uncommon: 2,
    rare: 4,
    epic: 8,
    legendary: 16
  }
  return pet.level * (rarityMultiplier[pet.rarity as keyof typeof rarityMultiplier] || 1) * 10
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

const handleExchange = async () => {
  if (!selectedPet.value || !walletStore.isConnected) return

  exchanging.value = true
  try {
    const tokenAmount = calculateExchangeValue(selectedPet.value)

    // 模拟兑换过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 添加到历史记录
    const transaction = {
      id: Date.now().toString(),
      petId: selectedPet.value.id,
      petName: selectedPet.value.name,
      petLevel: selectedPet.value.level,
      petRarity: selectedPet.value.rarity,
      tokenAmount: tokenAmount.toString(),
      transactionHash: `0x${Math.random().toString(16).substring(2, 10)}`,
      timestamp: Date.now(),
      status: 'confirmed' as const,
      pet: selectedPet.value
    }

    exchangeHistory.value.unshift(transaction)
    totalTokensEarned.value += tokenAmount
    totalExchanges.value += 1

    // 从萌宠列表中移除
    petStore.removePet(selectedPet.value.id)

    showToast('兑换成功！')
    selectedPet.value = null
    activeTab.value = 'history'
  } catch (error) {
    console.error('兑换失败:', error)
    showToast('兑换失败，请重试')
  } finally {
    exchanging.value = false
  }
}

// 移除不需要的方法

const connectWallet = async () => {
  try {
    // Import wallet service dynamically
    const { walletService } = await import('../services/wallet.service')

    // Try to connect MetaMask first
    const walletInfo = await walletService.connectMetaMask()

    // Update wallet store
    walletStore.setWalletInfo(walletInfo)

    showToast('钱包连接成功')
  } catch (error) {
    console.error('连接钱包失败:', error)
    showToast('连接钱包失败')
  }
}

// 移除重复的方法

// 生命周期
onMounted(() => {
  // 如果没有萌宠，提示用户先创建萌宠
  if (petStore.pets.length === 0) {
    showNotify({
      type: 'warning',
      message: '您还没有萌宠，请先创建萌宠',
      duration: 3000
    })
  }
})
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem 0 2rem;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-desc {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* 兑换内容区域 */
.exchange-content-section {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

/* 兑换统计卡片 */
.exchange-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 18px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
  animation: float 3s ease-in-out infinite;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
}

/* 标签页样式 */
.exchange-tabs {
  margin-bottom: 2rem;
}

.tab-buttons {
  display: flex;
  background: white;
  border-radius: 15px;
  padding: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: none;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6b7280;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.02);
}

.tab-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.tab-text {
  font-weight: 600;
  font-size: 0.9rem;
}

/* 萌宠选择区域 */
.pet-selection {
  background: white;
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selection-header {
  text-align: center;
  margin-bottom: 2rem;
}

.selection-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.selection-desc {
  color: #6b7280;
  margin: 0;
}

/* 萌宠网格 */
.pets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.pet-exchange-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pet-exchange-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.pet-image {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-image img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.8);
}

.rarity-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.rarity-badge.common { background: #6b7280; }
.rarity-badge.uncommon { background: #10b981; }
.rarity-badge.rare { background: #3b82f6; }
.rarity-badge.epic { background: #8b5cf6; }
.rarity-badge.legendary { background: #f59e0b; }

.pet-info {
  padding: 1.5rem;
}

.pet-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pet-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat {
  font-size: 0.9rem;
  color: #6b7280;
}

.exchange-value {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.unit {
  font-size: 0.9rem;
  color: #6b7280;
}

.exchange-button {
  padding: 0 1.5rem 1.5rem;
}

.select-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.empty-desc {
  color: #6b7280;
  margin-bottom: 2rem;
}

.create-pet-btn {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.create-pet-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 兑换确认界面 */
.exchange-confirm {
  background: white;
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.confirm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.confirm-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.back-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #e5e7eb;
}

.exchange-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 15px;
}

.pet-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.pet-preview img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 1rem;
}

.pet-details h4 {
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.pet-details p {
  color: #6b7280;
  font-size: 0.9rem;
}

.exchange-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.token-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.token-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.token-amount {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.token-label {
  color: #6b7280;
  font-size: 0.9rem;
}

.exchange-actions {
  text-align: center;
}

.confirm-exchange-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 3rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-exchange-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.confirm-exchange-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 兑换历史 */
.history-tab {
  background: white;
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-header {
  text-align: center;
  margin-bottom: 2rem;
}

.history-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.history-desc {
  color: #6b7280;
  margin: 0;
}

.history-list {
  space-y: 1rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.history-item:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.history-pet {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.history-pet img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.history-info h4 {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.history-info p {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0;
}

.history-value {
  text-align: right;
}

.history-value .amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: #10b981;
}

.history-value .unit {
  color: #6b7280;
  font-size: 0.9rem;
  margin-left: 0.25rem;
}

.empty-history {
  text-align: center;
  padding: 3rem 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .exchange-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .pets-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .exchange-preview {
    flex-direction: column;
    gap: 1rem;
  }

  .exchange-arrow {
    transform: rotate(90deg);
  }

  .confirm-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .history-item {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 2rem 0 1.5rem;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .pet-selection {
    padding: 1rem;
  }

  .container {
    padding: 0 0.5rem;
  }

  .exchange-confirm,
  .history-tab {
    padding: 1rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .confirm-exchange-btn {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }
}

/* 动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
