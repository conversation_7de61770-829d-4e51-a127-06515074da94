<template>
  <div class="token-exchange-view">
    <!-- 页面标题 -->
    <van-nav-bar
      title="代币兑换"
      left-arrow
      @click-left="$router.back()"
    />

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <!-- 兑换萌宠 -->
      <van-tab title="兑换萌宠" name="exchange">
        <div class="tab-content">
          <!-- 萌宠选择 -->
          <div v-if="!selectedPet" class="pet-selection">
            <van-cell-group>
              <van-cell title="选择要兑换的萌宠" is-link @click="showPetPicker = true" />
            </van-cell-group>

            <!-- 萌宠列表 -->
            <div v-if="exchangeablePets.length > 0" class="pet-grid mt-4">
              <van-card
                v-for="pet in exchangeablePets"
                :key="pet.id"
                :title="pet.name"
                :desc="`等级 ${pet.level} | ${getRarityText(pet.rarity)}`"
                :thumb="getPetImageUrl(pet)"
                class="pet-card"
                @click="selectPet(pet)"
              >
                <template #tags>
                  <van-tag :type="getRarityTagType(pet.rarity)" size="small">
                    {{ getRarityText(pet.rarity) }}
                  </van-tag>
                </template>

                <template #footer>
                  <div class="text-center">
                    <van-button size="small" type="primary">选择兑换</van-button>
                  </div>
                </template>
              </van-card>
            </div>

            <!-- 无可兑换萌宠 -->
            <van-empty
              v-else
              description="暂无可兑换的萌宠"
              image="search"
            >
              <template #description>
                <div class="text-center">
                  <p>萌宠需要满足以下条件才能兑换：</p>
                  <ul class="text-left mt-2 space-y-1">
                    <li>• 等级达到 5 级或以上</li>
                    <li>• 健康度达到 50% 以上</li>
                    <li>• 快乐度达到 30% 以上</li>
                  </ul>
                </div>
              </template>
            </van-empty>
          </div>

          <!-- 兑换界面 -->
          <div v-else>
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold">兑换 {{ selectedPet.name }}</h3>
              <van-button size="small" @click="selectedPet = null">重新选择</van-button>
            </div>

            <PetTokenExchange
              :pet="selectedPet"
              :wallet-connected="walletStore.isConnected"
              :user-address="walletStore.address"
              @connect-wallet="connectWallet"
              @exchange-success="handleExchangeSuccess"
              @exchange-failed="handleExchangeFailed"
            />
          </div>
        </div>
      </van-tab>

      <!-- 兑换历史 -->
      <van-tab title="兑换历史" name="history">
        <div class="tab-content">
          <PetExchangeHistory ref="historyRef" />
        </div>
      </van-tab>

      <!-- 兑换说明 -->
      <van-tab title="兑换说明" name="guide">
        <div class="tab-content">
          <van-cell-group>
            <van-cell title="兑换规则" />
          </van-cell-group>

          <div class="guide-content p-4 space-y-4">
            <div>
              <h4 class="font-semibold mb-2">🎯 兑换条件</h4>
              <ul class="space-y-1 text-sm text-gray-600">
                <li>• 萌宠等级需要达到 5 级或以上</li>
                <li>• 萌宠健康度需要达到 50% 以上</li>
                <li>• 萌宠快乐度需要达到 30% 以上</li>
              </ul>
            </div>

            <div>
              <h4 class="font-semibold mb-2">💰 价值计算</h4>
              <ul class="space-y-1 text-sm text-gray-600">
                <li>• 基础价值 = 10 × 1.2^(等级-1)</li>
                <li>• 稀有度加成：普通×1.0，稀有×1.5，珍稀×2.0，史诗×3.0，传说×5.0，神话×10.0</li>
                <li>• 健康度加成：当前健康度 / 最大健康度</li>
                <li>• 快乐度加成：当前快乐度 / 最大快乐度</li>
                <li>• 装备加成：每件装备根据稀有度提供额外加成</li>
                <li>• 特质加成：每个特质根据稀有度和等级提供加成</li>
              </ul>
            </div>

            <div>
              <h4 class="font-semibold mb-2">⚠️ 注意事项</h4>
              <ul class="space-y-1 text-sm text-gray-600">
                <li>• 兑换后萌宠将被永久移除，无法恢复</li>
                <li>• 兑换需要支付少量 Gas 费用</li>
                <li>• 交易确认后代币将直接转入您的钱包</li>
                <li>• 建议在萌宠状态最佳时进行兑换以获得最高价值</li>
              </ul>
            </div>

            <div>
              <h4 class="font-semibold mb-2">🚀 提升价值技巧</h4>
              <ul class="space-y-1 text-sm text-gray-600">
                <li>• 通过训练和喂食提升萌宠等级</li>
                <li>• 保持萌宠的健康度和快乐度</li>
                <li>• 为萌宠装备高稀有度的装备</li>
                <li>• 培养萌宠的特质和技能</li>
                <li>• 选择合适的时机进行兑换</li>
              </ul>
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>

    <!-- 萌宠选择器 -->
    <van-popup v-model:show="showPetPicker" position="bottom">
      <van-picker
        :columns="petPickerColumns"
        @confirm="onPetPickerConfirm"
        @cancel="showPetPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showToast, showNotify } from 'vant'
import { usePetStore } from '../stores/pet'
import { useWalletStore } from '../stores/wallet'
import PetTokenExchange from '../components/pet/PetTokenExchange.vue'
import PetExchangeHistory from '../components/pet/PetExchangeHistory.vue'
import type { Pet } from '../types/typesWithoutCircular'
import type { ExchangeTransaction } from '../services/token-exchange.service'
import { tokenExchangeService } from '../services/token-exchange.service'

// 存储
const petStore = usePetStore()
const walletStore = useWalletStore()

// 响应式数据
const activeTab = ref('exchange')
const selectedPet = ref<Pet | null>(null)
const showPetPicker = ref(false)
const historyRef = ref<InstanceType<typeof PetExchangeHistory> | null>(null)

// 计算属性
const exchangeablePets = computed(() => {
  return petStore.pets.filter(pet => {
    const canExchange = tokenExchangeService.canExchangePet(pet)
    return canExchange.canExchange
  })
})

const petPickerColumns = computed(() => {
  return exchangeablePets.value.map(pet => ({
    text: `${pet.name} (等级 ${pet.level})`,
    value: pet.id
  }))
})

// 方法
const getRarityText = (rarity: string): string => {
  const rarityMap: Record<string, string> = {
    'common': '普通',
    'uncommon': '稀有',
    'rare': '珍稀',
    'epic': '史诗',
    'legendary': '传说',
    'mythical': '神话'
  }
  return rarityMap[rarity] || rarity
}

const getRarityTagType = (rarity: string): string => {
  const typeMap: Record<string, string> = {
    'common': 'default',
    'uncommon': 'primary',
    'rare': 'success',
    'epic': 'warning',
    'legendary': 'danger',
    'mythical': 'primary'
  }
  return typeMap[rarity] || 'default'
}

const getPetImageUrl = (pet: Pet): string => {
  return `/images/pets/${pet.type}/${pet.rarity}.png`
}

const selectPet = (pet: any) => {
  // Create a mutable copy of the pet to avoid readonly issues
  selectedPet.value = JSON.parse(JSON.stringify(pet)) as Pet
}

const onPetPickerConfirm = ({ selectedOptions }: any) => {
  const petId = selectedOptions[0].value
  const pet = petStore.pets.find(p => p.id === petId)
  if (pet) {
    // Create a mutable copy of the pet to avoid readonly issues
    selectedPet.value = JSON.parse(JSON.stringify(pet)) as Pet
  }
  showPetPicker.value = false
}

const connectWallet = async () => {
  try {
    // Import wallet service dynamically
    const { walletService } = await import('../services/wallet.service')

    // Try to connect MetaMask first
    const walletInfo = await walletService.connectMetaMask()

    // Update wallet store
    walletStore.setWalletInfo(walletInfo)

    showToast('钱包连接成功')
  } catch (error) {
    console.error('连接钱包失败:', error)
    showToast('连接钱包失败')
  }
}

const handleExchangeSuccess = (transaction: ExchangeTransaction) => {
  // 从萌宠列表中移除已兑换的萌宠
  if (selectedPet.value) {
    petStore.removePet(selectedPet.value.id)
    selectedPet.value = null
  }

  // 显示成功通知
  showNotify({
    type: 'success',
    message: '兑换成功！代币已转入您的钱包',
    duration: 3000
  })

  // 刷新历史记录
  if (historyRef.value) {
    historyRef.value.refreshHistory()
  }

  // 切换到历史标签页
  activeTab.value = 'history'
}

const handleExchangeFailed = (error: string) => {
  showNotify({
    type: 'danger',
    message: `兑换失败: ${error}`,
    duration: 3000
  })
}

// 生命周期
onMounted(() => {
  // 如果没有萌宠，提示用户先创建萌宠
  if (petStore.pets.length === 0) {
    showNotify({
      type: 'warning',
      message: '您还没有萌宠，请先创建萌宠',
      duration: 3000
    })
  }
})
</script>

<style scoped>
.token-exchange-view {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.tab-content {
  padding: 16px;
  min-height: 60vh;
}

.pet-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.pet-card {
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pet-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pet-selection {
  min-height: 400px;
}

.guide-content h4 {
  color: #323233;
}

.guide-content ul {
  padding-left: 16px;
}

.guide-content li {
  list-style: none;
  position: relative;
}

@media (max-width: 768px) {
  .pet-grid {
    grid-template-columns: 1fr;
  }

  .tab-content {
    padding: 12px;
  }
}
</style>
