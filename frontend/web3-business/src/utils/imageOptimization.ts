/**
 * 图片优化工具类
 * 提供图片懒加载、预加载、压缩等功能
 */

interface ImageLoadOptions {
  lazy?: boolean
  placeholder?: string
  quality?: number
  format?: 'webp' | 'avif' | 'auto'
  sizes?: string
}

interface PreloadOptions {
  priority?: 'high' | 'low'
  as?: 'image' | 'fetch'
  crossorigin?: 'anonymous' | 'use-credentials'
}

class ImageOptimizer {
  private static instance: ImageOptimizer
  private imageCache = new Map<string, HTMLImageElement>()
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>()
  private observer?: IntersectionObserver

  static getInstance(): ImageOptimizer {
    if (!ImageOptimizer.instance) {
      ImageOptimizer.instance = new ImageOptimizer()
    }
    return ImageOptimizer.instance
  }

  constructor() {
    this.initLazyLoadObserver()
  }

  /**
   * 初始化懒加载观察器
   */
  private initLazyLoadObserver() {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return
    }

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.dataset.src
            if (src) {
              this.loadImage(src).then((loadedImg) => {
                img.src = loadedImg.src
                img.classList.remove('lazy-loading')
                img.classList.add('lazy-loaded')
                this.observer?.unobserve(img)
              }).catch(() => {
                img.classList.add('lazy-error')
                this.observer?.unobserve(img)
              })
            }
          }
        })
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    )
  }

  /**
   * 加载图片
   */
  async loadImage(src: string): Promise<HTMLImageElement> {
    // 检查缓存
    if (this.imageCache.has(src)) {
      return this.imageCache.get(src)!
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!
    }

    // 创建加载 Promise
    const loadPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image()

      img.onload = () => {
        this.imageCache.set(src, img)
        this.loadingPromises.delete(src)
        resolve(img)
      }

      img.onerror = () => {
        this.loadingPromises.delete(src)
        reject(new Error(`Failed to load image: ${src}`))
      }

      img.src = src
    })

    this.loadingPromises.set(src, loadPromise)
    return loadPromise
  }

  /**
   * 预加载图片
   */
  preloadImage(src: string, options: PreloadOptions = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = options.as || 'image'
      link.href = src

      if (options.crossorigin) {
        link.crossOrigin = options.crossorigin
      }

      link.onload = () => resolve()
      link.onerror = () => reject(new Error(`Failed to preload: ${src}`))

      document.head.appendChild(link)
    })
  }

  /**
   * 批量预加载图片
   */
  async preloadImages(urls: string[], options: PreloadOptions = {}): Promise<void> {
    const promises = urls.map(url => this.preloadImage(url, options))
    await Promise.allSettled(promises)
  }

  /**
   * 设置懒加载
   */
  setupLazyLoad(img: HTMLImageElement, src: string, placeholder?: string) {
    if (!this.observer) {
      // 降级处理：直接加载
      img.src = src
      return
    }

    img.dataset.src = src
    img.classList.add('lazy-loading')

    if (placeholder) {
      img.src = placeholder
    }

    this.observer.observe(img)
  }

  /**
   * 获取优化后的图片 URL
   */
  getOptimizedImageUrl(src: string, options: ImageLoadOptions = {}): string {
    // 这里可以根据需要添加 CDN 或图片处理服务的逻辑
    // 例如：添加质量参数、格式转换等

    const url = new URL(src, window.location.origin)

    if (options.quality && options.quality < 100) {
      url.searchParams.set('quality', options.quality.toString())
    }

    if (options.format && options.format !== 'auto') {
      url.searchParams.set('format', options.format)
    }

    return url.toString()
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.imageCache.clear()
    this.loadingPromises.clear()
  }

  /**
   * 销毁观察器
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = undefined
    }
    this.clearCache()
  }
}

// 导出单例实例
export const imageOptimizer = ImageOptimizer.getInstance()

// Vue 3 组合式函数
export function useImageOptimization() {
  const loadImage = (src: string) => imageOptimizer.loadImage(src)
  const preloadImages = (urls: string[], options?: PreloadOptions) =>
    imageOptimizer.preloadImages(urls, options)
  const getOptimizedUrl = (src: string, options?: ImageLoadOptions) =>
    imageOptimizer.getOptimizedImageUrl(src, options)

  return {
    loadImage,
    preloadImages,
    getOptimizedUrl
  }
}

// 图片懒加载指令
export const vLazyImage = {
  mounted(el: HTMLImageElement, binding: any) {
    const { value, modifiers } = binding
    const src = typeof value === 'string' ? value : value.src
    const placeholder = value.placeholder || '/images/placeholder.png'

    imageOptimizer.setupLazyLoad(el, src, placeholder)
  },

  unmounted(el: HTMLImageElement) {
    imageOptimizer.observer?.unobserve(el)
  }
}
