import { describe, it, expect, beforeEach } from 'vitest'
import { PetGrowthSystem } from '../petGrowthSystem'
import { PetRarity, PetType, PetStatus, PetMood, GrowthStage } from '../../types/typesWithoutCircular'
import type { Pet } from '../../types/typesWithoutCircular'

describe('PetGrowthSystem', () => {
  let growthSystem: PetGrowthSystem
  let mockPet: Pet

  beforeEach(() => {
    growthSystem = new PetGrowthSystem()

    mockPet = {
      id: 'test-pet-1',
      name: '测试萌宠',
      type: PetType.CAT,
      level: 5,
      experience: 100, // At max experience for level 5
      maxExperience: 100,
      health: 80,
      maxHealth: 100,
      happiness: 70,
      maxHappiness: 100,
      energy: 60,
      maxEnergy: 100,
      rarity: PetRarity.COMMON,
      equipment: [],
      lastFeedTime: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
      lastPlayTime: Date.now() - 1 * 60 * 60 * 1000, // 1 hour ago
      lastTrainTime: Date.now() - 2 * 60 * 60 * 1000, // 2 hours ago
      lastRestTime: Date.now() - 30 * 60 * 1000, // 30 minutes ago
      birthTime: Date.now() - 24 * 60 * 60 * 1000, // 1 day ago
      avatar: '/images/pets/cat/orange_striped.png',
      stats: {
        strength: 15,
        intelligence: 20,
        agility: 25,
        charm: 18,
        vitality: 16,
        luck: 12
      },
      baseStats: {
        strength: 15,
        intelligence: 20,
        agility: 25,
        charm: 18,
        vitality: 16,
        luck: 12
      },
      appearance: {
        species: 'cat',
        color: 'orange',
        pattern: 'striped',
        size: 'small' as any,
        accessories: [],
        specialEffects: [],
        animations: ['idle', 'walk', 'play', 'sleep']
      },
      status: PetStatus.HEALTHY,
      growthStage: GrowthStage.CHILD,
      mood: PetMood.CONTENT,
      skills: [],
      achievements: [],
      totalTokensEarned: '0',
      evolutionPoints: 0,
      breedCount: 0,
      generation: 1,
      traits: [],
      metadata: {
        version: '1.0.0',
        createdBy: 'test',
        lastModified: Date.now(),
        checksum: 'test-checksum',
        tags: [],
        description: '测试萌宠'
      }
    }
  })

  describe('calculateRequiredExperience', () => {
    it('should calculate required experience correctly', () => {
      const level1Exp = growthSystem.calculateRequiredExperience(1, PetRarity.COMMON)
      const level2Exp = growthSystem.calculateRequiredExperience(2, PetRarity.COMMON)
      const level3Exp = growthSystem.calculateRequiredExperience(3, PetRarity.COMMON)

      expect(level1Exp).toBe(100)
      expect(level2Exp).toBeGreaterThan(level1Exp)
      expect(level3Exp).toBeGreaterThan(level2Exp)
    })

    it('should apply rarity multiplier', () => {
      const commonExp = growthSystem.calculateRequiredExperience(5, PetRarity.COMMON)
      const rareExp = growthSystem.calculateRequiredExperience(5, PetRarity.RARE)
      const legendaryExp = growthSystem.calculateRequiredExperience(5, PetRarity.LEGENDARY)

      expect(rareExp).toBeGreaterThan(commonExp)
      expect(legendaryExp).toBeGreaterThan(rareExp)
    })
  })

  describe('calculateLevelUpStatGains', () => {
    it('should calculate stat gains for level up', () => {
      const statGains = growthSystem.calculateLevelUpStatGains(mockPet)

      expect(statGains.strength).toBeGreaterThan(0)
      expect(statGains.intelligence).toBeGreaterThan(0)
      expect(statGains.agility).toBeGreaterThan(0)
      expect(statGains.charm).toBeGreaterThan(0)
      expect(statGains.vitality).toBeGreaterThan(0)
      expect(statGains.luck).toBeGreaterThan(0)
    })

    it('should apply rarity multiplier to stat gains', () => {
      const commonPet = { ...mockPet, rarity: PetRarity.COMMON }
      const legendaryPet = { ...mockPet, rarity: PetRarity.LEGENDARY }

      const commonGains = growthSystem.calculateLevelUpStatGains(commonPet)
      const legendaryGains = growthSystem.calculateLevelUpStatGains(legendaryPet)

      expect(legendaryGains.strength).toBeGreaterThan(commonGains.strength!)
      expect(legendaryGains.intelligence).toBeGreaterThan(commonGains.intelligence!)
    })
  })

  describe('performLevelUp', () => {
    it('should successfully level up pet with sufficient experience', () => {
      const result = growthSystem.performLevelUp(mockPet)

      expect(result.success).toBe(true)
      expect(result.newLevel).toBe(mockPet.level + 1)
      expect(result.statGains).toBeDefined()
      expect(result.newMaxValues).toBeDefined()
      expect(result.newMaxValues.health).toBeGreaterThan(mockPet.maxHealth)
    })

    it('should fail to level up pet without sufficient experience', () => {
      const lowExpPet = { ...mockPet, experience: 50 }
      const result = growthSystem.performLevelUp(lowExpPet)

      expect(result.success).toBe(false)
      expect(result.newLevel).toBe(lowExpPet.level)
    })

    it('should unlock features at specific levels', () => {
      const level10Pet = { ...mockPet, level: 9, experience: 1000, maxExperience: 1000 }
      const result = growthSystem.performLevelUp(level10Pet)

      expect(result.success).toBe(true)
      expect(result.unlockedFeatures).toContain('comprehensive_training')
    })
  })

  describe('determineGrowthStage', () => {
    it('should determine correct growth stage for different levels', () => {
      expect(growthSystem.determineGrowthStage(3)).toBe(GrowthStage.BABY)
      expect(growthSystem.determineGrowthStage(10)).toBe(GrowthStage.CHILD)
      expect(growthSystem.determineGrowthStage(20)).toBe(GrowthStage.TEEN)
      expect(growthSystem.determineGrowthStage(40)).toBe(GrowthStage.ADULT)
      expect(growthSystem.determineGrowthStage(70)).toBe(GrowthStage.ELDER)
      expect(growthSystem.determineGrowthStage(95)).toBe(GrowthStage.LEGENDARY_FORM)
    })
  })

  describe('calculateTimeBasedGrowth', () => {
    it('should apply hunger effects after long periods without feeding', () => {
      const hungryPet = {
        ...mockPet,
        lastFeedTime: Date.now() - 5 * 60 * 60 * 1000 // 5 hours ago
      }

      const updates = growthSystem.calculateTimeBasedGrowth(hungryPet)

      expect(updates.health).toBeLessThan(hungryPet.health)
      expect(updates.happiness).toBeLessThan(hungryPet.happiness)
    })

    it('should apply loneliness effects after long periods without play', () => {
      const lonelyPet = {
        ...mockPet,
        lastPlayTime: Date.now() - 7 * 60 * 60 * 1000 // 7 hours ago
      }

      const updates = growthSystem.calculateTimeBasedGrowth(lonelyPet)

      expect(updates.happiness).toBeLessThan(lonelyPet.happiness)
    })

    it('should restore energy naturally over time', () => {
      const tiredPet = {
        ...mockPet,
        energy: 50,
        lastRestTime: Date.now() - 2 * 60 * 60 * 1000 // 2 hours ago
      }

      const updates = growthSystem.calculateTimeBasedGrowth(tiredPet)

      expect(updates.energy).toBeGreaterThan(tiredPet.energy)
    })

    it('should update mood based on overall status', () => {
      const sickPet = {
        ...mockPet,
        health: 20
      }

      const updates = growthSystem.calculateTimeBasedGrowth(sickPet)

      expect(updates.mood).toBe(PetMood.SICK)
      expect(updates.status).toBe(PetStatus.SICK)
    })

    it('should set happy mood for healthy pets', () => {
      const healthyPet = {
        ...mockPet,
        health: 90,
        happiness: 90,
        energy: 80
      }

      const updates = growthSystem.calculateTimeBasedGrowth(healthyPet)

      expect(updates.mood).toBe(PetMood.HAPPY)
    })
  })

  describe('checkEvolutionAvailability', () => {
    it('should allow evolution for qualified pets', () => {
      const qualifiedPet = {
        ...mockPet,
        level: 35,
        health: 85,
        happiness: 75,
        stats: {
          strength: 60,
          intelligence: 60,
          agility: 60,
          charm: 60,
          vitality: 60,
          luck: 60
        },
        birthTime: Date.now() - 8 * 24 * 60 * 60 * 1000 // 8 days ago
      }

      const canEvolve = growthSystem.checkEvolutionAvailability(qualifiedPet)
      expect(canEvolve).toBe(true)
    })

    it('should prevent evolution for unqualified pets', () => {
      const unqualifiedPet = {
        ...mockPet,
        level: 20, // Too low level
        health: 50, // Too low health
        happiness: 40 // Too low happiness
      }

      const canEvolve = growthSystem.checkEvolutionAvailability(unqualifiedPet)
      expect(canEvolve).toBe(false)
    })
  })

  describe('getGrowthMilestones', () => {
    it('should return correct milestones for pet', () => {
      const milestones = growthSystem.getGrowthMilestones(mockPet)

      expect(milestones).toBeInstanceOf(Array)
      expect(milestones.length).toBeGreaterThan(0)

      const firstLevelUp = milestones.find(m => m.id === 'first_level_up')
      expect(firstLevelUp).toBeDefined()
      expect(firstLevelUp!.achieved).toBe(true) // Pet is level 5
    })

    it('should calculate progress correctly', () => {
      const milestones = growthSystem.getGrowthMilestones(mockPet)

      const childStage = milestones.find(m => m.id === 'child_stage')
      expect(childStage).toBeDefined()
      expect(childStage!.achieved).toBe(false) // Pet is level 5, needs level 6
      expect(childStage!.progress).toBe(mockPet.level)
      expect(childStage!.requirement).toBe(6)
    })
  })

  describe('getGrowthEvents', () => {
    it('should return empty array initially', () => {
      const events = growthSystem.getGrowthEvents()
      expect(events).toBeInstanceOf(Array)
      expect(events.length).toBe(0)
    })

    it('should record events after level up', () => {
      growthSystem.performLevelUp(mockPet)

      const events = growthSystem.getGrowthEvents()
      expect(events.length).toBe(1)
      expect(events[0].type).toBe('level_up')
      expect(events[0].petId).toBe(mockPet.id)
    })

    it('should filter events by pet ID', () => {
      growthSystem.performLevelUp(mockPet)

      const petEvents = growthSystem.getGrowthEvents(mockPet.id)
      const otherEvents = growthSystem.getGrowthEvents('other-pet')

      expect(petEvents.length).toBe(1)
      expect(otherEvents.length).toBe(0)
    })
  })

  describe('cleanupOldEvents', () => {
    it('should remove old events', () => {
      // Perform level up to create an event
      growthSystem.performLevelUp(mockPet)

      expect(growthSystem.getGrowthEvents().length).toBe(1)

      // Clean up events older than 1ms (should remove all)
      growthSystem.cleanupOldEvents(1)

      // Wait a bit to ensure the event is older than 1ms
      setTimeout(() => {
        expect(growthSystem.getGrowthEvents().length).toBe(0)
      }, 10)
    })
  })
})