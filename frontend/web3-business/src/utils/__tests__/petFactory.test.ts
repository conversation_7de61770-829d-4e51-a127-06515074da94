import { describe, it, expect, beforeEach } from 'vitest'
import { PetFactory, petFactory, PET_TEMPLATES, TRAIT_DATABASE, SKILL_DATABASE } from '../petFactory'
import { PetType, PetRarity, PetSize, GrowthStage } from '../../types/typesWithoutCircular'
import type { PetGenerationOptions } from '../../types/typesWithoutCircular'

describe('PetFactory', () => {
  let factory: PetFactory

  beforeEach(() => {
    factory = new PetFactory()
  })

  describe('generateRandomPet', () => {
    it('should generate a valid random pet', () => {
      const pet = factory.generateRandomPet()

      expect(pet.id).toBeDefined()
      expect(pet.name).toBeDefined()
      expect(Object.values(PetType)).toContain(pet.type)
      expect(Object.values(PetRarity)).toContain(pet.rarity)
      expect(pet.level).toBe(1)
      expect(pet.experience).toBe(0)
      expect(pet.health).toBeGreaterThan(0)
      expect(pet.happiness).toBeGreaterThan(0)
      expect(pet.energy).toBeGreaterThan(0)
      expect(pet.stats).toBeDefined()
      expect(pet.appearance).toBeDefined()
      expect(pet.metadata).toBeDefined()
    })

    it('should respect generation options', () => {
      const options: PetGenerationOptions = {
        type: PetType.DRAGON,
        rarity: PetRarity.LEGENDARY,
        level: 10,
        name: '测试龙'
      }

      const pet = factory.generateRandomPet(options)

      expect(pet.type).toBe(PetType.DRAGON)
      expect(pet.rarity).toBe(PetRarity.LEGENDARY)
      expect(pet.level).toBe(10)
      expect(pet.name).toBe('测试龙')
    })

    it('should generate pets with appropriate stats for rarity', () => {
      const commonPet = factory.generateRandomPet({ rarity: PetRarity.COMMON })
      const legendaryPet = factory.generateRandomPet({ rarity: PetRarity.LEGENDARY })

      // 传说级萌宠的属性应该普遍高于普通萌宠
      const commonTotal = Object.values(commonPet.stats).reduce((sum, val) => sum + val, 0)
      const legendaryTotal = Object.values(legendaryPet.stats).reduce((sum, val) => sum + val, 0)

      expect(legendaryTotal).toBeGreaterThan(commonTotal)
    })
  })

  describe('createPet', () => {
    it('should create pet with specific options', () => {
      const options: PetGenerationOptions = {
        type: PetType.CAT,
        rarity: PetRarity.RARE,
        level: 5,
        name: '小花',
        customStats: {
          strength: 30,
          intelligence: 35,
          agility: 40,
          charm: 25,
          vitality: 28,
          luck: 20
        }
      }

      const pet = factory.createPet(options)

      expect(pet.type).toBe(PetType.CAT)
      expect(pet.rarity).toBe(PetRarity.RARE)
      expect(pet.level).toBe(5)
      expect(pet.name).toBe('小花')
      expect(pet.stats).toEqual(options.customStats)
    })

    it('should set correct growth stage based on level', () => {
      const babyPet = factory.createPet({ type: PetType.CAT, rarity: PetRarity.COMMON, level: 3 })
      const adultPet = factory.createPet({ type: PetType.CAT, rarity: PetRarity.COMMON, level: 35 })

      expect(babyPet.growthStage).toBe(GrowthStage.BABY)
      expect(adultPet.growthStage).toBe(GrowthStage.ADULT)
    })

    it('should generate traits based on rarity', () => {
      const commonPet = factory.createPet({ type: PetType.CAT, rarity: PetRarity.COMMON })
      const legendaryPet = factory.createPet({ type: PetType.CAT, rarity: PetRarity.LEGENDARY })

      // 传说级萌宠应该有更多特质
      expect(legendaryPet.traits.length).toBeGreaterThanOrEqual(commonPet.traits.length)
    })
  })

  describe('breedPets', () => {
    it('should breed two pets successfully', () => {
      const parent1 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.RARE,
        level: 15,
        name: '妈妈'
      })

      const parent2 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.EPIC,
        level: 12,
        name: '爸爸'
      })

      const offspring = factory.breedPets(parent1, parent2)

      expect(offspring.generation).toBe(2)
      expect(offspring.parents?.mother).toBe(parent1.id)
      expect(offspring.parents?.father).toBe(parent2.id)
      expect(offspring.level).toBe(1) // 新生萌宠从1级开始
      expect(offspring.type).toBe(PetType.CAT) // 同种族繁殖
    })

    it('should throw error if parents level too low', () => {
      const parent1 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.COMMON,
        level: 5
      })

      const parent2 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.COMMON,
        level: 8
      })

      expect(() => factory.breedPets(parent1, parent2)).toThrow('萌宠等级必须达到10级才能繁殖')
    })

    it('should inherit stats from parents', () => {
      const parent1 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.RARE,
        level: 15,
        customStats: {
          strength: 50,
          intelligence: 40,
          agility: 60,
          charm: 30,
          vitality: 45,
          luck: 25
        }
      })

      const parent2 = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.RARE,
        level: 12,
        customStats: {
          strength: 40,
          intelligence: 60,
          agility: 50,
          charm: 50,
          vitality: 35,
          luck: 35
        }
      })

      const offspring = factory.breedPets(parent1, parent2)

      // 后代属性应该在父母属性范围内
      expect(offspring.stats.strength).toBeGreaterThan(35)
      expect(offspring.stats.strength).toBeLessThan(60)
      expect(offspring.stats.intelligence).toBeGreaterThan(35)
      expect(offspring.stats.intelligence).toBeLessThan(70)
    })
  })

  describe('evolvePet', () => {
    it('should evolve pet successfully', () => {
      const pet = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.RARE,
        level: 25
      })

      // 设置足够的经验值
      pet.experience = pet.maxExperience

      const evolutionPath = {
        from: PetType.CAT,
        to: PetType.KITSUNE,
        requirements: {
          level: 20,
          experience: pet.maxExperience
        },
        rarity: PetRarity.LEGENDARY,
        probability: 1.0
      }

      const evolvedPet = factory.evolvePet(pet, evolutionPath)

      expect(evolvedPet.type).toBe(PetType.KITSUNE)
      expect(evolvedPet.rarity).toBe(PetRarity.LEGENDARY)
      expect(evolvedPet.evolutionPoints).toBe(1)

      // 进化后属性应该得到提升
      expect(evolvedPet.stats.strength).toBeGreaterThan(pet.stats.strength)
    })

    it('should throw error if evolution requirements not met', () => {
      const pet = factory.createPet({
        type: PetType.CAT,
        rarity: PetRarity.COMMON,
        level: 5
      })

      const evolutionPath = {
        from: PetType.CAT,
        to: PetType.KITSUNE,
        requirements: {
          level: 20,
          experience: 1000
        },
        rarity: PetRarity.LEGENDARY,
        probability: 1.0
      }

      expect(() => factory.evolvePet(pet, evolutionPath)).toThrow('萌宠等级不足')
    })
  })
})

describe('PET_TEMPLATES', () => {
  it('should have templates for all pet types', () => {
    const petTypes = Object.values(PetType)

    petTypes.forEach(type => {
      expect(PET_TEMPLATES[type]).toBeDefined()
      expect(PET_TEMPLATES[type].type).toBe(type)
      expect(PET_TEMPLATES[type].baseStats).toBeDefined()
      expect(PET_TEMPLATES[type].appearance).toBeDefined()
    })
  })

  it('should have valid stat ranges for different rarities', () => {
    Object.values(PET_TEMPLATES).forEach(template => {
      const stats = template.baseStats

      expect(stats.strength).toBeGreaterThan(0)
      expect(stats.intelligence).toBeGreaterThan(0)
      expect(stats.agility).toBeGreaterThan(0)
      expect(stats.charm).toBeGreaterThan(0)
      expect(stats.vitality).toBeGreaterThan(0)
      expect(stats.luck).toBeGreaterThan(0)
    })
  })
})

describe('TRAIT_DATABASE', () => {
  it('should have valid trait definitions', () => {
    Object.entries(TRAIT_DATABASE).forEach(([key, trait]) => {
      expect(trait.name).toBeDefined()
      expect(trait.type).toBeDefined()
      expect(trait.rarity).toBeDefined()
      expect(trait.description).toBeDefined()
      expect(trait.effects).toBeInstanceOf(Array)
      expect(trait.effects.length).toBeGreaterThan(0)
    })
  })

  it('should have effects with valid structure', () => {
    Object.values(TRAIT_DATABASE).forEach(trait => {
      trait.effects.forEach(effect => {
        expect(effect.type).toBeDefined()
        expect(effect.target).toBeDefined()
        expect(typeof effect.value).toBe('number')
      })
    })
  })
})

describe('SKILL_DATABASE', () => {
  it('should have valid skill definitions', () => {
    Object.entries(SKILL_DATABASE).forEach(([key, skill]) => {
      expect(skill.name).toBeDefined()
      expect(skill.maxLevel).toBeGreaterThan(0)
      expect(skill.maxExperience).toBeGreaterThan(0)
      expect(skill.type).toBeDefined()
      expect(skill.description).toBeDefined()
      expect(skill.effects).toBeInstanceOf(Array)
      expect(skill.requirements).toBeInstanceOf(Array)
    })
  })

  it('should have requirements with valid structure', () => {
    Object.values(SKILL_DATABASE).forEach(skill => {
      skill.requirements.forEach(req => {
        expect(req.type).toBeDefined()
        expect(req.target).toBeDefined()
        expect(typeof req.value).toBe('number')
      })
    })
  })
})

describe('petFactory singleton', () => {
  it('should be properly initialized', () => {
    expect(petFactory).toBeInstanceOf(PetFactory)
  })

  it('should generate consistent pets', () => {
    const pet1 = petFactory.generateRandomPet({ type: PetType.CAT, rarity: PetRarity.COMMON })
    const pet2 = petFactory.generateRandomPet({ type: PetType.CAT, rarity: PetRarity.COMMON })

    expect(pet1.type).toBe(pet2.type)
    expect(pet1.rarity).toBe(pet2.rarity)
    expect(pet1.id).not.toBe(pet2.id) // 应该有不同的ID
  })
})
