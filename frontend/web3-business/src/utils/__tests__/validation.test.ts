import { describe, it, expect } from 'vitest'
import { InputValidator, InputSanitizer, SecurityValidator } from '../validation'
import { ethers } from 'ethers'

// Use a well-known valid Ethereum address
const VALID_ADDRESS = '******************************************' // vitalik.eth

describe('InputValidator', () => {
  describe('validateAddress', () => {
    it('should validate correct Ethereum addresses', () => {
      expect(InputValidator.validateAddress(VALID_ADDRESS)).toBe(true)
    })

    it('should reject invalid addresses', () => {
      expect(InputValidator.validateAddress('invalid')).toBe(false)
      expect(InputValidator.validateAddress('')).toBe(false)
      expect(InputValidator.validateAddress('0x123')).toBe(false)
    })

    it('should handle null and undefined', () => {
      expect(InputValidator.validateAddress(null as any)).toBe(false)
      expect(InputValidator.validateAddress(undefined as any)).toBe(false)
    })
  })

  describe('validateAmount', () => {
    it('should validate positive amounts', () => {
      expect(InputValidator.validateAmount('1.0')).toBe(true)
      expect(InputValidator.validateAmount('0.1')).toBe(true)
      expect(InputValidator.validateAmount('100')).toBe(true)
    })

    it('should reject zero and negative amounts', () => {
      expect(InputValidator.validateAmount('0')).toBe(false)
      expect(InputValidator.validateAmount('-1')).toBe(false)
    })

    it('should reject invalid formats', () => {
      expect(InputValidator.validateAmount('abc')).toBe(false)
      expect(InputValidator.validateAmount('')).toBe(false)
      expect(InputValidator.validateAmount('1.2.3')).toBe(false)
    })
  })

  describe('validatePrivateKey', () => {
    it('should validate correct private key formats', () => {
      const privateKey = '0x' + 'a'.repeat(64)
      expect(InputValidator.validatePrivateKey(privateKey)).toBe(true)

      const privateKeyWithoutPrefix = 'a'.repeat(64)
      expect(InputValidator.validatePrivateKey(privateKeyWithoutPrefix)).toBe(true)
    })

    it('should reject invalid private keys', () => {
      expect(InputValidator.validatePrivateKey('0x123')).toBe(false)
      expect(InputValidator.validatePrivateKey('invalid')).toBe(false)
      expect(InputValidator.validatePrivateKey('')).toBe(false)
    })
  })

  describe('validateTransactionHash', () => {
    it('should validate correct transaction hash', () => {
      const hash = '0x' + 'a'.repeat(64)
      expect(InputValidator.validateTransactionHash(hash)).toBe(true)
    })

    it('should reject invalid hashes', () => {
      expect(InputValidator.validateTransactionHash('0x123')).toBe(false)
      expect(InputValidator.validateTransactionHash('invalid')).toBe(false)
    })
  })

  describe('validateChainId', () => {
    it('should validate positive chain IDs', () => {
      expect(InputValidator.validateChainId(1)).toBe(true)
      expect(InputValidator.validateChainId('1')).toBe(true)
      expect(InputValidator.validateChainId(11155111)).toBe(true)
    })

    it('should reject invalid chain IDs', () => {
      expect(InputValidator.validateChainId(0)).toBe(false)
      expect(InputValidator.validateChainId(-1)).toBe(false)
      expect(InputValidator.validateChainId('abc')).toBe(false)
    })
  })

  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      expect(InputValidator.validateUrl('https://example.com')).toBe(true)
      expect(InputValidator.validateUrl('http://localhost:3000')).toBe(true)
    })

    it('should reject invalid URLs', () => {
      expect(InputValidator.validateUrl('invalid-url')).toBe(false)
      expect(InputValidator.validateUrl('')).toBe(false)
    })
  })

  describe('validatePetName', () => {
    it('should validate correct pet names', () => {
      expect(InputValidator.validatePetName('Fluffy')).toBe(true)
      expect(InputValidator.validatePetName('Pet123')).toBe(true)
      expect(InputValidator.validatePetName('小宠物')).toBe(true)
    })

    it('should reject invalid pet names', () => {
      expect(InputValidator.validatePetName('')).toBe(false)
      expect(InputValidator.validatePetName('a'.repeat(21))).toBe(false)
      expect(InputValidator.validatePetName('Pet<script>')).toBe(false)
      expect(InputValidator.validatePetName('Pet"name')).toBe(false)
    })
  })

  describe('validateNumberRange', () => {
    it('should validate numbers within range', () => {
      expect(InputValidator.validateNumberRange(5, 1, 10)).toBe(true)
      expect(InputValidator.validateNumberRange(1, 1, 10)).toBe(true)
      expect(InputValidator.validateNumberRange(10, 1, 10)).toBe(true)
    })

    it('should reject numbers outside range', () => {
      expect(InputValidator.validateNumberRange(0, 1, 10)).toBe(false)
      expect(InputValidator.validateNumberRange(11, 1, 10)).toBe(false)
      expect(InputValidator.validateNumberRange(NaN, 1, 10)).toBe(false)
      expect(InputValidator.validateNumberRange(Infinity, 1, 10)).toBe(false)
    })
  })

  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(InputValidator.validateEmail('<EMAIL>')).toBe(true)
      expect(InputValidator.validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email addresses', () => {
      expect(InputValidator.validateEmail('invalid-email')).toBe(false)
      expect(InputValidator.validateEmail('test@')).toBe(false)
      expect(InputValidator.validateEmail('@example.com')).toBe(false)
      expect(InputValidator.validateEmail('')).toBe(false)
    })
  })
})

describe('InputSanitizer', () => {
  describe('sanitizeHtml', () => {
    it('should remove HTML tags and escape special characters', () => {
      expect(InputSanitizer.sanitizeHtml('<script>alert("xss")</script>')).toBe('scriptalert(&quot;xss&quot;)/script')
      expect(InputSanitizer.sanitizeHtml('Hello <b>World</b>')).toBe('Hello bWorld/b')
    })

    it('should escape special characters', () => {
      expect(InputSanitizer.sanitizeHtml('Hello "World"')).toBe('Hello &quot;World&quot;')
      expect(InputSanitizer.sanitizeHtml("Hello 'World'")).toBe('Hello &#x27;World&#x27;')
      expect(InputSanitizer.sanitizeHtml('Hello & World')).toBe('Hello &amp; World')
    })

    it('should handle empty and null inputs', () => {
      expect(InputSanitizer.sanitizeHtml('')).toBe('')
      expect(InputSanitizer.sanitizeHtml(null as any)).toBe('')
      expect(InputSanitizer.sanitizeHtml(undefined as any)).toBe('')
    })
  })

  describe('sanitizePetName', () => {
    it('should trim and limit length', () => {
      expect(InputSanitizer.sanitizePetName('  Fluffy  ')).toBe('Fluffy')
      expect(InputSanitizer.sanitizePetName('a'.repeat(25))).toBe('a'.repeat(20))
    })

    it('should remove dangerous characters', () => {
      expect(InputSanitizer.sanitizePetName('Pet<script>')).toBe('Petscript')
      expect(InputSanitizer.sanitizePetName('Pet"name')).toBe('Petname')
    })
  })

  describe('sanitizeNumber', () => {
    it('should keep only numbers and dots', () => {
      expect(InputSanitizer.sanitizeNumber('123.45abc')).toBe('123.45')
      expect(InputSanitizer.sanitizeNumber('$100.00')).toBe('100.00')
    })

    it('should handle invalid inputs', () => {
      expect(InputSanitizer.sanitizeNumber('')).toBe('0')
      expect(InputSanitizer.sanitizeNumber(null as any)).toBe('0')
    })
  })

  describe('sanitizeAddress', () => {
    it('should trim and convert to lowercase', () => {
      expect(InputSanitizer.sanitizeAddress('  0xABC123  ')).toBe('0xabc123')
    })

    it('should handle invalid inputs', () => {
      expect(InputSanitizer.sanitizeAddress('')).toBe('')
      expect(InputSanitizer.sanitizeAddress(null as any)).toBe('')
    })
  })

  describe('sanitizeUrl', () => {
    it('should add https prefix if missing', () => {
      expect(InputSanitizer.sanitizeUrl('example.com')).toBe('https://example.com')
      expect(InputSanitizer.sanitizeUrl('http://example.com')).toBe('http://example.com')
      expect(InputSanitizer.sanitizeUrl('https://example.com')).toBe('https://example.com')
    })

    it('should handle invalid inputs', () => {
      expect(InputSanitizer.sanitizeUrl('')).toBe('')
      expect(InputSanitizer.sanitizeUrl(null as any)).toBe('')
    })
  })
})

describe('SecurityValidator', () => {
  describe('validateTransactionSecurity', () => {
    it('should validate secure transaction parameters', () => {
      const params = {
        to: VALID_ADDRESS,
        value: '1.0',
        gasLimit: '21000'
      }

      const result = SecurityValidator.validateTransactionSecurity(params)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should detect invalid addresses', () => {
      const params = {
        to: 'invalid-address',
        value: '1.0'
      }

      const result = SecurityValidator.validateTransactionSecurity(params)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('无效的接收地址')
    })

    it('should detect invalid amounts', () => {
      const params = {
        to: VALID_ADDRESS,
        value: 'invalid-amount'
      }

      const result = SecurityValidator.validateTransactionSecurity(params)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('无效的转账金额')
    })

    it('should detect dangerous gas limits', () => {
      const params = {
        to: VALID_ADDRESS,
        gasLimit: '20000000'
      }

      const result = SecurityValidator.validateTransactionSecurity(params)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Gas限制超出安全范围')
    })

    it('should detect overly long transaction data', () => {
      const params = {
        to: VALID_ADDRESS,
        data: '0x' + 'a'.repeat(200000)
      }

      const result = SecurityValidator.validateTransactionSecurity(params)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('交易数据过长，可能存在安全风险')
    })
  })

  describe('validateAmountSafety', () => {
    it('should validate amounts within safety limits', () => {
      expect(SecurityValidator.validateAmountSafety('1.0', '10.0')).toBe(true)
      expect(SecurityValidator.validateAmountSafety('10.0', '10.0')).toBe(true)
    })

    it('should reject amounts exceeding safety limits', () => {
      expect(SecurityValidator.validateAmountSafety('11.0', '10.0')).toBe(false)
      expect(SecurityValidator.validateAmountSafety('1001.0', '1000.0')).toBe(false)
    })

    it('should handle invalid amounts', () => {
      expect(SecurityValidator.validateAmountSafety('invalid', '10.0')).toBe(false)
      expect(SecurityValidator.validateAmountSafety('1.0', 'invalid')).toBe(false)
    })
  })
})
