/**
 * 存储工具测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { StorageUtil, STORAGE_VERSION } from '../storage'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

// Mock global objects
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
})

Object.defineProperty(global, 'crypto', {
  value: {
    subtle: {
      digest: vi.fn().mockResolvedValue(new ArrayBuffer(32))
    }
  },
  writable: true
})

// Mock window for setTimeout
Object.defineProperty(global, 'window', {
  value: {
    setTimeout: vi.fn((fn, delay) => setTimeout(fn, delay)),
    clearTimeout: vi.fn(clearTimeout)
  },
  writable: true
})

// Mock atob and btoa for encryption tests
Object.defineProperty(global, 'atob', {
  value: vi.fn((str: string) => Buffer.from(str, 'base64').toString('binary')),
  writable: true
})

Object.defineProperty(global, 'btoa', {
  value: vi.fn((str: string) => Buffer.from(str, 'binary').toString('base64')),
  writable: true
})

describe('StorageUtil', () => {
  let storageUtil: StorageUtil

  beforeEach(() => {
    storageUtil = new StorageUtil()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('setItem', () => {
    it('should save data successfully', async () => {
      const testData = { name: 'test', value: 123 }
      localStorageMock.setItem.mockImplementation(() => {})

      const result = await storageUtil.setItem('test-key', testData)

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalled()

      const savedData = JSON.parse(localStorageMock.setItem.mock.calls[0][1])
      expect(savedData.version).toBe(STORAGE_VERSION)
      expect(savedData.data).toEqual(testData)
      expect(savedData.timestamp).toBeTypeOf('number')
    })

    it('should handle save errors gracefully', async () => {
      const testData = { name: 'test' }
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage full')
      })

      const result = await storageUtil.setItem('test-key', testData)

      expect(result).toBe(false)
    })

    it('should encrypt data when requested', async () => {
      const testData = { secret: 'password' }
      localStorageMock.setItem.mockImplementation(() => {})

      const result = await storageUtil.setItem('test-key', testData, { encrypt: true })

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalled()

      // When encrypted, the data is base64 encoded, so we need to decode it first
      const encryptedData = localStorageMock.setItem.mock.calls[0][1]
      const decodedData = atob(encryptedData)
      const savedData = JSON.parse(decodedData)
      expect(savedData.checksum).toBeDefined()
    })
  })

  describe('getItem', () => {
    it('should retrieve data successfully', async () => {
      const testData = { name: 'test', value: 123 }
      const storageData = {
        version: STORAGE_VERSION,
        timestamp: Date.now(),
        data: testData
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(storageData))

      const result = await storageUtil.getItem('test-key')

      expect(result).toEqual(testData)
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key')
    })

    it('should return null for non-existent keys', async () => {
      localStorageMock.getItem.mockReturnValue(null)

      const result = await storageUtil.getItem('non-existent-key')

      expect(result).toBeNull()
    })

    it('should handle expired data', async () => {
      const testData = { name: 'test' }
      const storageData = {
        version: STORAGE_VERSION,
        timestamp: Date.now() - 2000, // 2 seconds ago
        data: testData
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(storageData))
      localStorageMock.removeItem.mockImplementation(() => {})

      const result = await storageUtil.getItem('test-key', { expiry: 1000 }) // 1 second expiry

      expect(result).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key')
    })

    it('should handle version incompatibility', async () => {
      const testData = { name: 'test' }
      const storageData = {
        version: '0.9.0', // Old version
        timestamp: Date.now(),
        data: testData
      }

      localStorageMock.getItem
        .mockReturnValueOnce(JSON.stringify(storageData))
        .mockReturnValueOnce(JSON.stringify({
          ...storageData,
          version: STORAGE_VERSION
        }))

      localStorageMock.setItem.mockImplementation(() => {})

      const result = await storageUtil.getItem('test-key')

      expect(result).toEqual(testData)
      expect(localStorageMock.setItem).toHaveBeenCalled() // Migration should occur
    })
  })

  describe('removeItem', () => {
    it('should remove item successfully', async () => {
      localStorageMock.removeItem.mockImplementation(() => {})

      const result = await storageUtil.removeItem('test-key')

      expect(result).toBe(true)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key')
    })

    it('should handle removal errors', async () => {
      localStorageMock.removeItem.mockImplementation(() => {
        throw new Error('Cannot remove')
      })

      const result = await storageUtil.removeItem('test-key')

      expect(result).toBe(false)
    })
  })

  describe('hasItem', () => {
    it('should return true for existing items', () => {
      localStorageMock.getItem.mockReturnValue('some-data')

      const result = storageUtil.hasItem('test-key')

      expect(result).toBe(true)
    })

    it('should return false for non-existing items', () => {
      localStorageMock.getItem.mockReturnValue(null)

      const result = storageUtil.hasItem('test-key')

      expect(result).toBe(false)
    })
  })

  describe('getStorageStats', () => {
    it('should return storage statistics', () => {
      localStorageMock.getItem
        .mockReturnValueOnce(JSON.stringify({
          version: STORAGE_VERSION,
          timestamp: 1000,
          data: { test: 'data1' }
        }))
        .mockReturnValueOnce(JSON.stringify({
          version: STORAGE_VERSION,
          timestamp: 2000,
          data: { test: 'data2' }
        }))
        .mockReturnValueOnce(null)

      const stats = storageUtil.getStorageStats()

      expect(stats).toMatchObject({
        itemCount: expect.any(Number),
        totalSize: expect.any(Number),
        lastModified: expect.any(Number),
        version: STORAGE_VERSION
      })
    })
  })

  describe('clear', () => {
    it('should clear all game-related storage', async () => {
      localStorageMock.removeItem.mockImplementation(() => {})

      const result = await storageUtil.clear()

      expect(result).toBe(true)
      expect(localStorageMock.removeItem).toHaveBeenCalledTimes(4) // Number of STORAGE_KEYS
    })
  })
})
