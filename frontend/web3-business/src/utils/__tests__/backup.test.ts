/**
 * 备份工具测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { BackupManager } from '../backup'
import { storageUtil } from '../storage'

// Mock storageUtil
vi.mock('../storage', () => ({
  storageUtil: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  },
  STORAGE_VERSION: '1.0.0'
}))

// Mock global objects
Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'mock-url'),
    revokeObjectURL: vi.fn()
  },
  writable: true
})

Object.defineProperty(global, 'Blob', {
  value: vi.fn(),
  writable: true
})

Object.defineProperty(global, 'document', {
  value: {
    createElement: vi.fn(() => ({
      href: '',
      download: '',
      click: vi.fn(),
      remove: vi.fn()
    })),
    body: {
      appendChild: vi.fn(),
      removeChild: vi.fn()
    }
  },
  writable: true
})

Object.defineProperty(global, 'FileReader', {
  value: vi.fn(() => ({
    readAsText: vi.fn(),
    onload: null,
    onerror: null,
    result: null
  })),
  writable: true
})

Object.defineProperty(global, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  },
  writable: true
})

Object.defineProperty(global, 'navigator', {
  value: {
    platform: 'test',
    language: 'en-US',
    cookieEnabled: true,
    onLine: true,
    userAgent: 'test-agent'
  },
  writable: true
})

Object.defineProperty(global, 'screen', {
  value: {
    width: 1920,
    height: 1080,
    colorDepth: 24
  },
  writable: true
})

Object.defineProperty(global, 'window', {
  value: {
    localStorage: {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn()
    }
  },
  writable: true
})

Object.defineProperty(global, 'File', {
  value: vi.fn((content, name) => ({
    content,
    name,
    size: content.length,
    type: 'application/json'
  })),
  writable: true
})

describe('BackupManager', () => {
  let backupManager: BackupManager

  beforeEach(() => {
    backupManager = new BackupManager()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('createBackup', () => {
    it('should create backup successfully', async () => {
      const mockGameData = { coins: 1000, tokens: '100' }
      const mockPetData = { pets: [], currentPetId: null }
      const mockSettings = { soundEnabled: true }

      vi.mocked(storageUtil.getItem)
        .mockResolvedValueOnce(mockGameData)
        .mockResolvedValueOnce(mockPetData)
        .mockResolvedValueOnce(mockSettings)
        .mockResolvedValueOnce(null) // wallet state

      const result = await backupManager.createBackup('0x123')

      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.data?.walletAddress).toBe('0x123')
      expect(result.data?.gameData).toEqual(mockGameData)
      expect(result.data?.petData).toEqual(mockPetData)
      expect(result.data?.userSettings).toEqual(mockSettings)
    })

    it('should handle backup creation errors', async () => {
      vi.mocked(storageUtil.getItem).mockRejectedValue(new Error('Storage error'))

      const result = await backupManager.createBackup()

      expect(result.success).toBe(false)
      expect(result.message).toContain('备份失败')
    })
  })

  describe('exportBackup', () => {
    it('should export backup to file', async () => {
      const mockBackupData = {
        version: '1.0.0',
        timestamp: Date.now(),
        walletAddress: '0x123',
        gameData: { coins: 1000 },
        petData: { pets: [] },
        userSettings: { soundEnabled: true },
        metadata: {
          deviceInfo: '{}',
          userAgent: 'test',
          exportedAt: Date.now()
        }
      }

      vi.mocked(storageUtil.getItem)
        .mockResolvedValueOnce(mockBackupData.gameData)
        .mockResolvedValueOnce(mockBackupData.petData)
        .mockResolvedValueOnce(mockBackupData.userSettings)
        .mockResolvedValueOnce(null)

      const mockLink = {
        href: '',
        download: '',
        click: vi.fn()
      }
      vi.mocked(document.createElement).mockReturnValue(mockLink as any)

      const result = await backupManager.exportBackup('0x123')

      expect(result.success).toBe(true)
      expect(mockLink.click).toHaveBeenCalled()
      expect(global.URL.createObjectURL).toHaveBeenCalled()
      expect(global.URL.revokeObjectURL).toHaveBeenCalled()
    })
  })

  describe('restoreFromBackup', () => {
    it('should restore data from valid backup', async () => {
      const mockBackupData = {
        version: '1.0.0',
        timestamp: Date.now(),
        gameData: { coins: 1000 },
        petData: { pets: [] },
        userSettings: { soundEnabled: true },
        metadata: {
          deviceInfo: '{}',
          userAgent: 'test',
          exportedAt: Date.now()
        }
      }

      vi.mocked(storageUtil.setItem).mockResolvedValue(true)

      const result = await backupManager.restoreFromBackup(mockBackupData)

      expect(result.success).toBe(true)
      expect(result.restoredItems).toContain('游戏状态')
      expect(result.restoredItems).toContain('萌宠数据')
      expect(result.restoredItems).toContain('用户设置')
    })

    it('should handle invalid backup data', async () => {
      const invalidBackupData = {
        // Missing required fields
        version: '1.0.0'
      }

      const result = await backupManager.restoreFromBackup(invalidBackupData as any)

      expect(result.success).toBe(false)
      expect(result.message).toContain('备份数据格式无效')
    })

    it('should handle storage errors during restore', async () => {
      const mockBackupData = {
        version: '1.0.0',
        timestamp: Date.now(),
        gameData: { coins: 1000 },
        petData: { pets: [] },
        userSettings: { soundEnabled: true },
        metadata: {
          deviceInfo: '{}',
          userAgent: 'test',
          exportedAt: Date.now()
        }
      }

      vi.mocked(storageUtil.setItem).mockResolvedValue(false)

      const result = await backupManager.restoreFromBackup(mockBackupData)

      expect(result.success).toBe(false)
      expect(result.skippedItems.length).toBeGreaterThan(0)
    })
  })

  describe('autoBackup', () => {
    it('should perform auto backup when needed', async () => {
      // Mock localStorage for auto backup timing
      const mockLocalStorage = {
        getItem: vi.fn().mockReturnValue(null), // No previous backup
        setItem: vi.fn()
      }
      Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })

      vi.mocked(storageUtil.getItem)
        .mockResolvedValueOnce({ coins: 1000 })
        .mockResolvedValueOnce({ pets: [] })
        .mockResolvedValueOnce({ soundEnabled: true })
        .mockResolvedValueOnce(null)

      vi.mocked(storageUtil.setItem).mockResolvedValue(true)

      const result = await backupManager.autoBackup('0x123')

      expect(result).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('should skip auto backup when not needed', async () => {
      const recentTime = Date.now() - 1000 // 1 second ago
      const mockLocalStorage = {
        getItem: vi.fn().mockReturnValue(recentTime.toString()),
        setItem: vi.fn()
      }
      Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })

      const result = await backupManager.autoBackup('0x123')

      expect(result).toBe(true) // Returns true but doesn't perform backup
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
    })
  })

  describe('importBackup', () => {
    it('should import backup from file', async () => {
      const mockBackupData = {
        version: '1.0.0',
        timestamp: Date.now(),
        gameData: { coins: 1000 },
        petData: { pets: [] },
        userSettings: { soundEnabled: true },
        metadata: {
          deviceInfo: '{}',
          userAgent: 'test',
          exportedAt: Date.now()
        }
      }

      const mockFile = new File([JSON.stringify(mockBackupData)], 'backup.json')

      // Mock FileReader
      const mockFileReader = {
        readAsText: vi.fn(),
        onload: null,
        onerror: null,
        result: JSON.stringify(mockBackupData)
      }

      vi.mocked(FileReader).mockImplementation(() => mockFileReader as any)
      vi.mocked(storageUtil.setItem).mockResolvedValue(true)

      // Simulate successful file read
      setTimeout(() => {
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: JSON.stringify(mockBackupData) } } as any)
        }
      }, 0)

      const result = await backupManager.importBackup(mockFile)

      expect(mockFileReader.readAsText).toHaveBeenCalledWith(mockFile)
    })

    it('should handle file read errors', async () => {
      const mockFile = new File(['invalid json'], 'backup.json')

      const mockFileReader = {
        readAsText: vi.fn(),
        onload: null,
        onerror: null,
        result: 'invalid json'
      }

      vi.mocked(FileReader).mockImplementation(() => mockFileReader as any)

      // Simulate file read with invalid JSON
      setTimeout(() => {
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: 'invalid json' } } as any)
        }
      }, 0)

      const result = await backupManager.importBackup(mockFile)

      expect(result.success).toBe(false)
      expect(result.message).toContain('导入失败')
    })
  })
})
