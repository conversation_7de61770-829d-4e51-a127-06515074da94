import { ethers } from 'ethers'
import { CONTRACT_CONFIG } from '@/config/constants'

/**
 * 格式化代币金额 (从wei转换为显示单位)
 * @param amount 代币金额 (wei)
 * @param decimals 小数位数
 * @returns 格式化后的金额
 */
export function formatTokenAmount(amount: string | bigint, decimals: number = 18): string {
  try {
    const formatted = ethers.formatUnits(amount, decimals)
    // 移除尾部的零
    return formatted.replace(/\.?0+$/, '')
  } catch (error) {
    console.error('格式化代币金额失败:', error)
    return '0'
  }
}

/**
 * 解析代币金额 (从显示单位转换为wei)
 * @param amount 代币金额 (显示单位)
 * @param decimals 小数位数
 * @returns 解析后的金额 (wei)
 */
export function parseTokenAmount(amount: string, decimals: number = 18): string {
  try {
    return ethers.parseUnits(amount, decimals).toString()
  } catch (error) {
    console.error('解析代币金额失败:', error)
    return '0'
  }
}

/**
 * 缩短地址显示
 * @param address 地址
 * @param prefixLength 前缀长度
 * @param suffixLength 后缀长度
 * @returns 缩短后的地址
 */
export function shortenAddress(address: string, prefixLength: number = 6, suffixLength: number = 4): string {
  if (!address) return ''
  if (!ethers.isAddress(address)) return 'Invalid Address'
  if (address.length <= prefixLength + suffixLength) return address
  return `${address.slice(0, prefixLength)}...${address.slice(-suffixLength)}`
}

/**
 * 获取交易链接
 * @param txHash 交易哈希
 * @returns 交易链接
 */
export function getTransactionLink(txHash: string): string {
  if (!txHash) return ''
  if (!CONTRACT_CONFIG.EXPLORER_URL) return ''
  return `${CONTRACT_CONFIG.EXPLORER_URL}/tx/${txHash}`
}

/**
 * 获取地址链接
 * @param address 地址
 * @returns 地址链接
 */
export function getAddressLink(address: string): string {
  if (!address) return ''
  if (!CONTRACT_CONFIG.EXPLORER_URL) return ''
  return `${CONTRACT_CONFIG.EXPLORER_URL}/address/${address}`
}

/**
 * 验证地址是否有效
 * @param address 地址
 * @returns 是否有效
 */
export function isValidAddress(address: string): boolean {
  return ethers.isAddress(address)
}

/**
 * 验证金额是否有效
 * @param amount 金额
 * @returns 是否有效
 */
export function isValidAmount(amount: string): boolean {
  try {
    const parsed = parseFloat(amount)
    return !isNaN(parsed) && parsed > 0
  } catch {
    return false
  }
}

/**
 * 计算代币价值 (如果有价格信息)
 * @param amount 代币金额
 * @param price 代币价格
 * @param decimals 小数位数
 * @returns 代币价值
 */
export function calculateTokenValue(amount: string, price: number, decimals: number = 18): string {
  try {
    const tokenAmount = parseFloat(formatTokenAmount(amount, decimals))
    const value = tokenAmount * price
    return value.toFixed(2)
  } catch {
    return '0'
  }
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @returns 格式化后的时间
 */
export function formatTimestamp(timestamp: number): string {
  return new Date(timestamp).toLocaleString()
}

/**
 * 检查是否为零地址
 * @param address 地址
 * @returns 是否为零地址
 */
export function isZeroAddress(address: string): boolean {
  return address === '0x0000000000000000000000000000000000000000'
}

/**
 * 计算交易状态文本
 * @param isPending 是否待处理
 * @param isSuccess 是否成功
 * @param error 错误信息
 * @returns 状态文本
 */
export function getTransactionStatusText(isPending: boolean, isSuccess: boolean, error?: string): string {
  if (isPending) return '处理中'
  if (error) return `失败: ${error}`
  if (isSuccess) return '成功'
  return '未知'
}

/**
 * 计算交易状态类名
 * @param isPending 是否待处理
 * @param isSuccess 是否成功
 * @param error 错误信息
 * @returns 状态类名
 */
export function getTransactionStatusClass(isPending: boolean, isSuccess: boolean, error?: string): string {
  if (isPending) return 'text-yellow-500'
  if (error) return 'text-red-500'
  if (isSuccess) return 'text-green-500'
  return 'text-gray-500'
}
