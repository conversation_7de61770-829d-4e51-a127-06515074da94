/**
 * 请求缓存和防抖工具
 * 提供请求缓存、防抖、节流等功能
 */

import { ref, computed } from 'vue'
import { debounce, throttle } from 'lodash-es'

interface CacheEntry<T = any> {
  data: T
  timestamp: number
  expiry: number
  loading: boolean
  error?: Error
}

interface RequestOptions {
  cacheKey?: string
  cacheTTL?: number // 缓存时间（毫秒）
  debounceMs?: number
  throttleMs?: number
  retries?: number
  retryDelay?: number
  timeout?: number
}

interface RequestStats {
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  errors: number
  averageResponseTime: number
}

class RequestCache {
  private static instance: RequestCache
  private cache = new Map<string, CacheEntry>()
  private pendingRequests = new Map<string, Promise<any>>()
  private stats: RequestStats = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    errors: 0,
    averageResponseTime: 0
  }
  private responseTimes: number[] = []

  static getInstance(): RequestCache {
    if (!RequestCache.instance) {
      RequestCache.instance = new RequestCache()
    }
    return RequestCache.instance
  }

  /**
   * 执行缓存请求
   */
  async request<T>(
    requestFn: () => Promise<T>,
    options: RequestOptions = {}
  ): Promise<T> {
    const {
      cacheKey = this.generateCacheKey(requestFn.toString()),
      cacheTTL = 5 * 60 * 1000, // 默认5分钟
      retries = 3,
      retryDelay = 1000,
      timeout = 10000
    } = options

    this.stats.totalRequests++
    const startTime = performance.now()

    // 检查缓存
    const cached = this.getFromCache<T>(cacheKey)
    if (cached && !this.isExpired(cached)) {
      this.stats.cacheHits++
      return cached.data
    }

    this.stats.cacheMisses++

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      return this.pendingRequests.get(cacheKey)!
    }

    // 创建请求 Promise
    const requestPromise = this.executeWithRetry(
      requestFn,
      retries,
      retryDelay,
      timeout
    )

    this.pendingRequests.set(cacheKey, requestPromise)

    try {
      const result = await requestPromise
      const endTime = performance.now()

      // 更新统计
      this.updateResponseTime(endTime - startTime)

      // 缓存结果
      this.setCache(cacheKey, result, cacheTTL)

      return result
    } catch (error) {
      this.stats.errors++

      // 缓存错误（短时间）
      this.setCache(cacheKey, null, 30000, error as Error)

      throw error
    } finally {
      this.pendingRequests.delete(cacheKey)
    }
  }

  /**
   * 带重试的请求执行
   */
  private async executeWithRetry<T>(
    requestFn: () => Promise<T>,
    retries: number,
    retryDelay: number,
    timeout: number
  ): Promise<T> {
    let lastError: Error

    for (let i = 0; i <= retries; i++) {
      try {
        // 添加超时控制
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), timeout)
        })

        const result = await Promise.race([requestFn(), timeoutPromise])
        return result
      } catch (error) {
        lastError = error as Error

        if (i < retries) {
          // 指数退避
          const delay = retryDelay * Math.pow(2, i)
          await this.sleep(delay)
        }
      }
    }

    throw lastError!
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string): CacheEntry<T> | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }

    return entry as CacheEntry<T>
  }

  /**
   * 设置缓存
   */
  private setCache<T>(
    key: string,
    data: T,
    ttl: number,
    error?: Error
  ): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl,
      loading: false,
      error
    }

    this.cache.set(key, entry)
  }

  /**
   * 检查缓存是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiry
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(input: string): string {
    // 简单的哈希函数
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return `cache_${Math.abs(hash)}`
  }

  /**
   * 更新响应时间统计
   */
  private updateResponseTime(time: number): void {
    this.responseTimes.push(time)

    // 只保留最近100次的响应时间
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift()
    }

    this.stats.averageResponseTime =
      this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear()
      return
    }

    const regex = new RegExp(pattern)
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 获取缓存统计
   */
  getStats(): RequestStats {
    return { ...this.stats }
  }

  /**
   * 获取缓存大小
   */
  getCacheSize(): number {
    return this.cache.size
  }

  /**
   * 清理过期缓存
   */
  cleanupExpired(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache) {
      if (now > entry.expiry) {
        this.cache.delete(key)
      }
    }
  }
}

// 导出单例实例
export const requestCache = RequestCache.getInstance()

// 防抖和节流工具
export class DebounceThrottle {
  private static debouncedFunctions = new Map<string, Function>()
  private static throttledFunctions = new Map<string, Function>()

  /**
   * 创建防抖函数
   */
  static debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ): T {
    const cacheKey = key || fn.toString()

    if (!this.debouncedFunctions.has(cacheKey)) {
      this.debouncedFunctions.set(cacheKey, debounce(fn, delay))
    }

    return this.debouncedFunctions.get(cacheKey) as T
  }

  /**
   * 创建节流函数
   */
  static throttle<T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ): T {
    const cacheKey = key || fn.toString()

    if (!this.throttledFunctions.has(cacheKey)) {
      this.throttledFunctions.set(cacheKey, throttle(fn, delay))
    }

    return this.throttledFunctions.get(cacheKey) as T
  }

  /**
   * 清理缓存的函数
   */
  static clear(): void {
    this.debouncedFunctions.clear()
    this.throttledFunctions.clear()
  }
}

// Vue 3 组合式函数
export function useRequestCache() {
  const stats = ref(requestCache.getStats())
  const cacheSize = ref(requestCache.getCacheSize())

  // 定期更新统计信息
  const updateStats = () => {
    stats.value = requestCache.getStats()
    cacheSize.value = requestCache.getCacheSize()
  }

  // 缓存命中率
  const hitRate = computed(() => {
    const total = stats.value.cacheHits + stats.value.cacheMisses
    return total > 0 ? (stats.value.cacheHits / total * 100).toFixed(2) : '0'
  })

  // 错误率
  const errorRate = computed(() => {
    const total = stats.value.totalRequests
    return total > 0 ? (stats.value.errors / total * 100).toFixed(2) : '0'
  })

  const cachedRequest = <T>(
    requestFn: () => Promise<T>,
    options?: RequestOptions
  ) => requestCache.request(requestFn, options)

  const clearCache = (pattern?: string) => {
    requestCache.clearCache(pattern)
    updateStats()
  }

  const cleanupExpired = () => {
    requestCache.cleanupExpired()
    updateStats()
  }

  // 定期清理过期缓存
  const cleanupInterval = setInterval(() => {
    cleanupExpired()
  }, 60000) // 每分钟清理一次

  // 定期更新统计
  const statsInterval = setInterval(updateStats, 5000) // 每5秒更新一次

  // 清理定时器
  const cleanup = () => {
    clearInterval(cleanupInterval)
    clearInterval(statsInterval)
  }

  return {
    stats: computed(() => stats.value),
    cacheSize: computed(() => cacheSize.value),
    hitRate,
    errorRate,
    cachedRequest,
    clearCache,
    cleanupExpired,
    cleanup
  }
}

// 防抖和节流的组合式函数
export function useDebounceThrottle() {
  const createDebounced = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ) => DebounceThrottle.debounce(fn, delay, key)

  const createThrottled = <T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    key?: string
  ) => DebounceThrottle.throttle(fn, delay, key)

  return {
    createDebounced,
    createThrottled
  }
}
