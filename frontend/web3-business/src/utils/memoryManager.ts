/**
 * 内存管理工具
 * 提供内存监控、垃圾回收、资源清理等功能
 */

import { ref, computed, onUnmounted, nextTick } from 'vue'

interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  usage: number // 使用率百分比
}

interface ResourceTracker {
  id: string
  type: 'listener' | 'timer' | 'observer' | 'animation' | 'worker' | 'connection'
  resource: any
  cleanup: () => void
  created: number
}

interface MemoryStats {
  peakUsage: number
  averageUsage: number
  gcCount: number
  lastGcTime: number
  resourceCount: number
  leakWarnings: number
}

class MemoryManager {
  private static instance: MemoryManager
  private resources = new Map<string, ResourceTracker>()
  private memoryHistory: number[] = []
  private stats: MemoryStats = {
    peakUsage: 0,
    averageUsage: 0,
    gcCount: 0,
    lastGcTime: 0,
    resourceCount: 0,
    leakWarnings: 0
  }
  private monitoringInterval: number | null = null
  private gcObserver: PerformanceObserver | null = null

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager()
    }
    return MemoryManager.instance
  }

  constructor() {
    this.startMonitoring()
    this.setupGCObserver()
  }

  /**
   * 开始内存监控
   */
  private startMonitoring(): void {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return
    }

    this.monitoringInterval = window.setInterval(() => {
      const memInfo = this.getMemoryInfo()
      if (memInfo) {
        this.updateMemoryStats(memInfo)
        this.checkMemoryLeaks()
      }
    }, 5000) // 每5秒检查一次
  }

  /**
   * 设置垃圾回收观察器
   */
  private setupGCObserver(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    try {
      this.gcObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.entryType === 'measure' && entry.name.includes('gc')) {
            this.stats.gcCount++
            this.stats.lastGcTime = Date.now()
          }
        })
      })

      this.gcObserver.observe({ entryTypes: ['measure'] })
    } catch (e) {
      console.warn('GC monitoring not supported')
    }
  }

  /**
   * 获取内存信息
   */
  getMemoryInfo(): MemoryInfo | null {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return null
    }

    const memory = (performance as any).memory
    if (!memory) return null

    const usage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100

    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usage
    }
  }

  /**
   * 更新内存统计
   */
  private updateMemoryStats(memInfo: MemoryInfo): void {
    this.memoryHistory.push(memInfo.usage)

    // 只保留最近100次的记录
    if (this.memoryHistory.length > 100) {
      this.memoryHistory.shift()
    }

    // 更新峰值
    if (memInfo.usage > this.stats.peakUsage) {
      this.stats.peakUsage = memInfo.usage
    }

    // 计算平均值
    this.stats.averageUsage =
      this.memoryHistory.reduce((a, b) => a + b, 0) / this.memoryHistory.length

    // 更新资源计数
    this.stats.resourceCount = this.resources.size
  }

  /**
   * 检查内存泄漏
   */
  private checkMemoryLeaks(): void {
    const memInfo = this.getMemoryInfo()
    if (!memInfo) return

    // 内存使用率超过80%时发出警告
    if (memInfo.usage > 80) {
      this.stats.leakWarnings++
      console.warn('High memory usage detected:', memInfo.usage.toFixed(2) + '%')

      // 自动清理过期资源
      this.cleanupExpiredResources()

      // 建议垃圾回收
      this.suggestGarbageCollection()
    }

    // 检查长时间存在的资源
    const now = Date.now()
    const oldResources = Array.from(this.resources.values()).filter(
      resource => now - resource.created > 10 * 60 * 1000 // 10分钟
    )

    if (oldResources.length > 10) {
      console.warn('Potential memory leak: too many long-lived resources', oldResources.length)
      this.stats.leakWarnings++
    }
  }

  /**
   * 注册资源
   */
  registerResource(
    type: ResourceTracker['type'],
    resource: any,
    cleanup: () => void
  ): string {
    const id = this.generateId()

    const tracker: ResourceTracker = {
      id,
      type,
      resource,
      cleanup,
      created: Date.now()
    }

    this.resources.set(id, tracker)
    return id
  }

  /**
   * 注销资源
   */
  unregisterResource(id: string): void {
    const tracker = this.resources.get(id)
    if (tracker) {
      try {
        tracker.cleanup()
      } catch (e) {
        console.warn('Error cleaning up resource:', e)
      }
      this.resources.delete(id)
    }
  }

  /**
   * 清理过期资源
   */
  private cleanupExpiredResources(): void {
    const now = Date.now()
    const expiredResources = Array.from(this.resources.entries()).filter(
      ([_, tracker]) => now - tracker.created > 15 * 60 * 1000 // 15分钟
    )

    expiredResources.forEach(([id]) => {
      this.unregisterResource(id)
    })

    if (expiredResources.length > 0) {
      console.log(`Cleaned up ${expiredResources.length} expired resources`)
    }
  }

  /**
   * 清理所有资源
   */
  cleanupAllResources(): void {
    const resourceIds = Array.from(this.resources.keys())
    resourceIds.forEach(id => this.unregisterResource(id))

    console.log(`Cleaned up ${resourceIds.length} resources`)
  }

  /**
   * 建议垃圾回收
   */
  private suggestGarbageCollection(): void {
    // 在下一个事件循环中执行，避免阻塞当前操作
    setTimeout(() => {
      // 创建一些临时对象来触发垃圾回收
      const temp = new Array(1000).fill(null).map(() => ({}))
      temp.length = 0

      // 强制执行微任务队列
      Promise.resolve().then(() => {
        // 这里可以添加更多的清理逻辑
      })
    }, 0)
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取统计信息
   */
  getStats(): MemoryStats & { memoryInfo: MemoryInfo | null } {
    return {
      ...this.stats,
      memoryInfo: this.getMemoryInfo()
    }
  }

  /**
   * 获取资源列表
   */
  getResources(): ResourceTracker[] {
    return Array.from(this.resources.values())
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.monitoringInterval) {
      if (typeof window !== 'undefined' && window.clearInterval) {
        window.clearInterval(this.monitoringInterval)
      } else {
        clearInterval(this.monitoringInterval)
      }
      this.monitoringInterval = null
    }

    if (this.gcObserver) {
      try {
        this.gcObserver.disconnect()
      } catch (e) {
        console.warn('Error disconnecting GC observer:', e)
      }
      this.gcObserver = null
    }

    this.cleanupAllResources()

    // 清理内存历史
    this.memoryHistory = []

    // 重置统计信息
    this.stats = {
      peakUsage: 0,
      averageUsage: 0,
      gcCount: 0,
      lastGcTime: 0,
      resourceCount: 0,
      leakWarnings: 0
    }
  }
}

// 导出单例实例
export const memoryManager = MemoryManager.getInstance()

// Vue 3 组合式函数
export function useMemoryManager() {
  const stats = ref(memoryManager.getStats())
  const resources = ref(memoryManager.getResources())

  // 定期更新统计信息
  const updateStats = () => {
    stats.value = memoryManager.getStats()
    resources.value = memoryManager.getResources()
  }

  const statsInterval = setInterval(updateStats, 5000)

  // 注册资源的便捷方法
  const registerEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): string => {
    element.addEventListener(event, handler, options)

    return memoryManager.registerResource(
      'listener',
      { element, event, handler, options },
      () => element.removeEventListener(event, handler, options)
    )
  }

  const registerTimer = (
    callback: () => void,
    delay: number,
    type: 'timeout' | 'interval' = 'timeout'
  ): string => {
    const timerId = type === 'timeout'
      ? setTimeout(callback, delay)
      : setInterval(callback, delay)

    return memoryManager.registerResource(
      'timer',
      { timerId, type },
      () => {
        if (type === 'timeout') {
          clearTimeout(timerId)
        } else {
          clearInterval(timerId)
        }
      }
    )
  }

  const registerObserver = (
    observer: IntersectionObserver | MutationObserver | ResizeObserver | PerformanceObserver,
    name: string
  ): string => {
    return memoryManager.registerResource(
      'observer',
      { observer, name },
      () => observer.disconnect()
    )
  }

  const registerAnimation = (
    animation: Animation,
    name: string
  ): string => {
    return memoryManager.registerResource(
      'animation',
      { animation, name },
      () => animation.cancel()
    )
  }

  const registerWorker = (
    worker: Worker,
    name: string
  ): string => {
    return memoryManager.registerResource(
      'worker',
      { worker, name },
      () => worker.terminate()
    )
  }

  const unregister = (id: string) => {
    memoryManager.unregisterResource(id)
    updateStats()
  }

  const cleanup = () => {
    memoryManager.cleanupAllResources()
    updateStats()
  }

  // 组件卸载时清理
  onUnmounted(() => {
    clearInterval(statsInterval)
  })

  return {
    stats: computed(() => stats.value),
    resources: computed(() => resources.value),
    registerEventListener,
    registerTimer,
    registerObserver,
    registerAnimation,
    registerWorker,
    unregister,
    cleanup,
    updateStats
  }
}

// 简化的自动资源管理装饰器
export function autoCleanup<T extends (...args: any[]) => any>(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
): void {
  const originalMethod = descriptor.value

  descriptor.value = function (...args: any[]) {
    try {
      const result = originalMethod.apply(this, args)
      return result
    } catch (error) {
      // 发生错误时清理所有资源
      memoryManager.cleanupAllResources()
      throw error
    }
  }
}
