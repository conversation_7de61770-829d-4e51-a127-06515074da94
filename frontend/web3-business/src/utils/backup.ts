/**
 * 数据备份和恢复工具
 * 提供游戏数据的导出、导入、备份和恢复功能
 */

import { storageUtil, STORAGE_VERSION } from './storage'
import type { BackupData } from './storage'
import { STORAGE_KEYS } from '../config/constants'
import type { Pet } from '../types'
import type { GameSettings } from '../stores/game'

/**
 * 备份恢复结果
 */
export interface BackupResult {
  success: boolean
  message: string
  data?: BackupData
}

export interface RestoreResult {
  success: boolean
  message: string
  restoredItems: string[]
  skippedItems: string[]
}

/**
 * 备份管理器
 */
export class BackupManager {
  private readonly version = STORAGE_VERSION

  /**
   * 创建完整备份
   */
  async createBackup(walletAddress?: string): Promise<BackupResult> {
    try {
      // 收集所有游戏数据
      const gameData = await storageUtil.getItem(STORAGE_KEYS.GAME_STATE)
      const petData = await storageUtil.getItem(STORAGE_KEYS.PET_STATE)
      const userSettings = await storageUtil.getItem(STORAGE_KEYS.USER_SETTINGS)
      const walletState = await storageUtil.getItem(STORAGE_KEYS.WALLET_STATE)

      // 创建备份数据
      const backupData: BackupData = {
        version: this.version,
        timestamp: Date.now(),
        walletAddress,
        gameData,
        petData,
        userSettings,
        metadata: {
          deviceInfo: this.getDeviceInfo(),
          userAgent: navigator.userAgent,
          exportedAt: Date.now()
        }
      }

      return {
        success: true,
        message: '备份创建成功',
        data: backupData
      }
    } catch (error) {
      console.error('创建备份失败:', error)
      return {
        success: false,
        message: `备份失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 导出备份到文件
   */
  async exportBackup(walletAddress?: string): Promise<BackupResult> {
    const backupResult = await this.createBackup(walletAddress)

    if (!backupResult.success || !backupResult.data) {
      return backupResult
    }

    try {
      const backupJson = JSON.stringify(backupResult.data, null, 2)
      const blob = new Blob([backupJson], { type: 'application/json' })
      const url = URL.createObjectURL(blob)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = url
      link.download = `pet-game-backup-${Date.now()}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      URL.revokeObjectURL(url)

      return {
        success: true,
        message: '备份文件导出成功'
      }
    } catch (error) {
      console.error('导出备份失败:', error)
      return {
        success: false,
        message: `导出失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 从文件导入备份
   */
  async importBackup(file: File): Promise<RestoreResult> {
    try {
      const fileContent = await this.readFileAsText(file)
      const backupData: BackupData = JSON.parse(fileContent)

      return await this.restoreFromBackup(backupData)
    } catch (error) {
      console.error('导入备份失败:', error)
      return {
        success: false,
        message: `导入失败: ${error instanceof Error ? error.message : '文件格式错误'}`,
        restoredItems: [],
        skippedItems: []
      }
    }
  }

  /**
   * 从备份数据恢复
   */
  async restoreFromBackup(backupData: BackupData): Promise<RestoreResult> {
    const restoredItems: string[] = []
    const skippedItems: string[] = []

    try {
      // 验证备份数据
      if (!this.validateBackupData(backupData)) {
        return {
          success: false,
          message: '备份数据格式无效',
          restoredItems,
          skippedItems
        }
      }

      // 版本兼容性检查
      if (!this.isBackupVersionCompatible(backupData.version)) {
        console.warn(`备份版本不兼容: ${backupData.version} -> ${this.version}`)
        // 可以在这里实现版本迁移逻辑
      }

      // 恢复游戏数据
      if (backupData.gameData) {
        const success = await storageUtil.setItem(
          STORAGE_KEYS.GAME_STATE,
          backupData.gameData,
          { encrypt: true }
        )
        if (success) {
          restoredItems.push('游戏状态')
        } else {
          skippedItems.push('游戏状态')
        }
      }

      // 恢复萌宠数据
      if (backupData.petData) {
        const success = await storageUtil.setItem(
          STORAGE_KEYS.PET_STATE,
          backupData.petData,
          { encrypt: true }
        )
        if (success) {
          restoredItems.push('萌宠数据')
        } else {
          skippedItems.push('萌宠数据')
        }
      }

      // 恢复用户设置
      if (backupData.userSettings) {
        const success = await storageUtil.setItem(
          STORAGE_KEYS.USER_SETTINGS,
          backupData.userSettings
        )
        if (success) {
          restoredItems.push('用户设置')
        } else {
          skippedItems.push('用户设置')
        }
      }

      const success = restoredItems.length > 0
      const message = success
        ? `恢复成功: ${restoredItems.join(', ')}`
        : '没有数据被恢复'

      return {
        success,
        message,
        restoredItems,
        skippedItems
      }
    } catch (error) {
      console.error('恢复备份失败:', error)
      return {
        success: false,
        message: `恢复失败: ${error instanceof Error ? error.message : '未知错误'}`,
        restoredItems,
        skippedItems
      }
    }
  }

  /**
   * 自动备份
   */
  async autoBackup(walletAddress?: string): Promise<boolean> {
    try {
      const lastBackupKey = 'last_auto_backup'
      const lastBackupTime = localStorage.getItem(lastBackupKey)
      const now = Date.now()
      const backupInterval = 24 * 60 * 60 * 1000 // 24小时

      // 检查是否需要备份
      if (lastBackupTime && (now - parseInt(lastBackupTime)) < backupInterval) {
        return true // 不需要备份
      }

      const backupResult = await this.createBackup(walletAddress)

      if (backupResult.success && backupResult.data) {
        // 保存自动备份到本地存储
        const autoBackupKey = 'auto_backup_data'
        await storageUtil.setItem(autoBackupKey, backupResult.data, { encrypt: true })
        localStorage.setItem(lastBackupKey, now.toString())

        console.log('自动备份完成')
        return true
      }

      return false
    } catch (error) {
      console.error('自动备份失败:', error)
      return false
    }
  }

  /**
   * 获取自动备份
   */
  async getAutoBackup(): Promise<BackupData | null> {
    try {
      const autoBackupKey = 'auto_backup_data'
      return await storageUtil.getItem<BackupData>(autoBackupKey, { encrypt: true })
    } catch (error) {
      console.error('获取自动备份失败:', error)
      return null
    }
  }

  /**
   * 清除自动备份
   */
  async clearAutoBackup(): Promise<boolean> {
    try {
      const autoBackupKey = 'auto_backup_data'
      const lastBackupKey = 'last_auto_backup'

      await storageUtil.removeItem(autoBackupKey)
      localStorage.removeItem(lastBackupKey)

      return true
    } catch (error) {
      console.error('清除自动备份失败:', error)
      return false
    }
  }

  /**
   * 验证备份数据
   */
  private validateBackupData(backupData: any): backupData is BackupData {
    return (
      backupData &&
      typeof backupData === 'object' &&
      typeof backupData.version === 'string' &&
      typeof backupData.timestamp === 'number' &&
      backupData.metadata &&
      typeof backupData.metadata === 'object'
    )
  }

  /**
   * 检查备份版本兼容性
   */
  private isBackupVersionCompatible(backupVersion: string): boolean {
    // 简单的版本比较，可以根据需要扩展
    const currentVersion = this.version.split('.').map(Number)
    const backupVersionParts = backupVersion.split('.').map(Number)

    // 主版本号必须相同
    return currentVersion[0] === backupVersionParts[0]
  }

  /**
   * 读取文件内容
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string)
        } else {
          reject(new Error('文件读取失败'))
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取错误'))
      }

      reader.readAsText(file)
    })
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): string {
    const info = {
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      }
    }

    return JSON.stringify(info)
  }
}

// 创建全局备份管理器实例
export const backupManager = new BackupManager()

/**
 * 便捷函数
 */

/**
 * 快速导出备份
 */
export async function exportGameBackup(walletAddress?: string): Promise<BackupResult> {
  return await backupManager.exportBackup(walletAddress)
}

/**
 * 快速导入备份
 */
export async function importGameBackup(file: File): Promise<RestoreResult> {
  return await backupManager.importBackup(file)
}

/**
 * 快速自动备份
 */
export async function performAutoBackup(walletAddress?: string): Promise<boolean> {
  return await backupManager.autoBackup(walletAddress)
}
