import {
  PetRarity,
  PetType,
  PetStatus,
  GrowthStage,
  Pet<PERSON>ood,
  PetSize,
  TraitType,
  SkillType,
  PetStatsCalculator
} from '../types/typesWithoutCircular'

import type {
  Pet,
  PetStats,
  Pet<PERSON>ppearance,
  PetTrait,
  PetSkill,
  PetFactoryConfig,
  PetGenerationO<PERSON>s,
  PetTemplate,
  EvolutionPath
} from '../types/typesWithoutCircular'

// 默认工厂配置
const DEFAULT_FACTORY_CONFIG: PetFactoryConfig = {
  defaultRarity: PetRarity.COMMON,
  rarityWeights: {
    [PetRarity.COMMON]: 50,
    [PetRarity.UNCOMMON]: 25,
    [PetRarity.RARE]: 15,
    [PetRarity.EPIC]: 7,
    [PetRarity.LEGENDARY]: 2.5,
    [PetRarity.MYTHICAL]: 0.5
  },
  typeWeights: {
    [PetType.CAT]: 20,
    [PetType.DOG]: 20,
    [PetType.RABBIT]: 15,
    [PetType.BIRD]: 15,
    [PetType.DRAGON]: 8,
    [PetType.UNICORN]: 6,
    [PetType.PHOENIX]: 5,
    [PetType.GRIFFIN]: 4,
    [PetType.PEGASUS]: 4,
    [PetType.KITSUNE]: 3
  },
  statRanges: {
    [PetRarity.COMMON]: { min: 10, max: 20 },
    [PetRarity.UNCOMMON]: { min: 15, max: 30 },
    [PetRarity.RARE]: { min: 25, max: 45 },
    [PetRarity.EPIC]: { min: 40, max: 65 },
    [PetRarity.LEGENDARY]: { min: 60, max: 85 },
    [PetRarity.MYTHICAL]: { min: 80, max: 100 }
  },
  traitProbabilities: {
    [PetRarity.COMMON]: 0.1,
    [PetRarity.UNCOMMON]: 0.3,
    [PetRarity.RARE]: 0.5,
    [PetRarity.EPIC]: 0.7,
    [PetRarity.LEGENDARY]: 0.9,
    [PetRarity.MYTHICAL]: 1.0
  },
  skillProbabilities: {
    [PetRarity.COMMON]: 0.05,
    [PetRarity.UNCOMMON]: 0.15,
    [PetRarity.RARE]: 0.3,
    [PetRarity.EPIC]: 0.5,
    [PetRarity.LEGENDARY]: 0.7,
    [PetRarity.MYTHICAL]: 0.9
  }
}

// 萌宠模板数据
const PET_TEMPLATES: Record<PetType, PetTemplate> = {
  [PetType.CAT]: {
    id: 'cat_template',
    name: '小猫',
    type: PetType.CAT,
    rarity: PetRarity.COMMON,
    baseStats: { strength: 15, intelligence: 20, agility: 25, charm: 18, vitality: 16, luck: 12 },
    appearance: {
      species: 'cat',
      color: 'orange',
      pattern: 'striped',
      size: PetSize.SMALL,
      accessories: [],
      specialEffects: [],
      animations: ['idle', 'walk', 'play', 'sleep']
    },
    availableTraits: ['agile', 'curious', 'independent'],
    availableSkills: ['hunting', 'stealth', 'climbing'],
    growthModifiers: {
      experienceMultiplier: 1.0,
      statGrowthRate: 1.0,
      skillLearningRate: 1.2
    },
    specialAbilities: ['night_vision', 'balance'],
    description: '可爱的小猫咪，敏捷而聪明'
  },
  [PetType.DOG]: {
    id: 'dog_template',
    name: '小狗',
    type: PetType.DOG,
    rarity: PetRarity.COMMON,
    baseStats: { strength: 20, intelligence: 18, agility: 20, charm: 22, vitality: 20, luck: 10 },
    appearance: {
      species: 'dog',
      color: 'brown',
      pattern: 'solid',
      size: PetSize.MEDIUM,
      accessories: [],
      specialEffects: [],
      animations: ['idle', 'walk', 'play', 'bark']
    },
    availableTraits: ['loyal', 'friendly', 'energetic'],
    availableSkills: ['fetch', 'guard', 'tracking'],
    growthModifiers: {
      experienceMultiplier: 1.1,
      statGrowthRate: 1.0,
      skillLearningRate: 1.0
    },
    specialAbilities: ['loyalty_bonus', 'pack_leader'],
    description: '忠诚的小狗，人类最好的朋友'
  },
  [PetType.DRAGON]: {
    id: 'dragon_template',
    name: '幼龙',
    type: PetType.DRAGON,
    rarity: PetRarity.EPIC,
    baseStats: { strength: 35, intelligence: 30, agility: 25, charm: 20, vitality: 40, luck: 25 },
    appearance: {
      species: 'dragon',
      color: 'red',
      pattern: 'scales',
      size: PetSize.LARGE,
      accessories: [],
      specialEffects: ['fire_aura'],
      animations: ['idle', 'fly', 'breathe_fire', 'roar']
    },
    availableTraits: ['ancient_wisdom', 'fire_affinity', 'magical'],
    availableSkills: ['fire_breath', 'flight', 'magic_resistance'],
    growthModifiers: {
      experienceMultiplier: 0.8,
      statGrowthRate: 1.5,
      skillLearningRate: 0.9
    },
    specialAbilities: ['elemental_mastery', 'ancient_knowledge'],
    description: '传说中的龙族幼崽，拥有强大的魔法力量'
  },
  // 其他宠物类型的模板...
  [PetType.RABBIT]: {
    id: 'rabbit_template',
    name: '小兔',
    type: PetType.RABBIT,
    rarity: PetRarity.COMMON,
    baseStats: { strength: 12, intelligence: 16, agility: 28, charm: 20, vitality: 14, luck: 15 },
    appearance: {
      species: 'rabbit',
      color: 'white',
      pattern: 'fluffy',
      size: PetSize.SMALL,
      accessories: [],
      specialEffects: [],
      animations: ['idle', 'hop', 'eat', 'sleep']
    },
    availableTraits: ['quick', 'gentle', 'lucky'],
    availableSkills: ['speed_boost', 'herb_knowledge', 'evasion'],
    growthModifiers: {
      experienceMultiplier: 1.2,
      statGrowthRate: 0.9,
      skillLearningRate: 1.1
    },
    specialAbilities: ['lucky_find', 'speed_burst'],
    description: '温顺的小兔子，速度极快'
  },
  [PetType.BIRD]: {
    id: 'bird_template',
    name: '小鸟',
    type: PetType.BIRD,
    rarity: PetRarity.UNCOMMON,
    baseStats: { strength: 14, intelligence: 22, agility: 30, charm: 18, vitality: 12, luck: 18 },
    appearance: {
      species: 'bird',
      color: 'blue',
      pattern: 'feathered',
      size: PetSize.TINY,
      accessories: [],
      specialEffects: [],
      animations: ['idle', 'fly', 'sing', 'preen']
    },
    availableTraits: ['musical', 'free_spirit', 'observant'],
    availableSkills: ['flight', 'song', 'weather_sense'],
    growthModifiers: {
      experienceMultiplier: 1.1,
      statGrowthRate: 1.0,
      skillLearningRate: 1.3
    },
    specialAbilities: ['aerial_view', 'weather_prediction'],
    description: '自由的小鸟，拥有美妙的歌声'
  },
  [PetType.UNICORN]: {
    id: 'unicorn_template',
    name: '独角兽',
    type: PetType.UNICORN,
    rarity: PetRarity.LEGENDARY,
    baseStats: { strength: 30, intelligence: 35, agility: 28, charm: 40, vitality: 32, luck: 30 },
    appearance: {
      species: 'unicorn',
      color: 'white',
      pattern: 'ethereal',
      size: PetSize.LARGE,
      accessories: ['horn'],
      specialEffects: ['rainbow_aura', 'sparkles'],
      animations: ['idle', 'gallop', 'heal', 'magic']
    },
    availableTraits: ['pure_heart', 'magical_horn', 'healing_aura'],
    availableSkills: ['healing', 'purification', 'teleportation'],
    growthModifiers: {
      experienceMultiplier: 0.7,
      statGrowthRate: 1.8,
      skillLearningRate: 0.8
    },
    specialAbilities: ['divine_blessing', 'magic_immunity'],
    description: '神圣的独角兽，拥有治愈的力量'
  },
  [PetType.PHOENIX]: {
    id: 'phoenix_template',
    name: '凤凰',
    type: PetType.PHOENIX,
    rarity: PetRarity.MYTHICAL,
    baseStats: { strength: 40, intelligence: 45, agility: 35, charm: 38, vitality: 50, luck: 35 },
    appearance: {
      species: 'phoenix',
      color: 'gold',
      pattern: 'flame',
      size: PetSize.LARGE,
      accessories: [],
      specialEffects: ['fire_wings', 'rebirth_aura'],
      animations: ['idle', 'fly', 'rebirth', 'flame_dance']
    },
    availableTraits: ['immortal', 'fire_master', 'rebirth'],
    availableSkills: ['resurrection', 'flame_control', 'immortality'],
    growthModifiers: {
      experienceMultiplier: 0.5,
      statGrowthRate: 2.0,
      skillLearningRate: 0.6
    },
    specialAbilities: ['rebirth', 'flame_mastery', 'immortal_essence'],
    description: '不死的凤凰，火焰的主宰'
  },
  [PetType.GRIFFIN]: {
    id: 'griffin_template',
    name: '狮鹫',
    type: PetType.GRIFFIN,
    rarity: PetRarity.EPIC,
    baseStats: { strength: 38, intelligence: 28, agility: 32, charm: 25, vitality: 35, luck: 22 },
    appearance: {
      species: 'griffin',
      color: 'golden',
      pattern: 'feathered_fur',
      size: PetSize.LARGE,
      accessories: [],
      specialEffects: ['wind_aura'],
      animations: ['idle', 'fly', 'roar', 'dive']
    },
    availableTraits: ['noble', 'fierce', 'aerial_master'],
    availableSkills: ['aerial_combat', 'keen_sight', 'wind_control'],
    growthModifiers: {
      experienceMultiplier: 0.8,
      statGrowthRate: 1.4,
      skillLearningRate: 0.9
    },
    specialAbilities: ['sky_dominance', 'eagle_eye'],
    description: '高贵的狮鹫，天空的王者'
  },
  [PetType.PEGASUS]: {
    id: 'pegasus_template',
    name: '飞马',
    type: PetType.PEGASUS,
    rarity: PetRarity.RARE,
    baseStats: { strength: 28, intelligence: 25, agility: 35, charm: 30, vitality: 26, luck: 24 },
    appearance: {
      species: 'pegasus',
      color: 'white',
      pattern: 'winged',
      size: PetSize.LARGE,
      accessories: ['wings'],
      specialEffects: ['cloud_trail'],
      animations: ['idle', 'gallop', 'fly', 'land']
    },
    availableTraits: ['graceful', 'swift', 'cloud_walker'],
    availableSkills: ['flight', 'speed_burst', 'cloud_step'],
    growthModifiers: {
      experienceMultiplier: 0.9,
      statGrowthRate: 1.2,
      skillLearningRate: 1.0
    },
    specialAbilities: ['wind_rider', 'cloud_walking'],
    description: '优雅的飞马，风的伙伴'
  },
  [PetType.KITSUNE]: {
    id: 'kitsune_template',
    name: '九尾狐',
    type: PetType.KITSUNE,
    rarity: PetRarity.LEGENDARY,
    baseStats: { strength: 25, intelligence: 45, agility: 30, charm: 42, vitality: 28, luck: 35 },
    appearance: {
      species: 'kitsune',
      color: 'silver',
      pattern: 'mystical',
      size: PetSize.MEDIUM,
      accessories: ['multiple_tails'],
      specialEffects: ['fox_fire', 'illusion_aura'],
      animations: ['idle', 'transform', 'cast_spell', 'dance']
    },
    availableTraits: ['shapeshifter', 'wise', 'mystical'],
    availableSkills: ['illusion', 'transformation', 'fox_fire'],
    growthModifiers: {
      experienceMultiplier: 0.7,
      statGrowthRate: 1.6,
      skillLearningRate: 0.8
    },
    specialAbilities: ['shape_shifting', 'illusion_mastery', 'ancient_wisdom'],
    description: '神秘的九尾狐，智慧与魅力的化身'
  }
}

// 特质数据库
const TRAIT_DATABASE: Record<string, Omit<PetTrait, 'id'>> = {
  agile: {
    name: '敏捷',
    type: TraitType.PHYSICAL,
    value: 10,
    rarity: PetRarity.COMMON,
    description: '增加敏捷属性',
    effects: [{ type: 'stat_bonus', target: 'agility', value: 10 }]
  },
  strong: {
    name: '强壮',
    type: TraitType.PHYSICAL,
    value: 15,
    rarity: PetRarity.UNCOMMON,
    description: '增加力量属性',
    effects: [{ type: 'stat_bonus', target: 'strength', value: 15 }]
  },
  intelligent: {
    name: '聪明',
    type: TraitType.MENTAL,
    value: 12,
    rarity: PetRarity.COMMON,
    description: '增加智力属性',
    effects: [{ type: 'stat_bonus', target: 'intelligence', value: 12 }]
  },
  lucky: {
    name: '幸运',
    type: TraitType.SPECIAL,
    value: 20,
    rarity: PetRarity.RARE,
    description: '增加幸运属性',
    effects: [{ type: 'stat_bonus', target: 'luck', value: 20 }]
  },
  magical: {
    name: '魔法天赋',
    type: TraitType.MAGICAL,
    value: 25,
    rarity: PetRarity.EPIC,
    description: '拥有魔法天赋',
    effects: [
      { type: 'stat_bonus', target: 'intelligence', value: 15 },
      { type: 'skill_bonus', target: 'magic', value: 20 }
    ]
  }
}

// 技能数据库
const SKILL_DATABASE: Record<string, Omit<PetSkill, 'id' | 'level' | 'experience'>> = {
  fire_breath: {
    name: '火焰吐息',
    maxLevel: 10,
    maxExperience: 1000,
    type: SkillType.COMBAT,
    description: '喷出强力的火焰攻击',
    effects: [{ type: 'stat_modifier', value: 50 }],
    requirements: [{ type: 'level', target: 'pet', value: 10 }]
  },
  healing: {
    name: '治愈术',
    maxLevel: 8,
    maxExperience: 800,
    type: SkillType.MAGIC,
    description: '恢复生命值',
    effects: [{ type: 'special_action', value: 100 }],
    requirements: [{ type: 'stat', target: 'intelligence', value: 30 }]
  },
  stealth: {
    name: '潜行',
    maxLevel: 6,
    maxExperience: 600,
    type: SkillType.SURVIVAL,
    description: '隐身能力',
    effects: [{ type: 'stat_modifier', value: 30 }],
    requirements: [{ type: 'stat', target: 'agility', value: 25 }]
  }
}

// 萌宠工厂类
export class PetFactory {
  private config: PetFactoryConfig
  private statsCalculator: PetStatsCalculator

  constructor(config: Partial<PetFactoryConfig> = {}) {
    this.config = { ...DEFAULT_FACTORY_CONFIG, ...config }
    this.statsCalculator = new PetStatsCalculator()
  }

  // 生成随机萌宠
  generateRandomPet(options: PetGenerationOptions = {}): Pet {
    const type = options.type || this.selectRandomType()
    const rarity = options.rarity || this.selectRandomRarity()
    const level = options.level || 1
    const name = options.name || this.generateRandomName(type)

    return this.createPet({
      type,
      rarity,
      level,
      name,
      ...options
    })
  }

  // 创建萌宠
  createPet(options: PetGenerationOptions): Pet {
    const template = PET_TEMPLATES[options.type!]
    const id = this.generateId()
    const now = Date.now()

    // 生成基础属性
    const baseStats = options.customStats ? this.ensureFullStats(options.customStats) : this.generateStats(options.rarity!)
    const level = options.level || 1
    const maxExperience = this.statsCalculator.calculateMaxExperience(level)
    const maxHealth = this.statsCalculator.calculateMaxHealth(baseStats, level)
    const maxHappiness = this.statsCalculator.calculateMaxHappiness(baseStats)
    const maxEnergy = this.statsCalculator.calculateMaxEnergy(baseStats)

    // 生成外观
    const appearance = this.generateAppearance(template, options.appearance)

    // 生成特质
    const traits = this.generateTraits(options.rarity!, options.traits)

    // 生成技能
    const skills = this.generateSkills(options.rarity!, template.availableSkills)

    const pet: Pet = {
      id,
      name: options.name || template.name,
      type: options.type!,
      level,
      experience: 0,
      maxExperience,
      health: maxHealth,
      maxHealth,
      happiness: maxHappiness,
      maxHappiness,
      energy: maxEnergy,
      maxEnergy,
      rarity: options.rarity!,
      equipment: [],
      lastFeedTime: now,
      lastPlayTime: now,
      lastTrainTime: now,
      lastRestTime: now,
      birthTime: now,
      avatar: this.generateAvatar(options.type!, appearance),
      stats: baseStats,
      baseStats: { ...baseStats },
      appearance,
      status: PetStatus.HEALTHY,
      growthStage: this.determineGrowthStage(level),
      mood: PetMood.CONTENT,
      skills,
      achievements: [],
      totalTokensEarned: '0',
      evolutionPoints: 0,
      breedCount: 0,
      generation: options.generation || 1,
      parents: options.parentIds ? {
        mother: options.parentIds[0],
        father: options.parentIds[1]
      } : undefined,
      traits,
      metadata: {
        version: '1.0.0',
        createdBy: 'PetFactory',
        lastModified: now,
        checksum: '',
        tags: [options.type!, options.rarity!],
        description: template.description
      }
    }

    // 计算校验和
    pet.metadata.checksum = this.calculateChecksum(pet)

    return pet
  }

  // 确保Partial<PetStats>转换为完整的PetStats对象
  private ensureFullStats(partialStats: Partial<PetStats>): PetStats {
    return {
      strength: partialStats.strength ?? 10,
      intelligence: partialStats.intelligence ?? 10,
      agility: partialStats.agility ?? 10,
      charm: partialStats.charm ?? 10,
      vitality: partialStats.vitality ?? 10,
      luck: partialStats.luck ?? 5
    }
  }

  // 繁殖萌宠
  breedPets(parent1: Pet, parent2: Pet, options: Partial<PetGenerationOptions> = {}): Pet {
    // 验证繁殖条件
    if (parent1.level < 10 || parent2.level < 10) {
      throw new Error('萌宠等级必须达到10级才能繁殖')
    }

    if (parent1.type === parent2.type && Math.random() < 0.8) {
      // 同种族繁殖，80%概率产生同种族后代
      options.type = parent1.type
    } else {
      // 跨种族繁殖，随机选择父母之一的种族
      options.type = Math.random() < 0.5 ? parent1.type : parent2.type
    }

    // 稀有度继承逻辑
    const rarityValues = {
      [PetRarity.COMMON]: 1,
      [PetRarity.UNCOMMON]: 2,
      [PetRarity.RARE]: 3,
      [PetRarity.EPIC]: 4,
      [PetRarity.LEGENDARY]: 5,
      [PetRarity.MYTHICAL]: 6
    }

    const avgRarity = (rarityValues[parent1.rarity] + rarityValues[parent2.rarity]) / 2
    const rarityKeys = Object.keys(rarityValues) as PetRarity[]
    options.rarity = rarityKeys[Math.min(Math.floor(avgRarity), rarityKeys.length - 1)]

    // 属性继承
    options.customStats = this.inheritStats(parent1.stats, parent2.stats)

    // 特质继承
    options.traits = this.inheritTraits(parent1.traits, parent2.traits)

    // 世代信息
    options.generation = Math.max(parent1.generation, parent2.generation) + 1
    options.parentIds = [parent1.id, parent2.id]

    return this.createPet(options)
  }

  // 进化萌宠
  evolvePet(pet: Pet, evolutionPath: EvolutionPath): Pet {
    if (pet.type !== evolutionPath.from) {
      throw new Error('萌宠类型不匹配进化路径')
    }

    if (pet.level < evolutionPath.requirements.level) {
      throw new Error('萌宠等级不足')
    }

    if (pet.experience < evolutionPath.requirements.experience) {
      throw new Error('萌宠经验不足')
    }

    // 创建进化后的萌宠
    const evolvedPet: Pet = {
      ...pet,
      type: evolutionPath.to,
      rarity: evolutionPath.rarity,
      stats: this.enhanceStatsForEvolution(pet.stats, evolutionPath.rarity),
      growthStage: this.determineGrowthStage(pet.level),
      evolutionPoints: pet.evolutionPoints + 1,
      metadata: {
        ...pet.metadata,
        lastModified: Date.now(),
        tags: [...pet.metadata.tags, 'evolved', evolutionPath.to]
      }
    }

    // 重新计算最大值
    evolvedPet.maxHealth = this.statsCalculator.calculateMaxHealth(evolvedPet.stats, evolvedPet.level)
    evolvedPet.maxHappiness = this.statsCalculator.calculateMaxHappiness(evolvedPet.stats)
    evolvedPet.maxEnergy = this.statsCalculator.calculateMaxEnergy(evolvedPet.stats)

    return evolvedPet
  }

  // 私有方法
  private selectRandomType(): PetType {
    return this.weightedRandom(this.config.typeWeights) as PetType
  }

  private selectRandomRarity(): PetRarity {
    return this.weightedRandom(this.config.rarityWeights) as PetRarity
  }

  private generateStats(rarity: PetRarity): PetStats {
    const range = this.config.statRanges[rarity]

    return {
      strength: this.randomBetween(range.min, range.max),
      intelligence: this.randomBetween(range.min, range.max),
      agility: this.randomBetween(range.min, range.max),
      charm: this.randomBetween(range.min, range.max),
      vitality: this.randomBetween(range.min, range.max),
      luck: this.randomBetween(range.min * 0.5, range.max * 0.8)
    }
  }

  private generateAppearance(template: PetTemplate, customAppearance?: Partial<PetAppearance>): PetAppearance {
    const colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'black', 'white', 'brown']
    const patterns = ['solid', 'striped', 'spotted', 'gradient', 'mystical', 'ethereal']

    return {
      ...template.appearance,
      color: customAppearance?.color || colors[Math.floor(Math.random() * colors.length)],
      pattern: customAppearance?.pattern || patterns[Math.floor(Math.random() * patterns.length)],
      ...customAppearance
    }
  }

  private generateTraits(rarity: PetRarity, customTraits?: PetTrait[]): PetTrait[] {
    if (customTraits) return customTraits

    const traits: PetTrait[] = []
    const probability = this.config.traitProbabilities[rarity]
    const maxTraits = Math.floor(probability * 5) + 1

    const availableTraits = Object.keys(TRAIT_DATABASE)

    for (let i = 0; i < maxTraits && Math.random() < probability; i++) {
      const traitKey = availableTraits[Math.floor(Math.random() * availableTraits.length)]
      const traitTemplate = TRAIT_DATABASE[traitKey]

      traits.push({
        id: this.generateId(),
        ...traitTemplate
      })
    }

    return traits
  }

  private generateSkills(rarity: PetRarity, availableSkills: string[]): PetSkill[] {
    const skills: PetSkill[] = []
    const probability = this.config.skillProbabilities[rarity]
    const maxSkills = Math.floor(probability * 3) + 1

    for (let i = 0; i < maxSkills && Math.random() < probability; i++) {
      const skillKey = availableSkills[Math.floor(Math.random() * availableSkills.length)]
      const skillTemplate = SKILL_DATABASE[skillKey]

      if (skillTemplate) {
        skills.push({
          id: this.generateId(),
          level: 1,
          experience: 0,
          ...skillTemplate
        })
      }
    }

    return skills
  }

  private generateRandomName(type: PetType): string {
    const namesByType = {
      [PetType.CAT]: ['小咪', '橘子', '雪球', '黑炭', '花花'],
      [PetType.DOG]: ['旺财', '小白', '大黄', '豆豆', '球球'],
      [PetType.RABBIT]: ['小兔', '雪花', '胡萝卜', '跳跳', '毛毛'],
      [PetType.BIRD]: ['小鸟', '彩虹', '歌声', '翅膀', '云朵'],
      [PetType.DRAGON]: ['炎龙', '冰龙', '雷龙', '风龙', '圣龙'],
      [PetType.UNICORN]: ['星辰', '月光', '彩虹', '纯洁', '希望'],
      [PetType.PHOENIX]: ['涅槃', '火凤', '重生', '永恒', '辉煌'],
      [PetType.GRIFFIN]: ['雄鹰', '狮心', '天空', '勇者', '守护'],
      [PetType.PEGASUS]: ['天马', '云翼', '疾风', '自由', '优雅'],
      [PetType.KITSUNE]: ['九尾', '幻影', '智慧', '神秘', '月狐']
    }

    const names = namesByType[type]
    return names[Math.floor(Math.random() * names.length)]
  }

  private generateAvatar(type: PetType, appearance: PetAppearance): string {
    return `/images/pets/${type}/${appearance.color}_${appearance.pattern}.png`
  }

  private determineGrowthStage(level: number): GrowthStage {
    if (level < 5) return GrowthStage.BABY
    if (level < 15) return GrowthStage.CHILD
    if (level < 30) return GrowthStage.TEEN
    if (level < 60) return GrowthStage.ADULT
    if (level < 90) return GrowthStage.ELDER
    return GrowthStage.LEGENDARY_FORM
  }

  private inheritStats(parent1Stats: PetStats, parent2Stats: PetStats): PetStats {
    return {
      strength: Math.floor((parent1Stats.strength + parent2Stats.strength) / 2 + this.randomBetween(-5, 5)),
      intelligence: Math.floor((parent1Stats.intelligence + parent2Stats.intelligence) / 2 + this.randomBetween(-5, 5)),
      agility: Math.floor((parent1Stats.agility + parent2Stats.agility) / 2 + this.randomBetween(-5, 5)),
      charm: Math.floor((parent1Stats.charm + parent2Stats.charm) / 2 + this.randomBetween(-5, 5)),
      vitality: Math.floor((parent1Stats.vitality + parent2Stats.vitality) / 2 + this.randomBetween(-5, 5)),
      luck: Math.floor((parent1Stats.luck + parent2Stats.luck) / 2 + this.randomBetween(-3, 3))
    }
  }

  private inheritTraits(parent1Traits: PetTrait[], parent2Traits: PetTrait[]): PetTrait[] {
    const allTraits = [...parent1Traits, ...parent2Traits]
    const inheritedTraits: PetTrait[] = []

    // 50%概率继承每个特质
    allTraits.forEach(trait => {
      if (Math.random() < 0.5 && !inheritedTraits.find(t => t.name === trait.name)) {
        inheritedTraits.push({
          ...trait,
          id: this.generateId()
        })
      }
    })

    return inheritedTraits
  }

  private enhanceStatsForEvolution(stats: PetStats, newRarity: PetRarity): PetStats {
    const multiplier = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.2,
      [PetRarity.RARE]: 1.5,
      [PetRarity.EPIC]: 2.0,
      [PetRarity.LEGENDARY]: 3.0,
      [PetRarity.MYTHICAL]: 5.0
    }[newRarity]

    return {
      strength: Math.floor(stats.strength * multiplier),
      intelligence: Math.floor(stats.intelligence * multiplier),
      agility: Math.floor(stats.agility * multiplier),
      charm: Math.floor(stats.charm * multiplier),
      vitality: Math.floor(stats.vitality * multiplier),
      luck: Math.floor(stats.luck * multiplier)
    }
  }

  private weightedRandom(weights: Record<string, number>): string {
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0)
    let random = Math.random() * totalWeight

    for (const [key, weight] of Object.entries(weights)) {
      random -= weight
      if (random <= 0) {
        return key
      }
    }

    return Object.keys(weights)[0]
  }

  private randomBetween(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private calculateChecksum(pet: Pet): string {
    const data = JSON.stringify({
      id: pet.id,
      name: pet.name,
      type: pet.type,
      rarity: pet.rarity,
      stats: pet.stats,
      birthTime: pet.birthTime
    })

    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return hash.toString(16)
  }
}

// 导出工厂实例
export const petFactory = new PetFactory()

// 导出模板和数据库
export { PET_TEMPLATES, TRAIT_DATABASE, SKILL_DATABASE }
