/**
 * 本地存储工具类
 * 提供数据持久化、版本控制、加密存储等功能
 */

import { STORAGE_KEYS } from '../config/constants'

// 存储数据版本
export const STORAGE_VERSION = '1.0.0'

// 存储数据接口
export interface StorageData<T = any> {
  version: string
  timestamp: number
  data: T
  checksum?: string
}

// 存储配置接口
export interface StorageConfig {
  encrypt?: boolean
  compress?: boolean
  expiry?: number // 过期时间（毫秒）
}

// 存储统计信息
export interface StorageStats {
  totalSize: number
  itemCount: number
  lastModified: number
  version: string
}

// 备份数据接口
export interface BackupData {
  version: string
  timestamp: number
  walletAddress?: string
  gameData: any
  petData: any
  userSettings: any
  metadata: {
    deviceInfo: string
    userAgent: string
    exportedAt: number
  }
}

/**
 * 本地存储工具类
 */
export class StorageUtil {
  private readonly storageVersion = STORAGE_VERSION
  private readonly maxRetries = 3
  private readonly retryDelay = 100

  /**
   * 保存数据到本地存储
   */
  async setItem<T>(
    key: string,
    data: T,
    config: StorageConfig = {}
  ): Promise<boolean> {
    try {
      const storageData: StorageData<T> = {
        version: this.storageVersion,
        timestamp: Date.now(),
        data
      }

      // 添加校验和
      if (config.encrypt) {
        storageData.checksum = await this.generateChecksum(data)
      }

      let serializedData = JSON.stringify(storageData)

      // 数据压缩（如果需要）
      if (config.compress) {
        serializedData = await this.compressData(serializedData)
      }

      // 数据加密（如果需要）
      if (config.encrypt) {
        serializedData = await this.encryptData(serializedData)
      }

      // 重试机制
      for (let i = 0; i < this.maxRetries; i++) {
        try {
          localStorage.setItem(key, serializedData)
          return true
        } catch (error) {
          if (i === this.maxRetries - 1) throw error
          await this.delay(this.retryDelay * (i + 1))
        }
      }

      return false
    } catch (error) {
      console.error(`存储数据失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 从本地存储获取数据
   */
  async getItem<T>(
    key: string,
    config: StorageConfig = {}
  ): Promise<T | null> {
    try {
      let serializedData = localStorage.getItem(key)
      if (!serializedData) return null

      // 数据解密（如果需要）
      if (config.encrypt) {
        serializedData = await this.decryptData(serializedData)
      }

      // 数据解压缩（如果需要）
      if (config.compress) {
        serializedData = await this.decompressData(serializedData)
      }

      const storageData: StorageData<T> = JSON.parse(serializedData)

      // 版本检查
      if (!this.isVersionCompatible(storageData.version)) {
        console.warn(`数据版本不兼容 [${key}]: ${storageData.version} -> ${this.storageVersion}`)
        await this.migrateData(key, storageData)
        return await this.getItem<T>(key, config) // 递归获取迁移后的数据
      }

      // 过期检查
      if (config.expiry && this.isExpired(storageData.timestamp, config.expiry)) {
        await this.removeItem(key)
        return null
      }

      // 数据完整性检查
      if (config.encrypt && storageData.checksum) {
        const currentChecksum = await this.generateChecksum(storageData.data)
        if (currentChecksum !== storageData.checksum) {
          console.error(`数据完整性检查失败 [${key}]`)
          return null
        }
      }

      return storageData.data
    } catch (error) {
      console.error(`获取数据失败 [${key}]:`, error)
      return null
    }
  }

  /**
   * 删除存储项
   */
  async removeItem(key: string): Promise<boolean> {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`删除数据失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 清空所有存储数据
   */
  async clear(): Promise<boolean> {
    try {
      // 只清除游戏相关的数据
      const gameKeys = Object.values(STORAGE_KEYS)
      gameKeys.forEach(key => {
        localStorage.removeItem(key)
      })
      return true
    } catch (error) {
      console.error('清空存储失败:', error)
      return false
    }
  }

  /**
   * 检查存储项是否存在
   */
  hasItem(key: string): boolean {
    return localStorage.getItem(key) !== null
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats(): StorageStats {
    let totalSize = 0
    let itemCount = 0
    let lastModified = 0

    const gameKeys = Object.values(STORAGE_KEYS)

    gameKeys.forEach(key => {
      const item = localStorage.getItem(key)
      if (item) {
        totalSize += item.length
        itemCount++

        try {
          const data = JSON.parse(item) as StorageData
          if (data.timestamp > lastModified) {
            lastModified = data.timestamp
          }
        } catch {
          // 忽略解析错误
        }
      }
    })

    return {
      totalSize,
      itemCount,
      lastModified,
      version: this.storageVersion
    }
  }

  /**
   * 版本兼容性检查
   */
  private isVersionCompatible(version: string): boolean {
    // 简单的版本比较，可以根据需要扩展
    return version === this.storageVersion
  }

  /**
   * 数据过期检查
   */
  private isExpired(timestamp: number, expiry: number): boolean {
    return Date.now() - timestamp > expiry
  }

  /**
   * 生成数据校验和
   */
  private async generateChecksum(data: any): Promise<string> {
    const jsonString = JSON.stringify(data)
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(jsonString)

    if (crypto.subtle) {
      const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    } else {
      // 降级到简单哈希
      return this.simpleHash(jsonString)
    }
  }

  /**
   * 简单哈希函数（降级方案）
   */
  private simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  /**
   * 数据加密（简单实现）
   */
  private async encryptData(data: string): Promise<string> {
    // 这里使用简单的Base64编码作为示例
    // 在生产环境中应该使用更安全的加密方法
    try {
      return btoa(unescape(encodeURIComponent(data)))
    } catch (error) {
      console.warn('数据加密失败，使用原始数据:', error)
      return data
    }
  }

  /**
   * 数据解密
   */
  private async decryptData(encryptedData: string): Promise<string> {
    try {
      return decodeURIComponent(escape(atob(encryptedData)))
    } catch (error) {
      console.warn('数据解密失败，尝试使用原始数据:', error)
      return encryptedData
    }
  }

  /**
   * 数据压缩（简单实现）
   */
  private async compressData(data: string): Promise<string> {
    // 这里可以实现LZ压缩或其他压缩算法
    // 目前返回原始数据
    return data
  }

  /**
   * 数据解压缩
   */
  private async decompressData(compressedData: string): Promise<string> {
    // 对应压缩算法的解压缩
    return compressedData
  }

  /**
   * 数据迁移
   */
  private async migrateData(key: string, oldData: StorageData): Promise<void> {
    console.log(`开始数据迁移 [${key}]: ${oldData.version} -> ${this.storageVersion}`)

    // 这里可以实现具体的数据迁移逻辑
    // 目前简单地更新版本号
    const migratedData: StorageData = {
      ...oldData,
      version: this.storageVersion,
      timestamp: Date.now()
    }

    localStorage.setItem(key, JSON.stringify(migratedData))
    console.log(`数据迁移完成 [${key}]`)
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 创建全局存储实例
export const storageUtil = new StorageUtil()