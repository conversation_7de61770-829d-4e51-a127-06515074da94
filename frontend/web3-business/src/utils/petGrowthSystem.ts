import type { Pet, PetStats } from '../types/typesWithoutCircular'
import { PetRarity, PetStatus, PetMood, GrowthStage } from '../types/typesWithoutCircular'

export interface GrowthEvent {
  id: string
  petId: string
  type: 'level_up' | 'evolution' | 'skill_learned' | 'trait_gained' | 'milestone'
  timestamp: number
  data: any
  description: string
}

export interface LevelUpResult {
  success: boolean
  newLevel: number
  statGains: Partial<PetStats>
  newMaxValues: {
    health: number
    happiness: number
    energy: number
    experience: number
  }
  unlockedFeatures?: string[]
  evolutionAvailable?: boolean
}

export interface EvolutionRequirement {
  level: number
  experience: number
  health: number
  happiness: number
  specificStats?: Partial<PetStats>
  items?: string[]
  achievements?: string[]
  timeAlive?: number // 存活时间（毫秒）
}

export interface EvolutionPath {
  from: string
  to: string
  requirements: EvolutionRequirement
  probability: number
  description: string
}

// 成长阶段配置
const GROWTH_STAGE_CONFIG = {
  [GrowthStage.BABY]: {
    levelRange: [1, 5],
    statMultiplier: 0.8,
    experienceMultiplier: 1.5,
    description: '幼体阶段，成长迅速但属性较低'
  },
  [GrowthStage.CHILD]: {
    levelRange: [6, 15],
    statMultiplier: 1.0,
    experienceMultiplier: 1.2,
    description: '儿童阶段，平衡发展'
  },
  [GrowthStage.TEEN]: {
    levelRange: [16, 30],
    statMultiplier: 1.2,
    experienceMultiplier: 1.0,
    description: '青少年阶段，属性快速提升'
  },
  [GrowthStage.ADULT]: {
    levelRange: [31, 60],
    statMultiplier: 1.5,
    experienceMultiplier: 0.8,
    description: '成年阶段，属性强大但升级较慢'
  },
  [GrowthStage.ELDER]: {
    levelRange: [61, 90],
    statMultiplier: 1.8,
    experienceMultiplier: 0.6,
    description: '长者阶段，智慧与经验的体现'
  },
  [GrowthStage.LEGENDARY_FORM]: {
    levelRange: [91, 100],
    statMultiplier: 2.5,
    experienceMultiplier: 0.4,
    description: '传说形态，达到了萌宠的巅峰'
  }
}

// 等级升级奖励配置
const LEVEL_UP_REWARDS = {
  5: { description: '解锁魅力训练', feature: 'charm_training' },
  10: { description: '解锁综合训练', feature: 'comprehensive_training' },
  15: { description: '解锁装备系统', feature: 'equipment_system' },
  20: { description: '解锁技能学习', feature: 'skill_learning' },
  25: { description: '解锁繁殖功能', feature: 'breeding' },
  30: { description: '解锁进化系统', feature: 'evolution' },
  50: { description: '解锁传说技能', feature: 'legendary_skills' },
  75: { description: '解锁神话形态', feature: 'mythical_form' },
  100: { description: '达到最高等级', feature: 'max_level' }
}

export class PetGrowthSystem {
  private growthEvents: GrowthEvent[] = []

  // 计算等级升级所需经验
  calculateRequiredExperience(level: number, rarity: PetRarity): number {
    const baseExp = 100
    const levelMultiplier = Math.pow(1.5, level - 1)
    const rarityMultiplier = this.getRarityExperienceMultiplier(rarity)

    return Math.floor(baseExp * levelMultiplier * rarityMultiplier)
  }

  // 计算升级后的属性加成
  calculateLevelUpStatGains(pet: Pet): Partial<PetStats> {
    const stage = this.determineGrowthStage(pet.level + 1)
    const stageConfig = GROWTH_STAGE_CONFIG[stage]
    const rarityMultiplier = this.getRarityStatMultiplier(pet.rarity)

    // 基础属性增长
    const baseGain = 2
    const multiplier = stageConfig.statMultiplier * rarityMultiplier

    // 根据萌宠类型调整属性增长倾向
    const typeModifiers = this.getTypeStatModifiers(pet.type)

    return {
      strength: Math.floor(baseGain * multiplier * typeModifiers.strength),
      intelligence: Math.floor(baseGain * multiplier * typeModifiers.intelligence),
      agility: Math.floor(baseGain * multiplier * typeModifiers.agility),
      charm: Math.floor(baseGain * multiplier * typeModifiers.charm),
      vitality: Math.floor(baseGain * multiplier * typeModifiers.vitality),
      luck: Math.floor(baseGain * 0.5 * multiplier * typeModifiers.luck)
    }
  }

  // 执行等级升级
  performLevelUp(pet: Pet): LevelUpResult {
    if (pet.experience < pet.maxExperience) {
      return {
        success: false,
        newLevel: pet.level,
        statGains: {},
        newMaxValues: {
          health: pet.maxHealth,
          happiness: pet.maxHappiness,
          energy: pet.maxEnergy,
          experience: pet.maxExperience
        }
      }
    }

    const newLevel = pet.level + 1
    const statGains = this.calculateLevelUpStatGains(pet)

    // 计算新的基础属性
    const newStats: PetStats = {
      strength: pet.stats.strength + (statGains.strength || 0),
      intelligence: pet.stats.intelligence + (statGains.intelligence || 0),
      agility: pet.stats.agility + (statGains.agility || 0),
      charm: pet.stats.charm + (statGains.charm || 0),
      vitality: pet.stats.vitality + (statGains.vitality || 0),
      luck: pet.stats.luck + (statGains.luck || 0)
    }

    // 计算新的最大值
    const newMaxHealth = this.calculateMaxHealth(newStats, newLevel)
    const newMaxHappiness = this.calculateMaxHappiness(newStats)
    const newMaxEnergy = this.calculateMaxEnergy(newStats)
    const newMaxExperience = this.calculateRequiredExperience(newLevel, pet.rarity)

    // 检查解锁的功能
    const unlockedFeatures = []
    const levelReward = LEVEL_UP_REWARDS[newLevel]
    if (levelReward) {
      unlockedFeatures.push(levelReward.feature)
    }

    // 检查是否可以进化
    const evolutionAvailable = this.checkEvolutionAvailability(pet, newLevel, newStats)

    // 记录成长事件
    this.recordGrowthEvent({
      id: this.generateEventId(),
      petId: pet.id,
      type: 'level_up',
      timestamp: Date.now(),
      data: {
        oldLevel: pet.level,
        newLevel,
        statGains,
        unlockedFeatures
      },
      description: `${pet.name} 升级到了 ${newLevel} 级！`
    })

    return {
      success: true,
      newLevel,
      statGains,
      newMaxValues: {
        health: newMaxHealth,
        happiness: newMaxHappiness,
        energy: newMaxEnergy,
        experience: newMaxExperience
      },
      unlockedFeatures,
      evolutionAvailable
    }
  }

  // 确定成长阶段
  determineGrowthStage(level: number): GrowthStage {
    for (const [stage, config] of Object.entries(GROWTH_STAGE_CONFIG)) {
      const [min, max] = config.levelRange
      if (level >= min && level <= max) {
        return stage as GrowthStage
      }
    }
    return GrowthStage.LEGENDARY_FORM
  }

  // 计算时间相关的成长效果
  calculateTimeBasedGrowth(pet: Pet): Partial<Pet> {
    const now = Date.now()
    const updates: Partial<Pet> = {}

    // 计算存活时间
    const aliveTime = now - pet.birthTime
    const hoursAlive = aliveTime / (1000 * 60 * 60)

    // 长期存活奖励
    if (hoursAlive > 24 && hoursAlive % 24 < 1) { // 每24小时
      const bonusExp = Math.floor(pet.level * 0.1)
      updates.experience = (pet.experience || 0) + bonusExp
    }

    // 自然状态恢复
    const timeSinceLastRest = now - (pet.lastRestTime || now)
    if (timeSinceLastRest > 60 * 60 * 1000) { // 1小时后开始自然恢复
      const recoveryRate = Math.floor(timeSinceLastRest / (60 * 60 * 1000))

      if (pet.energy < pet.maxEnergy) {
        updates.energy = Math.min(pet.maxEnergy, pet.energy + recoveryRate)
      }

      if (pet.health < pet.maxHealth && pet.status !== PetStatus.SICK) {
        updates.health = Math.min(pet.maxHealth, pet.health + Math.floor(recoveryRate * 0.5))
      }
    }

    // 饥饿和疲劳系统
    const timeSinceLastFeed = now - (pet.lastFeedTime || now)
    const timeSinceLastPlay = now - (pet.lastPlayTime || now)

    // 饥饿影响
    if (timeSinceLastFeed > 4 * 60 * 60 * 1000) { // 4小时没喂食
      const hungerPenalty = Math.floor(timeSinceLastFeed / (60 * 60 * 1000)) - 4
      updates.health = Math.max(1, pet.health - hungerPenalty)
      updates.happiness = Math.max(0, pet.happiness - hungerPenalty * 2)

      if (pet.health <= 20) {
        updates.status = PetStatus.SICK
        updates.mood = PetMood.SICK
      }
    }

    // 缺乏互动影响
    if (timeSinceLastPlay > 6 * 60 * 60 * 1000) { // 6小时没互动
      const lonelinessEffect = Math.floor(timeSinceLastPlay / (60 * 60 * 1000)) - 6
      updates.happiness = Math.max(0, pet.happiness - lonelinessEffect)

      if (pet.happiness <= 10) {
        updates.mood = PetMood.SAD
      }
    }

    // 根据整体状态更新心情
    const health = updates.health ?? pet.health
    const happiness = updates.happiness ?? pet.happiness
    const energy = updates.energy ?? pet.energy

    if (health > 80 && happiness > 80 && energy > 60) {
      updates.mood = PetMood.HAPPY
    } else if (health < 30) {
      updates.mood = PetMood.SICK
      updates.status = PetStatus.SICK
    } else if (energy < 20) {
      updates.mood = PetMood.TIRED
    } else if (happiness < 30) {
      updates.mood = PetMood.SAD
    } else {
      updates.mood = PetMood.CONTENT
    }

    return updates
  }

  // 检查进化可用性
  checkEvolutionAvailability(pet: Pet, level?: number, stats?: PetStats): boolean {
    const currentLevel = level ?? pet.level
    const currentStats = stats ?? pet.stats

    // 基本进化条件
    if (currentLevel < 30) return false
    if (pet.health < 80) return false
    if (pet.happiness < 70) return false

    // 属性要求
    const totalStats = Object.values(currentStats).reduce((sum, stat) => sum + stat, 0)
    if (totalStats < 300) return false

    // 存活时间要求
    const aliveTime = Date.now() - pet.birthTime
    if (aliveTime < 7 * 24 * 60 * 60 * 1000) return false // 至少存活7天

    return true
  }

  // 获取成长里程碑
  getGrowthMilestones(pet: Pet): Array<{
    id: string
    name: string
    description: string
    achieved: boolean
    progress: number
    requirement: number
  }> {
    const milestones = [
      {
        id: 'first_level_up',
        name: '初次成长',
        description: '达到2级',
        achieved: pet.level >= 2,
        progress: pet.level,
        requirement: 2
      },
      {
        id: 'child_stage',
        name: '儿童阶段',
        description: '达到儿童成长阶段',
        achieved: pet.level >= 6,
        progress: pet.level,
        requirement: 6
      },
      {
        id: 'teen_stage',
        name: '青少年阶段',
        description: '达到青少年成长阶段',
        achieved: pet.level >= 16,
        progress: pet.level,
        requirement: 16
      },
      {
        id: 'adult_stage',
        name: '成年阶段',
        description: '达到成年阶段',
        achieved: pet.level >= 31,
        progress: pet.level,
        requirement: 31
      },
      {
        id: 'high_stats',
        name: '属性大师',
        description: '任意属性达到100',
        achieved: Object.values(pet.stats).some(stat => stat >= 100),
        progress: Math.max(...Object.values(pet.stats)),
        requirement: 100
      },
      {
        id: 'long_lived',
        name: '长寿萌宠',
        description: '存活30天',
        achieved: (Date.now() - pet.birthTime) >= 30 * 24 * 60 * 60 * 1000,
        progress: Math.floor((Date.now() - pet.birthTime) / (24 * 60 * 60 * 1000)),
        requirement: 30
      },
      {
        id: 'max_happiness',
        name: '快乐满满',
        description: '快乐度达到最大值',
        achieved: pet.happiness >= pet.maxHappiness,
        progress: pet.happiness,
        requirement: pet.maxHappiness
      },
      {
        id: 'evolution_ready',
        name: '进化准备',
        description: '满足进化条件',
        achieved: this.checkEvolutionAvailability(pet),
        progress: this.calculateEvolutionProgress(pet),
        requirement: 100
      }
    ]

    return milestones
  }

  // 计算进化进度
  private calculateEvolutionProgress(pet: Pet): number {
    let progress = 0

    // 等级进度 (30%)
    progress += Math.min(30, (pet.level / 30) * 30)

    // 健康度进度 (20%)
    progress += Math.min(20, (pet.health / 80) * 20)

    // 快乐度进度 (20%)
    progress += Math.min(20, (pet.happiness / 70) * 20)

    // 属性进度 (20%)
    const totalStats = Object.values(pet.stats).reduce((sum, stat) => sum + stat, 0)
    progress += Math.min(20, (totalStats / 300) * 20)

    // 存活时间进度 (10%)
    const aliveTime = Date.now() - pet.birthTime
    const requiredTime = 7 * 24 * 60 * 60 * 1000
    progress += Math.min(10, (aliveTime / requiredTime) * 10)

    return Math.floor(progress)
  }

  // 私有辅助方法
  private getRarityExperienceMultiplier(rarity: PetRarity): number {
    const multipliers = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.2,
      [PetRarity.RARE]: 1.5,
      [PetRarity.EPIC]: 2.0,
      [PetRarity.LEGENDARY]: 3.0,
      [PetRarity.MYTHICAL]: 5.0
    }
    return multipliers[rarity]
  }

  private getRarityStatMultiplier(rarity: PetRarity): number {
    const multipliers = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.1,
      [PetRarity.RARE]: 1.3,
      [PetRarity.EPIC]: 1.6,
      [PetRarity.LEGENDARY]: 2.0,
      [PetRarity.MYTHICAL]: 3.0
    }
    return multipliers[rarity]
  }

  private getTypeStatModifiers(type: string): Record<keyof PetStats, number> {
    // 不同类型萌宠的属性成长倾向
    const modifiers: Record<string, Record<keyof PetStats, number>> = {
      cat: { strength: 0.9, intelligence: 1.2, agility: 1.4, charm: 1.1, vitality: 0.8, luck: 1.3 },
      dog: { strength: 1.2, intelligence: 1.0, agility: 1.1, charm: 1.3, vitality: 1.2, luck: 0.9 },
      rabbit: { strength: 0.7, intelligence: 1.0, agility: 1.5, charm: 1.2, vitality: 0.8, luck: 1.4 },
      bird: { strength: 0.8, intelligence: 1.3, agility: 1.4, charm: 1.1, vitality: 0.7, luck: 1.2 },
      dragon: { strength: 1.5, intelligence: 1.4, agility: 1.1, charm: 1.0, vitality: 1.6, luck: 1.2 },
      unicorn: { strength: 1.2, intelligence: 1.5, agility: 1.3, charm: 1.6, vitality: 1.3, luck: 1.4 },
      phoenix: { strength: 1.4, intelligence: 1.6, agility: 1.3, charm: 1.4, vitality: 1.8, luck: 1.5 },
      griffin: { strength: 1.6, intelligence: 1.2, agility: 1.4, charm: 1.0, vitality: 1.4, luck: 1.1 },
      pegasus: { strength: 1.3, intelligence: 1.1, agility: 1.6, charm: 1.3, vitality: 1.2, luck: 1.2 },
      kitsune: { strength: 1.0, intelligence: 1.8, agility: 1.3, charm: 1.7, vitality: 1.1, luck: 1.6 }
    }

    return modifiers[type] || { strength: 1.0, intelligence: 1.0, agility: 1.0, charm: 1.0, vitality: 1.0, luck: 1.0 }
  }

  private calculateMaxHealth(stats: PetStats, level: number): number {
    return Math.floor(100 + (stats.vitality * 2) + (level * 5))
  }

  private calculateMaxHappiness(stats: PetStats): number {
    return Math.floor(100 + (stats.charm * 0.5))
  }

  private calculateMaxEnergy(stats: PetStats): number {
    return Math.floor(100 + (stats.vitality * 1.5))
  }

  private recordGrowthEvent(event: GrowthEvent): void {
    this.growthEvents.push(event)

    // 保持事件历史在合理范围内
    if (this.growthEvents.length > 1000) {
      this.growthEvents = this.growthEvents.slice(-500)
    }
  }

  private generateEventId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 公共方法获取成长事件
  getGrowthEvents(petId?: string): GrowthEvent[] {
    if (petId) {
      return this.growthEvents.filter(event => event.petId === petId)
    }
    return [...this.growthEvents]
  }

  // 清理过期事件
  cleanupOldEvents(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge
    this.growthEvents = this.growthEvents.filter(event => event.timestamp > cutoff)
  }
}

// 导出单例实例
export const petGrowthSystem = new PetGrowthSystem()