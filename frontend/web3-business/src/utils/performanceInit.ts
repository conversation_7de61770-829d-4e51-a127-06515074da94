/**
 * 性能优化初始化
 * 统一初始化所有性能优化功能
 */

import type { App } from 'vue'
import { memoryManager } from './memoryManager'
import { requestCache } from './requestCache'
import { imageOptimizer } from './imageOptimization'
import { performanceOptimizationService } from '@/services/performance-optimization.service'

interface PerformanceInitOptions {
  enableMemoryMonitoring?: boolean
  enableRequestCaching?: boolean
  enableImageOptimization?: boolean
  enableAutoOptimization?: boolean
  memoryThreshold?: number
  cacheCleanupInterval?: number
  debugMode?: boolean
}

class PerformanceInitializer {
  private static instance: PerformanceInitializer
  private initialized = false
  private options: Required<PerformanceInitOptions>

  static getInstance(): PerformanceInitializer {
    if (!PerformanceInitializer.instance) {
      PerformanceInitializer.instance = new PerformanceInitializer()
    }
    return PerformanceInitializer.instance
  }

  constructor() {
    this.options = {
      enableMemoryMonitoring: true,
      enableRequestCaching: true,
      enableImageOptimization: true,
      enableAutoOptimization: true,
      memoryThreshold: 80,
      cacheCleanupInterval: 5 * 60 * 1000,
      debugMode: process.env.NODE_ENV === 'development'
    }
  }

  /**
   * 初始化性能优化
   */
  init(app: App, options: PerformanceInitOptions = {}) {
    if (this.initialized) {
      console.warn('Performance optimization already initialized')
      return
    }

    this.options = { ...this.options, ...options }

    if (this.options.debugMode) {
      console.log('Initializing performance optimization with options:', this.options)
    }

    // 初始化各个模块
    this.initMemoryManagement()
    this.initRequestCaching()
    this.initImageOptimization()
    this.initAutoOptimization()
    this.initVueDirectives(app)
    this.initGlobalErrorHandling()
    this.initPerformanceObserver()

    this.initialized = true

    if (this.options.debugMode) {
      console.log('Performance optimization initialized successfully')
    }
  }

  /**
   * 初始化内存管理
   */
  private initMemoryManagement() {
    if (!this.options.enableMemoryMonitoring) return

    // 内存管理已经通过单例自动初始化
    if (this.options.debugMode) {
      console.log('Memory management initialized')
    }
  }

  /**
   * 初始化请求缓存
   */
  private initRequestCaching() {
    if (!this.options.enableRequestCaching) return

    // 设置定期清理
    setInterval(() => {
      requestCache.cleanupExpired()
    }, this.options.cacheCleanupInterval)

    if (this.options.debugMode) {
      console.log('Request caching initialized')
    }
  }

  /**
   * 初始化图片优化
   */
  private initImageOptimization() {
    if (!this.options.enableImageOptimization) return

    // 图片优化器已经通过单例自动初始化
    if (this.options.debugMode) {
      console.log('Image optimization initialized')
    }
  }

  /**
   * 初始化自动优化
   */
  private initAutoOptimization() {
    if (!this.options.enableAutoOptimization) return

    // 配置性能优化服务
    performanceOptimizationService.updateConfig({
      enableAutoOptimization: true,
      memoryThreshold: this.options.memoryThreshold,
      cacheCleanupInterval: this.options.cacheCleanupInterval,
      enableDebugMode: this.options.debugMode
    })

    if (this.options.debugMode) {
      console.log('Auto optimization initialized')
    }
  }

  /**
   * 初始化 Vue 指令
   */
  private initVueDirectives(app: App) {
    // 懒加载图片指令
    app.directive('lazy-image', {
      mounted(el: HTMLImageElement, binding) {
        const src = binding.value
        if (typeof src === 'string') {
          imageOptimizer.setupLazyLoad(el, src)
        }
      },
      unmounted(el: HTMLImageElement) {
        imageOptimizer.observer?.unobserve(el)
      }
    })

    // 性能监控指令
    app.directive('performance-track', {
      mounted(el: HTMLElement, binding) {
        const trackingId = binding.value || `element_${Date.now()}`

        // 注册元素为资源
        const resourceId = memoryManager.registerResource(
          'listener',
          { element: el, trackingId },
          () => {
            // 清理逻辑
          }
        )

        el.dataset.performanceResourceId = resourceId
      },
      unmounted(el: HTMLElement) {
        const resourceId = el.dataset.performanceResourceId
        if (resourceId) {
          memoryManager.unregisterResource(resourceId)
        }
      }
    })

    if (this.options.debugMode) {
      console.log('Vue directives initialized')
    }
  }

  /**
   * 初始化全局错误处理
   */
  private initGlobalErrorHandling() {
    // 捕获未处理的 Promise 错误
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason)

      // 如果是内存相关错误，触发清理
      if (event.reason?.message?.includes('memory') ||
          event.reason?.message?.includes('heap')) {
        this.performEmergencyCleanup()
      }
    })

    // 捕获全局错误
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error)

      // 检查是否是资源加载错误
      if (event.target && event.target !== window) {
        const target = event.target as HTMLElement
        if (target.tagName === 'IMG') {
          // 图片加载失败，可能需要清理图片缓存
          imageOptimizer.clearCache()
        }
      }
    })

    if (this.options.debugMode) {
      console.log('Global error handling initialized')
    }
  }

  /**
   * 初始化性能观察器
   */
  private initPerformanceObserver() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    try {
      // 观察长任务
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.duration > 50) { // 超过50ms的任务
            console.warn(`Long task detected: ${entry.duration.toFixed(2)}ms`)

            // 如果长任务过多，触发优化
            if (entry.duration > 100) {
              this.performEmergencyCleanup()
            }
          }
        })
      })

      longTaskObserver.observe({ entryTypes: ['longtask'] })

      // 观察布局偏移
      const layoutShiftObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.value > 0.1) { // CLS 阈值
            console.warn(`Layout shift detected: ${entry.value.toFixed(4)}`)
          }
        })
      })

      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })

      if (this.options.debugMode) {
        console.log('Performance observers initialized')
      }
    } catch (error) {
      console.warn('Failed to initialize performance observers:', error)
    }
  }

  /**
   * 执行紧急清理
   */
  private performEmergencyCleanup() {
    if (this.options.debugMode) {
      console.log('Performing emergency cleanup...')
    }

    // 清理内存
    memoryManager.cleanupAllResources()

    // 清理缓存
    requestCache.clearCache()

    // 清理图片缓存
    imageOptimizer.clearCache()

    // 触发垃圾回收
    this.forceGarbageCollection()
  }

  /**
   * 强制垃圾回收
   */
  private forceGarbageCollection() {
    // 创建临时对象触发垃圾回收
    const temp = new Array(1000).fill(null).map(() => ({}))
    temp.length = 0

    // 使用 setTimeout 确保在下一个事件循环中执行
    setTimeout(() => {
      Promise.resolve().then(() => {
        // 微任务队列中的清理
      })
    }, 0)
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      memory: memoryManager.getStats(),
      cache: requestCache.getStats(),
      initialized: this.initialized,
      options: this.options
    }
  }

  /**
   * 销毁性能优化
   */
  destroy() {
    if (!this.initialized) return

    memoryManager.destroy()
    imageOptimizer.destroy()
    performanceOptimizationService.destroy()

    this.initialized = false

    if (this.options.debugMode) {
      console.log('Performance optimization destroyed')
    }
  }
}

// 导出单例实例
export const performanceInitializer = PerformanceInitializer.getInstance()

// Vue 插件
export const PerformanceOptimizationPlugin = {
  install(app: App, options: PerformanceInitOptions = {}) {
    performanceInitializer.init(app, options)

    // 提供全局属性
    app.config.globalProperties.$performance = {
      getStats: () => performanceInitializer.getPerformanceStats(),
      cleanup: () => performanceInitializer['performEmergencyCleanup'](),
      memoryManager,
      requestCache,
      imageOptimizer
    }

    // 提供注入
    app.provide('performance', performanceInitializer)
  }
}

// 便捷的初始化函数
export function initPerformanceOptimization(app: App, options: PerformanceInitOptions = {}) {
  app.use(PerformanceOptimizationPlugin, options)
}
