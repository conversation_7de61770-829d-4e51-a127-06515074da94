/**
 * 输入验证和清理工具类
 * Input validation and sanitization utilities
 */

import { ethers } from 'ethers'

export class InputValidator {
  /**
   * 验证以太坊地址格式
   */
  static validateAddress(address: string): boolean {
    if (!address || typeof address !== 'string') {
      return false
    }

    try {
      return ethers.isAddress(address.trim())
    } catch {
      return false
    }
  }

  /**
   * 验证代币数量格式
   */
  static validateAmount(amount: string): boolean {
    if (!amount || typeof amount !== 'string') {
      return false
    }

    try {
      const parsed = ethers.parseEther(amount.trim())
      return parsed > 0n
    } catch {
      return false
    }
  }

  /**
   * 验证私钥格式
   */
  static validatePrivateKey(privateKey: string): boolean {
    if (!privateKey || typeof privateKey !== 'string') {
      return false
    }

    const cleanKey = privateKey.trim()
    // 检查长度和格式
    return /^0x[a-fA-F0-9]{64}$/.test(cleanKey) || /^[a-fA-F0-9]{64}$/.test(cleanKey)
  }

  /**
   * 验证交易哈希格式
   */
  static validateTransactionHash(hash: string): boolean {
    if (!hash || typeof hash !== 'string') {
      return false
    }

    return /^0x[a-fA-F0-9]{64}$/.test(hash.trim())
  }

  /**
   * 验证网络ID
   */
  static validateChainId(chainId: number | string): boolean {
    const id = typeof chainId === 'string' ? parseInt(chainId) : chainId
    return Number.isInteger(id) && id > 0
  }

  /**
   * 验证URL格式
   */
  static validateUrl(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false
    }

    try {
      new URL(url.trim())
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证萌宠名称
   */
  static validatePetName(name: string): boolean {
    if (!name || typeof name !== 'string') {
      return false
    }

    const trimmed = name.trim()
    return trimmed.length >= 1 && trimmed.length <= 20 && !/[<>'"&]/.test(trimmed)
  }

  /**
   * 验证数字范围
   */
  static validateNumberRange(value: number, min: number, max: number): boolean {
    return Number.isFinite(value) && value >= min && value <= max
  }

  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.trim())
  }
}

export class InputSanitizer {
  /**
   * 清理HTML标签和特殊字符
   */
  static sanitizeHtml(input: string): string {
    if (!input || typeof input !== 'string') {
      return ''
    }

    return input
      .trim()
      .replace(/[<>]/g, '')
      .replace(/['"&]/g, (match) => {
        const entities: Record<string, string> = {
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;'
        }
        return entities[match] || match
      })
  }

  /**
   * 清理萌宠名称
   */
  static sanitizePetName(name: string): string {
    if (!name || typeof name !== 'string') {
      return ''
    }

    return name
      .trim()
      .slice(0, 20)
      .replace(/[<>'"&]/g, '')
  }

  /**
   * 清理数字输入
   */
  static sanitizeNumber(input: string): string {
    if (!input || typeof input !== 'string') {
      return '0'
    }

    return input.replace(/[^0-9.]/g, '')
  }

  /**
   * 清理地址输入
   */
  static sanitizeAddress(address: string): string {
    if (!address || typeof address !== 'string') {
      return ''
    }

    return address.trim().toLowerCase()
  }

  /**
   * 清理URL输入
   */
  static sanitizeUrl(url: string): string {
    if (!url || typeof url !== 'string') {
      return ''
    }

    const trimmed = url.trim()
    if (!trimmed.startsWith('http://') && !trimmed.startsWith('https://')) {
      return `https://${trimmed}`
    }
    return trimmed
  }
}

export class SecurityValidator {
  /**
   * 检查是否为可疑的合约地址
   */
  static async checkSuspiciousContract(address: string, provider: ethers.Provider): Promise<boolean> {
    try {
      if (!InputValidator.validateAddress(address)) {
        return true
      }

      const code = await provider.getCode(address)
      // 如果地址有代码但不是我们已知的合约，标记为可疑
      return code !== '0x' && !this.isKnownContract(address)
    } catch {
      return true
    }
  }

  /**
   * 检查是否为已知的安全合约
   */
  private static isKnownContract(address: string): boolean {
    const knownContracts = [
      process.env.VITE_CONTRACT_ADDRESS?.toLowerCase(),
      process.env.VITE_CONTRACT_V2_ADDRESS?.toLowerCase()
    ].filter(Boolean)

    return knownContracts.includes(address.toLowerCase())
  }

  /**
   * 验证交易参数安全性
   */
  static validateTransactionSecurity(params: {
    to: string
    value?: string
    data?: string
    gasLimit?: string
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证接收地址
    if (!InputValidator.validateAddress(params.to)) {
      errors.push('无效的接收地址')
    }

    // 验证转账金额
    if (params.value && !InputValidator.validateAmount(params.value)) {
      errors.push('无效的转账金额')
    }

    // 检查gas限制
    if (params.gasLimit) {
      const gasLimit = parseInt(params.gasLimit)
      if (!Number.isInteger(gasLimit) || gasLimit < 21000 || gasLimit > 10000000) {
        errors.push('Gas限制超出安全范围')
      }
    }

    // 检查交易数据长度
    if (params.data && params.data.length > 100000) {
      errors.push('交易数据过长，可能存在安全风险')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 检查金额是否在合理范围内
   */
  static validateAmountSafety(amount: string, maxAmount: string = '1000'): boolean {
    try {
      const amountWei = ethers.parseEther(amount)
      const maxAmountWei = ethers.parseEther(maxAmount)
      return amountWei <= maxAmountWei
    } catch {
      return false
    }
  }
}
