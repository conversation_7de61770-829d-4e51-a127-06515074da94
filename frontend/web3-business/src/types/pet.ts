import {
  PetR<PERSON>,
  PetT<PERSON>,
  PetStatus,
  GrowthStage,
  Pet<PERSON>ood,
  PetSize
} from './typesWithoutCircular'

import type {
  Pet,
  PetStats,
  PetAppearance,
  PetTrait,
  PetSkill,
  Equipment,
  Item
} from './typesWithoutCircular'

// 萌宠验证相关类型和函数
export interface PetValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface PetValidationRules {
  name: {
    minLength: number
    maxLength: number
    allowedChars: RegExp
  }
  level: {
    min: number
    max: number
  }
  stats: {
    min: number
    max: number
  }
  health: {
    min: number
    max: number
  }
  happiness: {
    min: number
    max: number
  }
  energy: {
    min: number
    max: number
  }
}

// 默认验证规则
export const DEFAULT_PET_VALIDATION_RULES: PetValidationRules = {
  name: {
    minLength: 1,
    maxLength: 20,
    allowedChars: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/
  },
  level: {
    min: 1,
    max: 100
  },
  stats: {
    min: 1,
    max: 999
  },
  health: {
    min: 0,
    max: 1000
  },
  happiness: {
    min: 0,
    max: 100
  },
  energy: {
    min: 0,
    max: 100
  }
}

// 萌宠属性计算相关类型
export interface StatCalculationContext {
  pet: Pet
  equipment: Equipment[]
  buffs: StatBuff[]
  environment: EnvironmentModifier[]
}

export interface StatBuff {
  id: string
  name: string
  type: 'temporary' | 'permanent'
  stats: Partial<PetStats>
  duration?: number
  startTime: number
}

export interface EnvironmentModifier {
  type: 'location' | 'weather' | 'time' | 'event'
  stats: Partial<PetStats>
  condition: string
}

// 萌宠成长相关类型
export interface GrowthRequirement {
  level: number
  experience: number
  items?: string[]
  achievements?: string[]
  timeRequired?: number
}

export interface EvolutionPath {
  from: PetType
  to: PetType
  requirements: GrowthRequirement
  rarity: PetRarity
  probability: number
}

// 萌宠序列化相关类型
export interface SerializedPet {
  version: string
  data: string
  checksum: string
  timestamp: number
}

export interface PetExportData {
  pet: Pet
  metadata: {
    exportedAt: number
    exportedBy: string
    version: string
    gameVersion: string
  }
}

// 萌宠工厂配置
export interface PetFactoryConfig {
  defaultRarity: PetRarity
  rarityWeights: Record<PetRarity, number>
  typeWeights: Record<PetType, number>
  statRanges: Record<PetRarity, { min: number; max: number }>
  traitProbabilities: Record<PetRarity, number>
  skillProbabilities: Record<PetRarity, number>
}

// 萌宠生成选项
export interface PetGenerationOptions {
  type?: PetType
  rarity?: PetRarity
  level?: number
  name?: string
  parentIds?: string[]
  traits?: PetTrait[]
  customStats?: Partial<PetStats>
  appearance?: Partial<PetAppearance>
  generation?: number
}

// 萌宠模板
export interface PetTemplate {
  id: string
  name: string
  type: PetType
  rarity: PetRarity
  baseStats: PetStats
  appearance: PetAppearance
  availableTraits: string[]
  availableSkills: string[]
  growthModifiers: {
    experienceMultiplier: number
    statGrowthRate: number
    skillLearningRate: number
  }
  specialAbilities: string[]
  description: string
}

// 萌宠验证函数
export class PetValidator {
  private rules: PetValidationRules

  constructor(rules: PetValidationRules = DEFAULT_PET_VALIDATION_RULES) {
    this.rules = rules
  }

  validatePet(pet: Pet): PetValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证名称
    if (!pet.name || pet.name.length < this.rules.name.minLength) {
      errors.push(`萌宠名称长度不能少于${this.rules.name.minLength}个字符`)
    }
    if (pet.name && pet.name.length > this.rules.name.maxLength) {
      errors.push(`萌宠名称长度不能超过${this.rules.name.maxLength}个字符`)
    }
    if (pet.name && !this.rules.name.allowedChars.test(pet.name)) {
      errors.push('萌宠名称包含非法字符')
    }

    // 验证等级
    if (pet.level < this.rules.level.min || pet.level > this.rules.level.max) {
      errors.push(`萌宠等级必须在${this.rules.level.min}-${this.rules.level.max}之间`)
    }

    // 验证属性值
    const stats = ['strength', 'intelligence', 'agility', 'charm', 'vitality', 'luck'] as const
    stats.forEach(stat => {
      const value = pet.stats[stat]
      if (value < this.rules.stats.min || value > this.rules.stats.max) {
        errors.push(`${stat}属性值必须在${this.rules.stats.min}-${this.rules.stats.max}之间`)
      }
    })

    // 验证健康度
    if (pet.health < this.rules.health.min || pet.health > pet.maxHealth) {
      errors.push(`健康度必须在${this.rules.health.min}-${pet.maxHealth}之间`)
    }

    // 验证快乐度
    if (pet.happiness < this.rules.happiness.min || pet.happiness > pet.maxHappiness) {
      errors.push(`快乐度必须在${this.rules.happiness.min}-${pet.maxHappiness}之间`)
    }

    // 验证能量值
    if (pet.energy < this.rules.energy.min || pet.energy > pet.maxEnergy) {
      errors.push(`能量值必须在${this.rules.energy.min}-${pet.maxEnergy}之间`)
    }

    // 验证经验值
    if (pet.experience < 0 || pet.experience > pet.maxExperience) {
      errors.push(`经验值必须在0-${pet.maxExperience}之间`)
    }

    // 验证时间戳
    const now = Date.now()
    if (pet.birthTime > now) {
      errors.push('出生时间不能晚于当前时间')
    }
    if (pet.lastFeedTime > now) {
      warnings.push('最后喂食时间晚于当前时间')
    }

    // 验证装备
    pet.equipment.forEach((equipment, index) => {
      if (!equipment.id || !equipment.name) {
        errors.push(`装备${index + 1}缺少必要信息`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  validateStats(stats: PetStats): boolean {
    const statValues = Object.values(stats)
    return statValues.every(value =>
      value >= this.rules.stats.min && value <= this.rules.stats.max
    )
  }

  validateName(name: string): boolean {
    return name.length >= this.rules.name.minLength &&
           name.length <= this.rules.name.maxLength &&
           this.rules.name.allowedChars.test(name)
  }
}

// 萌宠属性计算器
export class PetStatsCalculator {
  calculateTotalStats(context: StatCalculationContext): PetStats {
    const { pet, equipment, buffs, environment } = context

    // 基础属性
    let totalStats = { ...pet.baseStats }

    // 等级加成
    const levelBonus = this.calculateLevelBonus(pet.level, pet.rarity)
    totalStats = this.addStats(totalStats, levelBonus)

    // 装备加成
    equipment.forEach(item => {
      if (item.stats) {
        totalStats = this.addStats(totalStats, item.stats as PetStats)
      }
    })

    // Buff加成
    buffs.forEach(buff => {
      if (buff.stats) {
        totalStats = this.addStats(totalStats, buff.stats as PetStats)
      }
    })

    // 环境加成
    environment.forEach(modifier => {
      if (modifier.stats) {
        totalStats = this.addStats(totalStats, modifier.stats as PetStats)
      }
    })

    // 特质加成
    pet.traits.forEach(trait => {
      trait.effects.forEach(effect => {
        if (effect.type === 'stat_bonus') {
          const statName = effect.target as keyof PetStats
          if (statName in totalStats) {
            totalStats[statName] += effect.value
          }
        }
      })
    })

    return totalStats
  }

  calculateLevelBonus(level: number, rarity: PetRarity): Partial<PetStats> {
    const rarityMultipliers = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.2,
      [PetRarity.RARE]: 1.5,
      [PetRarity.EPIC]: 2.0,
      [PetRarity.LEGENDARY]: 3.0,
      [PetRarity.MYTHICAL]: 5.0
    }

    const multiplier = rarityMultipliers[rarity]
    const baseBonus = Math.floor((level - 1) * 2 * multiplier)

    return {
      strength: baseBonus,
      intelligence: baseBonus,
      agility: baseBonus,
      charm: baseBonus,
      vitality: baseBonus,
      luck: Math.floor(baseBonus * 0.5)
    }
  }

  calculateMaxExperience(level: number): number {
    return Math.floor(100 * Math.pow(1.5, level - 1))
  }

  calculateMaxHealth(stats: PetStats, level: number): number {
    return Math.floor(100 + (stats.vitality * 2) + (level * 5))
  }

  calculateMaxHappiness(stats: PetStats): number {
    return Math.floor(100 + (stats.charm * 0.5))
  }

  calculateMaxEnergy(stats: PetStats): number {
    return Math.floor(100 + (stats.vitality * 1.5))
  }

  calculateTokenValue(pet: Pet): string {
    const baseValue = this.getBaseTokenValue(pet.level)
    const rarityMultiplier = this.getRarityMultiplier(pet.rarity)
    const healthBonus = pet.health / pet.maxHealth
    const happinessBonus = pet.happiness / pet.maxHappiness
    const equipmentBonus = this.calculateEquipmentBonus(pet.equipment)
    const traitBonus = this.calculateTraitBonus(pet.traits)

    const totalValue = baseValue * rarityMultiplier * healthBonus * happinessBonus * equipmentBonus * traitBonus

    return (totalValue * 1e18).toString() // 转换为wei
  }

  private addStats(base: Partial<PetStats>, addition: Partial<PetStats>): PetStats {
    return {
      strength: (base.strength || 0) + (addition.strength || 0),
      intelligence: (base.intelligence || 0) + (addition.intelligence || 0),
      agility: (base.agility || 0) + (addition.agility || 0),
      charm: (base.charm || 0) + (addition.charm || 0),
      vitality: (base.vitality || 0) + (addition.vitality || 0),
      luck: (base.luck || 0) + (addition.luck || 0)
    }
  }

  private getBaseTokenValue(level: number): number {
    return Math.floor(10 * Math.pow(1.2, level - 1))
  }

  private getRarityMultiplier(rarity: PetRarity): number {
    const multipliers = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.5,
      [PetRarity.RARE]: 2.0,
      [PetRarity.EPIC]: 3.0,
      [PetRarity.LEGENDARY]: 5.0,
      [PetRarity.MYTHICAL]: 10.0
    }
    return multipliers[rarity]
  }

  private calculateEquipmentBonus(equipment: Equipment[]): number {
    return equipment.reduce((bonus, item) => {
      const rarityBonus = this.getRarityMultiplier(item.rarity) * 0.1
      return bonus + rarityBonus
    }, 1.0)
  }

  private calculateTraitBonus(traits: PetTrait[]): number {
    return traits.reduce((bonus, trait) => {
      const rarityBonus = this.getRarityMultiplier(trait.rarity) * 0.05
      return bonus + rarityBonus
    }, 1.0)
  }
}

// 萌宠数据迁移器
export class PetDataMigrator {
  private static readonly CURRENT_VERSION = '1.0.0'

  static migrateToCurrentVersion(pet: any, fromVersion: string): Pet {
    let migratedPet = { ...pet }

    // 版本迁移逻辑
    switch (fromVersion) {
      case '0.9.0':
        migratedPet = this.migrateFrom090(migratedPet)
        // 继续到下一个版本
      case '1.0.0':
        // 当前版本，无需迁移
        break
      default:
        throw new Error(`不支持的版本迁移: ${fromVersion} -> ${this.CURRENT_VERSION}`)
    }

    return migratedPet
  }

  private static migrateFrom090(pet: any): any {
    // 从0.9.0版本迁移的逻辑
    return {
      ...pet,
      // 添加新字段的默认值
      evolutionPoints: pet.evolutionPoints || 0,
      breedCount: pet.breedCount || 0,
      generation: pet.generation || 1,
      metadata: pet.metadata || {
        version: '1.0.0',
        createdBy: 'migration',
        lastModified: Date.now(),
        checksum: '',
        tags: [],
        description: ''
      }
    }
  }
}

// 萌宠比较器
export class PetComparator {
  static compareByLevel(pet1: Pet, pet2: Pet): number {
    return pet2.level - pet1.level
  }

  static compareByRarity(pet1: Pet, pet2: Pet): number {
    const rarityOrder = {
      [PetRarity.COMMON]: 1,
      [PetRarity.UNCOMMON]: 2,
      [PetRarity.RARE]: 3,
      [PetRarity.EPIC]: 4,
      [PetRarity.LEGENDARY]: 5,
      [PetRarity.MYTHICAL]: 6
    }
    return rarityOrder[pet2.rarity] - rarityOrder[pet1.rarity]
  }

  static compareByTokenValue(pet1: Pet, pet2: Pet): number {
    const calculator = new PetStatsCalculator()
    const value1 = BigInt(calculator.calculateTokenValue(pet1))
    const value2 = BigInt(calculator.calculateTokenValue(pet2))

    if (value1 > value2) return -1
    if (value1 < value2) return 1
    return 0
  }

  static compareByTotalStats(pet1: Pet, pet2: Pet): number {
    const total1 = Object.values(pet1.stats).reduce((sum, val) => sum + val, 0)
    const total2 = Object.values(pet2.stats).reduce((sum, val) => sum + val, 0)
    return total2 - total1
  }

  static compareByAge(pet1: Pet, pet2: Pet): number {
    return pet1.birthTime - pet2.birthTime
  }
}

// 萌宠搜索和过滤器
export class PetFilter {
  static filterByType(pets: Pet[], type: PetType): Pet[] {
    return pets.filter(pet => pet.type === type)
  }

  static filterByRarity(pets: Pet[], rarity: PetRarity): Pet[] {
    return pets.filter(pet => pet.rarity === rarity)
  }

  static filterByLevelRange(pets: Pet[], minLevel: number, maxLevel: number): Pet[] {
    return pets.filter(pet => pet.level >= minLevel && pet.level <= maxLevel)
  }

  static filterByStatus(pets: Pet[], status: PetStatus): Pet[] {
    return pets.filter(pet => pet.status === status)
  }

  static filterByGrowthStage(pets: Pet[], stage: GrowthStage): Pet[] {
    return pets.filter(pet => pet.growthStage === stage)
  }

  static filterByTrait(pets: Pet[], traitName: string): Pet[] {
    return pets.filter(pet =>
      pet.traits.some(trait => trait.name === traitName)
    )
  }

  static filterBySkill(pets: Pet[], skillName: string): Pet[] {
    return pets.filter(pet =>
      pet.skills.some(skill => skill.name === skillName)
    )
  }

  static searchByName(pets: Pet[], query: string): Pet[] {
    const lowerQuery = query.toLowerCase()
    return pets.filter(pet =>
      pet.name.toLowerCase().includes(lowerQuery)
    )
  }

  static filterByMinTokenValue(pets: Pet[], minValue: string): Pet[] {
    const calculator = new PetStatsCalculator()
    const minValueBigInt = BigInt(minValue)

    return pets.filter(pet => {
      const petValue = BigInt(calculator.calculateTokenValue(pet))
      return petValue >= minValueBigInt
    })
  }
}

// 萌宠序列化器
export class PetSerializer {
  private static readonly VERSION = '1.0.0'

  static serialize(pet: Pet): SerializedPet {
    const data = JSON.stringify(pet)
    const checksum = this.calculateChecksum(data)

    return {
      version: this.VERSION,
      data: this.encodeBase64(data), // Base64编码
      checksum,
      timestamp: Date.now()
    }
  }

  static deserialize(serialized: SerializedPet): Pet {
    // 验证版本
    if (serialized.version !== this.VERSION) {
      throw new Error(`不支持的版本: ${serialized.version}`)
    }

    // 解码数据
    const data = this.decodeBase64(serialized.data)

    // 验证校验和
    const calculatedChecksum = this.calculateChecksum(data)
    if (calculatedChecksum !== serialized.checksum) {
      throw new Error('数据校验失败')
    }

    // 解析JSON
    try {
      const pet = JSON.parse(data) as Pet
      return this.migratePetData(pet)
    } catch (error) {
      throw new Error('数据格式错误')
    }
  }

  static exportPet(pet: Pet, exportedBy: string): PetExportData {
    return {
      pet,
      metadata: {
        exportedAt: Date.now(),
        exportedBy,
        version: this.VERSION,
        gameVersion: '1.0.0'
      }
    }
  }

  static importPet(exportData: PetExportData): Pet {
    // 验证导入数据
    if (!exportData.pet || !exportData.metadata) {
      throw new Error('导入数据格式错误')
    }

    // 生成新的ID以避免冲突
    const pet = {
      ...exportData.pet,
      id: this.generateId(),
      birthTime: Date.now(),
      lastFeedTime: Date.now(),
      lastPlayTime: Date.now(),
      lastTrainTime: Date.now(),
      lastRestTime: Date.now()
    }

    return this.migratePetData(pet)
  }

  private static calculateChecksum(data: string): string {
    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  private static migratePetData(pet: Pet): Pet {
    // 数据迁移逻辑，确保向后兼容
    const migratedPet = { ...pet }

    // 确保必要字段存在
    if (!migratedPet.metadata) {
      migratedPet.metadata = {
        version: this.VERSION,
        createdBy: 'unknown',
        lastModified: Date.now(),
        checksum: '',
        tags: [],
        description: ''
      }
    }

    if (!migratedPet.traits) {
      migratedPet.traits = []
    }

    if (!migratedPet.skills) {
      migratedPet.skills = []
    }

    if (!migratedPet.achievements) {
      migratedPet.achievements = []
    }

    return migratedPet
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private static encodeBase64(str: string): string {
    // 处理Unicode字符
    const utf8Bytes = new TextEncoder().encode(str)
    const binaryString = Array.from(utf8Bytes, byte => String.fromCharCode(byte)).join('')
    return btoa(binaryString)
  }

  private static decodeBase64(base64: string): string {
    // 解码Base64并处理Unicode字符
    const binaryString = atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return new TextDecoder().decode(bytes)
  }
}
