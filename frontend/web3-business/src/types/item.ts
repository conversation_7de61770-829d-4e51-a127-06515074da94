import {
  ItemType,
  PetRarity,
  PetStatus,
  PetMood
} from './typesWithoutCircular'

import type {
  Item,
  ItemEffect,
  ItemRequirement,
  Pet,
  PetStats
} from './typesWithoutCircular'

// 道具使用结果
export interface ItemUseResult {
  success: boolean
  message: string
  effects: AppliedEffect[]
  cooldownRemaining?: number
}

export interface AppliedEffect {
  type: string
  target: string
  oldValue: number | string
  newValue: number | string
  duration?: number
}

// 道具管理器
export class ItemManager {
  private pet: Pet
  private inventory: Map<string, Item>
  private cooldowns: Map<string, number>

  constructor(pet: Pet, inventory: Item[] = []) {
    this.pet = pet
    this.inventory = new Map()
    this.cooldowns = new Map()

    // 初始化背包
    inventory.forEach(item => {
      this.inventory.set(item.id, item)
    })
  }

  // 使用道具
  useItem(itemId: string, quantity: number = 1): ItemUseResult {
    const item = this.inventory.get(itemId)
    if (!item) {
      return {
        success: false,
        message: '道具不存在',
        effects: []
      }
    }

    // 检查数量
    if (item.quantity < quantity) {
      return {
        success: false,
        message: '道具数量不足',
        effects: []
      }
    }

    // 检查冷却时间
    const cooldownRemaining = this.getCooldownRemaining(itemId)
    if (cooldownRemaining > 0) {
      return {
        success: false,
        message: `道具冷却中，剩余 ${Math.ceil(cooldownRemaining / 1000)} 秒`,
        effects: [],
        cooldownRemaining
      }
    }

    // 验证使用条件
    const validation = this.validateItemUse(item)
    if (!validation.canUse) {
      return {
        success: false,
        message: validation.reasons.join(', '),
        effects: []
      }
    }

    // 应用道具效果
    const effects = this.applyItemEffects(item, quantity)

    // 消耗道具
    if (item.consumable) {
      item.quantity -= quantity
      if (item.quantity <= 0) {
        this.inventory.delete(itemId)
      }
    }

    // 设置冷却时间
    if (item.cooldown > 0) {
      this.cooldowns.set(itemId, Date.now() + item.cooldown * 1000)
    }

    return {
      success: true,
      message: `成功使用 ${item.name}`,
      effects
    }
  }

  // 验证道具使用条件
  private validateItemUse(item: Item): { canUse: boolean; reasons: string[] } {
    const reasons: string[] = []

    // 检查等级要求
    item.requirements.forEach(req => {
      switch (req.type) {
        case 'level':
          if (this.pet.level < (req.value as number)) {
            reasons.push(`需要等级 ${req.value}`)
          }
          break
        case 'stat':
          const statValue = this.pet.stats[req.target as keyof PetStats]
          if (statValue < (req.value as number)) {
            reasons.push(`需要 ${req.target} ${req.value}`)
          }
          break
        case 'species':
          if (this.pet.type !== req.value) {
            reasons.push(`仅限 ${req.value} 种族使用`)
          }
          break
        case 'rarity':
          const rarityOrder = {
            [PetRarity.COMMON]: 1,
            [PetRarity.UNCOMMON]: 2,
            [PetRarity.RARE]: 3,
            [PetRarity.EPIC]: 4,
            [PetRarity.LEGENDARY]: 5,
            [PetRarity.MYTHICAL]: 6
          }
          const requiredRarity = rarityOrder[req.value as PetRarity]
          const petRarity = rarityOrder[this.pet.rarity]
          if (petRarity < requiredRarity) {
            reasons.push(`需要稀有度 ${req.value}`)
          }
          break
      }
    })

    return {
      canUse: reasons.length === 0,
      reasons
    }
  }

  // 应用道具效果
  private applyItemEffects(item: Item, quantity: number): AppliedEffect[] {
    const effects: AppliedEffect[] = []

    item.effects.forEach(effect => {
      // 检查触发概率
      if (effect.chance && Math.random() > effect.chance) {
        return
      }

      const appliedEffect = this.applyEffect(effect, quantity)
      if (appliedEffect) {
        effects.push(appliedEffect)
      }
    })

    return effects
  }

  // 应用单个效果
  private applyEffect(effect: ItemEffect, quantity: number): AppliedEffect | null {
    const totalValue = effect.value * quantity

    switch (effect.type) {
      case 'heal':
        return this.applyHealEffect(effect.target, totalValue)
      case 'buff':
        return this.applyBuffEffect(effect.target, totalValue, effect.duration)
      case 'experience':
        return this.applyExperienceEffect(totalValue)
      case 'happiness':
        return this.applyHappinessEffect(totalValue)
      case 'energy':
        return this.applyEnergyEffect(totalValue)
      default:
        return null
    }
  }

  // 治疗效果
  private applyHealEffect(target: string, value: number): AppliedEffect {
    const oldHealth = this.pet.health
    this.pet.health = Math.min(this.pet.health + value, this.pet.maxHealth)

    // 如果完全治愈，更新状态
    if (this.pet.health === this.pet.maxHealth && this.pet.status === PetStatus.SICK) {
      this.pet.status = PetStatus.HEALTHY
    }

    return {
      type: 'heal',
      target: 'health',
      oldValue: oldHealth,
      newValue: this.pet.health
    }
  }

  // Buff效果
  private applyBuffEffect(target: string, value: number, duration?: number): AppliedEffect {
    // 这里应该实现临时属性加成系统
    // 暂时直接修改属性
    const oldValue = this.pet.stats[target as keyof PetStats] || 0
    const newValue = oldValue + value

    if (target in this.pet.stats) {
      this.pet.stats[target as keyof PetStats] = newValue
    }

    return {
      type: 'buff',
      target,
      oldValue,
      newValue,
      duration
    }
  }

  // 经验值效果
  private applyExperienceEffect(value: number): AppliedEffect {
    const oldExperience = this.pet.experience
    this.pet.experience = Math.min(this.pet.experience + value, this.pet.maxExperience)

    return {
      type: 'experience',
      target: 'experience',
      oldValue: oldExperience,
      newValue: this.pet.experience
    }
  }

  // 快乐度效果
  private applyHappinessEffect(value: number): AppliedEffect {
    const oldHappiness = this.pet.happiness
    this.pet.happiness = Math.max(0, Math.min(this.pet.happiness + value, this.pet.maxHappiness))

    // 更新心情
    this.updatePetMood()

    return {
      type: 'happiness',
      target: 'happiness',
      oldValue: oldHappiness,
      newValue: this.pet.happiness
    }
  }

  // 能量效果
  private applyEnergyEffect(value: number): AppliedEffect {
    const oldEnergy = this.pet.energy
    this.pet.energy = Math.max(0, Math.min(this.pet.energy + value, this.pet.maxEnergy))

    // 如果能量恢复，更新状态
    if (this.pet.energy > this.pet.maxEnergy * 0.5 && this.pet.status === PetStatus.TIRED) {
      this.pet.status = PetStatus.HEALTHY
    }

    return {
      type: 'energy',
      target: 'energy',
      oldValue: oldEnergy,
      newValue: this.pet.energy
    }
  }

  // 更新萌宠心情
  private updatePetMood(): void {
    const happinessRatio = this.pet.happiness / this.pet.maxHappiness

    if (happinessRatio >= 0.9) {
      this.pet.mood = PetMood.ECSTATIC
    } else if (happinessRatio >= 0.7) {
      this.pet.mood = PetMood.HAPPY
    } else if (happinessRatio >= 0.5) {
      this.pet.mood = PetMood.CONTENT
    } else if (happinessRatio >= 0.3) {
      this.pet.mood = PetMood.NEUTRAL
    } else if (happinessRatio >= 0.1) {
      this.pet.mood = PetMood.UNHAPPY
    } else {
      this.pet.mood = PetMood.DEPRESSED
    }
  }

  // 获取冷却剩余时间
  getCooldownRemaining(itemId: string): number {
    const cooldownEnd = this.cooldowns.get(itemId)
    if (!cooldownEnd) return 0

    const remaining = cooldownEnd - Date.now()
    return Math.max(0, remaining)
  }

  // 添加道具到背包
  addItem(item: Item): boolean {
    const existingItem = Array.from(this.inventory.values()).find(i =>
      i.name === item.name && i.type === item.type
    )

    if (existingItem && existingItem.quantity + item.quantity <= existingItem.maxStack) {
      existingItem.quantity += item.quantity
      return true
    } else if (!existingItem) {
      this.inventory.set(item.id, { ...item })
      return true
    }

    return false // 背包已满或超出堆叠限制
  }

  // 移除道具
  removeItem(itemId: string, quantity: number = 1): boolean {
    const item = this.inventory.get(itemId)
    if (!item || item.quantity < quantity) {
      return false
    }

    item.quantity -= quantity
    if (item.quantity <= 0) {
      this.inventory.delete(itemId)
    }

    return true
  }

  // 获取背包物品列表
  getInventory(): Item[] {
    return Array.from(this.inventory.values())
  }

  // 按类型筛选道具
  getItemsByType(type: ItemType): Item[] {
    return this.getInventory().filter(item => item.type === type)
  }

  // 搜索道具
  searchItems(query: string): Item[] {
    const lowerQuery = query.toLowerCase()
    return this.getInventory().filter(item =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery)
    )
  }

  // 获取可用道具（满足使用条件的）
  getUsableItems(): Item[] {
    return this.getInventory().filter(item => {
      const validation = this.validateItemUse(item)
      const cooldown = this.getCooldownRemaining(item.id)
      return validation.canUse && cooldown === 0
    })
  }
}

// 道具工厂
export class ItemFactory {
  // 生成随机道具
  static generateRandomItem(type: ItemType, rarity: PetRarity): Item {
    const id = this.generateId()
    const itemData = this.getItemTemplate(type, rarity)

    return {
      id,
      name: itemData.name,
      type,
      rarity,
      quantity: 1,
      maxStack: itemData.maxStack,
      description: itemData.description,
      effects: itemData.effects,
      requirements: itemData.requirements,
      cooldown: itemData.cooldown,
      consumable: itemData.consumable,
      tradeable: itemData.tradeable,
      value: itemData.value,
      metadata: {
        version: '1.0.0',
        createdAt: Date.now(),
        createdBy: 'ItemFactory',
        lastModified: Date.now(),
        tags: [type, rarity],
        rarity
      }
    }
  }

  // 获取道具模板
  private static getItemTemplate(type: ItemType, rarity: PetRarity): any {
    const templates: Record<string, any> = {
      [ItemType.FOOD]: {
        name: this.getFoodName(rarity),
        maxStack: 99,
        description: '美味的食物，可以恢复萌宠的健康和快乐',
        effects: [
          { type: 'heal' as const, target: 'health', value: this.getHealValue(rarity) },
          { type: 'happiness' as const, target: 'happiness', value: this.getHappinessValue(rarity) }
        ],
        requirements: [],
        cooldown: 30,
        consumable: true,
        tradeable: true,
        value: this.getItemValue(rarity)
      },
      [ItemType.MEDICINE]: {
        name: this.getMedicineName(rarity),
        maxStack: 50,
        description: '治疗萌宠疾病的药物',
        effects: [
          { type: 'heal' as const, target: 'health', value: this.getHealValue(rarity) * 2 },
          { type: 'buff' as const, target: 'vitality', value: 10, duration: 3600 }
        ],
        requirements: [],
        cooldown: 60,
        consumable: true,
        tradeable: true,
        value: this.getItemValue(rarity) * 2
      },
      [ItemType.TOY]: {
        name: this.getToyName(rarity),
        maxStack: 20,
        description: '有趣的玩具，可以提升萌宠的快乐度',
        effects: [
          { type: 'happiness' as const, target: 'happiness', value: this.getHappinessValue(rarity) * 1.5 },
          { type: 'energy' as const, target: 'energy', value: -10 }
        ],
        requirements: [],
        cooldown: 120,
        consumable: false,
        tradeable: true,
        value: this.getItemValue(rarity) * 1.5
      },
      [ItemType.TRAINING_ITEM]: {
        name: this.getTrainingItemName(rarity),
        maxStack: 10,
        description: '训练用品，可以提升萌宠的经验值',
        effects: [
          { type: 'experience' as const, target: 'experience', value: this.getExperienceValue(rarity) },
          { type: 'energy' as const, target: 'energy', value: -20 }
        ],
        requirements: [{ type: 'level' as const, target: 'pet', value: 5 }],
        cooldown: 300,
        consumable: true,
        tradeable: true,
        value: this.getItemValue(rarity) * 3
      },
      [ItemType.EVOLUTION_ITEM]: {
        name: this.getEvolutionItemName(rarity),
        maxStack: 1,
        description: '神秘的进化道具，可能触发萌宠进化',
        effects: [
          { type: 'buff' as const, target: 'all_stats', value: 20, duration: 7200 }
        ],
        requirements: [
          { type: 'level' as const, target: 'pet', value: 20 },
          { type: 'rarity' as const, target: 'pet', value: rarity }
        ],
        cooldown: 86400, // 24小时
        consumable: true,
        tradeable: false,
        value: this.getItemValue(rarity) * 10
      }
    }

    return templates[type] || templates[ItemType.FOOD]
  }

  // 生成道具名称
  private static getFoodName(rarity: PetRarity): string {
    const names = {
      [PetRarity.COMMON]: ['普通饲料', '干粮', '水果'],
      [PetRarity.UNCOMMON]: ['营养餐', '美味罐头', '新鲜肉类'],
      [PetRarity.RARE]: ['高级料理', '珍馐美味', '灵果'],
      [PetRarity.EPIC]: ['传说美食', '仙品佳肴', '神果'],
      [PetRarity.LEGENDARY]: ['神级料理', '仙界珍馐', '龙血果'],
      [PetRarity.MYTHICAL]: ['创世美食', '混沌之果', '生命之源']
    }
    const nameList = names[rarity]
    return nameList[Math.floor(Math.random() * nameList.length)]
  }

  private static getMedicineName(rarity: PetRarity): string {
    const names = {
      [PetRarity.COMMON]: ['基础药水', '治疗药剂', '恢复药'],
      [PetRarity.UNCOMMON]: ['中级药水', '活力药剂', '治愈药'],
      [PetRarity.RARE]: ['高级药水', '生命药剂', '万能药'],
      [PetRarity.EPIC]: ['传说药水', '神圣药剂', '复活药'],
      [PetRarity.LEGENDARY]: ['神级药水', '不死药剂', '永生药'],
      [PetRarity.MYTHICAL]: ['创世药水', '混沌药剂', '时光药']
    }
    const nameList = names[rarity]
    return nameList[Math.floor(Math.random() * nameList.length)]
  }

  private static getToyName(rarity: PetRarity): string {
    const names = {
      [PetRarity.COMMON]: ['小球', '绳子', '骨头'],
      [PetRarity.UNCOMMON]: ['飞盘', '毛线球', '咬胶'],
      [PetRarity.RARE]: ['魔法球', '智慧拼图', '音乐盒'],
      [PetRarity.EPIC]: ['传说玩具', '时空球', '幻境盒'],
      [PetRarity.LEGENDARY]: ['神器玩具', '创造球', '梦想盒'],
      [PetRarity.MYTHICAL]: ['混沌玩具', '宇宙球', '永恒盒']
    }
    const nameList = names[rarity]
    return nameList[Math.floor(Math.random() * nameList.length)]
  }

  private static getTrainingItemName(rarity: PetRarity): string {
    const names = {
      [PetRarity.COMMON]: ['训练手册', '基础器械', '练习靶'],
      [PetRarity.UNCOMMON]: ['进阶教程', '专业器械', '训练场'],
      [PetRarity.RARE]: ['大师秘籍', '精密器械', '修炼室'],
      [PetRarity.EPIC]: ['传说秘籍', '神器器械', '时空道场'],
      [PetRarity.LEGENDARY]: ['神级秘籍', '创世器械', '混沌道场'],
      [PetRarity.MYTHICAL]: ['至尊秘籍', '永恒器械', '无限道场']
    }
    const nameList = names[rarity]
    return nameList[Math.floor(Math.random() * nameList.length)]
  }

  private static getEvolutionItemName(rarity: PetRarity): string {
    const names = {
      [PetRarity.COMMON]: ['进化石', '变异因子', '成长药剂'],
      [PetRarity.UNCOMMON]: ['进化水晶', '基因药剂', '蜕变石'],
      [PetRarity.RARE]: ['进化宝石', '神秘药剂', '超越石'],
      [PetRarity.EPIC]: ['进化神石', '传说药剂', '升华石'],
      [PetRarity.LEGENDARY]: ['进化圣石', '神级药剂', '涅槃石'],
      [PetRarity.MYTHICAL]: ['进化创世石', '混沌药剂', '永恒石']
    }
    const nameList = names[rarity]
    return nameList[Math.floor(Math.random() * nameList.length)]
  }

  // 计算道具效果值
  private static getHealValue(rarity: PetRarity): number {
    const values = {
      [PetRarity.COMMON]: 20,
      [PetRarity.UNCOMMON]: 35,
      [PetRarity.RARE]: 50,
      [PetRarity.EPIC]: 75,
      [PetRarity.LEGENDARY]: 100,
      [PetRarity.MYTHICAL]: 150
    }
    return values[rarity]
  }

  private static getHappinessValue(rarity: PetRarity): number {
    const values = {
      [PetRarity.COMMON]: 15,
      [PetRarity.UNCOMMON]: 25,
      [PetRarity.RARE]: 40,
      [PetRarity.EPIC]: 60,
      [PetRarity.LEGENDARY]: 80,
      [PetRarity.MYTHICAL]: 120
    }
    return values[rarity]
  }

  private static getExperienceValue(rarity: PetRarity): number {
    const values = {
      [PetRarity.COMMON]: 50,
      [PetRarity.UNCOMMON]: 100,
      [PetRarity.RARE]: 200,
      [PetRarity.EPIC]: 350,
      [PetRarity.LEGENDARY]: 500,
      [PetRarity.MYTHICAL]: 800
    }
    return values[rarity]
  }

  private static getItemValue(rarity: PetRarity): number {
    const values = {
      [PetRarity.COMMON]: 10,
      [PetRarity.UNCOMMON]: 25,
      [PetRarity.RARE]: 50,
      [PetRarity.EPIC]: 100,
      [PetRarity.LEGENDARY]: 200,
      [PetRarity.MYTHICAL]: 500
    }
    return values[rarity]
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

// 道具效果计算器
export class ItemEffectCalculator {
  // 计算道具对萌宠的总体影响
  static calculateItemImpact(item: Item, pet: Pet): {
    healthChange: number
    happinessChange: number
    energyChange: number
    experienceChange: number
    statChanges: Partial<PetStats>
  } {
    let healthChange = 0
    let happinessChange = 0
    let energyChange = 0
    let experienceChange = 0
    const statChanges: Partial<PetStats> = {}

    item.effects.forEach(effect => {
      switch (effect.type) {
        case 'heal':
          healthChange += effect.value
          break
        case 'happiness':
          happinessChange += effect.value
          break
        case 'energy':
          energyChange += effect.value
          break
        case 'experience':
          experienceChange += effect.value
          break
        case 'buff':
          if (effect.target in pet.stats) {
            statChanges[effect.target as keyof PetStats] =
              (statChanges[effect.target as keyof PetStats] || 0) + effect.value
          }
          break
      }
    })

    return {
      healthChange,
      happinessChange,
      energyChange,
      experienceChange,
      statChanges
    }
  }

  // 推荐适合萌宠的道具
  static recommendItems(pet: Pet, availableItems: Item[]): Item[] {
    const recommendations: { item: Item; score: number }[] = []

    availableItems.forEach(item => {
      let score = 0

      // 根据萌宠当前状态计算推荐分数
      if (pet.health < pet.maxHealth * 0.5) {
        // 健康度低，推荐治疗道具
        const healEffect = item.effects.find(e => e.type === 'heal')
        if (healEffect) score += healEffect.value * 2
      }

      if (pet.happiness < pet.maxHappiness * 0.5) {
        // 快乐度低，推荐玩具和食物
        const happinessEffect = item.effects.find(e => e.type === 'happiness')
        if (happinessEffect) score += happinessEffect.value * 1.5
      }

      if (pet.energy < pet.maxEnergy * 0.3) {
        // 能量低，推荐恢复道具
        const energyEffect = item.effects.find(e => e.type === 'energy' && e.value > 0)
        if (energyEffect) score += energyEffect.value * 1.8
      }

      // 经验值接近升级，推荐训练道具
      const expToNext = pet.maxExperience - pet.experience
      if (expToNext < pet.maxExperience * 0.2) {
        const expEffect = item.effects.find(e => e.type === 'experience')
        if (expEffect) score += expEffect.value * 1.3
      }

      if (score > 0) {
        recommendations.push({ item, score })
      }
    })

    // 按分数排序并返回前5个推荐
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(r => r.item)
  }
}
