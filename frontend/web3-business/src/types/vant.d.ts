declare module 'vant' {
  import { App } from 'vue'

  export function showToast(message: string | { message: string; type?: string; duration?: number }): void
  export function showDialog(options: { title?: string; message: string; confirmButtonText?: string; cancelButtonText?: string }): Promise<void>
  export function showNotify(options: { type?: string; message: string; duration?: number }): void
  export function showLoadingToast(message?: string): void
  export function closeToast(): void

  const Vant: {
    install(app: App): void
  }

  export default Vant
}

declare module '@vant/touch-emulator' {
  // Touch emulator for desktop
}

declare module 'vant/lib/index.css' {
  // CSS module
}