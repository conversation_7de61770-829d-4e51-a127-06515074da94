/**
 * 避免循环依赖的类型定义文件
 * 这个文件直接定义了所有需要的类型，避免了从其他文件导入
 */

// 从index.ts复制过来的类型
export interface Pet {
  id: string
  name: string
  type: PetType
  level: number
  experience: number
  maxExperience: number
  health: number
  maxHealth: number
  happiness: number
  maxHappiness: number
  energy: number
  maxEnergy: number
  rarity: PetRarity
  equipment: Equipment[]
  lastFeedTime: number
  lastPlayTime: number
  lastTrainTime: number
  lastRestTime: number
  birthTime: number
  avatar: string
  stats: PetStats
  baseStats: PetStats
  appearance: PetAppearance
  status: PetStatus
  growthStage: GrowthStage
  mood: PetMood
  skills: PetSkill[]
  achievements: string[]
  totalTokensEarned: string
  evolutionPoints: number
  breedCount: number
  generation: number
  parents?: {
    mother?: string
    father?: string
  }
  traits: PetTrait[]
  metadata: PetMetadata
}

export interface PetStats {
  strength: number
  intelligence: number
  agility: number
  charm: number
  vitality: number
  luck: number
}

export interface PetAppearance {
  species: string
  color: string
  pattern: string
  size: PetSize
  accessories: string[]
  specialEffects: string[]
  animations: string[]
}

export interface PetMetadata {
  version: string
  createdBy: string
  lastModified: number
  checksum: string
  tags: string[]
  description?: string
}

export interface PetTrait {
  id: string
  name: string
  type: TraitType
  value: number | string | boolean
  rarity: PetRarity
  description: string
  effects: TraitEffect[]
}

export interface TraitEffect {
  type: 'stat_bonus' | 'skill_bonus' | 'special_ability'
  target: string
  value: number
  condition?: string
}

export interface PetSkill {
  id: string
  name: string
  level: number
  maxLevel: number
  experience: number
  maxExperience: number
  type: SkillType
  description: string
  effects: SkillEffect[]
  requirements: SkillRequirement[]
}

export interface SkillEffect {
  type: 'stat_modifier' | 'token_bonus' | 'special_action'
  value: number
  duration?: number
  condition?: string
}

export interface SkillRequirement {
  type: 'level' | 'stat' | 'equipment' | 'achievement'
  target: string
  value: number
}

export enum PetType {
  CAT = 'cat',
  DOG = 'dog',
  RABBIT = 'rabbit',
  BIRD = 'bird',
  DRAGON = 'dragon',
  UNICORN = 'unicorn',
  PHOENIX = 'phoenix',
  GRIFFIN = 'griffin',
  PEGASUS = 'pegasus',
  KITSUNE = 'kitsune'
}

export enum PetRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHICAL = 'mythical'
}

export enum PetSize {
  TINY = 'tiny',
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  GIANT = 'giant'
}

export enum PetStatus {
  HEALTHY = 'healthy',
  SICK = 'sick',
  TIRED = 'tired',
  HUNGRY = 'hungry',
  HAPPY = 'happy',
  SAD = 'sad',
  EXCITED = 'excited',
  SLEEPING = 'sleeping',
  TRAINING = 'training',
  EVOLVING = 'evolving'
}

export enum GrowthStage {
  EGG = 'egg',
  BABY = 'baby',
  CHILD = 'child',
  TEEN = 'teen',
  ADULT = 'adult',
  ELDER = 'elder',
  LEGENDARY_FORM = 'legendary_form'
}

export enum PetMood {
  ECSTATIC = 'ecstatic',
  HAPPY = 'happy',
  CONTENT = 'content',
  NEUTRAL = 'neutral',
  UNHAPPY = 'unhappy',
  DEPRESSED = 'depressed',
  ANGRY = 'angry'
}

export enum TraitType {
  PHYSICAL = 'physical',
  MENTAL = 'mental',
  MAGICAL = 'magical',
  SOCIAL = 'social',
  SPECIAL = 'special'
}

export enum SkillType {
  COMBAT = 'combat',
  MAGIC = 'magic',
  CRAFTING = 'crafting',
  SOCIAL = 'social',
  SURVIVAL = 'survival',
  SPECIAL = 'special'
}

export interface Equipment {
  id: string
  name: string
  type: EquipmentType
  rarity: PetRarity
  level: number
  stats: Partial<PetStats>
  description: string
  requirements: EquipmentRequirement[]
  effects: EquipmentEffect[]
  durability: number
  maxDurability: number
  enchantments: Enchantment[]
  setBonus?: SetBonus
  tradeable: boolean
  soulbound: boolean
  metadata: ItemMetadata
}

export interface EquipmentRequirement {
  type: 'level' | 'stat' | 'rarity' | 'species'
  target: string
  value: number | string
}

export interface EquipmentEffect {
  type: 'stat_bonus' | 'skill_bonus' | 'special_ability' | 'passive_effect'
  target: string
  value: number
  condition?: string
  duration?: number
}

export interface Enchantment {
  id: string
  name: string
  level: number
  type: EnchantmentType
  effect: EquipmentEffect
  description: string
}

export interface SetBonus {
  setName: string
  requiredPieces: number
  currentPieces: number
  bonuses: EquipmentEffect[]
}

export interface ItemMetadata {
  version: string
  createdAt: number
  createdBy: string
  lastModified: number
  tags: string[]
  rarity: PetRarity
}

export enum EquipmentType {
  WEAPON = 'weapon',
  ARMOR = 'armor',
  HELMET = 'helmet',
  BOOTS = 'boots',
  GLOVES = 'gloves',
  ACCESSORY = 'accessory',
  RING = 'ring',
  NECKLACE = 'necklace',
  TOY = 'toy',
  FOOD = 'food',
  POTION = 'potion',
  SCROLL = 'scroll',
  TOOL = 'tool'
}

export enum EnchantmentType {
  OFFENSIVE = 'offensive',
  DEFENSIVE = 'defensive',
  UTILITY = 'utility',
  SPECIAL = 'special'
}

// 道具相关类型
export interface Item {
  id: string
  name: string
  type: ItemType
  rarity: PetRarity
  quantity: number
  maxStack: number
  description: string
  effects: ItemEffect[]
  requirements: ItemRequirement[]
  cooldown: number
  consumable: boolean
  tradeable: boolean
  value: number
  metadata: ItemMetadata
}

export interface ItemEffect {
  type: 'heal' | 'buff' | 'debuff' | 'experience' | 'happiness' | 'energy'
  target: string
  value: number
  duration?: number
  chance?: number
}

export interface ItemRequirement {
  type: 'level' | 'stat' | 'species' | 'rarity'
  target: string
  value: number | string
}

export enum ItemType {
  FOOD = 'food',
  MEDICINE = 'medicine',
  TOY = 'toy',
  TRAINING_ITEM = 'training_item',
  EVOLUTION_ITEM = 'evolution_item',
  BREEDING_ITEM = 'breeding_item',
  COSMETIC = 'cosmetic',
  CONSUMABLE = 'consumable',
  MATERIAL = 'material',
  CURRENCY = 'currency',
  SPECIAL = 'special'
}

// 从pet.ts复制过来的类型
export interface PetFactoryConfig {
  defaultRarity: PetRarity
  rarityWeights: Record<PetRarity, number>
  typeWeights: Record<PetType, number>
  statRanges: Record<PetRarity, { min: number; max: number }>
  traitProbabilities: Record<PetRarity, number>
  skillProbabilities: Record<PetRarity, number>
}

export interface PetGenerationOptions {
  type?: PetType
  rarity?: PetRarity
  level?: number
  name?: string
  parentIds?: string[]
  traits?: PetTrait[]
  customStats?: Partial<PetStats>
  appearance?: Partial<PetAppearance>
  generation?: number
}

export interface PetTemplate {
  id: string
  name: string
  type: PetType
  rarity: PetRarity
  baseStats: PetStats
  appearance: PetAppearance
  availableTraits: string[]
  availableSkills: string[]
  growthModifiers: {
    experienceMultiplier: number
    statGrowthRate: number
    skillLearningRate: number
  }
  specialAbilities: string[]
  description: string
}

export interface GrowthRequirement {
  level: number
  experience: number
  items?: string[]
  achievements?: string[]
  timeRequired?: number
}

export interface EvolutionPath {
  from: PetType
  to: PetType
  requirements: GrowthRequirement
  rarity: PetRarity
  probability: number
}

// PetStatsCalculator 的简化版本
export class PetStatsCalculator {
  calculateMaxExperience(level: number): number {
    return Math.floor(100 * Math.pow(1.5, level - 1))
  }

  calculateMaxHealth(stats: PetStats, level: number): number {
    return Math.floor(100 + (stats.vitality * 2) + (level * 5))
  }

  calculateMaxHappiness(stats: PetStats): number {
    return Math.floor(100 + (stats.charm * 0.5))
  }

  calculateMaxEnergy(stats: PetStats): number {
    return Math.floor(100 + (stats.vitality * 1.5))
  }

  calculateTokenValue(pet: Pet): string {
    const baseValue = this.getBaseTokenValue(pet.level)
    const rarityMultiplier = this.getRarityMultiplier(pet.rarity)
    const healthBonus = pet.health / pet.maxHealth
    const happinessBonus = pet.happiness / pet.maxHappiness

    const totalValue = baseValue * rarityMultiplier * healthBonus * happinessBonus

    return (totalValue * 1e18).toString() // 转换为wei
  }

  private getBaseTokenValue(level: number): number {
    return Math.floor(10 * Math.pow(1.2, level - 1))
  }

  private getRarityMultiplier(rarity: PetRarity): number {
    const multipliers = {
      [PetRarity.COMMON]: 1.0,
      [PetRarity.UNCOMMON]: 1.5,
      [PetRarity.RARE]: 2.0,
      [PetRarity.EPIC]: 3.0,
      [PetRarity.LEGENDARY]: 5.0,
      [PetRarity.MYTHICAL]: 10.0
    }
    return multipliers[rarity]
  }
}
