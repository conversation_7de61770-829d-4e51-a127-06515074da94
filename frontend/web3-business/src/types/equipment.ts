import {
  EquipmentType,
  PetRarity,
  EnchantmentType
} from './typesWithoutCircular'

import type {
  Equipment,
  EquipmentRequirement,
  EquipmentEffect,
  SetBonus,
  Pet,
  PetStats,
  Enchantment
} from './typesWithoutCircular'

// 装备验证相关类型
export interface EquipmentValidationResult {
  canEquip: boolean
  reasons: string[]
  warnings: string[]
}

export interface EquipmentSlot {
  type: EquipmentType
  equipped: Equipment | null
  locked: boolean
  unlockLevel: number
}

// 装备管理器
export class EquipmentManager {
  private pet: Pet
  private equipmentSlots: Map<EquipmentType, EquipmentSlot>

  constructor(pet: Pet) {
    this.pet = pet
    this.equipmentSlots = this.initializeSlots()
  }

  // 初始化装备槽位
  private initializeSlots(): Map<EquipmentType, EquipmentSlot> {
    const slots = new Map<EquipmentType, EquipmentSlot>()

    // 基础装备槽位
    const basicSlots = [
      { type: EquipmentType.WEAPON, unlockLevel: 1 },
      { type: EquipmentType.ARMOR, unlockLevel: 1 },
      { type: EquipmentType.HELMET, unlockLevel: 5 },
      { type: EquipmentType.BOOTS, unlockLevel: 8 },
      { type: EquipmentType.GLOVES, unlockLevel: 10 },
      { type: EquipmentType.ACCESSORY, unlockLevel: 15 },
      { type: EquipmentType.RING, unlockLevel: 20 },
      { type: EquipmentType.NECKLACE, unlockLevel: 25 }
    ]

    basicSlots.forEach(({ type, unlockLevel }) => {
      const currentEquipment = this.pet.equipment.find(eq => eq.type === type)
      slots.set(type, {
        type,
        equipped: currentEquipment || null,
        locked: this.pet.level < unlockLevel,
        unlockLevel
      })
    })

    return slots
  }

  // 验证装备是否可以装备
  validateEquipment(equipment: Equipment): EquipmentValidationResult {
    const reasons: string[] = []
    const warnings: string[] = []

    // 检查等级要求
    const levelReq = equipment.requirements.find(req => req.type === 'level')
    if (levelReq && this.pet.level < (levelReq.value as number)) {
      reasons.push(`需要等级 ${levelReq.value}，当前等级 ${this.pet.level}`)
    }

    // 检查属性要求
    equipment.requirements.forEach(req => {
      if (req.type === 'stat') {
        const statValue = this.pet.stats[req.target as keyof PetStats]
        if (statValue < (req.value as number)) {
          reasons.push(`需要 ${req.target} ${req.value}，当前 ${statValue}`)
        }
      }
    })

    // 检查稀有度要求
    const rarityReq = equipment.requirements.find(req => req.type === 'rarity')
    if (rarityReq) {
      const rarityOrder = {
        [PetRarity.COMMON]: 1,
        [PetRarity.UNCOMMON]: 2,
        [PetRarity.RARE]: 3,
        [PetRarity.EPIC]: 4,
        [PetRarity.LEGENDARY]: 5,
        [PetRarity.MYTHICAL]: 6
      }

      const requiredRarity = rarityOrder[rarityReq.value as PetRarity]
      const petRarity = rarityOrder[this.pet.rarity]

      if (petRarity < requiredRarity) {
        reasons.push(`需要稀有度 ${rarityReq.value}，当前 ${this.pet.rarity}`)
      }
    }

    // 检查种族要求
    const speciesReq = equipment.requirements.find(req => req.type === 'species')
    if (speciesReq && this.pet.type !== speciesReq.value) {
      reasons.push(`仅限 ${speciesReq.value} 种族使用`)
    }

    // 检查装备槽位是否解锁
    const slot = this.equipmentSlots.get(equipment.type)
    if (slot?.locked) {
      reasons.push(`装备槽位未解锁，需要等级 ${slot.unlockLevel}`)
    }

    // 检查耐久度
    if (equipment.durability <= 0) {
      reasons.push('装备已损坏，需要修理')
    } else if (equipment.durability < equipment.maxDurability * 0.2) {
      warnings.push('装备耐久度较低，建议修理')
    }

    // 检查是否为灵魂绑定装备
    if (equipment.soulbound && equipment.metadata.createdBy !== this.pet.id) {
      reasons.push('该装备已绑定其他萌宠')
    }

    return {
      canEquip: reasons.length === 0,
      reasons,
      warnings
    }
  }

  // 装备道具
  equipItem(equipment: Equipment): boolean {
    const validation = this.validateEquipment(equipment)
    if (!validation.canEquip) {
      throw new Error(`无法装备: ${validation.reasons.join(', ')}`)
    }

    const slot = this.equipmentSlots.get(equipment.type)
    if (!slot) {
      throw new Error(`未找到对应的装备槽位: ${equipment.type}`)
    }

    // 卸下当前装备
    if (slot.equipped) {
      this.unequipItem(equipment.type)
    }

    // 装备新道具
    slot.equipped = equipment
    this.updatePetEquipment()

    return true
  }

  // 卸下装备
  unequipItem(equipmentType: EquipmentType): Equipment | null {
    const slot = this.equipmentSlots.get(equipmentType)
    if (!slot || !slot.equipped) {
      return null
    }

    const unequippedItem = slot.equipped
    slot.equipped = null
    this.updatePetEquipment()

    return unequippedItem
  }

  // 更新萌宠装备列表
  private updatePetEquipment(): void {
    this.pet.equipment = Array.from(this.equipmentSlots.values())
      .filter(slot => slot.equipped !== null)
      .map(slot => slot.equipped!)
  }

  // 获取装备加成
  getEquipmentBonuses(): Partial<PetStats> {
    const bonuses: Partial<PetStats> = {}

    this.pet.equipment.forEach(equipment => {
      if (equipment.stats) {
        Object.entries(equipment.stats).forEach(([stat, value]) => {
          if (value) {
            bonuses[stat as keyof PetStats] = (bonuses[stat as keyof PetStats] || 0) + value
          }
        })
      }
    })

    return bonuses
  }

  // 计算套装加成
  calculateSetBonuses(): SetBonus[] {
    const setBonuses: SetBonus[] = []
    const setItems = new Map<string, Equipment[]>()

    // 按套装分组
    this.pet.equipment.forEach(equipment => {
      if (equipment.setBonus) {
        const setName = equipment.setBonus.setName
        if (!setItems.has(setName)) {
          setItems.set(setName, [])
        }
        setItems.get(setName)!.push(equipment)
      }
    })

    // 计算套装加成
    setItems.forEach((items, setName) => {
      const setBonus = items[0].setBonus!
      const currentPieces = items.length

      if (currentPieces >= setBonus.requiredPieces) {
        setBonuses.push({
          ...setBonus,
          currentPieces
        })
      }
    })

    return setBonuses
  }

  // 获取所有可用槽位
  getAvailableSlots(): EquipmentSlot[] {
    return Array.from(this.equipmentSlots.values()).filter(slot => !slot.locked)
  }

  // 获取已锁定槽位
  getLockedSlots(): EquipmentSlot[] {
    return Array.from(this.equipmentSlots.values()).filter(slot => slot.locked)
  }

  // 修理装备
  repairEquipment(equipmentId: string, repairAmount: number = 100): boolean {
    const equipment = this.pet.equipment.find(eq => eq.id === equipmentId)
    if (!equipment) {
      return false
    }

    equipment.durability = Math.min(
      equipment.durability + repairAmount,
      equipment.maxDurability
    )

    return true
  }

  // 强化装备
  enhanceEquipment(equipmentId: string, enhanceLevel: number = 1): boolean {
    const equipment = this.pet.equipment.find(eq => eq.id === equipmentId)
    if (!equipment) {
      return false
    }

    // 提升装备等级和属性
    equipment.level += enhanceLevel

    // 按比例提升属性
    const enhanceMultiplier = 1 + (enhanceLevel * 0.1)
    if (equipment.stats) {
      Object.keys(equipment.stats).forEach(stat => {
        const currentValue = equipment.stats[stat as keyof PetStats] || 0
        equipment.stats[stat as keyof PetStats] = Math.floor(currentValue * enhanceMultiplier)
      })
    }

    return true
  }
}

// 装备工厂
export class EquipmentFactory {
  // 生成随机装备
  static generateRandomEquipment(
    type: EquipmentType,
    rarity: PetRarity,
    level: number = 1
  ): Equipment {
    const id = this.generateId()
    const name = this.generateEquipmentName(type, rarity)
    const stats = this.generateEquipmentStats(type, rarity, level)
    const requirements = this.generateRequirements(type, rarity, level)
    const effects = this.generateEffects(type, rarity)

    return {
      id,
      name,
      type,
      rarity,
      level,
      stats,
      description: this.generateDescription(type, rarity),
      requirements,
      effects,
      durability: 100,
      maxDurability: 100,
      enchantments: [],
      tradeable: true,
      soulbound: false,
      metadata: {
        version: '1.0.0',
        createdAt: Date.now(),
        createdBy: 'EquipmentFactory',
        lastModified: Date.now(),
        tags: [type, rarity],
        rarity
      }
    }
  }

  // 生成装备名称
  private static generateEquipmentName(type: EquipmentType, rarity: PetRarity): string {
    const rarityPrefixes = {
      [PetRarity.COMMON]: ['普通的', '基础的'],
      [PetRarity.UNCOMMON]: ['优质的', '精良的'],
      [PetRarity.RARE]: ['稀有的', '珍贵的'],
      [PetRarity.EPIC]: ['史诗的', '传奇的'],
      [PetRarity.LEGENDARY]: ['传说的', '神话的'],
      [PetRarity.MYTHICAL]: ['神器级', '至尊的']
    }

    const typeNames = {
      [EquipmentType.WEAPON]: ['利爪', '牙齿', '魔法杖'],
      [EquipmentType.ARMOR]: ['护甲', '鳞片', '毛皮'],
      [EquipmentType.HELMET]: ['头盔', '王冠', '头饰'],
      [EquipmentType.BOOTS]: ['靴子', '爪套', '蹄铁'],
      [EquipmentType.GLOVES]: ['手套', '护爪', '臂甲'],
      [EquipmentType.ACCESSORY]: ['饰品', '徽章', '符文'],
      [EquipmentType.RING]: ['戒指', '指环', '魔戒'],
      [EquipmentType.NECKLACE]: ['项链', '护符', '吊坠'],
      [EquipmentType.TOY]: ['玩具', '球', '飞盘'],
      [EquipmentType.FOOD]: ['食物', '零食', '营养品'],
      [EquipmentType.POTION]: ['药水', '魔药', '灵药'],
      [EquipmentType.SCROLL]: ['卷轴', '法术书', '秘籍'],
      [EquipmentType.TOOL]: ['工具', '器具', '法器']
    }

    const prefix = rarityPrefixes[rarity][Math.floor(Math.random() * rarityPrefixes[rarity].length)]
    const typeName = typeNames[type][Math.floor(Math.random() * typeNames[type].length)]

    return `${prefix}${typeName}`
  }

  // 生成装备属性
  private static generateEquipmentStats(
    type: EquipmentType,
    rarity: PetRarity,
    level: number
  ): Partial<PetStats> {
    const baseValues = {
      [PetRarity.COMMON]: { min: 5, max: 15 },
      [PetRarity.UNCOMMON]: { min: 10, max: 25 },
      [PetRarity.RARE]: { min: 20, max: 40 },
      [PetRarity.EPIC]: { min: 35, max: 60 },
      [PetRarity.LEGENDARY]: { min: 55, max: 85 },
      [PetRarity.MYTHICAL]: { min: 80, max: 120 }
    }

    const range = baseValues[rarity]
    const levelMultiplier = 1 + (level - 1) * 0.1

    // 根据装备类型决定主要属性
    const primaryStats = this.getPrimaryStatsForType(type)
    const stats: Partial<PetStats> = {}

    primaryStats.forEach(stat => {
      const baseValue = Math.floor(Math.random() * (range.max - range.min + 1)) + range.min
      stats[stat] = Math.floor(baseValue * levelMultiplier)
    })

    return stats
  }

  // 获取装备类型的主要属性
  private static getPrimaryStatsForType(type: EquipmentType): (keyof PetStats)[] {
    const statMapping = {
      [EquipmentType.WEAPON]: ['strength', 'agility'],
      [EquipmentType.ARMOR]: ['vitality', 'strength'],
      [EquipmentType.HELMET]: ['intelligence', 'charm'],
      [EquipmentType.BOOTS]: ['agility', 'vitality'],
      [EquipmentType.GLOVES]: ['strength', 'agility'],
      [EquipmentType.ACCESSORY]: ['luck', 'charm'],
      [EquipmentType.RING]: ['intelligence', 'luck'],
      [EquipmentType.NECKLACE]: ['charm', 'intelligence'],
      [EquipmentType.TOY]: ['happiness'] as any,
      [EquipmentType.FOOD]: ['vitality'],
      [EquipmentType.POTION]: ['intelligence'],
      [EquipmentType.SCROLL]: ['intelligence'],
      [EquipmentType.TOOL]: ['strength']
    }

    return statMapping[type] || ['strength']
  }

  // 生成装备需求
  private static generateRequirements(
    type: EquipmentType,
    rarity: PetRarity,
    level: number
  ): EquipmentRequirement[] {
    const requirements: EquipmentRequirement[] = []

    // 等级需求
    const levelRequirement = Math.max(1, level * 2)
    requirements.push({
      type: 'level',
      target: 'pet',
      value: levelRequirement
    })

    // 高稀有度装备可能有额外需求
    if (rarity === PetRarity.EPIC || rarity === PetRarity.LEGENDARY || rarity === PetRarity.MYTHICAL) {
      requirements.push({
        type: 'rarity',
        target: 'pet',
        value: rarity
      })
    }

    return requirements
  }

  // 生成装备效果
  private static generateEffects(type: EquipmentType, rarity: PetRarity): EquipmentEffect[] {
    const effects: EquipmentEffect[] = []

    // 根据稀有度添加特殊效果
    if (rarity === PetRarity.RARE || rarity === PetRarity.EPIC) {
      effects.push({
        type: 'passive_effect',
        target: 'experience_gain',
        value: 10,
        condition: 'always'
      })
    }

    if (rarity === PetRarity.LEGENDARY || rarity === PetRarity.MYTHICAL) {
      effects.push({
        type: 'special_ability',
        target: 'token_bonus',
        value: 20,
        condition: 'on_exchange'
      })
    }

    return effects
  }

  // 生成装备描述
  private static generateDescription(type: EquipmentType, rarity: PetRarity): string {
    const descriptions = {
      [EquipmentType.WEAPON]: '提升萌宠的攻击能力',
      [EquipmentType.ARMOR]: '增强萌宠的防御力',
      [EquipmentType.HELMET]: '保护萌宠的头部',
      [EquipmentType.BOOTS]: '提升萌宠的移动速度',
      [EquipmentType.GLOVES]: '增强萌宠的操作能力',
      [EquipmentType.ACCESSORY]: '提供各种特殊效果',
      [EquipmentType.RING]: '蕴含神秘的魔法力量',
      [EquipmentType.NECKLACE]: '散发着迷人的光芒',
      [EquipmentType.TOY]: '让萌宠更加快乐',
      [EquipmentType.FOOD]: '提供营养和能量',
      [EquipmentType.POTION]: '具有神奇的效果',
      [EquipmentType.SCROLL]: '记录着古老的知识',
      [EquipmentType.TOOL]: '实用的工具装备'
    }

    return descriptions[type] || '神秘的装备'
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

// 附魔系统
export class EnchantmentSystem {
  // 为装备添加附魔
  static addEnchantment(equipment: Equipment, enchantmentType: EnchantmentType, level: number = 1): boolean {
    // 检查是否已有相同类型的附魔
    const existingEnchantment = equipment.enchantments.find(e => e.type === enchantmentType)
    if (existingEnchantment) {
      // 升级现有附魔
      existingEnchantment.level = Math.min(existingEnchantment.level + level, 10)
      this.updateEnchantmentEffect(existingEnchantment)
    } else {
      // 添加新附魔
      const newEnchantment = this.createEnchantment(enchantmentType, level)
      equipment.enchantments.push(newEnchantment)
    }

    return true
  }

  // 创建附魔
  private static createEnchantment(type: EnchantmentType, level: number): Enchantment {
    const enchantmentData = this.getEnchantmentData(type)

    return {
      id: this.generateId(),
      name: enchantmentData.name,
      level,
      type,
      effect: {
        type: enchantmentData.effectType,
        target: enchantmentData.target,
        value: enchantmentData.baseValue * level,
        condition: enchantmentData.condition
      },
      description: `${enchantmentData.description} (等级 ${level})`
    }
  }

  // 获取附魔数据
  private static getEnchantmentData(type: EnchantmentType) {
    const enchantmentDatabase = {
      [EnchantmentType.OFFENSIVE]: {
        name: '锋利',
        effectType: 'stat_bonus' as const,
        target: 'strength',
        baseValue: 5,
        condition: 'always',
        description: '增加攻击力'
      },
      [EnchantmentType.DEFENSIVE]: {
        name: '坚固',
        effectType: 'stat_bonus' as const,
        target: 'vitality',
        baseValue: 8,
        condition: 'always',
        description: '增加防御力'
      },
      [EnchantmentType.UTILITY]: {
        name: '敏捷',
        effectType: 'stat_bonus' as const,
        target: 'agility',
        baseValue: 6,
        condition: 'always',
        description: '增加敏捷性'
      },
      [EnchantmentType.SPECIAL]: {
        name: '幸运',
        effectType: 'stat_bonus' as const,
        target: 'luck',
        baseValue: 10,
        condition: 'always',
        description: '增加幸运值'
      }
    }

    return enchantmentDatabase[type]
  }

  // 更新附魔效果
  private static updateEnchantmentEffect(enchantment: Enchantment): void {
    const baseData = this.getEnchantmentData(enchantment.type)
    enchantment.effect.value = baseData.baseValue * enchantment.level
    enchantment.description = `${baseData.description} (等级 ${enchantment.level})`
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}
