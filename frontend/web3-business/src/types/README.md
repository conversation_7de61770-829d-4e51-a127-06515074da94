# 萌宠数据模型和类型定义

本文档描述了萌宠养成代币游戏中的核心数据模型、类型定义、验证函数、序列化机制和工厂模式实现。

## 核心数据类型

### Pet (萌宠)

萌宠是游戏的核心实体，包含以下主要属性：

```typescript
interface Pet {
  // 基础信息
  id: string                    // 唯一标识符
  name: string                  // 萌宠名称
  type: PetType                 // 萌宠类型
  rarity: PetRarity            // 稀有度
  
  // 等级和经验
  level: number                 // 当前等级
  experience: number            // 当前经验值
  maxExperience: number         // 升级所需经验
  
  // 生命值和状态
  health: number                // 当前健康度
  maxHealth: number             // 最大健康度
  happiness: number             // 当前快乐度
  maxHappiness: number          // 最大快乐度
  energy: number                // 当前能量值
  maxEnergy: number             // 最大能量值
  
  // 属性和外观
  stats: PetStats              // 萌宠属性
  baseStats: PetStats          // 基础属性
  appearance: PetAppearance    // 外观设定
  
  // 游戏状态
  status: PetStatus            // 当前状态
  growthStage: GrowthStage     // 成长阶段
  mood: PetMood                // 心情状态
  
  // 装备和技能
  equipment: Equipment[]        // 装备列表
  skills: PetSkill[]           // 技能列表
  traits: PetTrait[]           // 特质列表
  
  // 时间记录
  lastFeedTime: number         // 最后喂食时间
  lastPlayTime: number         // 最后游戏时间
  lastTrainTime: number        // 最后训练时间
  lastRestTime: number         // 最后休息时间
  birthTime: number            // 出生时间
  
  // 游戏数据
  achievements: string[]        // 成就列表
  totalTokensEarned: string    // 总代币收益
  evolutionPoints: number      // 进化点数
  breedCount: number           // 繁殖次数
  generation: number           // 世代数
  
  // 家族信息
  parents?: {
    mother?: string
    father?: string
  }
  
  // 元数据
  metadata: PetMetadata        // 元数据信息
}
```

### PetStats (萌宠属性)

萌宠的六维属性系统：

```typescript
interface PetStats {
  strength: number      // 力量 - 影响攻击力和负重
  intelligence: number  // 智力 - 影响魔法和学习能力
  agility: number      // 敏捷 - 影响速度和闪避
  charm: number        // 魅力 - 影响社交和快乐度上限
  vitality: number     // 体力 - 影响生命值和能量上限
  luck: number         // 幸运 - 影响暴击和稀有物品获取
}
```

### Equipment (装备)

萌宠可装备的物品：

```typescript
interface Equipment {
  id: string                      // 装备ID
  name: string                    // 装备名称
  type: EquipmentType            // 装备类型
  rarity: PetRarity              // 稀有度
  level: number                  // 装备等级
  stats: Partial<PetStats>       // 属性加成
  description: string            // 描述
  requirements: EquipmentRequirement[]  // 装备需求
  effects: EquipmentEffect[]     // 特殊效果
  durability: number             // 当前耐久度
  maxDurability: number          // 最大耐久度
  enchantments: Enchantment[]    // 附魔效果
  setBonus?: SetBonus           // 套装加成
  tradeable: boolean            // 是否可交易
  soulbound: boolean            // 是否绑定
  metadata: ItemMetadata        // 物品元数据
}
```

## 枚举类型

### PetType (萌宠类型)

```typescript
enum PetType {
  CAT = 'cat',           // 猫咪
  DOG = 'dog',           // 小狗
  RABBIT = 'rabbit',     // 兔子
  BIRD = 'bird',         // 小鸟
  DRAGON = 'dragon',     // 龙
  UNICORN = 'unicorn',   // 独角兽
  PHOENIX = 'phoenix',   // 凤凰
  GRIFFIN = 'griffin',   // 狮鹫
  PEGASUS = 'pegasus',   // 飞马
  KITSUNE = 'kitsune'    // 九尾狐
}
```

### PetRarity (稀有度)

```typescript
enum PetRarity {
  COMMON = 'common',         // 普通 (50%)
  UNCOMMON = 'uncommon',     // 不常见 (25%)
  RARE = 'rare',             // 稀有 (15%)
  EPIC = 'epic',             // 史诗 (7%)
  LEGENDARY = 'legendary',   // 传说 (2.5%)
  MYTHICAL = 'mythical'      // 神话 (0.5%)
}
```

## 核心功能类

### PetValidator (萌宠验证器)

负责验证萌宠数据的完整性和有效性：

```typescript
class PetValidator {
  validatePet(pet: Pet): PetValidationResult
  validateStats(stats: PetStats): boolean
  validateName(name: string): boolean
}
```

**验证规则：**
- 名称长度：1-20个字符
- 等级范围：1-100
- 属性值范围：1-999
- 健康度、快乐度、能量值：0-最大值
- 时间戳：不能晚于当前时间

### PetStatsCalculator (属性计算器)

计算萌宠的各种数值：

```typescript
class PetStatsCalculator {
  calculateTotalStats(context: StatCalculationContext): PetStats
  calculateLevelBonus(level: number, rarity: PetRarity): Partial<PetStats>
  calculateMaxExperience(level: number): number
  calculateMaxHealth(stats: PetStats, level: number): number
  calculateMaxHappiness(stats: PetStats): number
  calculateMaxEnergy(stats: PetStats): number
  calculateTokenValue(pet: Pet): string
}
```

**计算公式：**
- 等级加成：`(level - 1) * 2 * rarityMultiplier`
- 最大经验：`100 * (1.5 ^ (level - 1))`
- 最大生命：`100 + (vitality * 2) + (level * 5)`
- 最大快乐：`100 + (charm * 0.5)`
- 最大能量：`100 + (vitality * 1.5)`

### PetSerializer (序列化器)

处理萌宠数据的序列化和反序列化：

```typescript
class PetSerializer {
  static serialize(pet: Pet): SerializedPet
  static deserialize(serialized: SerializedPet): Pet
  static exportPet(pet: Pet, exportedBy: string): PetExportData
  static importPet(exportData: PetExportData): Pet
}
```

**特性：**
- Base64编码支持Unicode字符
- 数据完整性校验
- 版本兼容性检查
- 数据迁移支持

### PetFactory (萌宠工厂)

生成和创建萌宠的工厂类：

```typescript
class PetFactory {
  generateRandomPet(options?: PetGenerationOptions): Pet
  createPet(options: PetGenerationOptions): Pet
  breedPets(parent1: Pet, parent2: Pet, options?: Partial<PetGenerationOptions>): Pet
  evolvePet(pet: Pet, evolutionPath: EvolutionPath): Pet
}
```

**功能：**
- 随机生成萌宠
- 自定义创建萌宠
- 萌宠繁殖系统
- 萌宠进化系统

## 数据模板

### 萌宠模板 (PET_TEMPLATES)

每种萌宠类型的基础模板，包含：
- 基础属性值
- 外观设定
- 可用特质列表
- 可学习技能
- 成长修正值
- 特殊能力

### 特质数据库 (TRAIT_DATABASE)

预定义的特质效果：
- `agile`: 增加敏捷属性
- `strong`: 增加力量属性
- `intelligent`: 增加智力属性
- `lucky`: 增加幸运属性
- `magical`: 魔法天赋，增加智力和魔法技能

### 技能数据库 (SKILL_DATABASE)

可学习的技能：
- `fire_breath`: 火焰吐息攻击技能
- `healing`: 治愈术恢复技能
- `stealth`: 潜行隐身技能

## 使用示例

### 创建萌宠

```typescript
import { petFactory } from '../utils/petFactory'
import { PetType, PetRarity } from '../types/index'

// 生成随机萌宠
const randomPet = petFactory.generateRandomPet()

// 创建指定类型萌宠
const dragonPet = petFactory.generateRandomPet({
  type: PetType.DRAGON,
  rarity: PetRarity.LEGENDARY,
  level: 10,
  name: '炎龙'
})

// 自定义属性萌宠
const customPet = petFactory.createPet({
  type: PetType.CAT,
  rarity: PetRarity.RARE,
  name: '小花',
  customStats: {
    strength: 30,
    intelligence: 35,
    agility: 40,
    charm: 25,
    vitality: 28,
    luck: 20
  }
})
```

### 萌宠验证

```typescript
import { PetValidator } from '../types/pet'

const validator = new PetValidator()
const result = validator.validatePet(pet)

if (!result.isValid) {
  console.error('萌宠数据验证失败:', result.errors)
} else {
  console.log('萌宠数据验证通过')
}
```

### 属性计算

```typescript
import { PetStatsCalculator } from '../types/pet'

const calculator = new PetStatsCalculator()

// 计算总属性
const totalStats = calculator.calculateTotalStats({
  pet,
  equipment: pet.equipment,
  buffs: [],
  environment: []
})

// 计算代币价值
const tokenValue = calculator.calculateTokenValue(pet)
```

### 数据序列化

```typescript
import { PetSerializer } from '../types/pet'

// 序列化萌宠
const serialized = PetSerializer.serialize(pet)

// 反序列化萌宠
const deserializedPet = PetSerializer.deserialize(serialized)

// 导出萌宠
const exportData = PetSerializer.exportPet(pet, 'user123')

// 导入萌宠
const importedPet = PetSerializer.importPet(exportData)
```

### 萌宠繁殖

```typescript
// 繁殖萌宠（需要父母都达到10级）
const parent1 = petFactory.createPet({
  type: PetType.CAT,
  rarity: PetRarity.RARE,
  level: 15
})

const parent2 = petFactory.createPet({
  type: PetType.CAT,
  rarity: PetRarity.EPIC,
  level: 12
})

const offspring = petFactory.breedPets(parent1, parent2)
console.log(`新生萌宠世代: ${offspring.generation}`)
```

### 萌宠进化

```typescript
// 萌宠进化
const evolutionPath = {
  from: PetType.CAT,
  to: PetType.KITSUNE,
  requirements: {
    level: 20,
    experience: pet.maxExperience
  },
  rarity: PetRarity.LEGENDARY,
  probability: 1.0
}

const evolvedPet = petFactory.evolvePet(pet, evolutionPath)
```

## 测试覆盖

项目包含完整的单元测试：

- `src/types/__tests__/pet.test.ts` - 验证器、计算器、序列化器测试
- `src/utils/__tests__/petFactory.test.ts` - 工厂类和模板数据测试

运行测试：
```bash
npm test -- --run src/types/__tests__/pet.test.ts
npm test -- --run src/utils/__tests__/petFactory.test.ts
```

## 性能考虑

1. **属性计算缓存**: 使用计算属性缓存复杂的属性计算结果
2. **序列化优化**: 使用高效的Base64编码处理Unicode字符
3. **验证规则**: 提供可配置的验证规则以适应不同场景
4. **内存管理**: 避免循环引用和内存泄漏

## 扩展性

1. **新萌宠类型**: 在`PetType`枚举和`PET_TEMPLATES`中添加新类型
2. **新特质效果**: 在`TRAIT_DATABASE`中定义新特质
3. **新技能系统**: 在`SKILL_DATABASE`中添加新技能
4. **自定义验证**: 继承`PetValidator`类实现自定义验证逻辑
5. **数据迁移**: `PetSerializer`支持版本升级和数据迁移

## 装备系统

### EquipmentManager (装备管理器)

管理萌宠的装备槽位和装备验证：

```typescript
class EquipmentManager {
  validateEquipment(equipment: Equipment): EquipmentValidationResult
  equipItem(equipment: Equipment): boolean
  unequipItem(equipmentType: EquipmentType): Equipment | null
  getEquipmentBonuses(): Partial<PetStats>
  calculateSetBonuses(): SetBonus[]
  repairEquipment(equipmentId: string, repairAmount?: number): boolean
  enhanceEquipment(equipmentId: string, enhanceLevel?: number): boolean
}
```

### EquipmentFactory (装备工厂)

生成随机装备：

```typescript
class EquipmentFactory {
  static generateRandomEquipment(
    type: EquipmentType,
    rarity: PetRarity,
    level?: number
  ): Equipment
}
```

### EnchantmentSystem (附魔系统)

为装备添加魔法增强：

```typescript
class EnchantmentSystem {
  static addEnchantment(
    equipment: Equipment,
    enchantmentType: EnchantmentType,
    level?: number
  ): boolean
}
```

## 道具系统

### ItemManager (道具管理器)

处理道具使用和背包管理：

```typescript
class ItemManager {
  useItem(itemId: string, quantity?: number): ItemUseResult
  addItem(item: Item): boolean
  removeItem(itemId: string, quantity?: number): boolean
  getInventory(): Item[]
  getItemsByType(type: ItemType): Item[]
  searchItems(query: string): Item[]
  getUsableItems(): Item[]
  getCooldownRemaining(itemId: string): number
}
```

### ItemFactory (道具工厂)

生成随机道具：

```typescript
class ItemFactory {
  static generateRandomItem(type: ItemType, rarity: PetRarity): Item
}
```

### ItemEffectCalculator (道具效果计算器)

计算道具影响和推荐：

```typescript
class ItemEffectCalculator {
  static calculateItemImpact(item: Item, pet: Pet): ItemImpact
  static recommendItems(pet: Pet, availableItems: Item[]): Item[]
}
```

## 新增功能示例

### 装备管理

```typescript
import { EquipmentManager, EquipmentFactory } from '../types/equipment'

const manager = new EquipmentManager(pet)

// 生成装备
const sword = EquipmentFactory.generateRandomEquipment(
  EquipmentType.WEAPON,
  PetRarity.EPIC,
  10
)

// 验证并装备
const validation = manager.validateEquipment(sword)
if (validation.canEquip) {
  manager.equipItem(sword)
  console.log('装备加成:', manager.getEquipmentBonuses())
} else {
  console.log('无法装备:', validation.reasons)
}

// 修理装备
manager.repairEquipment(sword.id, 50)

// 强化装备
manager.enhanceEquipment(sword.id, 2)
```

### 道具使用

```typescript
import { ItemManager, ItemFactory } from '../types/item'

const manager = new ItemManager(pet, inventory)

// 生成治疗药水
const potion = ItemFactory.generateRandomItem(
  ItemType.MEDICINE,
  PetRarity.RARE
)

// 添加到背包
manager.addItem(potion)

// 使用道具
const result = manager.useItem(potion.id, 1)
if (result.success) {
  console.log('使用成功:', result.effects)
} else {
  console.log('使用失败:', result.message)
}

// 获取推荐道具
const recommendations = ItemEffectCalculator.recommendItems(
  pet,
  manager.getInventory()
)
```

### 附魔系统

```typescript
import { EnchantmentSystem } from '../types/equipment'

// 为武器添加锋利附魔
EnchantmentSystem.addEnchantment(
  sword,
  EnchantmentType.OFFENSIVE,
  3
)

// 为护甲添加坚固附魔
EnchantmentSystem.addEnchantment(
  armor,
  EnchantmentType.DEFENSIVE,
  2
)
```

## 数据过滤和比较

### PetComparator (萌宠比较器)

提供多种排序方式：

```typescript
import { PetComparator } from '../types/pet'

// 按等级排序
pets.sort(PetComparator.compareByLevel)

// 按稀有度排序
pets.sort(PetComparator.compareByRarity)

// 按代币价值排序
pets.sort(PetComparator.compareByTokenValue)

// 按总属性排序
pets.sort(PetComparator.compareByTotalStats)
```

### PetFilter (萌宠过滤器)

提供多种过滤方式：

```typescript
import { PetFilter } from '../types/pet'

// 按类型过滤
const catPets = PetFilter.filterByType(pets, PetType.CAT)

// 按稀有度过滤
const rarePets = PetFilter.filterByRarity(pets, PetRarity.RARE)

// 按等级范围过滤
const midLevelPets = PetFilter.filterByLevelRange(pets, 10, 30)

// 按特质过滤
const agilePets = PetFilter.filterByTrait(pets, '敏捷')

// 按名称搜索
const searchResults = PetFilter.searchByName(pets, '小花')

// 按最小代币价值过滤
const valuablePets = PetFilter.filterByMinTokenValue(pets, '1000000000000000000')
```

## 测试覆盖

项目包含完整的单元测试：

- `src/types/__tests__/pet.test.ts` - 验证器、计算器、序列化器测试
- `src/types/__tests__/equipment.test.ts` - 装备管理、工厂、附魔系统测试
- `src/types/__tests__/item.test.ts` - 道具管理、工厂、效果计算测试
- `src/utils/__tests__/petFactory.test.ts` - 工厂类和模板数据测试

运行所有测试：
```bash
npm test -- --run src/types/__tests__/ src/utils/__tests__/petFactory.test.ts
```

这套完整的数据模型为萌宠养成游戏提供了全面、可扩展、高性能的基础架构，涵盖了萌宠管理、装备系统、道具系统、数据验证、序列化、过滤排序等核心功能。