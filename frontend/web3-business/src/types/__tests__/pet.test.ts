import { describe, it, expect, beforeEach } from 'vitest'
import {
  PetValidator,
  PetStatsCalculator,
  PetSerializer,
  DEFAULT_PET_VALIDATION_RULES
} from '../pet'
import { Pet, PetRarity, PetType, PetStatus, GrowthStage, PetMood, PetSize } from '../index'

// 创建测试用的萌宠数据
const createTestPet = (): Pet => ({
  id: 'test-pet-1',
  name: '测试萌宠',
  type: PetType.CAT,
  level: 5,
  experience: 100,
  maxExperience: 500,
  health: 80,
  maxHealth: 100,
  happiness: 70,
  maxHappiness: 100,
  energy: 60,
  maxEnergy: 100,
  rarity: PetRarity.COMMON,
  equipment: [],
  lastFeedTime: Date.now() - 60000,
  lastPlayTime: Date.now() - 120000,
  lastTrainTime: Date.now() - 180000,
  lastRestTime: Date.now() - 240000,
  birthTime: Date.now() - 86400000,
  avatar: '/images/pets/cat/orange_striped.png',
  stats: {
    strength: 15,
    intelligence: 20,
    agility: 25,
    charm: 18,
    vitality: 16,
    luck: 12
  },
  baseStats: {
    strength: 10,
    intelligence: 15,
    agility: 20,
    charm: 13,
    vitality: 11,
    luck: 7
  },
  appearance: {
    species: 'cat',
    color: 'orange',
    pattern: 'striped',
    size: PetSize.SMALL,
    accessories: [],
    specialEffects: [],
    animations: ['idle', 'walk', 'play', 'sleep']
  },
  status: PetStatus.HEALTHY,
  growthStage: GrowthStage.CHILD,
  mood: PetMood.CONTENT,
  skills: [],
  achievements: [],
  totalTokensEarned: '0',
  evolutionPoints: 0,
  breedCount: 0,
  generation: 1,
  traits: [],
  metadata: {
    version: '1.0.0',
    createdBy: 'test',
    lastModified: Date.now(),
    checksum: 'test-checksum',
    tags: ['cat', 'common'],
    description: '测试用萌宠'
  }
})

describe('PetValidator', () => {
  let validator: PetValidator
  let testPet: Pet

  beforeEach(() => {
    validator = new PetValidator()
    testPet = createTestPet()
  })

  describe('validatePet', () => {
    it('should validate a valid pet', () => {
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject pet with invalid name', () => {
      testPet.name = ''
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('萌宠名称长度不能少于1个字符')
    })

    it('should reject pet with name too long', () => {
      testPet.name = 'a'.repeat(25)
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('萌宠名称长度不能超过20个字符')
    })

    it('should reject pet with invalid level', () => {
      testPet.level = 0
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('萌宠等级必须在1-100之间')
    })

    it('should reject pet with invalid stats', () => {
      testPet.stats.strength = 1000
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('strength属性值必须在1-999之间')
    })

    it('should reject pet with invalid health', () => {
      testPet.health = -10
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('健康度必须在0-100之间')
    })

    it('should reject pet with future birth time', () => {
      testPet.birthTime = Date.now() + 86400000
      const result = validator.validatePet(testPet)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('出生时间不能晚于当前时间')
    })
  })

  describe('validateStats', () => {
    it('should validate valid stats', () => {
      const stats = {
        strength: 50,
        intelligence: 60,
        agility: 70,
        charm: 40,
        vitality: 55,
        luck: 30
      }
      expect(validator.validateStats(stats)).toBe(true)
    })

    it('should reject stats with values too high', () => {
      const stats = {
        strength: 1000,
        intelligence: 60,
        agility: 70,
        charm: 40,
        vitality: 55,
        luck: 30
      }
      expect(validator.validateStats(stats)).toBe(false)
    })
  })

  describe('validateName', () => {
    it('should validate valid names', () => {
      expect(validator.validateName('小猫')).toBe(true)
      expect(validator.validateName('Cat123')).toBe(true)
      expect(validator.validateName('My Pet')).toBe(true)
    })

    it('should reject invalid names', () => {
      expect(validator.validateName('')).toBe(false)
      expect(validator.validateName('a'.repeat(25))).toBe(false)
      expect(validator.validateName('Pet<script>')).toBe(false)
    })
  })
})

describe('PetStatsCalculator', () => {
  let calculator: PetStatsCalculator
  let testPet: Pet

  beforeEach(() => {
    calculator = new PetStatsCalculator()
    testPet = createTestPet()
  })

  describe('calculateTotalStats', () => {
    it('should calculate base stats correctly', () => {
      const context = {
        pet: testPet,
        equipment: [],
        buffs: [],
        environment: []
      }

      const totalStats = calculator.calculateTotalStats(context)

      // 应该包含基础属性 + 等级加成
      expect(totalStats.strength).toBeGreaterThan(testPet.baseStats.strength)
      expect(totalStats.intelligence).toBeGreaterThan(testPet.baseStats.intelligence)
    })

    it('should apply equipment bonuses', () => {
      const equipment = [{
        id: 'test-equipment',
        name: '力量护符',
        type: 'accessory' as any,
        rarity: PetRarity.COMMON,
        level: 1,
        stats: { strength: 10 },
        description: '增加力量',
        requirements: [],
        effects: [],
        durability: 100,
        maxDurability: 100,
        enchantments: [],
        tradeable: true,
        soulbound: false,
        metadata: {
          version: '1.0.0',
          createdAt: Date.now(),
          createdBy: 'test',
          lastModified: Date.now(),
          tags: [],
          rarity: PetRarity.COMMON
        }
      }]

      const context = {
        pet: testPet,
        equipment,
        buffs: [],
        environment: []
      }

      const totalStats = calculator.calculateTotalStats(context)
      const baseContext = {
        pet: testPet,
        equipment: [],
        buffs: [],
        environment: []
      }
      const baseStats = calculator.calculateTotalStats(baseContext)

      expect(totalStats.strength).toBe(baseStats.strength + 10)
    })
  })

  describe('calculateLevelBonus', () => {
    it('should calculate level bonus correctly', () => {
      const bonus = calculator.calculateLevelBonus(5, PetRarity.COMMON)
      expect(bonus.strength).toBe(8) // (5-1) * 2 * 1.0
      expect(bonus.luck).toBe(4) // (5-1) * 2 * 1.0 * 0.5
    })

    it('should apply rarity multiplier', () => {
      const commonBonus = calculator.calculateLevelBonus(5, PetRarity.COMMON)
      const rareBonus = calculator.calculateLevelBonus(5, PetRarity.RARE)

      expect(rareBonus.strength).toBe(commonBonus.strength! * 1.5)
    })
  })

  describe('calculateMaxExperience', () => {
    it('should calculate max experience correctly', () => {
      expect(calculator.calculateMaxExperience(1)).toBe(100)
      expect(calculator.calculateMaxExperience(2)).toBe(150)
      expect(calculator.calculateMaxExperience(3)).toBe(225)
    })
  })

  describe('calculateMaxHealth', () => {
    it('should calculate max health based on vitality and level', () => {
      const maxHealth = calculator.calculateMaxHealth(testPet.stats, testPet.level)
      const expected = 100 + (testPet.stats.vitality * 2) + (testPet.level * 5)
      expect(maxHealth).toBe(expected)
    })
  })

  describe('calculateTokenValue', () => {
    it('should calculate token value correctly', () => {
      const tokenValue = calculator.calculateTokenValue(testPet)
      expect(tokenValue).toBeDefined()
      expect(typeof tokenValue).toBe('string')
      expect(BigInt(tokenValue)).toBeGreaterThan(0n)
    })

    it('should give higher value for higher rarity pets', () => {
      const commonPet = { ...testPet, rarity: PetRarity.COMMON }
      const rarePet = { ...testPet, rarity: PetRarity.RARE }

      const commonValue = BigInt(calculator.calculateTokenValue(commonPet))
      const rareValue = BigInt(calculator.calculateTokenValue(rarePet))

      expect(rareValue).toBeGreaterThan(commonValue)
    })
  })
})

describe('PetSerializer', () => {
  let testPet: Pet

  beforeEach(() => {
    testPet = createTestPet()
  })

  describe('serialize and deserialize', () => {
    it('should serialize and deserialize pet correctly', () => {
      const serialized = PetSerializer.serialize(testPet)

      expect(serialized.version).toBe('1.0.0')
      expect(serialized.data).toBeDefined()
      expect(serialized.checksum).toBeDefined()
      expect(serialized.timestamp).toBeDefined()

      const deserialized = PetSerializer.deserialize(serialized)

      expect(deserialized.id).toBe(testPet.id)
      expect(deserialized.name).toBe(testPet.name)
      expect(deserialized.type).toBe(testPet.type)
      expect(deserialized.level).toBe(testPet.level)
    })

    it('should throw error for invalid checksum', () => {
      const serialized = PetSerializer.serialize(testPet)
      serialized.checksum = 'invalid-checksum'

      expect(() => PetSerializer.deserialize(serialized)).toThrow('数据校验失败')
    })

    it('should throw error for unsupported version', () => {
      const serialized = PetSerializer.serialize(testPet)
      serialized.version = '2.0.0'

      expect(() => PetSerializer.deserialize(serialized)).toThrow('不支持的版本: 2.0.0')
    })
  })

  describe('exportPet and importPet', () => {
    it('should export and import pet correctly', () => {
      const exportData = PetSerializer.exportPet(testPet, 'test-user')

      expect(exportData.pet).toEqual(testPet)
      expect(exportData.metadata.exportedBy).toBe('test-user')
      expect(exportData.metadata.version).toBe('1.0.0')

      const importedPet = PetSerializer.importPet(exportData)

      expect(importedPet.name).toBe(testPet.name)
      expect(importedPet.type).toBe(testPet.type)
      expect(importedPet.level).toBe(testPet.level)
      expect(importedPet.id).not.toBe(testPet.id) // 应该生成新的ID
    })

    it('should throw error for invalid export data', () => {
      const invalidData = { pet: null, metadata: null }

      expect(() => PetSerializer.importPet(invalidData as any)).toThrow('导入数据格式错误')
    })
  })
})