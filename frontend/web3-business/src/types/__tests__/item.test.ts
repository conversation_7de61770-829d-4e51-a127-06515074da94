import { describe, it, expect, beforeEach } from 'vitest'
import {
  ItemManager,
  ItemFactory,
  ItemEffectCalculator
} from '../item'
import {
  Pet,
  Item,
  ItemType,
  PetRarity,
  PetType,
  PetStatus,
  GrowthStage,
  PetMood,
  PetSize
} from '../index'

// 创建测试用萌宠
const createTestPet = (): Pet => ({
  id: 'test-pet-1',
  name: '测试萌宠',
  type: PetType.CAT,
  level: 10,
  experience: 200,
  maxExperience: 500,
  health: 50, // 设置较低的健康值用于测试
  maxHealth: 100,
  happiness: 40, // 设置较低的快乐度用于测试
  maxHappiness: 100,
  energy: 30, // 设置较低的能量值用于测试
  maxEnergy: 100,
  rarity: PetRarity.RARE,
  equipment: [],
  lastFeedTime: Date.now() - 60000,
  lastPlayTime: Date.now() - 120000,
  lastTrainTime: Date.now() - 180000,
  lastRestTime: Date.now() - 240000,
  birthTime: Date.now() - 86400000,
  avatar: '/images/pets/cat/orange_striped.png',
  stats: {
    strength: 25,
    intelligence: 30,
    agility: 35,
    charm: 28,
    vitality: 26,
    luck: 22
  },
  baseStats: {
    strength: 20,
    intelligence: 25,
    agility: 30,
    charm: 23,
    vitality: 21,
    luck: 17
  },
  appearance: {
    species: 'cat',
    color: 'orange',
    pattern: 'striped',
    size: PetSize.SMALL,
    accessories: [],
    specialEffects: [],
    animations: ['idle', 'walk', 'play', 'sleep']
  },
  status: PetStatus.HEALTHY,
  growthStage: GrowthStage.CHILD,
  mood: PetMood.NEUTRAL,
  skills: [],
  achievements: [],
  totalTokensEarned: '0',
  evolutionPoints: 0,
  breedCount: 0,
  generation: 1,
  traits: [],
  metadata: {
    version: '1.0.0',
    createdBy: 'test',
    lastModified: Date.now(),
    checksum: 'test-checksum',
    tags: ['cat', 'rare'],
    description: '测试用萌宠'
  }
})

// 创建测试道具
const createTestItem = (type: ItemType = ItemType.FOOD): Item => ({
  id: 'test-item-1',
  name: '测试食物',
  type,
  rarity: PetRarity.COMMON,
  quantity: 5,
  maxStack: 99,
  description: '测试用道具',
  effects: [
    { type: 'heal', target: 'health', value: 30 },
    { type: 'happiness', target: 'happiness', value: 20 }
  ],
  requirements: [],
  cooldown: 30,
  consumable: true,
  tradeable: true,
  value: 10,
  metadata: {
    version: '1.0.0',
    createdAt: Date.now(),
    createdBy: 'test',
    lastModified: Date.now(),
    tags: ['food', 'common'],
    rarity: PetRarity.COMMON
  }
})

describe('ItemManager', () => {
  let manager: ItemManager
  let testPet: Pet
  let testItem: Item

  beforeEach(() => {
    testPet = createTestPet()
    testItem = createTestItem()
    manager = new ItemManager(testPet, [testItem])
  })

  describe('useItem', () => {
    it('should use item successfully', () => {
      const result = manager.useItem(testItem.id, 1)

      expect(result.success).toBe(true)
      expect(result.message).toContain('成功使用')
      expect(result.effects).toHaveLength(2)
      expect(testItem.quantity).toBe(4) // 5 - 1
    })

    it('should apply healing effect', () => {
      const oldHealth = testPet.health
      manager.useItem(testItem.id, 1)

      expect(testPet.health).toBe(Math.min(oldHealth + 30, testPet.maxHealth))
    })

    it('should apply happiness effect', () => {
      const oldHappiness = testPet.happiness
      manager.useItem(testItem.id, 1)

      expect(testPet.happiness).toBe(Math.min(oldHappiness + 20, testPet.maxHappiness))
    })

    it('should update pet mood based on happiness', () => {
      // 使用多个道具提升快乐度
      manager.useItem(testItem.id, 3)

      // 快乐度应该提升，心情也应该改善
      expect(testPet.happiness).toBeGreaterThan(40)
      expect([PetMood.CONTENT, PetMood.HAPPY, PetMood.ECSTATIC]).toContain(testPet.mood)
    })

    it('should fail if item does not exist', () => {
      const result = manager.useItem('non-existent', 1)

      expect(result.success).toBe(false)
      expect(result.message).toBe('道具不存在')
    })

    it('should fail if insufficient quantity', () => {
      const result = manager.useItem(testItem.id, 10)

      expect(result.success).toBe(false)
      expect(result.message).toBe('道具数量不足')
    })

    it('should respect cooldown', () => {
      // 第一次使用
      const result1 = manager.useItem(testItem.id, 1)
      expect(result1.success).toBe(true)

      // 立即再次使用应该失败
      const result2 = manager.useItem(testItem.id, 1)
      expect(result2.success).toBe(false)
      expect(result2.message).toContain('道具冷却中')
    })

    it('should remove item when quantity reaches zero', () => {
      // 使用所有道具
      manager.useItem(testItem.id, 5)

      expect(manager.getInventory().find(item => item.id === testItem.id)).toBeUndefined()
    })

    it('should not consume non-consumable items', () => {
      testItem.consumable = false
      const originalQuantity = testItem.quantity

      manager.useItem(testItem.id, 1)

      expect(testItem.quantity).toBe(originalQuantity)
    })
  })

  describe('addItem', () => {
    it('should add new item to inventory', () => {
      const newItem = createTestItem(ItemType.MEDICINE)
      newItem.id = 'new-item'
      newItem.name = '新药品'

      const success = manager.addItem(newItem)

      expect(success).toBe(true)
      const inventory = manager.getInventory()
      const addedItem = inventory.find(item => item.id === 'new-item')
      expect(addedItem).toBeDefined()
      expect(addedItem?.name).toBe('新药品')
    })

    it('should stack identical items', () => {
      const identicalItem = createTestItem()
      identicalItem.id = 'identical-item'
      identicalItem.quantity = 3

      const originalQuantity = testItem.quantity
      const success = manager.addItem(identicalItem)

      expect(success).toBe(true)
      expect(testItem.quantity).toBe(originalQuantity + 3)
    })

    it('should fail if stacking exceeds max stack', () => {
      const largeStackItem = createTestItem()
      largeStackItem.id = 'large-stack'
      largeStackItem.quantity = 95 // 5 + 95 = 100 > 99 (maxStack)

      const success = manager.addItem(largeStackItem)

      expect(success).toBe(false)
    })
  })

  describe('removeItem', () => {
    it('should remove item successfully', () => {
      const success = manager.removeItem(testItem.id, 2)

      expect(success).toBe(true)
      expect(testItem.quantity).toBe(3) // 5 - 2
    })

    it('should remove item completely when quantity reaches zero', () => {
      const success = manager.removeItem(testItem.id, 5)

      expect(success).toBe(true)
      expect(manager.getInventory().find(item => item.id === testItem.id)).toBeUndefined()
    })

    it('should fail if insufficient quantity', () => {
      const success = manager.removeItem(testItem.id, 10)

      expect(success).toBe(false)
      expect(testItem.quantity).toBe(5) // 数量不变
    })

    it('should fail if item does not exist', () => {
      const success = manager.removeItem('non-existent', 1)

      expect(success).toBe(false)
    })
  })

  describe('getItemsByType', () => {
    it('should filter items by type', () => {
      const medicineItem = createTestItem(ItemType.MEDICINE)
      medicineItem.id = 'medicine-item'
      manager.addItem(medicineItem)

      const foodItems = manager.getItemsByType(ItemType.FOOD)
      const medicineItems = manager.getItemsByType(ItemType.MEDICINE)

      expect(foodItems).toHaveLength(1)
      expect(medicineItems).toHaveLength(1)
      expect(foodItems[0].type).toBe(ItemType.FOOD)
      expect(medicineItems[0].type).toBe(ItemType.MEDICINE)
    })
  })

  describe('searchItems', () => {
    it('should search items by name', () => {
      const results = manager.searchItems('测试')

      expect(results).toHaveLength(1)
      expect(results[0]).toBe(testItem)
    })

    it('should search items by description', () => {
      const results = manager.searchItems('道具')

      expect(results).toHaveLength(1)
      expect(results[0]).toBe(testItem)
    })

    it('should be case insensitive', () => {
      const results = manager.searchItems('测试')

      expect(results).toHaveLength(1)
    })
  })

  describe('getUsableItems', () => {
    it('should return items that can be used', () => {
      const usableItems = manager.getUsableItems()

      expect(usableItems).toContain(testItem)
    })

    it('should exclude items on cooldown', () => {
      // 使用道具触发冷却
      manager.useItem(testItem.id, 1)

      const usableItems = manager.getUsableItems()

      expect(usableItems).not.toContain(testItem)
    })

    it('should exclude items that do not meet requirements', () => {
      testItem.requirements = [
        { type: 'level', target: 'pet', value: 50 }
      ]

      const usableItems = manager.getUsableItems()

      expect(usableItems).not.toContain(testItem)
    })
  })
})

describe('ItemFactory', () => {
  describe('generateRandomItem', () => {
    it('should generate valid food item', () => {
      const item = ItemFactory.generateRandomItem(ItemType.FOOD, PetRarity.COMMON)

      expect(item.id).toBeDefined()
      expect(item.name).toBeDefined()
      expect(item.type).toBe(ItemType.FOOD)
      expect(item.rarity).toBe(PetRarity.COMMON)
      expect(item.quantity).toBe(1)
      expect(item.consumable).toBe(true)
      expect(item.effects).toBeDefined()
      expect(item.effects.length).toBeGreaterThan(0)
    })

    it('should generate valid medicine item', () => {
      const item = ItemFactory.generateRandomItem(ItemType.MEDICINE, PetRarity.RARE)

      expect(item.type).toBe(ItemType.MEDICINE)
      expect(item.rarity).toBe(PetRarity.RARE)
      expect(item.consumable).toBe(true)

      // 药品应该有治疗效果
      const healEffect = item.effects.find(e => e.type === 'heal')
      expect(healEffect).toBeDefined()
    })

    it('should generate valid toy item', () => {
      const item = ItemFactory.generateRandomItem(ItemType.TOY, PetRarity.UNCOMMON)

      expect(item.type).toBe(ItemType.TOY)
      expect(item.rarity).toBe(PetRarity.UNCOMMON)
      expect(item.consumable).toBe(false)

      // 玩具应该有快乐度效果
      const happinessEffect = item.effects.find(e => e.type === 'happiness')
      expect(happinessEffect).toBeDefined()
    })

    it('should generate higher value items for higher rarity', () => {
      const commonItem = ItemFactory.generateRandomItem(ItemType.FOOD, PetRarity.COMMON)
      const legendaryItem = ItemFactory.generateRandomItem(ItemType.FOOD, PetRarity.LEGENDARY)

      expect(legendaryItem.value).toBeGreaterThan(commonItem.value)
    })

    it('should generate stronger effects for higher rarity', () => {
      const commonItem = ItemFactory.generateRandomItem(ItemType.FOOD, PetRarity.COMMON)
      const legendaryItem = ItemFactory.generateRandomItem(ItemType.FOOD, PetRarity.LEGENDARY)

      const commonHealEffect = commonItem.effects.find(e => e.type === 'heal')
      const legendaryHealEffect = legendaryItem.effects.find(e => e.type === 'heal')

      if (commonHealEffect && legendaryHealEffect) {
        expect(legendaryHealEffect.value).toBeGreaterThan(commonHealEffect.value)
      }
    })
  })
})

describe('ItemEffectCalculator', () => {
  let testPet: Pet
  let testItem: Item

  beforeEach(() => {
    testPet = createTestPet()
    testItem = createTestItem()
  })

  describe('calculateItemImpact', () => {
    it('should calculate item impact correctly', () => {
      const impact = ItemEffectCalculator.calculateItemImpact(testItem, testPet)

      expect(impact.healthChange).toBe(30)
      expect(impact.happinessChange).toBe(20)
      expect(impact.energyChange).toBe(0)
      expect(impact.experienceChange).toBe(0)
    })

    it('should handle buff effects', () => {
      testItem.effects = [
        { type: 'buff', target: 'strength', value: 15 }
      ]

      const impact = ItemEffectCalculator.calculateItemImpact(testItem, testPet)

      expect(impact.statChanges.strength).toBe(15)
    })

    it('should handle multiple effects', () => {
      testItem.effects = [
        { type: 'heal', target: 'health', value: 25 },
        { type: 'happiness', target: 'happiness', value: 15 },
        { type: 'energy', target: 'energy', value: 10 },
        { type: 'experience', target: 'experience', value: 50 },
        { type: 'buff', target: 'agility', value: 8 }
      ]

      const impact = ItemEffectCalculator.calculateItemImpact(testItem, testPet)

      expect(impact.healthChange).toBe(25)
      expect(impact.happinessChange).toBe(15)
      expect(impact.energyChange).toBe(10)
      expect(impact.experienceChange).toBe(50)
      expect(impact.statChanges.agility).toBe(8)
    })
  })

  describe('recommendItems', () => {
    it('should recommend healing items for low health pets', () => {
      testPet.health = 20 // 很低的健康值

      const healingItem = createTestItem(ItemType.MEDICINE)
      healingItem.effects = [{ type: 'heal', target: 'health', value: 50 }]

      const toyItem = createTestItem(ItemType.TOY)
      toyItem.effects = [{ type: 'happiness', target: 'happiness', value: 30 }]

      const recommendations = ItemEffectCalculator.recommendItems(testPet, [healingItem, toyItem])

      expect(recommendations[0]).toBe(healingItem) // 治疗道具应该排在前面
    })

    it('should recommend happiness items for sad pets', () => {
      testPet.happiness = 10 // 很低的快乐度

      const toyItem = createTestItem(ItemType.TOY)
      toyItem.effects = [{ type: 'happiness', target: 'happiness', value: 40 }]

      const trainingItem = createTestItem(ItemType.TRAINING_ITEM)
      trainingItem.effects = [{ type: 'experience', target: 'experience', value: 100 }]

      const recommendations = ItemEffectCalculator.recommendItems(testPet, [toyItem, trainingItem])

      expect(recommendations[0]).toBe(toyItem) // 玩具应该排在前面
    })

    it('should recommend training items for pets close to leveling', () => {
      testPet.experience = 450 // 接近升级 (maxExperience = 500)

      const trainingItem = createTestItem(ItemType.TRAINING_ITEM)
      trainingItem.effects = [{ type: 'experience', target: 'experience', value: 100 }]

      const foodItem = createTestItem(ItemType.FOOD)
      foodItem.effects = [{ type: 'heal', target: 'health', value: 30 }]

      const recommendations = ItemEffectCalculator.recommendItems(testPet, [trainingItem, foodItem])

      expect(recommendations[0]).toBe(trainingItem) // 训练道具应该排在前面
    })

    it('should limit recommendations to 5 items', () => {
      const items = Array.from({ length: 10 }, (_, i) => {
        const item = createTestItem(ItemType.FOOD)
        item.id = `item-${i}`
        item.effects = [{ type: 'heal', target: 'health', value: 20 }]
        return item
      })

      const recommendations = ItemEffectCalculator.recommendItems(testPet, items)

      expect(recommendations.length).toBeLessThanOrEqual(5)
    })
  })
})
