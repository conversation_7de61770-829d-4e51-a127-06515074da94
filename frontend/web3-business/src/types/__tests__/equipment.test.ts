import { describe, it, expect, beforeEach } from 'vitest'
import {
  EquipmentManager,
  EquipmentFactory,
  EnchantmentSystem
} from '../equipment'
import {
  Pet,
  Equipment,
  EquipmentType,
  PetRarity,
  PetType,
  PetStatus,
  GrowthStage,
  PetMood,
  PetSize,
  EnchantmentType
} from '../index'

// 创建测试用萌宠
const createTestPet = (): Pet => ({
  id: 'test-pet-1',
  name: '测试萌宠',
  type: PetType.CAT,
  level: 10,
  experience: 200,
  maxExperience: 500,
  health: 80,
  maxHealth: 100,
  happiness: 70,
  maxHappiness: 100,
  energy: 60,
  maxEnergy: 100,
  rarity: PetRarity.RARE,
  equipment: [],
  lastFeedTime: Date.now() - 60000,
  lastPlayTime: Date.now() - 120000,
  lastTrainTime: Date.now() - 180000,
  lastRestTime: Date.now() - 240000,
  birthTime: Date.now() - 86400000,
  avatar: '/images/pets/cat/orange_striped.png',
  stats: {
    strength: 25,
    intelligence: 30,
    agility: 35,
    charm: 28,
    vitality: 26,
    luck: 22
  },
  baseStats: {
    strength: 20,
    intelligence: 25,
    agility: 30,
    charm: 23,
    vitality: 21,
    luck: 17
  },
  appearance: {
    species: 'cat',
    color: 'orange',
    pattern: 'striped',
    size: PetSize.SMALL,
    accessories: [],
    specialEffects: [],
    animations: ['idle', 'walk', 'play', 'sleep']
  },
  status: PetStatus.HEALTHY,
  growthStage: GrowthStage.CHILD,
  mood: PetMood.CONTENT,
  skills: [],
  achievements: [],
  totalTokensEarned: '0',
  evolutionPoints: 0,
  breedCount: 0,
  generation: 1,
  traits: [],
  metadata: {
    version: '1.0.0',
    createdBy: 'test',
    lastModified: Date.now(),
    checksum: 'test-checksum',
    tags: ['cat', 'rare'],
    description: '测试用萌宠'
  }
})

// 创建测试装备
const createTestEquipment = (type: EquipmentType = EquipmentType.WEAPON): Equipment => ({
  id: 'test-equipment-1',
  name: '测试武器',
  type,
  rarity: PetRarity.COMMON,
  level: 1,
  stats: { strength: 10, agility: 5 },
  description: '测试用装备',
  requirements: [
    { type: 'level', target: 'pet', value: 5 }
  ],
  effects: [],
  durability: 100,
  maxDurability: 100,
  enchantments: [],
  tradeable: true,
  soulbound: false,
  metadata: {
    version: '1.0.0',
    createdAt: Date.now(),
    createdBy: 'test',
    lastModified: Date.now(),
    tags: ['weapon', 'common'],
    rarity: PetRarity.COMMON
  }
})

describe('EquipmentManager', () => {
  let manager: EquipmentManager
  let testPet: Pet
  let testEquipment: Equipment

  beforeEach(() => {
    testPet = createTestPet()
    testEquipment = createTestEquipment()
    manager = new EquipmentManager(testPet)
  })

  describe('validateEquipment', () => {
    it('should validate equipment that meets requirements', () => {
      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(true)
      expect(result.reasons).toHaveLength(0)
    })

    it('should reject equipment with insufficient level', () => {
      testEquipment.requirements = [
        { type: 'level', target: 'pet', value: 20 }
      ]

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(false)
      expect(result.reasons).toContain('需要等级 20，当前等级 10')
    })

    it('should reject equipment with insufficient stats', () => {
      testEquipment.requirements = [
        { type: 'stat', target: 'strength', value: 50 }
      ]

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(false)
      expect(result.reasons).toContain('需要 strength 50，当前 25')
    })

    it('should reject equipment with insufficient rarity', () => {
      testEquipment.requirements = [
        { type: 'rarity', target: 'pet', value: PetRarity.LEGENDARY }
      ]

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(false)
      expect(result.reasons).toContain('需要稀有度 legendary，当前 rare')
    })

    it('should reject equipment for wrong species', () => {
      testEquipment.requirements = [
        { type: 'species', target: 'pet', value: PetType.DOG }
      ]

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(false)
      expect(result.reasons).toContain('仅限 dog 种族使用')
    })

    it('should reject damaged equipment', () => {
      testEquipment.durability = 0

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(false)
      expect(result.reasons).toContain('装备已损坏，需要修理')
    })

    it('should warn about low durability', () => {
      testEquipment.durability = 10 // 10% durability

      const result = manager.validateEquipment(testEquipment)
      expect(result.canEquip).toBe(true)
      expect(result.warnings).toContain('装备耐久度较低，建议修理')
    })
  })

  describe('equipItem', () => {
    it('should equip valid equipment', () => {
      const success = manager.equipItem(testEquipment)
      expect(success).toBe(true)
      expect(testPet.equipment).toContain(testEquipment)
    })

    it('should throw error for invalid equipment', () => {
      testEquipment.requirements = [
        { type: 'level', target: 'pet', value: 50 }
      ]

      expect(() => manager.equipItem(testEquipment)).toThrow('无法装备')
    })

    it('should replace existing equipment in same slot', () => {
      const firstWeapon = createTestEquipment(EquipmentType.WEAPON)
      const secondWeapon = createTestEquipment(EquipmentType.WEAPON)
      secondWeapon.id = 'test-equipment-2'
      secondWeapon.name = '第二把武器'

      manager.equipItem(firstWeapon)
      expect(testPet.equipment).toHaveLength(1)

      manager.equipItem(secondWeapon)
      expect(testPet.equipment).toHaveLength(1)
      expect(testPet.equipment[0]).toBe(secondWeapon)
    })
  })

  describe('unequipItem', () => {
    it('should unequip equipment successfully', () => {
      manager.equipItem(testEquipment)
      const unequipped = manager.unequipItem(EquipmentType.WEAPON)

      expect(unequipped).toBe(testEquipment)
      expect(testPet.equipment).toHaveLength(0)
    })

    it('should return null if no equipment in slot', () => {
      const unequipped = manager.unequipItem(EquipmentType.WEAPON)
      expect(unequipped).toBeNull()
    })
  })

  describe('getEquipmentBonuses', () => {
    it('should calculate equipment bonuses correctly', () => {
      const weapon = createTestEquipment(EquipmentType.WEAPON)
      weapon.stats = { strength: 15, agility: 10 }

      const armor = createTestEquipment(EquipmentType.ARMOR)
      armor.stats = { vitality: 20, strength: 5 }

      manager.equipItem(weapon)
      manager.equipItem(armor)

      const bonuses = manager.getEquipmentBonuses()

      expect(bonuses.strength).toBe(20) // 15 + 5
      expect(bonuses.agility).toBe(10)
      expect(bonuses.vitality).toBe(20)
    })
  })

  describe('repairEquipment', () => {
    it('should repair equipment successfully', () => {
      testEquipment.durability = 50
      manager.equipItem(testEquipment)

      const success = manager.repairEquipment(testEquipment.id, 30)
      expect(success).toBe(true)
      expect(testEquipment.durability).toBe(80)
    })

    it('should not exceed max durability', () => {
      testEquipment.durability = 90
      manager.equipItem(testEquipment)

      manager.repairEquipment(testEquipment.id, 50)
      expect(testEquipment.durability).toBe(100)
    })

    it('should return false for non-existent equipment', () => {
      const success = manager.repairEquipment('non-existent', 50)
      expect(success).toBe(false)
    })
  })

  describe('enhanceEquipment', () => {
    it('should enhance equipment successfully', () => {
      testEquipment.stats = { strength: 10 }
      testEquipment.level = 1
      manager.equipItem(testEquipment)

      const success = manager.enhanceEquipment(testEquipment.id, 1)
      expect(success).toBe(true)
      expect(testEquipment.level).toBe(2)
      expect(testEquipment.stats.strength).toBe(11) // 10 * 1.1 = 11
    })
  })
})

describe('EquipmentFactory', () => {
  describe('generateRandomEquipment', () => {
    it('should generate valid equipment', () => {
      const equipment = EquipmentFactory.generateRandomEquipment(
        EquipmentType.WEAPON,
        PetRarity.RARE,
        5
      )

      expect(equipment.id).toBeDefined()
      expect(equipment.name).toBeDefined()
      expect(equipment.type).toBe(EquipmentType.WEAPON)
      expect(equipment.rarity).toBe(PetRarity.RARE)
      expect(equipment.level).toBe(5)
      expect(equipment.stats).toBeDefined()
      expect(equipment.requirements).toBeDefined()
      expect(equipment.durability).toBe(100)
      expect(equipment.maxDurability).toBe(100)
    })

    it('should generate higher stats for higher rarity', () => {
      const commonEquipment = EquipmentFactory.generateRandomEquipment(
        EquipmentType.WEAPON,
        PetRarity.COMMON,
        1
      )

      const legendaryEquipment = EquipmentFactory.generateRandomEquipment(
        EquipmentType.WEAPON,
        PetRarity.LEGENDARY,
        1
      )

      const commonTotal = Object.values(commonEquipment.stats).reduce((sum, val) => sum + (val || 0), 0)
      const legendaryTotal = Object.values(legendaryEquipment.stats).reduce((sum, val) => sum + (val || 0), 0)

      expect(legendaryTotal).toBeGreaterThan(commonTotal)
    })

    it('should generate appropriate requirements', () => {
      const equipment = EquipmentFactory.generateRandomEquipment(
        EquipmentType.WEAPON,
        PetRarity.EPIC,
        10
      )

      const levelReq = equipment.requirements.find(req => req.type === 'level')
      expect(levelReq).toBeDefined()
      expect(levelReq!.value).toBeGreaterThan(0)

      const rarityReq = equipment.requirements.find(req => req.type === 'rarity')
      expect(rarityReq).toBeDefined()
      expect(rarityReq!.value).toBe(PetRarity.EPIC)
    })
  })
})

describe('EnchantmentSystem', () => {
  let testEquipment: Equipment

  beforeEach(() => {
    testEquipment = createTestEquipment()
  })

  describe('addEnchantment', () => {
    it('should add new enchantment successfully', () => {
      const success = EnchantmentSystem.addEnchantment(
        testEquipment,
        EnchantmentType.OFFENSIVE,
        1
      )

      expect(success).toBe(true)
      expect(testEquipment.enchantments).toHaveLength(1)
      expect(testEquipment.enchantments[0].type).toBe(EnchantmentType.OFFENSIVE)
      expect(testEquipment.enchantments[0].level).toBe(1)
    })

    it('should upgrade existing enchantment', () => {
      // 先添加一个附魔
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.OFFENSIVE, 1)

      // 再次添加相同类型的附魔
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.OFFENSIVE, 2)

      expect(testEquipment.enchantments).toHaveLength(1)
      expect(testEquipment.enchantments[0].level).toBe(3) // 1 + 2
    })

    it('should not exceed max enchantment level', () => {
      // 添加高等级附魔
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.OFFENSIVE, 9)
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.OFFENSIVE, 5)

      expect(testEquipment.enchantments[0].level).toBe(10) // 最大等级
    })

    it('should allow multiple different enchantment types', () => {
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.OFFENSIVE, 1)
      EnchantmentSystem.addEnchantment(testEquipment, EnchantmentType.DEFENSIVE, 1)

      expect(testEquipment.enchantments).toHaveLength(2)
      expect(testEquipment.enchantments.find(e => e.type === EnchantmentType.OFFENSIVE)).toBeDefined()
      expect(testEquipment.enchantments.find(e => e.type === EnchantmentType.DEFENSIVE)).toBeDefined()
    })
  })
})
