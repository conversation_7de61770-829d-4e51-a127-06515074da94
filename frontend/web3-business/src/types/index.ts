// 萌宠相关类型
export interface Pet {
  id: string
  name: string
  type: PetType
  level: number
  experience: number
  maxExperience: number
  health: number
  maxHealth: number
  happiness: number
  maxHappiness: number
  energy: number
  maxEnergy: number
  rarity: PetRarity
  equipment: Equipment[]
  lastFeedTime: number
  lastPlayTime: number
  lastTrainTime: number
  lastRestTime: number
  birthTime: number
  avatar: string
  stats: PetStats
  baseStats: PetStats
  appearance: PetAppearance
  status: PetStatus
  growthStage: GrowthStage
  mood: PetMood
  skills: PetSkill[]
  achievements: string[]
  totalTokensEarned: string
  evolutionPoints: number
  breedCount: number
  generation: number
  parents?: {
    mother?: string
    father?: string
  }
  traits: PetTrait[]
  metadata: PetMetadata
}

export interface PetStats {
  strength: number
  intelligence: number
  agility: number
  charm: number
  vitality: number
  luck: number
}

export interface PetAppearance {
  species: string
  color: string
  pattern: string
  size: PetSize
  accessories: string[]
  specialEffects: string[]
  animations: string[]
}

export interface PetMetadata {
  version: string
  createdBy: string
  lastModified: number
  checksum: string
  tags: string[]
  description?: string
}

export interface PetTrait {
  id: string
  name: string
  type: TraitType
  value: number | string | boolean
  rarity: PetRarity
  description: string
  effects: TraitEffect[]
}

export interface TraitEffect {
  type: 'stat_bonus' | 'skill_bonus' | 'special_ability'
  target: string
  value: number
  condition?: string
}

export interface PetSkill {
  id: string
  name: string
  level: number
  maxLevel: number
  experience: number
  maxExperience: number
  type: SkillType
  description: string
  effects: SkillEffect[]
  requirements: SkillRequirement[]
}

export interface SkillEffect {
  type: 'stat_modifier' | 'token_bonus' | 'special_action'
  value: number
  duration?: number
  condition?: string
}

export interface SkillRequirement {
  type: 'level' | 'stat' | 'equipment' | 'achievement'
  target: string
  value: number
}

export enum PetType {
  CAT = 'cat',
  DOG = 'dog',
  RABBIT = 'rabbit',
  BIRD = 'bird',
  DRAGON = 'dragon',
  UNICORN = 'unicorn',
  PHOENIX = 'phoenix',
  GRIFFIN = 'griffin',
  PEGASUS = 'pegasus',
  KITSUNE = 'kitsune'
}

export enum PetRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHICAL = 'mythical'
}

export enum PetSize {
  TINY = 'tiny',
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  GIANT = 'giant'
}

export enum PetStatus {
  HEALTHY = 'healthy',
  SICK = 'sick',
  TIRED = 'tired',
  HUNGRY = 'hungry',
  HAPPY = 'happy',
  SAD = 'sad',
  EXCITED = 'excited',
  SLEEPING = 'sleeping',
  TRAINING = 'training',
  EVOLVING = 'evolving'
}

export enum GrowthStage {
  EGG = 'egg',
  BABY = 'baby',
  CHILD = 'child',
  TEEN = 'teen',
  ADULT = 'adult',
  ELDER = 'elder',
  LEGENDARY_FORM = 'legendary_form'
}

export enum PetMood {
  ECSTATIC = 'ecstatic',
  HAPPY = 'happy',
  CONTENT = 'content',
  NEUTRAL = 'neutral',
  UNHAPPY = 'unhappy',
  DEPRESSED = 'depressed',
  ANGRY = 'angry'
}

export enum TraitType {
  PHYSICAL = 'physical',
  MENTAL = 'mental',
  MAGICAL = 'magical',
  SOCIAL = 'social',
  SPECIAL = 'special'
}

export enum SkillType {
  COMBAT = 'combat',
  MAGIC = 'magic',
  CRAFTING = 'crafting',
  SOCIAL = 'social',
  SURVIVAL = 'survival',
  SPECIAL = 'special'
}

export interface Equipment {
  id: string
  name: string
  type: EquipmentType
  rarity: PetRarity
  level: number
  stats: Partial<PetStats>
  description: string
  requirements: EquipmentRequirement[]
  effects: EquipmentEffect[]
  durability: number
  maxDurability: number
  enchantments: Enchantment[]
  setBonus?: SetBonus
  tradeable: boolean
  soulbound: boolean
  metadata: ItemMetadata
}

export interface EquipmentRequirement {
  type: 'level' | 'stat' | 'rarity' | 'species'
  target: string
  value: number | string
}

export interface EquipmentEffect {
  type: 'stat_bonus' | 'skill_bonus' | 'special_ability' | 'passive_effect'
  target: string
  value: number
  condition?: string
  duration?: number
}

export interface Enchantment {
  id: string
  name: string
  level: number
  type: EnchantmentType
  effect: EquipmentEffect
  description: string
}

export interface SetBonus {
  setName: string
  requiredPieces: number
  currentPieces: number
  bonuses: EquipmentEffect[]
}

export interface ItemMetadata {
  version: string
  createdAt: number
  createdBy: string
  lastModified: number
  tags: string[]
  rarity: PetRarity
}

export enum EquipmentType {
  WEAPON = 'weapon',
  ARMOR = 'armor',
  HELMET = 'helmet',
  BOOTS = 'boots',
  GLOVES = 'gloves',
  ACCESSORY = 'accessory',
  RING = 'ring',
  NECKLACE = 'necklace',
  TOY = 'toy',
  FOOD = 'food',
  POTION = 'potion',
  SCROLL = 'scroll',
  TOOL = 'tool'
}

export enum EnchantmentType {
  OFFENSIVE = 'offensive',
  DEFENSIVE = 'defensive',
  UTILITY = 'utility',
  SPECIAL = 'special'
}

// 道具相关类型
export interface Item {
  id: string
  name: string
  type: ItemType
  rarity: PetRarity
  quantity: number
  maxStack: number
  description: string
  effects: ItemEffect[]
  requirements: ItemRequirement[]
  cooldown: number
  consumable: boolean
  tradeable: boolean
  value: number
  metadata: ItemMetadata
}

export interface ItemEffect {
  type: 'heal' | 'buff' | 'debuff' | 'experience' | 'happiness' | 'energy'
  target: string
  value: number
  duration?: number
  chance?: number
}

export interface ItemRequirement {
  type: 'level' | 'stat' | 'species' | 'rarity'
  target: string
  value: number | string
}

export enum ItemType {
  FOOD = 'food',
  MEDICINE = 'medicine',
  TOY = 'toy',
  TRAINING_ITEM = 'training_item',
  EVOLUTION_ITEM = 'evolution_item',
  BREEDING_ITEM = 'breeding_item',
  COSMETIC = 'cosmetic',
  CONSUMABLE = 'consumable',
  MATERIAL = 'material',
  CURRENCY = 'currency',
  SPECIAL = 'special'
}

// 游戏相关类型
export interface GameState {
  currentPet: Pet | null
  pets: Pet[]
  tokens: string
  level: number
  achievements: Achievement[]
  inventory: InventoryItem[]
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  unlocked: boolean
  unlockedAt?: number
  reward: {
    tokens?: number
    equipment?: Equipment
  }
}

export interface InventoryItem {
  id: string
  name: string
  type: 'food' | 'toy' | 'medicine' | 'equipment'
  quantity: number
  description: string
  icon: string
}

// Web3相关类型
export interface ContractInfo {
  address: string
  abi: any[]
  name: string
  symbol: string
  decimals: number
  totalSupply: string
  version: string
}

export interface WalletState {
  connected: boolean
  address: string | null
  balance: string
  chainId: number | null
  provider: any
  signer: any
}

export interface TransactionState {
  loading: boolean
  hash: string | null
  error: string | null
  success: boolean
}

// Ethereum provider types
export interface EthereumProvider {
  isMetaMask?: boolean
  request: (args: { method: string; params?: any[] }) => Promise<any>
  on: (event: string, handler: (...args: any[]) => void) => void
  removeListener: (event: string, handler: (...args: any[]) => void) => void
  selectedAddress: string | null
  chainId: string
  networkVersion: string
  enable?: () => Promise<string[]>
}

// Wallet connection types
export interface WalletInfo {
  isConnected: boolean
  address: string
  balance: string
  chainId: number
  provider: any
}

export interface WalletConnector {
  name: string
  icon: string
  connect: () => Promise<WalletInfo>
  disconnect: () => void
  isInstalled: () => boolean
}

// Network configuration
export interface NetworkConfig {
  chainId: number
  chainName: string
  nativeCurrency: {
    name: string
    symbol: string
    decimals: number
  }
  rpcUrls: string[]
  blockExplorerUrls: string[]
}

// Declare global ethereum object
declare global {
  interface Window {
    ethereum?: EthereumProvider & {
      selectedAddress: string | null
      request: (args: { method: string; params?: any[] }) => Promise<any>
      on: (event: string, handler: (...args: any[]) => void) => void
      removeListener: (event: string, handler: (...args: any[]) => void) => void
    }
  }
}

// 活动相关类型
export interface Activity {
  id: string
  type: ActivityType
  name: string
  description: string
  duration: number
  cost: {
    energy?: number
    tokens?: number
    items?: string[]
  }
  rewards: {
    experience: number
    tokens?: number
    items?: InventoryItem[]
    happiness?: number
    health?: number
  }
  requirements: {
    level?: number
    equipment?: string[]
    stats?: Partial<PetStats>
  }
}

export enum ActivityType {
  FEED = 'feed',
  PLAY = 'play',
  TRAIN = 'train',
  ADVENTURE = 'adventure',
  REST = 'rest',
  BATTLE = 'battle'
}

// 商店相关类型
export interface ShopItem {
  id: string
  name: string
  description: string
  price: number
  currency: 'tokens' | 'eth'
  type: 'food' | 'toy' | 'equipment' | 'pet'
  rarity: PetRarity
  icon: string
  available: boolean
  limited?: {
    total: number
    remaining: number
  }
}

// 排行榜类型
export interface LeaderboardEntry {
  rank: number
  address: string
  petName: string
  level: number
  tokens: string
  achievements: number
}

// 事件类型
export interface GameEvent {
  id: string
  type: 'level_up' | 'achievement' | 'token_earned' | 'pet_evolved'
  timestamp: number
  data: any
  message: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 本地存储类型
export interface LocalStorageData {
  gameState: GameState
  settings: UserSettings
  lastSaveTime: number
}

export interface UserSettings {
  soundEnabled: boolean
  musicEnabled: boolean
  notificationsEnabled: boolean
  language: 'zh-CN' | 'en-US'
  theme: 'light' | 'dark' | 'auto'
}

// 导出新增的类型和工具类
// 修改这里，导出typesWithoutCircular中的所有类型，避免循环依赖
export * from './typesWithoutCircular'
// export * from './pet'
// export * from './equipment'
// export * from './item'
