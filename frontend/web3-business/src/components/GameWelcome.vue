<template>
  <div class="game-welcome">
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="banner-title">萌宠养成代币游戏</h1>
        <p class="banner-subtitle">养成可爱萌宠，赚取真实代币</p>

        <div class="banner-actions">
          <van-button
            type="primary"
            size="large"
            @click="startGame"
            class="start-button"
            :loading="isLoading"
            :disabled="isLoading"
          >
            开始游戏
          </van-button>

          <div class="action-links">
            <van-button
              plain
              size="small"
              @click="goToIntroduction"
              :disabled="isLoading"
            >
              游戏介绍
            </van-button>

            <van-button
              plain
              size="small"
              @click="goToTutorial"
              :disabled="isLoading"
            >
              游戏教程
            </van-button>
          </div>
        </div>
      </div>

      <div class="banner-decoration">
        <div class="pet-image">🐱</div>
        <div class="token-image">💰</div>
      </div>
    </div>

    <div class="feature-cards">
      <div class="feature-card">
        <div class="card-icon">🐱</div>
        <h3 class="card-title">萌宠养成</h3>
        <p class="card-desc">创建和养成你的专属萌宠，提升等级和属性</p>
      </div>

      <div class="feature-card">
        <div class="card-icon">💰</div>
        <h3 class="card-title">代币兑换</h3>
        <p class="card-desc">将养成的萌宠兑换成有价值的区块链代币</p>
      </div>

      <div class="feature-card">
        <div class="card-icon">🎮</div>
        <h3 class="card-title">互动玩法</h3>
        <p class="card-desc">通过喂食、训练、装备道具等方式互动</p>
      </div>

      <div class="feature-card">
        <div class="card-icon">🔒</div>
        <h3 class="card-title">区块链技术</h3>
        <p class="card-desc">基于以太坊智能合约，保障资产安全</p>
      </div>
    </div>

    <div class="game-news">
      <h2 class="news-title">游戏动态</h2>
      <div class="news-list">
        <div class="news-item">
          <div class="news-date">2025/07/24</div>
          <div class="news-content">
            <h4 class="news-headline">游戏正式上线</h4>
            <p class="news-summary">萌宠养成代币游戏正式上线，欢迎体验！</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-date">2025/07/20</div>
          <div class="news-content">
            <h4 class="news-headline">新增萌宠品种</h4>
            <p class="news-summary">游戏新增多种萌宠品种，丰富养成体验。</p>
          </div>
        </div>

        <div class="news-item">
          <div class="news-date">2025/07/15</div>
          <div class="news-content">
            <h4 class="news-headline">代币系统升级</h4>
            <p class="news-summary">代币系统升级，优化兑换机制和奖励算法。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { usePetStore } from '../stores/pet'
import { useGameStore } from '../stores/game'
import { ref, onMounted } from 'vue'
import { showToast, showDialog } from 'vant'

const router = useRouter()
const petStore = usePetStore()
const gameStore = useGameStore()
const isLoading = ref(false)

// 检查游戏状态并初始化
onMounted(async () => {
  // 尝试加载游戏数据
  await gameStore.loadGameData()
  await petStore.loadPetData()
})

const startGame = async () => {
  isLoading.value = true

  try {
    // 检查用户是否已完成教程
    if (!gameStore.settings.tutorialCompleted) {
      // 未完成教程，提示用户
      showDialog({
        title: '开始游戏',
        message: '建议先完成游戏教程，以便了解游戏玩法。',
        confirmButtonText: '开始教程',
        cancelButtonText: '直接开始游戏'
      }).then(() => {
        // 用户选择开始教程
        router.push('/tutorial')
      }).catch(() => {
        // 用户选择直接开始游戏
        checkPetAndStart()
      })
    } else {
      // 已完成教程，检查萌宠状态
      checkPetAndStart()
    }
  } catch (error) {
    console.error('游戏启动错误:', error)
    showToast('游戏启动失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 检查萌宠状态并开始游戏
const checkPetAndStart = async () => {
  // 检查用户是否有萌宠
  if (petStore.petCount === 0) {
    // 没有萌宠，引导创建萌宠
    showDialog({
      title: '创建萌宠',
      message: '你还没有萌宠，是否创建一个新的萌宠？',
      confirmButtonText: '创建萌宠',
      cancelButtonText: '自定义创建'
    }).then(() => {
      // 生成一个随机萌宠
      try {
        const newPet = petStore.generateRandomPet()
        showToast('成功创建萌宠: ' + newPet.name)
        // 创建成功后进入萌宠详情页
        router.push(`/pet/${newPet.id}`)
      } catch (error) {
        console.error('创建萌宠失败:', error)
        showToast('创建萌宠失败，请重试')
      }
    }).catch(() => {
      // 用户选择自定义创建
      router.push('/pet/create')
    })
  } else {
    // 已有萌宠，进入萌宠列表页
    router.push('/pets')
    showToast('欢迎回来！')
  }
}

const goToIntroduction = () => {
  router.push('/introduction')
}

const goToTutorial = () => {
  router.push('/tutorial')
}
</script>

<script lang="ts">
export default {
  name: 'GameWelcome'
}
</script>

<style scoped>
.game-welcome {
  @apply space-y-8;
}

.welcome-banner {
  @apply relative bg-gradient-to-r from-purple-500 to-blue-500 text-white p-8 rounded-xl overflow-hidden flex flex-col md:flex-row items-center;
}

.banner-content {
  @apply relative z-10 md:w-2/3;
}

.banner-title {
  @apply text-3xl md:text-4xl font-bold mb-2;
}

.banner-subtitle {
  @apply text-lg opacity-90 mb-6;
}

.banner-actions {
  @apply space-y-4;
}

.start-button {
  @apply bg-white text-purple-600 border-0 shadow-lg;
}

.action-links {
  @apply flex space-x-4;
}

.banner-decoration {
  @apply hidden md:flex md:w-1/3 justify-center items-center space-x-4;
}

.pet-image {
  @apply text-7xl transform -rotate-12 animate-bounce-slow;
}

.token-image {
  @apply text-7xl transform rotate-12 animate-pulse-slow;
}

.feature-cards {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.feature-card {
  @apply bg-white rounded-lg p-6 shadow-sm text-center hover:shadow-md transition-shadow duration-200;
}

.card-icon {
  @apply text-4xl mb-4;
}

.card-title {
  @apply text-xl font-semibold text-gray-800 mb-2;
}

.card-desc {
  @apply text-sm text-gray-600;
}

.game-news {
  @apply bg-white rounded-lg p-6 shadow-sm;
}

.news-title {
  @apply text-2xl font-bold text-gray-800 mb-4;
}

.news-list {
  @apply space-y-4;
}

.news-item {
  @apply flex items-start space-x-4 p-3 border-b border-gray-100 last:border-0;
}

.news-date {
  @apply text-sm text-gray-500 whitespace-nowrap;
}

.news-content {
  @apply flex-1;
}

.news-headline {
  @apply font-semibold text-gray-800 mb-1;
}

.news-summary {
  @apply text-sm text-gray-600;
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0) rotate(-12deg);
  }
  50% {
    transform: translateY(-10px) rotate(-12deg);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s infinite;
}
</style>
