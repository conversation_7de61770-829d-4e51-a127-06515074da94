<template>
  <div class="security-settings">
    <van-nav-bar title="安全设置" left-arrow @click-left="$emit('back')" />

    <div class="p-4 space-y-6">
      <!-- 安全级别设置 -->
      <van-cell-group title="安全级别">
        <van-cell>
          <template #title>
            <div class="flex items-center">
              <van-icon name="shield-o" class="mr-2" />
              <span>当前安全级别</span>
            </div>
          </template>
          <template #value>
            <van-tag :type="securityLevelColor">{{ securityLevelText }}</van-tag>
          </template>
        </van-cell>

        <van-cell>
          <van-radio-group v-model="localSecurityLevel" @change="onSecurityLevelChange">
            <van-cell-group>
              <van-cell clickable @click="localSecurityLevel = 'low'">
                <template #title>
                  <van-radio name="low">低级</van-radio>
                </template>
                <template #label>
                  <span class="text-gray-500 text-sm">基础安全检查，适合熟练用户</span>
                </template>
              </van-cell>

              <van-cell clickable @click="localSecurityLevel = 'medium'">
                <template #title>
                  <van-radio name="medium">中级</van-radio>
                </template>
                <template #label>
                  <span class="text-gray-500 text-sm">标准安全检查，推荐设置</span>
                </template>
              </van-cell>

              <van-cell clickable @click="localSecurityLevel = 'high'">
                <template #title>
                  <van-radio name="high">高级</van-radio>
                </template>
                <template #label>
                  <span class="text-gray-500 text-sm">严格安全检查，最高安全性</span>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </van-cell>
      </van-cell-group>

      <!-- 交易确认设置 -->
      <van-cell-group title="交易确认">
        <van-cell>
          <template #title>
            <div class="flex items-center">
              <van-icon name="checked" class="mr-2" />
              <span>启用交易确认</span>
            </div>
          </template>
          <template #right-icon>
            <van-switch v-model="transactionConfirmation" @change="onTransactionConfirmationChange" />
          </template>
        </van-cell>

        <van-cell title="大额交易阈值" :value="`${largeAmountThreshold} ETH`" is-link @click="showAmountThresholdPicker = true" />

        <van-cell title="最大单笔交易限额" :value="`${maxTransactionAmount} ETH`" is-link @click="showMaxAmountPicker = true" />
      </van-cell-group>

      <!-- 安全监控 -->
      <van-cell-group title="安全监控">
        <van-cell>
          <template #title>
            <div class="flex items-center">
              <van-icon name="eye-o" class="mr-2" />
              <span>启用安全监控</span>
            </div>
          </template>
          <template #right-icon>
            <van-switch v-model="securityMonitoring" @change="onSecurityMonitoringChange" />
          </template>
        </van-cell>

        <van-cell title="查看安全日志" is-link @click="showSecurityLogs" />

        <van-cell title="导出安全报告" is-link @click="exportSecurityReport" />

        <van-cell title="清除安全日志" is-link @click="clearSecurityLogs" />
      </van-cell-group>

      <!-- 钱包安全 -->
      <van-cell-group title="钱包安全">
        <van-cell title="验证钱包连接" is-link @click="validateWalletConnection" />

        <van-cell>
          <template #title>
            <div class="flex items-center">
              <van-icon name="lock" class="mr-2" />
              <span>自动断开连接</span>
            </div>
          </template>
          <template #right-icon>
            <van-switch v-model="autoDisconnect" @change="onAutoDisconnectChange" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 安全统计 -->
      <van-cell-group title="安全统计">
        <van-cell title="总交易数" :value="securityMetrics.totalTransactions.toString()" />
        <van-cell title="失败交易数" :value="securityMetrics.failedTransactions.toString()" />
        <van-cell title="可疑活动" :value="securityMetrics.suspiciousActivities.toString()" />
        <van-cell title="平均交易金额" :value="`${securityMetrics.averageTransactionValue} ETH`" />
      </van-cell-group>
    </div>

    <!-- 金额阈值选择器 -->
    <van-popup v-model:show="showAmountThresholdPicker" position="bottom">
      <van-picker
        :columns="amountColumns"
        @confirm="onAmountThresholdConfirm"
        @cancel="showAmountThresholdPicker = false"
      />
    </van-popup>

    <!-- 最大金额选择器 -->
    <van-popup v-model:show="showMaxAmountPicker" position="bottom">
      <van-picker
        :columns="maxAmountColumns"
        @confirm="onMaxAmountConfirm"
        @cancel="showMaxAmountPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showNotify, showConfirmDialog } from 'vant'
import { useSecurity } from '../../composables/useSecurity'
import { securityAuditService } from '../../services/security-audit.service'

const emit = defineEmits<{
  back: []
}>()

const {
  securityLevel,
  isSecurityEnabled,
  setSecurityLevel,
  toggleSecurity,
  updateSecurityConfig,
  getSecurityConfig,
  validateWalletSecurity,
  getTransactionLogs,
  clearTransactionLogs
} = useSecurity()

// 本地状态
const localSecurityLevel = ref(securityLevel.value)
const transactionConfirmation = ref(true)
const securityMonitoring = ref(true)
const autoDisconnect = ref(false)
const largeAmountThreshold = ref('10')
const maxTransactionAmount = ref('100')
const showAmountThresholdPicker = ref(false)
const showMaxAmountPicker = ref(false)
const securityMetrics = ref({
  totalTransactions: 0,
  failedTransactions: 0,
  suspiciousActivities: 0,
  walletConnections: 0,
  averageTransactionValue: '0',
  lastAuditTime: new Date()
})

// 计算属性
const securityLevelColor = computed(() => {
  const colors = {
    low: 'warning',
    medium: 'primary',
    high: 'success'
  }
  return colors[localSecurityLevel.value] || 'primary'
})

const securityLevelText = computed(() => {
  const texts = {
    low: '低级',
    medium: '中级',
    high: '高级'
  }
  return texts[localSecurityLevel.value] || '中级'
})

const amountColumns = [
  { text: '1 ETH', value: '1' },
  { text: '5 ETH', value: '5' },
  { text: '10 ETH', value: '10' },
  { text: '20 ETH', value: '20' },
  { text: '50 ETH', value: '50' }
]

const maxAmountColumns = [
  { text: '10 ETH', value: '10' },
  { text: '50 ETH', value: '50' },
  { text: '100 ETH', value: '100' },
  { text: '500 ETH', value: '500' },
  { text: '1000 ETH', value: '1000' }
]

// 方法
const onSecurityLevelChange = (value: 'low' | 'medium' | 'high') => {
  setSecurityLevel(value)
  showNotify({
    type: 'success',
    message: `安全级别已设置为${securityLevelText.value}`
  })
}

const onTransactionConfirmationChange = (value: boolean) => {
  toggleSecurity(value)
  showNotify({
    type: 'success',
    message: value ? '交易确认已启用' : '交易确认已禁用'
  })
}

const onSecurityMonitoringChange = (value: boolean) => {
  if (value) {
    securityAuditService.startMonitoring()
    showNotify({
      type: 'success',
      message: '安全监控已启用'
    })
  } else {
    securityAuditService.stopMonitoring()
    showNotify({
      type: 'success',
      message: '安全监控已禁用'
    })
  }
}

const onAutoDisconnectChange = (value: boolean) => {
  // 这里可以实现自动断开连接的逻辑
  showNotify({
    type: 'success',
    message: value ? '自动断开连接已启用' : '自动断开连接已禁用'
  })
}

const onAmountThresholdConfirm = ({ selectedValues }: any) => {
  largeAmountThreshold.value = selectedValues[0]
  updateSecurityConfig({
    requireConfirmationAbove: selectedValues[0]
  })
  showAmountThresholdPicker.value = false
  showNotify({
    type: 'success',
    message: `大额交易阈值已设置为 ${selectedValues[0]} ETH`
  })
}

const onMaxAmountConfirm = ({ selectedValues }: any) => {
  maxTransactionAmount.value = selectedValues[0]
  updateSecurityConfig({
    maxTransactionAmount: selectedValues[0]
  })
  showMaxAmountPicker.value = false
  showNotify({
    type: 'success',
    message: `最大交易限额已设置为 ${selectedValues[0]} ETH`
  })
}

const showSecurityLogs = () => {
  // 这里可以导航到安全日志页面
  showNotify({
    type: 'primary',
    message: '正在打开安全日志...'
  })
}

const exportSecurityReport = async () => {
  try {
    const report = securityAuditService.exportSecurityReport()
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)

    showNotify({
      type: 'success',
      message: '安全报告已导出'
    })
  } catch (error) {
    showNotify({
      type: 'danger',
      message: '导出安全报告失败'
    })
  }
}

const clearSecurityLogs = async () => {
  try {
    const confirmed = await showConfirmDialog({
      title: '确认清除',
      message: '确定要清除所有安全日志吗？此操作不可撤销。'
    })

    if (confirmed) {
      securityAuditService.clearEvents()
      clearTransactionLogs()
      showNotify({
        type: 'success',
        message: '安全日志已清除'
      })
      loadSecurityMetrics()
    }
  } catch {
    // 用户取消
  }
}

const validateWalletConnection = async () => {
  try {
    showNotify({
      type: 'loading',
      message: '正在验证钱包连接...',
      duration: 0
    })

    const result = await validateWalletSecurity()

    if (result.isSecure) {
      showNotify({
        type: 'success',
        message: '钱包连接安全'
      })
    } else {
      showNotify({
        type: 'warning',
        message: `钱包连接存在问题：${result.warnings.join(', ')}`
      })
    }
  } catch (error) {
    showNotify({
      type: 'danger',
      message: '钱包连接验证失败'
    })
  }
}

const loadSecurityMetrics = () => {
  securityMetrics.value = securityAuditService.generateSecurityMetrics()
}

const loadSecurityConfig = () => {
  const config = getSecurityConfig()
  largeAmountThreshold.value = config.requireConfirmationAbove
  maxTransactionAmount.value = config.maxTransactionAmount
}

onMounted(() => {
  loadSecurityConfig()
  loadSecurityMetrics()

  // 启动安全监控
  if (securityMonitoring.value) {
    securityAuditService.startMonitoring()
  }
})
</script>

<style scoped>
.security-settings {
  min-height: 100vh;
  background-color: #f7f8fa;
}
</style>
