<template>
  <div v-if="visible" class="security-alert">
    <van-overlay :show="visible" @click="handleOverlayClick">
      <div class="alert-container" @click.stop>
        <div class="alert-header">
          <van-icon :name="alertIcon" :color="alertColor" size="24" />
          <h3 class="alert-title">{{ alertTitle }}</h3>
        </div>

        <div class="alert-content">
          <p class="alert-message">{{ message }}</p>

          <div v-if="details && details.length > 0" class="alert-details">
            <van-collapse v-model="activeDetails">
              <van-collapse-item title="详细信息" name="details">
                <ul class="details-list">
                  <li v-for="(detail, index) in details" :key="index">
                    {{ detail }}
                  </li>
                </ul>
              </van-collapse-item>
            </van-collapse>
          </div>

          <div v-if="recommendations && recommendations.length > 0" class="alert-recommendations">
            <h4 class="recommendations-title">建议操作：</h4>
            <ul class="recommendations-list">
              <li v-for="(rec, index) in recommendations" :key="index">
                {{ rec }}
              </li>
            </ul>
          </div>
        </div>

        <div class="alert-actions">
          <van-button
            v-if="showCancel"
            plain
            type="default"
            @click="handleCancel"
          >
            {{ cancelText }}
          </van-button>

          <van-button
            :type="confirmButtonType"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </van-button>
        </div>

        <div v-if="showDontShowAgain" class="alert-footer">
          <van-checkbox v-model="dontShowAgain">
            不再显示此类警告
          </van-checkbox>
        </div>
      </div>
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

export interface SecurityAlertProps {
  visible: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  title?: string
  message: string
  details?: string[]
  recommendations?: string[]
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
  showDontShowAgain?: boolean
  allowOverlayClose?: boolean
}

const props = withDefaults(defineProps<SecurityAlertProps>(), {
  title: '',
  showCancel: true,
  cancelText: '取消',
  confirmText: '确认',
  showDontShowAgain: false,
  allowOverlayClose: false
})

const emit = defineEmits<{
  confirm: [dontShowAgain: boolean]
  cancel: []
  close: []
}>()

const activeDetails = ref<string[]>([])
const dontShowAgain = ref(false)

const alertIcon = computed(() => {
  const icons = {
    low: 'info-o',
    medium: 'warning-o',
    high: 'warning',
    critical: 'close'
  }
  return icons[props.severity]
})

const alertColor = computed(() => {
  const colors = {
    low: '#1989fa',
    medium: '#ff976a',
    high: '#ee0a24',
    critical: '#ad0e0e'
  }
  return colors[props.severity]
})

const alertTitle = computed(() => {
  if (props.title) return props.title

  const titles = {
    low: '安全提示',
    medium: '安全警告',
    high: '高危警告',
    critical: '严重安全威胁'
  }
  return titles[props.severity]
})

const confirmButtonType = computed(() => {
  const types = {
    low: 'primary',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return types[props.severity]
})

const handleOverlayClick = () => {
  if (props.allowOverlayClose) {
    emit('close')
  }
}

const handleConfirm = () => {
  emit('confirm', dontShowAgain.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.security-alert {
  z-index: 9999;
}

.alert-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.alert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.alert-title {
  margin: 0 0 0 12px;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.alert-content {
  margin-bottom: 20px;
}

.alert-message {
  margin: 0 0 16px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #646566;
}

.alert-details {
  margin-bottom: 16px;
}

.details-list {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
  color: #969799;
}

.details-list li {
  margin-bottom: 4px;
}

.alert-recommendations {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.recommendations-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

.recommendations-list {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
  color: #646566;
}

.recommendations-list li {
  margin-bottom: 4px;
}

.alert-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.alert-actions .van-button {
  flex: 1;
}

.alert-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebedf0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .alert-container {
    width: 95%;
    padding: 16px;
  }

  .alert-title {
    font-size: 16px;
  }

  .alert-actions {
    flex-direction: column;
  }
}
</style>
