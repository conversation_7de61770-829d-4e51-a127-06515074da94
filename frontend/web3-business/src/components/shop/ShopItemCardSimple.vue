<template>
  <div class="shop-item-card">
    <div class="item-image">
      <span class="item-icon">{{ getItemIcon(item.type) }}</span>
      <div v-if="isRecommended" class="recommended-badge">推荐</div>
    </div>

    <div class="item-info">
      <h3 class="item-name">{{ item.name }}</h3>
      <p class="item-description">{{ item.description }}</p>

      <div class="item-footer">
        <div class="item-price">
          <span class="price-value">{{ item.price }}</span>
          <span class="price-unit">{{ item.currency === 'coins' ? '金币' : '代币' }}</span>
        </div>

        <van-button
          type="primary"
          size="small"
          @click="handlePurchase"
          :loading="purchasing"
        >
          购买
        </van-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { showToast } from 'vant'

interface ShopItem {
  id: string
  name: string
  description: string
  type: string
  price: number
  currency: 'coins' | 'tokens'
}

export default defineComponent({
  name: 'ShopItemCardSimple',
  props: {
    item: {
      type: Object as () => ShopItem,
      required: true
    },
    isRecommended: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const purchasing = ref(false)

    const getItemIcon = (type: string): string => {
      const icons = {
        food: '🍎',
        toy: '🎾',
        equipment: '⚔️',
        medicine: '💊',
        training: '📚'
      }
      return icons[type as keyof typeof icons] || '📦'
    }

    const handlePurchase = async () => {
      purchasing.value = true
      try {
        // 模拟购买逻辑
        await new Promise(resolve => setTimeout(resolve, 1000))
        showToast('购买成功！')
      } catch (error) {
        showToast('购买失败，请重试')
      } finally {
        purchasing.value = false
      }
    }

    return {
      purchasing,
      getItemIcon,
      handlePurchase
    }
  }
})
</script>

<style scoped>
.shop-item-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.shop-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.item-image {
  position: relative;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  padding: 2rem;
  text-align: center;
}

.item-icon {
  font-size: 3rem;
  display: block;
}

.recommended-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #ff6b6b;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.item-info {
  padding: 1rem;
}

.item-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.item-description {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  display: flex;
  flex-direction: column;
}

.price-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #667eea;
}

.price-unit {
  font-size: 0.8rem;
  color: #9ca3af;
}
</style>
