<template>
  <div class="inventory-manager">
    <van-nav-bar title="背包管理" left-arrow @click-left="$emit('close')" />

    <!-- 统计信息 -->
    <div class="inventory-stats">
      <div class="stat-item">
        <span class="stat-label">物品总数</span>
        <span class="stat-value">{{ totalItems }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">种类数量</span>
        <span class="stat-value">{{ uniqueItems }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总价值</span>
        <span class="stat-value">{{ totalValue }}🪙</span>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="inventory-filters">
      <van-search
        v-model="searchQuery"
        placeholder="搜索物品..."
        @search="handleSearch"
        @clear="handleClearSearch"
      />

      <van-tabs v-model:active="activeTab" @change="handleTabChange">
        <van-tab title="全部" name="all" />
        <van-tab title="食物" name="food" />
        <van-tab title="玩具" name="toy" />
        <van-tab title="装备" name="equipment" />
        <van-tab title="药品" name="medicine" />
        <van-tab title="训练" name="training" />
      </van-tabs>
    </div>

    <!-- 排序选项 -->
    <div class="sort-options">
      <van-dropdown-menu>
        <van-dropdown-item v-model="sortBy" :options="sortOptions" />
      </van-dropdown-menu>
    </div>

    <!-- 物品列表 -->
    <div class="inventory-list">
      <van-empty v-if="filteredItems.length === 0" description="背包空空如也" />

      <div v-else class="item-grid">
        <div
          v-for="item in paginatedItems"
          :key="item.id"
          class="inventory-item"
          @click="handleItemClick(item)"
        >
          <div class="item-icon">{{ getItemIcon(item) }}</div>
          <div class="item-info">
            <h4 class="item-name">{{ item.name }}</h4>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-quantity">数量: {{ item.quantity }}</div>
            <div v-if="item.effect" class="item-effects">
              <span
                v-for="(value, effect) in item.effect"
                :key="effect"
                class="effect-tag"
              >
                {{ getEffectText(effect, value) }}
              </span>
            </div>
          </div>
          <div class="item-actions">
            <van-button
              size="mini"
              type="primary"
              @click.stop="handleUseItem(item)"
              :disabled="!canUseItem(item)"
            >
              使用
            </van-button>
            <van-button
              size="mini"
              @click.stop="handleItemOptions(item)"
            >
              更多
            </van-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <van-pagination
        v-if="totalPages > 1"
        v-model="currentPage"
        :total-items="filteredItems.length"
        :items-per-page="itemsPerPage"
        :show-page-size="3"
        class="pagination"
      />
    </div>

    <!-- 物品详情弹窗 -->
    <van-popup
      v-model:show="showItemDetail"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <div v-if="selectedItem" class="item-detail">
        <div class="detail-header">
          <h3>{{ selectedItem.name }}</h3>
          <van-button size="small" @click="showItemDetail = false">关闭</van-button>
        </div>

        <div class="detail-content">
          <div class="detail-icon">{{ getItemIcon(selectedItem) }}</div>
          <p class="detail-description">{{ selectedItem.description }}</p>

          <div class="detail-stats">
            <div class="stat-row">
              <span>类型:</span>
              <span>{{ getTypeText(selectedItem.type) }}</span>
            </div>
            <div class="stat-row">
              <span>数量:</span>
              <span>{{ selectedItem.quantity }}</span>
            </div>
            <div v-if="selectedItem.effect" class="stat-row">
              <span>效果:</span>
              <div class="effects-list">
                <span
                  v-for="(value, effect) in selectedItem.effect"
                  :key="effect"
                  class="effect-item"
                >
                  {{ getEffectText(effect, value) }}
                </span>
              </div>
            </div>
          </div>

          <div class="detail-actions">
            <van-button
              type="primary"
              block
              @click="handleUseItem(selectedItem)"
              :disabled="!canUseItem(selectedItem)"
            >
              使用物品
            </van-button>
            <van-button
              block
              @click="handleDropItem(selectedItem)"
              class="mt-2"
            >
              丢弃物品
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 物品操作菜单 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="itemActions"
      @select="handleActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useGameStore } from '../../stores/game'
import type { Item } from '../../stores/game'

interface Props {
  visible?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  useItem: [item: Item]
}>()

const gameStore = useGameStore()

// 响应式数据
const searchQuery = ref('')
const activeTab = ref('all')
const sortBy = ref('name')
const currentPage = ref(1)
const itemsPerPage = 12
const showItemDetail = ref(false)
const selectedItem = ref<Item | null>(null)
const showActionSheet = ref(false)

// 排序选项
const sortOptions = [
  { text: '按名称', value: 'name' },
  { text: '按类型', value: 'type' },
  { text: '按数量', value: 'quantity' },
  { text: '按获得时间', value: 'time' }
]

// 物品操作选项
const itemActions = computed(() => [
  { name: '使用', value: 'use', disabled: !canUseItem(selectedItem.value) },
  { name: '查看详情', value: 'detail' },
  { name: '丢弃', value: 'drop', color: '#ee0a24' }
])

// 计算属性
const inventory = computed(() => gameStore.inventory)

const totalItems = computed(() =>
  inventory.value.reduce((sum, item) => sum + item.quantity, 0)
)

const uniqueItems = computed(() => inventory.value.length)

const totalValue = computed(() => {
  // 简化的价值计算
  return inventory.value.reduce((sum, item) => {
    const baseValue = getItemBaseValue(item)
    return sum + (baseValue * item.quantity)
  }, 0)
})

const filteredItems = computed(() => {
  let items = [...inventory.value]

  // 按类型筛选
  if (activeTab.value !== 'all') {
    items = items.filter(item => item.type === activeTab.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    items = items.filter(item =>
      item.name.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query)
    )
  }

  // 排序
  items.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'type':
        return a.type.localeCompare(b.type)
      case 'quantity':
        return b.quantity - a.quantity
      case 'time':
        // 假设有创建时间字段
        return 0
      default:
        return 0
    }
  })

  return items
})

const totalPages = computed(() =>
  Math.ceil(filteredItems.value.length / itemsPerPage)
)

const paginatedItems = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredItems.value.slice(start, end)
})

// 方法
const getItemIcon = (item: Item): string => {
  const iconMap = {
    food: '🍖',
    toy: '🎾',
    equipment: '⚔️',
    decoration: '🎨',
    medicine: '💊',
    training: '📚'
  }
  return item.icon || iconMap[item.type as keyof typeof iconMap] || '📦'
}

const getTypeText = (type: string): string => {
  const typeMap = {
    food: '食物',
    toy: '玩具',
    equipment: '装备',
    decoration: '装饰',
    medicine: '药品',
    training: '训练用品'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getEffectText = (effect: string, value: number): string => {
  const prefix = value > 0 ? '+' : ''
  const effectMap = {
    health: '生命',
    happiness: '快乐',
    experience: '经验',
    energy: '能量'
  }
  const effectName = effectMap[effect as keyof typeof effectMap] || effect
  return `${prefix}${value} ${effectName}`
}

const getItemBaseValue = (item: Item): number => {
  // 简化的价值计算逻辑
  const baseValues = {
    food: 10,
    toy: 15,
    equipment: 50,
    decoration: 20,
    medicine: 25,
    training: 35
  }
  return baseValues[item.type as keyof typeof baseValues] || 10
}

const canUseItem = (item: Item | null): boolean => {
  if (!item) return false

  // 装备和装饰品不能直接使用
  if (item.type === 'equipment' || item.type === 'decoration') {
    return false
  }

  return item.quantity > 0
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleClearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
}

const handleTabChange = () => {
  currentPage.value = 1
}

const handleItemClick = (item: Item) => {
  selectedItem.value = item
  showItemDetail.value = true
}

const handleUseItem = async (item: Item) => {
  if (!canUseItem(item)) {
    showToast('该物品无法使用')
    return
  }

  const confirmed = await showConfirmDialog({
    title: '使用物品',
    message: `确定要使用 ${item.name} 吗？`,
    confirmButtonText: '确认使用',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (!confirmed) return

  try {
    // 使用物品逻辑
    const success = gameStore.removeItem(item.id, 1)

    if (success) {
      // 应用物品效果
      if (item.effect) {
        // 这里应该调用萌宠系统应用效果
        console.log('应用物品效果:', item.effect)
      }

      showToast({
        type: 'success',
        message: `成功使用 ${item.name}`
      })

      emit('useItem', item)
      showItemDetail.value = false
    } else {
      showToast({
        type: 'fail',
        message: '使用失败'
      })
    }
  } catch (error) {
    console.error('使用物品失败:', error)
    showToast({
      type: 'fail',
      message: '使用失败，请重试'
    })
  }
}

const handleDropItem = async (item: Item) => {
  const confirmed = await showConfirmDialog({
    title: '丢弃物品',
    message: `确定要丢弃 ${item.name} 吗？此操作不可恢复！`,
    confirmButtonText: '确认丢弃',
    cancelButtonText: '取消',
    confirmButtonColor: '#ee0a24'
  }).catch(() => false)

  if (!confirmed) return

  const success = gameStore.removeItem(item.id, item.quantity)

  if (success) {
    showToast({
      type: 'success',
      message: `已丢弃 ${item.name}`
    })
    showItemDetail.value = false
  } else {
    showToast({
      type: 'fail',
      message: '丢弃失败'
    })
  }
}

const handleItemOptions = (item: Item) => {
  selectedItem.value = item
  showActionSheet.value = true
}

const handleActionSelect = (action: any) => {
  if (!selectedItem.value) return

  switch (action.value) {
    case 'use':
      handleUseItem(selectedItem.value)
      break
    case 'detail':
      showItemDetail.value = true
      break
    case 'drop':
      handleDropItem(selectedItem.value)
      break
  }

  showActionSheet.value = false
}

// 监听页面变化重置分页
watch([activeTab, searchQuery], () => {
  currentPage.value = 1
})
</script>

<style scoped>
.inventory-manager {
  @apply bg-gray-50 min-h-screen;
}

.inventory-stats {
  @apply bg-white p-4 flex justify-around border-b;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-sm text-gray-500 mb-1;
}

.stat-value {
  @apply text-lg font-bold text-blue-600;
}

.inventory-filters {
  @apply bg-white;
}

.sort-options {
  @apply bg-white px-4 pb-2 border-b;
}

.inventory-list {
  @apply p-4;
}

.item-grid {
  @apply grid grid-cols-1 gap-4;
}

.inventory-item {
  @apply bg-white rounded-lg p-4 shadow-sm flex items-center cursor-pointer;
  transition: all 0.2s ease;
}

.inventory-item:hover {
  @apply shadow-md transform scale-105;
}

.item-icon {
  @apply text-2xl mr-3 flex-shrink-0;
}

.item-info {
  @apply flex-1 min-w-0;
}

.item-name {
  @apply font-bold text-gray-800 truncate;
}

.item-description {
  @apply text-sm text-gray-600 truncate mt-1;
}

.item-quantity {
  @apply text-xs text-blue-600 mt-1;
}

.item-effects {
  @apply flex flex-wrap gap-1 mt-2;
}

.effect-tag {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs;
}

.item-actions {
  @apply flex flex-col gap-2 ml-3;
}

.pagination {
  @apply mt-6;
}

/* 物品详情弹窗样式 */
.item-detail {
  @apply p-4 h-full flex flex-col;
}

.detail-header {
  @apply flex justify-between items-center mb-4 pb-2 border-b;
}

.detail-header h3 {
  @apply text-lg font-bold;
}

.detail-content {
  @apply flex-1 overflow-y-auto;
}

.detail-icon {
  @apply text-6xl text-center mb-4;
}

.detail-description {
  @apply text-gray-600 mb-4 text-center;
}

.detail-stats {
  @apply space-y-3 mb-6;
}

.stat-row {
  @apply flex justify-between items-center py-2 border-b border-gray-100;
}

.stat-row span:first-child {
  @apply font-medium text-gray-700;
}

.effects-list {
  @apply flex flex-wrap gap-1;
}

.effect-item {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded text-xs;
}

.detail-actions {
  @apply mt-auto pt-4 border-t;
}

/* 响应式设计 */
@media (min-width: 640px) {
  .item-grid {
    @apply grid-cols-2;
  }
}

@media (min-width: 1024px) {
  .item-grid {
    @apply grid-cols-3;
  }
}
</style>
