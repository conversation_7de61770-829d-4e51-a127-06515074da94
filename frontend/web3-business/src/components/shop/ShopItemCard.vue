<template>
  <div class="shop-item-card" :class="rarityClass">
    <div class="item-header">
      <div class="item-icon">{{ item.icon }}</div>
      <div class="item-rarity" :class="`rarity-${item.rarity}`">
        {{ rarityText }}
      </div>
    </div>

    <div class="item-content">
      <h3 class="item-name">{{ item.name }}</h3>
      <p class="item-description">{{ item.description }}</p>

      <!-- 物品效果 -->
      <div v-if="item.effects" class="item-effects">
        <div v-for="(value, effect) in item.effects" :key="effect" class="effect-item">
          <span class="effect-icon">{{ getEffectIcon(effect) }}</span>
          <span class="effect-text">{{ getEffectText(effect, value) }}</span>
        </div>
      </div>

      <!-- 购买需求 -->
      <div v-if="item.requirements" class="item-requirements">
        <div v-if="item.requirements.level" class="requirement">
          <van-icon name="upgrade" />
          <span>需要等级 {{ item.requirements.level }}</span>
        </div>
        <div v-if="item.requirements.rarity" class="requirement">
          <van-icon name="diamond" />
          <span>需要稀有度 {{ item.requirements.rarity }}</span>
        </div>
      </div>
    </div>

    <div class="item-footer">
      <div class="price-section">
        <div class="price">
          <span class="currency-icon">{{ currencyIcon }}</span>
          <span class="price-value">{{ item.price }}</span>
        </div>
        <div class="stock" :class="{ 'low-stock': item.stock < 5 }">
          库存: {{ item.stock }}
        </div>
      </div>

      <div class="action-section">
        <van-stepper
          v-model="quantity"
          :min="1"
          :max="Math.min(item.stock, 99)"
          :disabled="!canPurchase"
          class="quantity-stepper"
        />

        <van-button
          type="primary"
          size="small"
          :disabled="!canPurchase"
          :loading="purchasing"
          @click="handlePurchase"
          class="purchase-btn"
        >
          {{ purchaseButtonText }}
        </van-button>
      </div>
    </div>

    <!-- 限时标签 -->
    <div v-if="item.isLimited" class="limited-badge">
      <van-icon name="clock" />
      <span>限时</span>
    </div>

    <!-- 推荐标签 -->
    <div v-if="isRecommended" class="recommended-badge">
      <van-icon name="star" />
      <span>推荐</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import type { ShopItem } from '../../stores/shop'
import { useShopStore } from '../../stores/shop'
import { useGameStore } from '../../stores/game'

interface Props {
  item: ShopItem
  isRecommended?: boolean
}

const props = defineProps<Props>()

const shopStore = useShopStore()
const gameStore = useGameStore()

const quantity = ref(1)
const purchasing = ref(false)

// 计算属性
const rarityClass = computed(() => `rarity-${props.item.rarity}`)

const rarityText = computed(() => {
  const rarityMap = {
    common: '普通',
    uncommon: '优秀',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return rarityMap[props.item.rarity as keyof typeof rarityMap] || '未知'
})

const currencyIcon = computed(() => {
  return props.item.currency === 'coins' ? '🪙' : '💎'
})

const canPurchase = computed(() => {
  const result = shopStore.canPurchase(props.item.id, quantity.value)
  return result.canBuy && props.item.stock > 0
})

const purchaseButtonText = computed(() => {
  if (props.item.stock === 0) return '缺货'
  if (!canPurchase.value) {
    const result = shopStore.canPurchase(props.item.id, quantity.value)
    return result.reason || '无法购买'
  }
  const totalPrice = props.item.price * quantity.value
  return `购买 ${totalPrice}${currencyIcon.value}`
})

// 方法
const getEffectIcon = (effect: string): string => {
  const iconMap = {
    health: '❤️',
    happiness: '😊',
    experience: '⭐',
    energy: '⚡'
  }
  return iconMap[effect as keyof typeof iconMap] || '✨'
}

const getEffectText = (effect: string, value: number): string => {
  const prefix = value > 0 ? '+' : ''
  const effectMap = {
    health: '生命值',
    happiness: '快乐度',
    experience: '经验值',
    energy: '能量'
  }
  const effectName = effectMap[effect as keyof typeof effectMap] || effect
  return `${prefix}${value} ${effectName}`
}

const handlePurchase = async () => {
  if (!canPurchase.value) {
    const result = shopStore.canPurchase(props.item.id, quantity.value)
    showToast(result.reason || '无法购买')
    return
  }

  const totalPrice = props.item.price * quantity.value
  const confirmed = await showConfirmDialog({
    title: '确认购买',
    message: `确定要购买 ${props.item.name} x${quantity.value} 吗？\n总价: ${totalPrice}${currencyIcon.value}`,
    confirmButtonText: '确认购买',
    cancelButtonText: '取消'
  }).catch(() => false)

  if (!confirmed) return

  purchasing.value = true

  try {
    const result = await shopStore.purchaseItem(props.item.id, quantity.value)

    if (result.success) {
      showToast({
        type: 'success',
        message: result.message
      })
      quantity.value = 1 // 重置数量
    } else {
      showToast({
        type: 'fail',
        message: result.message
      })
    }
  } catch (error) {
    console.error('购买失败:', error)
    showToast({
      type: 'fail',
      message: '购买失败，请重试'
    })
  } finally {
    purchasing.value = false
  }
}
</script>

<style scoped>
.shop-item-card {
  @apply bg-white rounded-lg shadow-md p-4 relative overflow-hidden;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.shop-item-card:hover {
  @apply shadow-lg transform -translate-y-1;
}

/* 稀有度边框颜色 */
.rarity-common {
  border-color: #9ca3af;
}

.rarity-uncommon {
  border-color: #10b981;
}

.rarity-rare {
  border-color: #3b82f6;
}

.rarity-epic {
  border-color: #8b5cf6;
}

.rarity-legendary {
  border-color: #f59e0b;
}

.rarity-mythical {
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.item-header {
  @apply flex justify-between items-start mb-3;
}

.item-icon {
  @apply text-3xl;
}

.item-rarity {
  @apply px-2 py-1 rounded-full text-xs font-bold text-white;
}

.rarity-common .item-rarity {
  @apply bg-gray-500;
}

.rarity-uncommon .item-rarity {
  @apply bg-green-500;
}

.rarity-rare .item-rarity {
  @apply bg-blue-500;
}

.rarity-epic .item-rarity {
  @apply bg-purple-500;
}

.rarity-legendary .item-rarity {
  @apply bg-yellow-500;
}

.rarity-mythical .item-rarity {
  @apply bg-red-500;
}

.item-content {
  @apply mb-4;
}

.item-name {
  @apply text-lg font-bold mb-2 text-gray-800;
}

.item-description {
  @apply text-sm text-gray-600 mb-3;
}

.item-effects {
  @apply space-y-1 mb-3;
}

.effect-item {
  @apply flex items-center text-sm;
}

.effect-icon {
  @apply mr-2;
}

.effect-text {
  @apply text-green-600 font-medium;
}

.item-requirements {
  @apply space-y-1 mb-3;
}

.requirement {
  @apply flex items-center text-xs text-orange-600;
}

.requirement .van-icon {
  @apply mr-1;
}

.item-footer {
  @apply space-y-3;
}

.price-section {
  @apply flex justify-between items-center;
}

.price {
  @apply flex items-center font-bold text-lg;
}

.currency-icon {
  @apply mr-1;
}

.price-value {
  @apply text-blue-600;
}

.stock {
  @apply text-sm text-gray-500;
}

.low-stock {
  @apply text-red-500 font-medium;
}

.action-section {
  @apply flex justify-between items-center;
}

.quantity-stepper {
  @apply flex-shrink-0;
}

.purchase-btn {
  @apply ml-3 flex-1;
}

.limited-badge {
  @apply absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center;
}

.limited-badge .van-icon {
  @apply mr-1;
}

.recommended-badge {
  @apply absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center;
}

.recommended-badge .van-icon {
  @apply mr-1;
}

/* 动画效果 */
.shop-item-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
