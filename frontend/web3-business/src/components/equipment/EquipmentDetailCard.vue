<template>
  <div class="equipment-detail-card p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold">装备详情</h3>
      <van-icon name="cross" @click="$emit('close')" class="cursor-pointer" />
    </div>

    <div class="equipment-info text-center mb-6">
      <div class="equipment-icon text-4xl mb-2">⚔️</div>
      <h4 class="text-lg font-bold">{{ equipment.name }}</h4>
      <div class="text-sm text-gray-500">{{ equipment.description }}</div>
    </div>

    <div class="equipment-stats mb-6">
      <h5 class="font-medium mb-2">属性加成</h5>
      <div class="stats-list space-y-1">
        <div v-for="(value, stat) in equipment.stats" :key="stat" class="flex justify-between">
          <span>{{ stat }}:</span>
          <span class="text-green-600">+{{ value }}</span>
        </div>
      </div>
    </div>

    <div class="equipment-actions space-y-2">
      <van-button type="primary" @click="$emit('enhance', equipment.id, 1)" class="w-full">
        强化装备
      </van-button>
      <van-button @click="$emit('repair', equipment.id)" class="w-full">
        修理装备
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Equipment } from '../../types/typesWithoutCircular'

interface Props {
  equipment: Equipment
}

interface Emits {
  (e: 'close'): void
  (e: 'enhance', equipmentId: string, level: number): void
  (e: 'repair', equipmentId: string): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
