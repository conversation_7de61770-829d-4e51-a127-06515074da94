<template>
  <div class="token-balance-display">
    <!-- 简单模式 -->
    <div v-if="mode === 'simple'" class="simple-balance">
      <div class="balance-amount">
        {{ formattedBalance }} {{ balanceSymbol }}
      </div>
      <van-button
        v-if="showRefresh"
        size="mini"
        type="primary"
        plain
        @click="handleRefresh"
        :loading="balanceLoading"
        class="ml-2"
      >
        刷新
      </van-button>
    </div>

    <!-- 卡片模式 -->
    <van-card v-else-if="mode === 'card'" class="balance-card">
      <template #title>
        <div class="flex items-center justify-between">
          <span class="text-lg font-bold">代币余额</span>
          <van-button
            v-if="showRefresh"
            size="small"
            type="primary"
            plain
            @click="handleRefresh"
            :loading="balanceLoading"
          >
            刷新
          </van-button>
        </div>
      </template>
      <template #desc>
        <div class="balance-info">
          <div class="balance-amount text-2xl font-bold mb-2">
            {{ formattedBalance }} {{ balanceSymbol }}
          </div>
          <div v-if="showAddress" class="balance-address text-sm text-gray-500">
            {{ walletStore.shortAddress }}
          </div>
          <div v-if="showLastUpdated" class="balance-updated text-xs text-gray-400 mt-1">
            最后更新: {{ formatTime(tokenBalance?.lastUpdated) }}
          </div>
        </div>
      </template>
    </van-card>

    <!-- 详细模式 -->
    <div v-else-if="mode === 'detailed'" class="detailed-balance">
      <van-cell-group>
        <van-cell
          title="代币余额"
          :value="`${formattedBalance} ${balanceSymbol}`"
          :label="showAddress ? walletStore.shortAddress : undefined"
        >
          <template #right-icon>
            <van-button
              v-if="showRefresh"
              size="mini"
              type="primary"
              plain
              @click="handleRefresh"
              :loading="balanceLoading"
            >
              刷新
            </van-button>
          </template>
        </van-cell>
        <van-cell
          v-if="showLastUpdated && tokenBalance"
          title="最后更新"
          :value="formatTime(tokenBalance.lastUpdated)"
        />
        <van-cell
          v-if="showDecimals && tokenBalance"
          title="精度"
          :value="tokenBalance.decimals.toString()"
        />
      </van-cell-group>
    </div>

    <!-- 加载状态 -->
    <div v-if="balanceLoading && !tokenBalance" class="loading-state">
      <van-loading size="20" />
      <span class="ml-2 text-gray-500">加载余额中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-if="!walletStore.isConnected" class="error-state">
      <van-empty
        image="network"
        description="请先连接钱包"
        :image-size="60"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWalletStore } from '../stores/wallet'
import { useTokenManagement } from '../composables/useTokenManagement'
import { showToast } from 'vant'

interface Props {
  mode?: 'simple' | 'card' | 'detailed'
  showRefresh?: boolean
  showAddress?: boolean
  showLastUpdated?: boolean
  showDecimals?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'simple',
  showRefresh: true,
  showAddress: false,
  showLastUpdated: false,
  showDecimals: false,
  clickable: false
})

const emit = defineEmits<{
  click: []
  refresh: []
}>()

const walletStore = useWalletStore()
const {
  tokenBalance,
  balanceLoading,
  formattedBalance,
  balanceSymbol,
  refreshBalance
} = useTokenManagement()

// 计算属性
const displayClass = computed(() => {
  return {
    'cursor-pointer': props.clickable,
    'hover:bg-gray-50': props.clickable
  }
})

// 方法
const handleRefresh = async () => {
  try {
    await refreshBalance()
    emit('refresh')
  } catch (error) {
    console.error('刷新余额失败:', error)
    showToast('刷新余额失败')
  }
}

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

// 工具函数
const formatTime = (timestamp?: number) => {
  if (!timestamp) return '未知'

  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}
</script>

<style scoped>
.token-balance-display {
  width: 100%;
}

.simple-balance {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.balance-amount {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.balance-card {
  border-radius: 8px;
  overflow: hidden;
}

.balance-info {
  padding: 8px 0;
}

.detailed-balance {
  border-radius: 8px;
  overflow: hidden;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-state {
  padding: 20px;
  text-align: center;
}

.cursor-pointer {
  cursor: pointer;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}
</style>
