<template>
  <div class="token-system">
    <!-- 代币系统概述 -->
    <div class="token-section">
      <h3 class="section-title">💰 代币系统介绍</h3>
      <div class="section-content">
        <p class="intro-text">
          萌宠养成代币游戏的核心特色是将游戏内的萌宠养成与区块链代币经济相结合。
          通过养成高价值的萌宠，玩家可以将其兑换成真实的区块链代币，这些代币可以在游戏内使用，也可以在区块链生态中流通。
        </p>
      </div>
    </div>

    <!-- 代币类型 -->
    <div class="token-section">
      <h3 class="section-title">🪙 代币类型</h3>
      <div class="token-types">
        <div class="token-type">
          <div class="token-icon game-coin">🪙</div>
          <div class="token-info">
            <h4 class="token-name">游戏币</h4>
            <p class="token-desc">游戏内部货币，用于购买基础道具和食物</p>
            <div class="token-features">
              <div class="feature">
                <span class="feature-label">获取方式</span>
                <span class="feature-value">日常任务、成就奖励</span>
              </div>
              <div class="feature">
                <span class="feature-label">用途</span>
                <span class="feature-value">购买普通道具、食物</span>
              </div>
            </div>
          </div>
        </div>

        <div class="token-type">
          <div class="token-icon blockchain-token">💎</div>
          <div class="token-info">
            <h4 class="token-name">区块链代币 (MyToken)</h4>
            <p class="token-desc">基于以太坊的ERC20代币，可在区块链生态中流通</p>
            <div class="token-features">
              <div class="feature">
                <span class="feature-label">获取方式</span>
                <span class="feature-value">萌宠兑换、直接购买</span>
              </div>
              <div class="feature">
                <span class="feature-label">用途</span>
                <span class="feature-value">购买稀有道具、外部交易</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 兑换机制 -->
    <div class="token-section">
      <h3 class="section-title">🔄 兑换机制</h3>
      <div class="exchange-process">
        <div class="process-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h5 class="step-title">养成萌宠</h5>
            <p class="step-desc">提升萌宠等级、稀有度和装备</p>
          </div>
        </div>

        <div class="process-arrow">→</div>

        <div class="process-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h5 class="step-title">计算价值</h5>
            <p class="step-desc">系统根据萌宠属性计算代币价值</p>
          </div>
        </div>

        <div class="process-arrow">→</div>

        <div class="process-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h5 class="step-title">确认兑换</h5>
            <p class="step-desc">确认交易并支付Gas费用</p>
          </div>
        </div>

        <div class="process-arrow">→</div>

        <div class="process-step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h5 class="step-title">获得代币</h5>
            <p class="step-desc">代币直接发送到你的钱包</p>
          </div>
        </div>
      </div>

      <div class="exchange-note">
        <van-icon name="warning-o" class="note-icon" />
        <p class="note-text">
          <strong>重要提示：</strong> 兑换是不可逆操作，萌宠兑换后将永久消失。请确保萌宠达到理想状态再进行兑换。
        </p>
      </div>
    </div>

    <!-- 价值计算 -->
    <div class="token-section">
      <h3 class="section-title">📊 价值计算</h3>
      <div class="value-calculation">
        <div class="formula-box">
          <h4 class="formula-title">萌宠价值计算公式</h4>
          <div class="formula">
            <p class="formula-text">代币价值 = 基础价值 × 稀有度加成 × 健康度加成 × 装备加成</p>
          </div>
        </div>

        <div class="factors">
          <div class="factor">
            <h5 class="factor-title">基础价值</h5>
            <p class="factor-desc">基于萌宠等级，每级提升约20%价值</p>
            <div class="factor-example">
              <span class="example-label">例：5级萌宠</span>
              <span class="example-value">≈ 50 基础代币</span>
            </div>
          </div>

          <div class="factor">
            <h5 class="factor-title">稀有度加成</h5>
            <p class="factor-desc">不同稀有度有不同倍率加成</p>
            <div class="rarity-multipliers">
              <div class="rarity common">
                <span class="rarity-name">普通</span>
                <span class="multiplier">×1.0</span>
              </div>
              <div class="rarity uncommon">
                <span class="rarity-name">罕见</span>
                <span class="multiplier">×1.5</span>
              </div>
              <div class="rarity rare">
                <span class="rarity-name">稀有</span>
                <span class="multiplier">×2.0</span>
              </div>
              <div class="rarity epic">
                <span class="rarity-name">史诗</span>
                <span class="multiplier">×3.0</span>
              </div>
              <div class="rarity legendary">
                <span class="rarity-name">传说</span>
                <span class="multiplier">×5.0</span>
              </div>
            </div>
          </div>

          <div class="factor">
            <h5 class="factor-title">健康度加成</h5>
            <p class="factor-desc">健康度越高，加成越大</p>
            <div class="health-multipliers">
              <div class="health-range">
                <span class="range">0-20%</span>
                <span class="multiplier">×0.5</span>
              </div>
              <div class="health-range">
                <span class="range">21-50%</span>
                <span class="multiplier">×0.8</span>
              </div>
              <div class="health-range">
                <span class="range">51-80%</span>
                <span class="multiplier">×1.0</span>
              </div>
              <div class="health-range">
                <span class="range">81-100%</span>
                <span class="multiplier">×1.2</span>
              </div>
            </div>
          </div>

          <div class="factor">
            <h5 class="factor-title">装备加成</h5>
            <p class="factor-desc">装备数量和稀有度提供额外加成</p>
            <div class="equipment-bonus">
              <div class="bonus-item">
                <span class="bonus-label">每件普通装备</span>
                <span class="bonus-value">+5%</span>
              </div>
              <div class="bonus-item">
                <span class="bonus-label">每件稀有装备</span>
                <span class="bonus-value">+10%</span>
              </div>
              <div class="bonus-item">
                <span class="bonus-label">每件史诗装备</span>
                <span class="bonus-value">+20%</span>
              </div>
              <div class="bonus-item">
                <span class="bonus-label">每件传说装备</span>
                <span class="bonus-value">+30%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 代币用途 -->
    <div class="token-section">
      <h3 class="section-title">🛒 代币用途</h3>
      <div class="token-usage">
        <div class="usage-card">
          <div class="usage-icon">🛍️</div>
          <h4 class="usage-title">购买稀有道具</h4>
          <p class="usage-desc">使用代币购买游戏内的稀有装备和特殊道具</p>
        </div>

        <div class="usage-card">
          <div class="usage-icon">🔄</div>
          <h4 class="usage-title">交易流通</h4>
          <p class="usage-desc">在支持的交易平台上交易代币</p>
        </div>

        <div class="usage-card">
          <div class="usage-icon">🎁</div>
          <h4 class="usage-title">赠送好友</h4>
          <p class="usage-desc">将代币转账给其他玩家或朋友</p>
        </div>

        <div class="usage-card">
          <div class="usage-icon">💼</div>
          <h4 class="usage-title">长期持有</h4>
          <p class="usage-desc">作为数字资产长期持有</p>
        </div>
      </div>
    </div>

    <!-- 智能合约信息 -->
    <div class="token-section">
      <h3 class="section-title">📝 智能合约</h3>
      <div class="contract-info">
        <div class="info-item">
          <span class="info-label">代币名称</span>
          <span class="info-value">MyToken (MTK)</span>
        </div>
        <div class="info-item">
          <span class="info-label">合约类型</span>
          <span class="info-value">ERC20 (可升级)</span>
        </div>
        <div class="info-item">
          <span class="info-label">总供应量</span>
          <span class="info-value">动态 (基于兑换)</span>
        </div>
        <div class="info-item">
          <span class="info-label">网络</span>
          <span class="info-value">以太坊测试网</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要是静态内容展示
</script>

<style scoped>
.token-system {
  @apply space-y-8;
}

.token-section {
  @apply bg-white rounded-lg p-6 shadow-sm;
}

.section-title {
  @apply text-xl font-bold text-gray-800 mb-4;
}

.section-content {
  @apply space-y-4;
}

.intro-text {
  @apply text-gray-600 leading-relaxed;
}

.token-types {
  @apply space-y-4;
}

.token-type {
  @apply flex items-start space-x-4 bg-gray-50 rounded-lg p-4;
}

.token-icon {
  @apply w-12 h-12 rounded-full flex items-center justify-center text-2xl flex-shrink-0;
}

.token-icon.game-coin {
  @apply bg-yellow-100;
}

.token-icon.blockchain-token {
  @apply bg-blue-100;
}

.token-info {
  @apply flex-1;
}

.token-name {
  @apply text-lg font-semibold text-gray-800 mb-1;
}

.token-desc {
  @apply text-sm text-gray-600 mb-3;
}

.token-features {
  @apply space-y-2;
}

.feature {
  @apply flex flex-col sm:flex-row sm:justify-between bg-white rounded-lg p-2 text-sm;
}

.feature-label {
  @apply font-medium text-gray-700;
}

.feature-value {
  @apply text-gray-600;
}

.exchange-process {
  @apply flex flex-col md:flex-row items-start md:items-center justify-between space-y-4 md:space-y-0 md:space-x-2;
}

.process-step {
  @apply flex flex-row md:flex-col items-center md:text-center space-x-3 md:space-x-0;
}

.step-number {
  @apply w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0;
}

.step-content {
  @apply md:mt-2;
}

.step-title {
  @apply font-semibold text-gray-800 mb-1;
}

.step-desc {
  @apply text-xs text-gray-600;
}

.process-arrow {
  @apply hidden md:block text-gray-400 text-xl;
}

.exchange-note {
  @apply flex items-start space-x-2 mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-3;
}

.note-icon {
  @apply text-yellow-500 flex-shrink-0 mt-0.5;
}

.note-text {
  @apply text-sm text-yellow-700;
}

.value-calculation {
  @apply space-y-6;
}

.formula-box {
  @apply bg-blue-50 rounded-lg p-4 text-center;
}

.formula-title {
  @apply font-semibold text-blue-800 mb-2;
}

.formula {
  @apply bg-white rounded-lg p-3;
}

.formula-text {
  @apply text-sm font-mono text-blue-900;
}

.factors {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.factor {
  @apply bg-gray-50 rounded-lg p-4;
}

.factor-title {
  @apply font-semibold text-gray-800 mb-2;
}

.factor-desc {
  @apply text-sm text-gray-600 mb-3;
}

.factor-example {
  @apply flex justify-between items-center bg-white rounded-lg px-3 py-2 text-sm;
}

.example-label {
  @apply text-gray-600;
}

.example-value {
  @apply font-semibold text-green-600;
}

.rarity-multipliers {
  @apply space-y-1;
}

.rarity {
  @apply flex justify-between items-center px-3 py-1 rounded-md text-xs;
}

.rarity.common {
  @apply bg-gray-200 text-gray-800;
}

.rarity.uncommon {
  @apply bg-green-200 text-green-800;
}

.rarity.rare {
  @apply bg-blue-200 text-blue-800;
}

.rarity.epic {
  @apply bg-purple-200 text-purple-800;
}

.rarity.legendary {
  @apply bg-yellow-200 text-yellow-800;
}

.health-multipliers {
  @apply space-y-1;
}

.health-range {
  @apply flex justify-between items-center bg-white rounded-md px-3 py-1 text-xs;
}

.equipment-bonus {
  @apply space-y-1;
}

.bonus-item {
  @apply flex justify-between items-center bg-white rounded-md px-3 py-1 text-xs;
}

.token-usage {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4;
}

.usage-card {
  @apply bg-gray-50 rounded-lg p-4 text-center;
}

.usage-icon {
  @apply text-3xl mb-2;
}

.usage-title {
  @apply font-semibold text-gray-800 mb-1;
}

.usage-desc {
  @apply text-sm text-gray-600;
}

.contract-info {
  @apply bg-gray-50 rounded-lg p-4 space-y-2;
}

.info-item {
  @apply flex justify-between items-center bg-white rounded-md px-3 py-2 text-sm;
}

.info-label {
  @apply font-medium text-gray-700;
}

.info-value {
  @apply text-gray-600;
}
</style>
