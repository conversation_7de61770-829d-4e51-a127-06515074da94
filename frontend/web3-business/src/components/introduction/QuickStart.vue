<template>
  <div class="quick-start">
    <!-- 快速开始指南 -->
    <div class="start-section">
      <h3 class="section-title">🚀 快速开始指南</h3>
      <div class="section-content">
        <p class="intro-text">
          欢迎来到萌宠养成代币游戏！这个指南将帮助你快速上手游戏，开始你的萌宠养成之旅。
          按照以下步骤操作，你很快就能体验到游戏的乐趣。
        </p>
      </div>
    </div>

    <!-- 步骤指引 -->
    <div class="steps-container">
      <div class="step-card">
        <div class="step-header">
          <div class="step-number">1</div>
          <h4 class="step-title">连接钱包</h4>
        </div>
        <div class="step-content">
          <div class="step-image">
            <img src="/images/wallets/metamask.svg" alt="MetaMask" class="wallet-icon" />
            <img src="/images/wallets/walletconnect.svg" alt="WalletConnect" class="wallet-icon" />
          </div>
          <div class="step-instructions">
            <p class="instruction-text">
              点击首页右上角的"连接钱包"按钮，选择你的钱包类型（MetaMask或WalletConnect）。
              确保你的钱包已连接到正确的测试网络。
            </p>
            <div class="instruction-tips">
              <van-icon name="info-o" class="tip-icon" />
              <span class="tip-text">首次连接需要授权，请在钱包中确认连接请求。</span>
            </div>
          </div>
        </div>
      </div>

      <div class="step-card">
        <div class="step-header">
          <div class="step-number">2</div>
          <h4 class="step-title">创建萌宠</h4>
        </div>
        <div class="step-content">
          <div class="step-image">
            <div class="pet-placeholder">
              <span class="pet-emoji">🐱</span>
            </div>
          </div>
          <div class="step-instructions">
            <p class="instruction-text">
              首次进入游戏后，系统会引导你创建你的第一只萌宠。
              你可以为萌宠命名，系统会随机生成萌宠的基础属性和外观。
            </p>
            <div class="instruction-tips">
              <van-icon name="info-o" class="tip-icon" />
              <span class="tip-text">萌宠的稀有度是随机的，越稀有的萌宠价值越高。</span>
            </div>
          </div>
        </div>
      </div>

      <div class="step-card">
        <div class="step-header">
          <div class="step-number">3</div>
          <h4 class="step-title">养成萌宠</h4>
        </div>
        <div class="step-content">
          <div class="step-image">
            <div class="action-icons">
              <span class="action-icon">🍖</span>
              <span class="action-icon">🏋️</span>
              <span class="action-icon">🧸</span>
              <span class="action-icon">🛌</span>
            </div>
          </div>
          <div class="step-instructions">
            <p class="instruction-text">
              通过喂食、训练、互动和休息来提升萌宠的属性。
              定期照顾萌宠，保持健康度，提升等级和经验值。
            </p>
            <div class="instruction-tips">
              <van-icon name="info-o" class="tip-icon" />
              <span class="tip-text">平衡各种养成活动，避免萌宠健康度过低。</span>
            </div>
          </div>
        </div>
      </div>

      <div class="step-card">
        <div class="step-header">
          <div class="step-number">4</div>
          <h4 class="step-title">装备道具</h4>
        </div>
        <div class="step-content">
          <div class="step-image">
            <div class="equipment-icons">
              <span class="equipment-icon">👑</span>
              <span class="equipment-icon">🧣</span>
              <span class="equipment-icon">🦺</span>
              <span class="equipment-icon">🧤</span>
            </div>
          </div>
          <div class="step-instructions">
            <p class="instruction-text">
              在商店购买装备，为萌宠装备各种道具来增强属性。
              不同类型和稀有度的装备有不同的效果加成。
            </p>
            <div class="instruction-tips">
              <van-icon name="info-o" class="tip-icon" />
              <span class="tip-text">根据萌宠特点选择合适的装备可以事半功倍。</span>
            </div>
          </div>
        </div>
      </div>

      <div class="step-card">
        <div class="step-header">
          <div class="step-number">5</div>
          <h4 class="step-title">兑换代币</h4>
        </div>
        <div class="step-content">
          <div class="step-image">
            <div class="token-icon">💰</div>
          </div>
          <div class="step-instructions">
            <p class="instruction-text">
              当萌宠达到理想状态时，可以将其兑换成代币。
              点击"兑换代币"按钮，确认交易，代币将直接发送到你的钱包。
            </p>
            <div class="instruction-tips warning">
              <van-icon name="warning-o" class="tip-icon" />
              <span class="tip-text">兑换是不可逆操作，萌宠兑换后将永久消失。</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷键和操作提示 -->
    <div class="start-section">
      <h3 class="section-title">⌨️ 快捷操作</h3>
      <div class="shortcuts-grid">
        <div class="shortcut-item">
          <div class="shortcut-key">F</div>
          <span class="shortcut-desc">喂食萌宠</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">T</div>
          <span class="shortcut-desc">训练萌宠</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">P</div>
          <span class="shortcut-desc">与萌宠互动</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">R</div>
          <span class="shortcut-desc">让萌宠休息</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">I</div>
          <span class="shortcut-desc">打开背包</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">S</div>
          <span class="shortcut-desc">打开商店</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">H</div>
          <span class="shortcut-desc">查看帮助</span>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">Esc</div>
          <span class="shortcut-desc">关闭弹窗</span>
        </div>
      </div>
    </div>

    <!-- 开始游戏按钮 -->
    <div class="start-action">
      <van-button
        type="primary"
        size="large"
        block
        @click="startGame"
        class="start-button"
      >
        立即开始游戏
      </van-button>
      <p class="start-note">
        准备好了吗？点击上方按钮开始你的萌宠养成之旅！
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const startGame = () => {
  router.push('/')
}
</script>

<style scoped>
.quick-start {
  @apply space-y-8;
}

.start-section {
  @apply bg-white rounded-lg p-6 shadow-sm;
}

.section-title {
  @apply text-xl font-bold text-gray-800 mb-4;
}

.section-content {
  @apply space-y-4;
}

.intro-text {
  @apply text-gray-600 leading-relaxed;
}

.steps-container {
  @apply space-y-4;
}

.step-card {
  @apply bg-white rounded-lg shadow-sm overflow-hidden;
}

.step-header {
  @apply flex items-center space-x-3 bg-gradient-to-r from-blue-500 to-purple-500 p-4 text-white;
}

.step-number {
  @apply w-8 h-8 bg-white text-blue-500 rounded-full flex items-center justify-center text-lg font-bold;
}

.step-title {
  @apply text-lg font-semibold;
}

.step-content {
  @apply p-4 flex flex-col md:flex-row md:items-center;
}

.step-image {
  @apply flex justify-center items-center mb-4 md:mb-0 md:w-1/3 md:pr-4;
}

.wallet-icon {
  @apply h-12 mx-2;
}

.pet-placeholder {
  @apply w-20 h-20 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center;
}

.pet-emoji {
  @apply text-4xl;
}

.action-icons {
  @apply flex space-x-2;
}

.action-icon {
  @apply text-2xl bg-gray-100 w-10 h-10 rounded-full flex items-center justify-center;
}

.equipment-icons {
  @apply flex space-x-2;
}

.equipment-icon {
  @apply text-2xl bg-gray-100 w-10 h-10 rounded-full flex items-center justify-center;
}

.token-icon {
  @apply text-4xl bg-yellow-100 w-20 h-20 rounded-full flex items-center justify-center;
}

.step-instructions {
  @apply md:w-2/3;
}

.instruction-text {
  @apply text-gray-600 mb-3;
}

.instruction-tips {
  @apply flex items-start space-x-2 bg-blue-50 rounded-lg p-3;
}

.instruction-tips.warning {
  @apply bg-yellow-50;
}

.tip-icon {
  @apply text-blue-500 flex-shrink-0 mt-0.5;
}

.instruction-tips.warning .tip-icon {
  @apply text-yellow-500;
}

.tip-text {
  @apply text-sm text-blue-700;
}

.instruction-tips.warning .tip-text {
  @apply text-yellow-700;
}

.shortcuts-grid {
  @apply grid grid-cols-2 sm:grid-cols-4 gap-3;
}

.shortcut-item {
  @apply flex items-center space-x-2 bg-gray-50 rounded-lg p-3;
}

.shortcut-key {
  @apply bg-gray-200 text-gray-800 rounded px-2 py-1 text-sm font-mono font-semibold;
}

.shortcut-desc {
  @apply text-sm text-gray-600;
}

.start-action {
  @apply text-center;
}

.start-button {
  @apply bg-gradient-to-r from-green-500 to-blue-500 mb-3;
}

.start-note {
  @apply text-sm text-gray-500;
}
</style>