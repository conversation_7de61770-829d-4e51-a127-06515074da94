<template>
  <div class="gameplay-intro">
    <!-- 游戏玩法概述 -->
    <div class="intro-section">
      <h3 class="section-title">🎮 游戏玩法</h3>
      <div class="section-content">
        <p class="intro-text">
          萌宠养成代币游戏的核心玩法是通过各种互动方式养成你的萌宠，提升其属性和价值，最终将其兑换成代币。
          游戏结合了传统养成游戏的乐趣和区块链代币的价值，为玩家提供了一种全新的游戏体验。
        </p>
      </div>
    </div>

    <!-- 养成系统 -->
    <div class="intro-section">
      <h3 class="section-title">🐱 萌宠养成</h3>
      <div class="nurturing-grid">
        <div class="nurturing-card">
          <div class="card-icon">🍖</div>
          <h4 class="card-title">喂食系统</h4>
          <p class="card-desc">
            通过喂食不同的食物来提升萌宠的健康度和经验值。
            食物种类丰富，效果各异，合理搭配可以加速萌宠成长。
          </p>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-label">冷却时间</span>
              <span class="stat-value">3秒</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">健康度提升</span>
              <span class="stat-value">+5~20</span>
            </div>
          </div>
        </div>

        <div class="nurturing-card">
          <div class="card-icon">🏋️</div>
          <h4 class="card-title">训练系统</h4>
          <p class="card-desc">
            通过各种训练活动快速提升萌宠的经验值和特定属性。
            训练会消耗健康度，需要合理安排训练和休息时间。
          </p>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-label">冷却时间</span>
              <span class="stat-value">5秒</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">经验值提升</span>
              <span class="stat-value">+10~30</span>
            </div>
          </div>
        </div>

        <div class="nurturing-card">
          <div class="card-icon">🧸</div>
          <h4 class="card-title">互动系统</h4>
          <p class="card-desc">
            与萌宠进行各种互动，提升萌宠的快乐度和亲密度。
            高快乐度的萌宠成长速度更快，属性提升更明显。
          </p>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-label">冷却时间</span>
              <span class="stat-value">2秒</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">快乐度提升</span>
              <span class="stat-value">+5~15</span>
            </div>
          </div>
        </div>

        <div class="nurturing-card">
          <div class="card-icon">🛌</div>
          <h4 class="card-title">休息系统</h4>
          <p class="card-desc">
            让萌宠休息可以恢复健康度和消除疲劳状态。
            定期休息对萌宠的长期成长至关重要。
          </p>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-label">恢复时间</span>
              <span class="stat-value">30秒</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">健康度恢复</span>
              <span class="stat-value">+30~50</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 装备系统 -->
    <div class="intro-section">
      <h3 class="section-title">🎒 装备系统</h3>
      <div class="section-content">
        <p class="intro-text">
          萌宠可以装备各种道具来增强属性。装备分为不同类型和稀有度，效果各异。
        </p>

        <div class="equipment-types">
          <div class="equipment-type">
            <div class="type-icon">👑</div>
            <div class="type-info">
              <h5 class="type-name">头部装饰</h5>
              <p class="type-effect">提升魅力和稀有度加成</p>
            </div>
          </div>

          <div class="equipment-type">
            <div class="type-icon">🧣</div>
            <div class="type-info">
              <h5 class="type-name">颈部装饰</h5>
              <p class="type-effect">提升健康度上限</p>
            </div>
          </div>

          <div class="equipment-type">
            <div class="type-icon">🦺</div>
            <div class="type-info">
              <h5 class="type-name">身体装备</h5>
              <p class="type-effect">提升防御力和耐久度</p>
            </div>
          </div>

          <div class="equipment-type">
            <div class="type-icon">🧤</div>
            <div class="type-info">
              <h5 class="type-name">特殊装备</h5>
              <p class="type-effect">提供独特能力和效果</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成长系统 -->
    <div class="intro-section">
      <h3 class="section-title">📈 成长系统</h3>
      <div class="growth-system">
        <div class="level-chart">
          <div class="chart-header">
            <span class="chart-title">等级经验需求</span>
          </div>
          <div class="chart-body">
            <div class="chart-row">
              <span class="level">1级</span>
              <div class="exp-bar" style="width: 20%"></div>
              <span class="exp-value">100经验</span>
            </div>
            <div class="chart-row">
              <span class="level">2级</span>
              <div class="exp-bar" style="width: 30%"></div>
              <span class="exp-value">150经验</span>
            </div>
            <div class="chart-row">
              <span class="level">3级</span>
              <div class="exp-bar" style="width: 45%"></div>
              <span class="exp-value">225经验</span>
            </div>
            <div class="chart-row">
              <span class="level">4级</span>
              <div class="exp-bar" style="width: 60%"></div>
              <span class="exp-value">340经验</span>
            </div>
            <div class="chart-row">
              <span class="level">5级</span>
              <div class="exp-bar" style="width: 80%"></div>
              <span class="exp-value">500经验</span>
            </div>
          </div>
        </div>

        <div class="growth-info">
          <h4 class="info-title">成长特点</h4>
          <ul class="info-list">
            <li>萌宠等级越高，兑换价值越大</li>
            <li>每提升一级，萌宠会获得属性点</li>
            <li>高等级解锁更多互动和装备选项</li>
            <li>等级提升所需经验呈指数增长</li>
            <li>不同稀有度的萌宠成长速度不同</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 互动提示 -->
    <div class="intro-section">
      <h3 class="section-title">💡 游戏技巧</h3>
      <div class="tips-grid">
        <div class="tip-card">
          <van-icon name="star-o" class="tip-icon" />
          <div class="tip-content">
            <h5 class="tip-title">平衡发展</h5>
            <p class="tip-text">平衡萌宠的各项属性，避免单一发展</p>
          </div>
        </div>

        <div class="tip-card">
          <van-icon name="star-o" class="tip-icon" />
          <div class="tip-content">
            <h5 class="tip-title">合理休息</h5>
            <p class="tip-text">注意萌宠健康度，及时安排休息</p>
          </div>
        </div>

        <div class="tip-card">
          <van-icon name="star-o" class="tip-icon" />
          <div class="tip-content">
            <h5 class="tip-title">装备搭配</h5>
            <p class="tip-text">根据萌宠特点选择合适的装备</p>
          </div>
        </div>

        <div class="tip-card">
          <van-icon name="star-o" class="tip-icon" />
          <div class="tip-content">
            <h5 class="tip-title">定期互动</h5>
            <p class="tip-text">保持高频率互动可获得额外奖励</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要是静态内容展示
</script>

<style scoped>
.gameplay-intro {
  @apply space-y-8;
}

.intro-section {
  @apply bg-white rounded-lg p-6 shadow-sm;
}

.section-title {
  @apply text-xl font-bold text-gray-800 mb-4;
}

.section-content {
  @apply space-y-4;
}

.intro-text {
  @apply text-gray-600 leading-relaxed;
}

.nurturing-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.nurturing-card {
  @apply bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow duration-200;
}

.card-icon {
  @apply text-3xl mb-3;
}

.card-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.card-desc {
  @apply text-sm text-gray-600 leading-relaxed mb-3;
}

.card-stats {
  @apply flex flex-wrap gap-2;
}

.stat-item {
  @apply bg-white rounded-lg px-3 py-1 text-xs flex items-center justify-between;
  width: calc(50% - 4px);
}

.stat-label {
  @apply text-gray-500;
}

.stat-value {
  @apply font-semibold text-blue-600;
}

.equipment-types {
  @apply space-y-3 mt-4;
}

.equipment-type {
  @apply flex items-center space-x-3 bg-gray-50 rounded-lg p-3;
}

.type-icon {
  @apply text-2xl flex-shrink-0;
}

.type-info {
  @apply flex-1;
}

.type-name {
  @apply font-semibold text-gray-800 mb-1;
}

.type-effect {
  @apply text-sm text-gray-600;
}

.growth-system {
  @apply space-y-4 md:space-y-0 md:flex md:gap-4;
}

.level-chart {
  @apply bg-gray-50 rounded-lg overflow-hidden md:w-1/2;
}

.chart-header {
  @apply bg-blue-500 text-white p-3 text-center;
}

.chart-title {
  @apply font-semibold;
}

.chart-body {
  @apply p-3 space-y-2;
}

.chart-row {
  @apply flex items-center space-x-2;
}

.level {
  @apply text-sm font-medium text-gray-700 w-10;
}

.exp-bar {
  @apply h-4 bg-blue-400 rounded-full flex-1;
}

.exp-value {
  @apply text-xs text-gray-500 w-16 text-right;
}

.growth-info {
  @apply bg-gray-50 rounded-lg p-4 md:w-1/2;
}

.info-title {
  @apply font-semibold text-gray-800 mb-3;
}

.info-list {
  @apply space-y-2;
}

.info-list li {
  @apply text-sm text-gray-600 flex items-start;
}

.info-list li::before {
  content: "•";
  @apply mr-2 text-blue-500;
}

.tips-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.tip-card {
  @apply flex items-start space-x-3 bg-blue-50 rounded-lg p-3;
}

.tip-icon {
  @apply text-blue-500 flex-shrink-0 mt-0.5;
}

.tip-content {
  @apply flex-1;
}

.tip-title {
  @apply font-semibold text-gray-800 mb-1;
}

.tip-text {
  @apply text-sm text-gray-600;
}
</style>