<template>
  <div class="introduction-guide">
    <div class="guide-container">
      <div class="guide-header">
        <h3 class="guide-title">欢迎来到萌宠养成代币游戏</h3>
        <p class="guide-subtitle">让我们一起开始这段奇妙的旅程吧！</p>
      </div>

      <div class="guide-content">
        <div class="guide-section">
          <div class="section-icon">🎮</div>
          <div class="section-content">
            <h4 class="section-title">游戏介绍</h4>
            <p class="section-desc">了解游戏的基本玩法和特色功能</p>
            <van-button
              type="primary"
              size="small"
              @click="goToIntroduction"
              class="section-button"
            >
              查看介绍
            </van-button>
          </div>
        </div>

        <div class="guide-section">
          <div class="section-icon">📚</div>
          <div class="section-content">
            <h4 class="section-title">互动教程</h4>
            <p class="section-desc">通过互动演示学习游戏的核心玩法</p>
            <van-button
              type="primary"
              size="small"
              @click="goToTutorial"
              class="section-button"
            >
              开始教程
            </van-button>
          </div>
        </div>

        <div class="guide-section">
          <div class="section-icon">📋</div>
          <div class="section-content">
            <h4 class="section-title">游戏规则</h4>
            <p class="section-desc">查看详细的游戏规则和奖励机制</p>
            <van-button
              type="primary"
              size="small"
              @click="goToRules"
              class="section-button"
            >
              查看规则
            </van-button>
          </div>
        </div>

        <div class="guide-section">
          <div class="section-icon">🎮</div>
          <div class="section-content">
            <h4 class="section-title">开始游戏</h4>
            <p class="section-desc">跳过介绍，直接开始游戏体验</p>
            <van-button
              type="danger"
              size="small"
              @click="startGame"
              class="section-button"
            >
              立即开始
            </van-button>
          </div>
        </div>
      </div>

      <div class="guide-footer">
        <van-checkbox v-model="dontShowAgain">不再显示此引导</van-checkbox>
        <van-button
          plain
          size="small"
          @click="closeGuide"
        >
          关闭
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useGameStore } from '../../stores/game'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

const router = useRouter()
const gameStore = useGameStore()
const dontShowAgain = ref(false)

const goToIntroduction = () => {
  router.push('/introduction')
  closeGuide()
}

const goToTutorial = () => {
  router.push('/tutorial')
  closeGuide()
}

const goToRules = () => {
  router.push('/game-rules')
  closeGuide()
}

const startGame = () => {
  router.push('/')
  closeGuide()
}

const closeGuide = () => {
  if (dontShowAgain.value) {
    gameStore.updateSettings({
      showTutorialHints: false
    })
  }
  emit('close')
}
</script>

<script lang="ts">
export default {
  name: 'IntroductionGuide'
}
</script>

<style scoped>
.introduction-guide {
  @apply fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50;
}

.guide-container {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden;
}

.guide-header {
  @apply p-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center;
}

.guide-title {
  @apply text-xl font-bold mb-2;
}

.guide-subtitle {
  @apply text-sm opacity-90;
}

.guide-content {
  @apply p-6 space-y-4;
}

.guide-section {
  @apply flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200;
}

.section-icon {
  @apply text-3xl flex-shrink-0;
}

.section-content {
  @apply flex-1;
}

.section-title {
  @apply font-semibold text-gray-800 mb-1;
}

.section-desc {
  @apply text-sm text-gray-600 mb-2;
}

.section-button {
  @apply text-sm;
}

.guide-footer {
  @apply flex items-center justify-between p-4 border-t border-gray-200;
}
</style>
