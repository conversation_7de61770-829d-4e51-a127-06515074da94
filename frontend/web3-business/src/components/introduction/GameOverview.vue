<template>
  <div class="game-overview">
    <!-- 游戏简介 -->
    <div class="overview-section">
      <h3 class="section-title">🎮 游戏简介</h3>
      <div class="section-content">
        <p class="intro-text">
          萌宠养成代币游戏是一款创新的区块链游戏，结合了传统的宠物养成玩法和现代的代币经济系统。
          在这里，你可以养成可爱的虚拟萌宠，通过悉心照料提升它们的属性，最终将它们兑换成有价值的代币。
        </p>
      </div>
    </div>

    <!-- 核心特色 -->
    <div class="overview-section">
      <h3 class="section-title">✨ 核心特色</h3>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🐱</div>
          <h4 class="feature-title">可爱萌宠</h4>
          <p class="feature-desc">多种萌宠品种，每只都有独特的外观和属性</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📈</div>
          <h4 class="feature-title">成长系统</h4>
          <p class="feature-desc">通过喂食、训练、装备提升萌宠等级和能力</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">💰</div>
          <h4 class="feature-title">代币兑换</h4>
          <p class="feature-desc">将养成的萌宠兑换成真实的区块链代币</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🛒</div>
          <h4 class="feature-title">道具商店</h4>
          <p class="feature-desc">丰富的道具系统，助力萌宠快速成长</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h4 class="feature-title">安全可靠</h4>
          <p class="feature-desc">基于区块链技术，保障资产安全</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h4 class="feature-title">移动优先</h4>
          <p class="feature-desc">完美适配移动设备，随时随地养成萌宠</p>
        </div>
      </div>
    </div>

    <!-- 游戏流程 -->
    <div class="overview-section">
      <h3 class="section-title">🔄 游戏流程</h3>
      <div class="flow-diagram">
        <div class="flow-step">
          <div class="step-icon">👛</div>
          <div class="step-content">
            <h5 class="step-title">连接钱包</h5>
            <p class="step-desc">使用Web3钱包连接游戏</p>
          </div>
          <div class="step-arrow">→</div>
        </div>

        <div class="flow-step">
          <div class="step-icon">🥚</div>
          <div class="step-content">
            <h5 class="step-title">获得萌宠</h5>
            <p class="step-desc">创建或领养你的第一只萌宠</p>
          </div>
          <div class="step-arrow">→</div>
        </div>

        <div class="flow-step">
          <div class="step-icon">🍼</div>
          <div class="step-content">
            <h5 class="step-title">悉心养成</h5>
            <p class="step-desc">通过各种方式提升萌宠属性</p>
          </div>
          <div class="step-arrow">→</div>
        </div>

        <div class="flow-step">
          <div class="step-icon">💎</div>
          <div class="step-content">
            <h5 class="step-title">兑换代币</h5>
            <p class="step-desc">将萌宠兑换成有价值的代币</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术优势 -->
    <div class="overview-section">
      <h3 class="section-title">🚀 技术优势</h3>
      <div class="tech-list">
        <div class="tech-item">
          <van-icon name="success" class="tech-icon" />
          <div class="tech-content">
            <h5 class="tech-title">区块链技术</h5>
            <p class="tech-desc">基于以太坊智能合约，保障交易安全透明</p>
          </div>
        </div>

        <div class="tech-item">
          <van-icon name="success" class="tech-icon" />
          <div class="tech-content">
            <h5 class="tech-title">本地存储</h5>
            <p class="tech-desc">游戏数据安全存储在本地，支持备份恢复</p>
          </div>
        </div>

        <div class="tech-item">
          <van-icon name="success" class="tech-icon" />
          <div class="tech-content">
            <h5 class="tech-title">现代前端</h5>
            <p class="tech-desc">Vue3 + TypeScript 构建，性能优异体验流畅</p>
          </div>
        </div>

        <div class="tech-item">
          <van-icon name="success" class="tech-icon" />
          <div class="tech-content">
            <h5 class="tech-title">响应式设计</h5>
            <p class="tech-desc">完美适配各种设备尺寸，移动端优先</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要是静态内容展示
</script>

<style scoped>
.game-overview {
  @apply space-y-8;
}

.overview-section {
  @apply bg-white rounded-lg p-6 shadow-sm;
}

.section-title {
  @apply text-xl font-bold text-gray-800 mb-4;
}

.section-content {
  @apply space-y-4;
}

.intro-text {
  @apply text-gray-600 leading-relaxed;
}

.features-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.feature-card {
  @apply bg-gray-50 rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-200;
}

.feature-icon {
  @apply text-3xl mb-3;
}

.feature-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.feature-desc {
  @apply text-sm text-gray-600 leading-relaxed;
}

.flow-diagram {
  @apply space-y-4 md:space-y-0 md:flex md:items-center md:justify-between;
}

.flow-step {
  @apply flex items-center space-x-3 md:flex-col md:space-x-0 md:space-y-2 md:text-center;
}

.step-icon {
  @apply text-3xl flex-shrink-0;
}

.step-content {
  @apply flex-1;
}

.step-title {
  @apply font-semibold text-gray-800 mb-1;
}

.step-desc {
  @apply text-sm text-gray-600;
}

.step-arrow {
  @apply text-2xl text-gray-400 hidden md:block md:transform md:rotate-90;
}

.tech-list {
  @apply space-y-4;
}

.tech-item {
  @apply flex items-start space-x-3;
}

.tech-icon {
  @apply text-green-500 flex-shrink-0 mt-1;
}

.tech-content {
  @apply flex-1;
}

.tech-title {
  @apply font-semibold text-gray-800 mb-1;
}

.tech-desc {
  @apply text-sm text-gray-600;
}
</style>