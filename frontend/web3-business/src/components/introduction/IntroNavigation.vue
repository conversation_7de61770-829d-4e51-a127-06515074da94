<template>
  <div class="intro-navigation">
    <div class="nav-container">
      <div class="nav-title">游戏指南</div>
      <div class="nav-links">
        <router-link
          to="/introduction"
          class="nav-link"
          active-class="active"
        >
          <van-icon name="guide-o" class="link-icon" />
          <span class="link-text">游戏介绍</span>
        </router-link>

        <router-link
          to="/tutorial"
          class="nav-link"
          active-class="active"
        >
          <van-icon name="play-circle-o" class="link-icon" />
          <span class="link-text">互动教程</span>
        </router-link>

        <router-link
          to="/shop"
          class="nav-link"
          active-class="active"
        >
          <van-icon name="shop-o" class="link-icon" />
          <span class="link-text">游戏商店</span>
        </router-link>

        <router-link
          to="/"
          class="nav-link"
          active-class="active"
        >
          <van-icon name="home-o" class="link-icon" />
          <span class="link-text">返回首页</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导航组件，用于在游戏介绍、教程和首页之间切换
</script>

<script lang="ts">
export default {
  name: 'IntroNavigation'
}
</script>

<style scoped>
.intro-navigation {
  @apply bg-white shadow-sm mb-4 rounded-lg overflow-hidden;
}

.nav-container {
  @apply p-4;
}

.nav-title {
  @apply text-lg font-semibold text-gray-800 mb-3 text-center;
}

.nav-links {
  @apply flex flex-wrap justify-center gap-2;
}

.nav-link {
  @apply flex flex-col items-center p-3 rounded-lg bg-gray-50 text-gray-600 transition-colors duration-200;
  width: calc(50% - 0.5rem);
}

.nav-link:hover {
  @apply bg-gray-100;
}

.nav-link.active {
  @apply bg-blue-50 text-blue-600;
}

.link-icon {
  @apply text-xl mb-1;
}

.link-text {
  @apply text-sm;
}

@media (min-width: 640px) {
  .nav-links {
    @apply flex-nowrap;
  }

  .nav-link {
    @apply flex-row space-x-2;
    width: auto;
  }

  .link-icon {
    @apply mb-0;
  }
}
</style>
