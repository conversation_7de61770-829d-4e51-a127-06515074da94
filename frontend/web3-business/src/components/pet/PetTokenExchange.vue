<template>
  <div class="pet-token-exchange">
    <!-- 兑换预览卡片 -->
    <van-card
      v-if="pet"
      :title="pet.name"
      :desc="`等级 ${pet.level} | ${rarityText}`"
      :thumb="petImageUrl"
      class="mb-4"
    >
      <template #tags>
        <van-tag :type="rarityTagType" size="medium">{{ rarityText }}</van-tag>
      </template>

      <template #footer>
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-600">代币价值</span>
          <span class="text-lg font-bold text-primary-600">
            {{ exchangePreview?.calculation?.tokenAmountFormatted || '0' }} MTK
          </span>
        </div>
      </template>
    </van-card>

    <!-- 兑换条件检查 -->
    <van-cell-group v-if="exchangePreview" class="mb-4">
      <van-cell title="兑换条件" :value="canExchange ? '✅ 满足' : '❌ 不满足'" />
      <van-cell
        v-if="!canExchange && exchangePreview.reason"
        title="原因"
        :value="exchangePreview.reason"
        label-class="text-red-500"
      />
    </van-cell-group>

    <!-- 价值计算详情 -->
    <van-collapse v-if="exchangePreview?.calculation" v-model="showDetails" class="mb-4">
      <van-collapse-item title="价值计算详情" name="details">
        <div class="space-y-2 p-2">
          <div class="flex justify-between">
            <span>基础价值 (等级 {{ pet?.level }})</span>
            <span>{{ exchangePreview.calculation.baseValue.toFixed(2) }} MTK</span>
          </div>
          <div class="flex justify-between">
            <span>稀有度加成 (×{{ exchangePreview.calculation.rarityMultiplier }})</span>
            <span>{{ (exchangePreview.calculation.baseValue * exchangePreview.calculation.rarityMultiplier).toFixed(2) }} MTK</span>
          </div>
          <div class="flex justify-between">
            <span>健康度加成 (×{{ exchangePreview.calculation.healthBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ Math.round(exchangePreview.calculation.healthBonus * 100) }}%</span>
          </div>
          <div class="flex justify-between">
            <span>快乐度加成 (×{{ exchangePreview.calculation.happinessBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ Math.round(exchangePreview.calculation.happinessBonus * 100) }}%</span>
          </div>
          <div class="flex justify-between">
            <span>装备加成 (×{{ exchangePreview.calculation.equipmentBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ pet?.equipment.length || 0 }} 件装备</span>
          </div>
          <div class="flex justify-between">
            <span>特质加成 (×{{ exchangePreview.calculation.traitBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ pet?.traits.length || 0 }} 个特质</span>
          </div>
          <div class="flex justify-between">
            <span>技能加成 (×{{ exchangePreview.calculation.skillBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ pet?.skills.length || 0 }} 个技能</span>
          </div>
          <div class="flex justify-between">
            <span>属性加成 (×{{ exchangePreview.calculation.statsBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">总属性值</span>
          </div>
          <div class="flex justify-between">
            <span>年龄加成 (×{{ exchangePreview.calculation.ageBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ calculateAge(pet?.birthTime) }}</span>
          </div>
          <div class="flex justify-between">
            <span>成就加成 (×{{ exchangePreview.calculation.achievementBonus.toFixed(2) }})</span>
            <span class="text-sm text-gray-600">{{ pet?.achievements.length || 0 }} 个成就</span>
          </div>
          <van-divider />
          <div class="flex justify-between font-bold text-lg">
            <span>最终价值</span>
            <span class="text-primary-600">{{ exchangePreview.calculation.tokenAmountFormatted }} MTK</span>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>

    <!-- 兑换确认 -->
    <div class="space-y-4">
      <van-button
        v-if="canExchange"
        type="primary"
        size="large"
        block
        :loading="isExchanging"
        :disabled="!walletConnected"
        @click="showConfirmDialog = true"
      >
        {{ isExchanging ? '兑换中...' : '兑换代币' }}
      </van-button>

      <van-button
        v-if="!walletConnected"
        type="warning"
        size="large"
        block
        @click="$emit('connect-wallet')"
      >
        连接钱包
      </van-button>

      <van-button
        v-if="!canExchange"
        type="default"
        size="large"
        block
        disabled
      >
        不满足兑换条件
      </van-button>
    </div>

    <!-- 兑换确认对话框 -->
    <van-dialog
      v-model:show="showConfirmDialog"
      title="确认兑换"
      :message="confirmMessage"
      show-cancel-button
      confirm-button-text="确认兑换"
      cancel-button-text="取消"
      @confirm="handleExchange"
    />

    <!-- 兑换进度对话框 -->
    <van-dialog
      v-model:show="showProgressDialog"
      title="兑换进行中"
      :message="progressMessage"
      :show-cancel-button="false"
      :show-confirm-button="false"
      :close-on-click-overlay="false"
    >
      <template #message>
        <div class="text-center py-4">
          <van-loading size="24px" class="mb-2" />
          <div>{{ progressMessage }}</div>
        </div>
      </template>
    </van-dialog>

    <!-- 兑换结果对话框 -->
    <van-dialog
      v-model:show="showResultDialog"
      :title="exchangeResult?.success ? '兑换成功' : '兑换失败'"
      :message="exchangeResult?.message"
      :show-cancel-button="false"
      confirm-button-text="确定"
      @confirm="handleResultConfirm"
    >
      <template #message>
        <div class="text-center py-4">
          <div class="text-6xl mb-4">
            {{ exchangeResult?.success ? '🎉' : '😞' }}
          </div>
          <div class="mb-4">{{ exchangeResult?.message }}</div>
          <div v-if="exchangeResult?.success && exchangeResult?.transaction" class="text-sm text-gray-600">
            <div>交易哈希: {{ exchangeResult.transaction.transactionHash.slice(0, 10) }}...</div>
            <div>获得代币: {{ exchangeResult.transaction.tokenAmount }} wei</div>
          </div>
        </div>
      </template>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { showToast } from 'vant'
import type { Pet } from '../../types/typesWithoutCircular'
import { tokenExchangeService } from '../../services/token-exchange.service'
import type { ExchangeTransaction } from '../../services/token-exchange.service'

interface Props {
  pet: Pet | null
  walletConnected: boolean
  userAddress?: string
}

interface Emits {
  (e: 'connect-wallet'): void
  (e: 'exchange-success', transaction: ExchangeTransaction): void
  (e: 'exchange-failed', error: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const showDetails = ref<string[]>([])
const showConfirmDialog = ref(false)
const showProgressDialog = ref(false)
const showResultDialog = ref(false)
const isExchanging = ref(false)
const progressMessage = ref('')
const exchangePreview = ref<any>(null)
const exchangeResult = ref<{
  success: boolean
  message: string
  transaction?: ExchangeTransaction
} | null>(null)

// 计算属性
const canExchange = computed(() => {
  return exchangePreview.value?.canExchange || false
})

const rarityText = computed(() => {
  if (!props.pet) return ''
  const rarityMap: Record<string, string> = {
    'common': '普通',
    'uncommon': '稀有',
    'rare': '珍稀',
    'epic': '史诗',
    'legendary': '传说',
    'mythical': '神话'
  }
  return rarityMap[props.pet.rarity] || props.pet.rarity
})

const rarityTagType = computed(() => {
  if (!props.pet) return 'default'
  const typeMap: Record<string, string> = {
    'common': 'default',
    'uncommon': 'primary',
    'rare': 'success',
    'epic': 'warning',
    'legendary': 'danger',
    'mythical': 'primary'
  }
  return typeMap[props.pet.rarity] || 'default'
})

const petImageUrl = computed(() => {
  if (!props.pet) return ''
  // 根据萌宠类型和稀有度返回对应的图片URL
  return `/images/pets/${props.pet.type}/${props.pet.rarity}.png`
})

const confirmMessage = computed(() => {
  if (!props.pet || !exchangePreview.value?.calculation) return ''

  return `确定要将 ${props.pet.name} 兑换成代币吗？\n\n` +
         `萌宠信息：\n` +
         `• 名称：${props.pet.name}\n` +
         `• 等级：${props.pet.level}\n` +
         `• 稀有度：${rarityText.value}\n\n` +
         `兑换价值：${exchangePreview.value.calculation.tokenAmountFormatted} MTK\n\n` +
         `⚠️ 注意：兑换后萌宠将被移除，此操作不可撤销！`
})

// 方法
const updateExchangePreview = () => {
  if (!props.pet) {
    exchangePreview.value = null
    return
  }

  try {
    exchangePreview.value = tokenExchangeService.previewExchange(props.pet)
  } catch (error) {
    console.error('更新兑换预览失败:', error)
    exchangePreview.value = null
  }
}

const handleExchange = async () => {
  if (!props.pet || !props.userAddress) {
    showToast('萌宠或钱包地址不存在')
    return
  }

  showConfirmDialog.value = false
  showProgressDialog.value = true
  isExchanging.value = true

  try {
    const transaction = await tokenExchangeService.exchangePetForTokens(
      props.pet,
      props.userAddress,
      (step: string) => {
        progressMessage.value = step
      }
    )

    // 兑换成功
    exchangeResult.value = {
      success: true,
      message: `成功兑换 ${props.pet.name}，获得 ${transaction.tokenAmount} wei 代币！`,
      transaction
    }

    emit('exchange-success', transaction)
    showToast('兑换成功！')

  } catch (error) {
    // 兑换失败
    const errorMessage = error instanceof Error ? error.message : '兑换失败'
    exchangeResult.value = {
      success: false,
      message: errorMessage
    }

    emit('exchange-failed', errorMessage)
    showToast(errorMessage)

  } finally {
    isExchanging.value = false
    showProgressDialog.value = false
    showResultDialog.value = true
  }
}

const handleResultConfirm = () => {
  showResultDialog.value = false
  exchangeResult.value = null
}

const calculateAge = (birthTime?: number): string => {
  if (!birthTime) return '未知'

  const ageInDays = Math.floor((Date.now() - birthTime) / (1000 * 60 * 60 * 24))

  if (ageInDays < 1) return '不到1天'
  if (ageInDays < 30) return `${ageInDays}天`
  if (ageInDays < 365) return `${Math.floor(ageInDays / 30)}个月`
  return `${Math.floor(ageInDays / 365)}年`
}

// 监听器
watch(() => props.pet, updateExchangePreview, { immediate: true })

// 生命周期
onMounted(() => {
  updateExchangePreview()
})
</script>

<style scoped>
.pet-token-exchange {
  padding: 16px;
}

.van-card {
  border-radius: 12px;
  overflow: hidden;
}

.van-collapse-item {
  border-radius: 8px;
}

.text-primary-600 {
  color: #7c3aed;
}
</style>
