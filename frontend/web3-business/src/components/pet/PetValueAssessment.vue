<template>
  <div class="pet-value-assessment bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 评估器头部 -->
    <div class="assessment-header bg-gradient-to-r from-yellow-500 to-orange-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold flex items-center space-x-2">
          <van-icon name="gold-coin-o" />
          <span>价值评估</span>
        </h3>
        <div class="flex items-center space-x-2">
          <van-button
            size="small"
            type="primary"
            @click="handleCalculateValue"
            :loading="calculating"
            class="bg-white text-yellow-500 hover:bg-gray-100"
          >
            重新评估
          </van-button>
        </div>
      </div>
    </div>

    <!-- 评估内容 -->
    <div class="assessment-content p-6">
      <!-- 总价值显示 -->
      <div class="total-value-card bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-6 text-white mb-6">
        <div class="text-center">
          <div class="text-sm opacity-90 mb-2">当前估值</div>
          <div class="text-4xl font-bold mb-2">{{ formatTokenValue(totalValue) }}</div>
          <div class="text-sm opacity-90">代币</div>
        </div>

        <div class="flex justify-between items-center mt-4 pt-4 border-t border-white border-opacity-30">
          <div class="text-center">
            <div class="text-xs opacity-75">评级</div>
            <div class="text-lg font-bold">{{ valueRank }}</div>
          </div>
          <div class="text-center">
            <div class="text-xs opacity-75">稀有度加成</div>
            <div class="text-lg font-bold">{{ rarityMultiplier }}x</div>
          </div>
          <div class="text-center">
            <div class="text-xs opacity-75">成长潜力</div>
            <div class="text-lg font-bold">{{ growthPotential }}%</div>
          </div>
        </div>
      </div>

      <!-- 价值构成分析 -->
      <div class="value-breakdown mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-blue-500">📊</span>
          <span>价值构成</span>
        </h4>

        <div class="breakdown-items space-y-3">
          <!-- 基础价值 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">⭐</span>
                <span class="font-medium">基础价值</span>
              </div>
              <span class="text-lg font-bold text-blue-600">{{ formatTokenValue(baseValue) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              基于等级 {{ pet.level }} 的基础价值
            </div>
          </div>

          <!-- 稀有度加成 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">💎</span>
                <span class="font-medium">稀有度加成</span>
              </div>
              <span class="text-lg font-bold text-purple-600">{{ formatTokenValue(rarityBonus) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              {{ getRarityText(pet.rarity) }} 稀有度 × {{ rarityMultiplier }}
            </div>
          </div>

          <!-- 属性加成 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">💪</span>
                <span class="font-medium">属性加成</span>
              </div>
              <span class="text-lg font-bold text-green-600">{{ formatTokenValue(statsBonus) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              总属性值: {{ totalStats }}
            </div>
          </div>

          <!-- 装备加成 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">⚔️</span>
                <span class="font-medium">装备加成</span>
              </div>
              <span class="text-lg font-bold text-orange-600">{{ formatTokenValue(equipmentBonus) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              已装备 {{ pet.equipment.length }} 件装备
            </div>
          </div>

          <!-- 技能特质加成 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">✨</span>
                <span class="font-medium">技能特质</span>
              </div>
              <span class="text-lg font-bold text-indigo-600">{{ formatTokenValue(skillTraitBonus) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              {{ pet.skills.length }} 个技能, {{ pet.traits.length }} 个特质
            </div>
          </div>

          <!-- 状态加成 -->
          <div class="breakdown-item bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="text-lg">❤️</span>
                <span class="font-medium">状态加成</span>
              </div>
              <span class="text-lg font-bold" :class="statusBonusClass">{{ formatTokenValue(statusBonus) }}</span>
            </div>
            <div class="text-sm text-gray-600">
              健康 {{ healthPercentage }}%, 快乐 {{ happinessPercentage }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 市场比较 -->
      <div class="market-comparison mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-green-500">📈</span>
          <span>市场比较</span>
        </h4>

        <div class="comparison-grid grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="comparison-card bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div class="text-2xl mb-2">📊</div>
            <div class="text-sm text-green-600 mb-1">同等级平均</div>
            <div class="text-lg font-bold text-green-700">{{ formatTokenValue(averageValue) }}</div>
            <div class="text-xs text-gray-500 mt-1">
              {{ valueComparison > 0 ? '+' : '' }}{{ valueComparison }}%
            </div>
          </div>

          <div class="comparison-card bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <div class="text-2xl mb-2">🏆</div>
            <div class="text-sm text-blue-600 mb-1">同稀有度最高</div>
            <div class="text-lg font-bold text-blue-700">{{ formatTokenValue(maxRarityValue) }}</div>
            <div class="text-xs text-gray-500 mt-1">
              排名 {{ rarityRanking }}
            </div>
          </div>

          <div class="comparison-card bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div class="text-2xl mb-2">🎯</div>
            <div class="text-sm text-purple-600 mb-1">升级后预估</div>
            <div class="text-lg font-bold text-purple-700">{{ formatTokenValue(nextLevelValue) }}</div>
            <div class="text-xs text-gray-500 mt-1">
              +{{ nextLevelIncrease }}%
            </div>
          </div>
        </div>
      </div>

      <!-- 兑换操作 -->
      <div class="exchange-actions">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-red-500">🔄</span>
          <span>代币兑换</span>
        </h4>

        <div class="exchange-card bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-4">
            <div>
              <div class="text-sm text-red-600 mb-1">兑换收益</div>
              <div class="text-2xl font-bold text-red-700">{{ formatTokenValue(exchangeValue) }}</div>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-500 mb-1">手续费</div>
              <div class="text-lg text-gray-600">{{ formatTokenValue(exchangeFee) }}</div>
            </div>
          </div>

          <div class="exchange-warning bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
            <div class="flex items-start space-x-2">
              <van-icon name="warning-o" class="text-yellow-500 mt-0.5" />
              <div class="text-sm text-yellow-700">
                <div class="font-medium mb-1">兑换提醒</div>
                <div>兑换后萌宠将被销毁，此操作不可逆转。建议在萌宠达到较高等级时进行兑换以获得更好收益。</div>
              </div>
            </div>
          </div>

          <van-button
            type="danger"
            @click="handleExchangeTokens"
            :disabled="!canExchange"
            class="w-full"
            size="large"
          >
            {{ exchangeButtonText }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Pet, PetRarity } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
}

interface Emits {
  (e: 'calculate-value'): void
  (e: 'exchange-tokens'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const calculating = ref(false)

// 计算属性 - 基础价值计算
const baseValue = computed(() => {
  return Math.floor(10 * Math.pow(1.2, props.pet.level - 1))
})

const rarityMultiplier = computed(() => {
  const multipliers = {
    common: 1.0,
    uncommon: 1.5,
    rare: 2.0,
    epic: 3.0,
    legendary: 5.0,
    mythical: 10.0
  }
  return multipliers[props.pet.rarity]
})

const rarityBonus = computed(() => {
  return Math.floor(baseValue.value * (rarityMultiplier.value - 1))
})

const totalStats = computed(() => {
  return Object.values(props.pet.stats).reduce((sum, stat) => sum + stat, 0)
})

const statsBonus = computed(() => {
  return Math.floor(totalStats.value * 0.5)
})

const equipmentBonus = computed(() => {
  return props.pet.equipment.reduce((bonus, equipment) => {
    const rarityBonus = rarityMultiplier.value * 10
    return bonus + rarityBonus
  }, 0)
})

const skillTraitBonus = computed(() => {
  const skillBonus = props.pet.skills.reduce((sum, skill) => sum + skill.level * 20, 0)
  const traitBonus = props.pet.traits.reduce((sum, trait) => sum + trait.level * 30, 0)
  return skillBonus + traitBonus
})

const healthPercentage = computed(() => {
  return Math.round((props.pet.health / props.pet.maxHealth) * 100)
})

const happinessPercentage = computed(() => {
  return Math.round((props.pet.happiness / props.pet.maxHappiness) * 100)
})

const statusBonus = computed(() => {
  const healthFactor = props.pet.health / props.pet.maxHealth
  const happinessFactor = props.pet.happiness / props.pet.maxHappiness
  const avgStatus = (healthFactor + happinessFactor) / 2
  return Math.floor(baseValue.value * avgStatus * 0.3)
})

const statusBonusClass = computed(() => {
  const avgStatus = (healthPercentage.value + happinessPercentage.value) / 2
  if (avgStatus >= 80) return 'text-green-600'
  if (avgStatus >= 60) return 'text-yellow-600'
  return 'text-red-600'
})

const totalValue = computed(() => {
  return baseValue.value + rarityBonus.value + statsBonus.value +
         equipmentBonus.value + skillTraitBonus.value + statusBonus.value
})

// 计算属性 - 价值评级
const valueRank = computed(() => {
  const value = totalValue.value
  if (value >= 1000) return 'S'
  if (value >= 800) return 'A'
  if (value >= 600) return 'B'
  if (value >= 400) return 'C'
  if (value >= 200) return 'D'
  return 'E'
})

const growthPotential = computed(() => {
  const maxLevel = 100
  const levelProgress = props.pet.level / maxLevel
  const potential = (1 - levelProgress) * 100
  return Math.round(potential)
})

// 计算属性 - 市场比较
const averageValue = computed(() => {
  // 模拟同等级平均价值
  return Math.floor(baseValue.value * rarityMultiplier.value * 0.8)
})

const valueComparison = computed(() => {
  const diff = totalValue.value - averageValue.value
  return Math.round((diff / averageValue.value) * 100)
})

const maxRarityValue = computed(() => {
  // 模拟同稀有度最高价值
  return Math.floor(baseValue.value * rarityMultiplier.value * 1.5)
})

const rarityRanking = computed(() => {
  // 模拟排名
  const percentage = (totalValue.value / maxRarityValue.value) * 100
  if (percentage >= 90) return 'Top 10%'
  if (percentage >= 70) return 'Top 30%'
  if (percentage >= 50) return 'Top 50%'
  return 'Top 70%'
})

const nextLevelValue = computed(() => {
  const nextLevelBase = Math.floor(10 * Math.pow(1.2, props.pet.level))
  return Math.floor(nextLevelBase * rarityMultiplier.value * 1.1)
})

const nextLevelIncrease = computed(() => {
  const increase = nextLevelValue.value - totalValue.value
  return Math.round((increase / totalValue.value) * 100)
})

// 计算属性 - 兑换相关
const exchangeFee = computed(() => {
  return Math.floor(totalValue.value * 0.05) // 5% 手续费
})

const exchangeValue = computed(() => {
  return totalValue.value - exchangeFee.value
})

const canExchange = computed(() => {
  return props.pet.level >= 5 && totalValue.value >= 100
})

const exchangeButtonText = computed(() => {
  if (!canExchange.value) {
    if (props.pet.level < 5) return '需要5级以上'
    if (totalValue.value < 100) return '价值过低'
  }
  return '确认兑换'
})

// 方法
const handleCalculateValue = async () => {
  calculating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟计算延迟
    emit('calculate-value')
  } finally {
    calculating.value = false
  }
}

const handleExchangeTokens = () => {
  emit('exchange-tokens')
}

const getRarityText = (rarity: PetRarity): string => {
  const rarityTexts = {
    common: '普通',
    uncommon: '优秀',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return rarityTexts[rarity]
}

const formatTokenValue = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M'
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}
</script>

<style scoped>
.pet-value-assessment {
  transition: all 0.3s ease;
}

.total-value-card {
  background: linear-gradient(135deg, #fbbf24 0%, #f97316 100%);
  box-shadow: 0 10px 25px rgba(251, 191, 36, 0.3);
}

.breakdown-item {
  transition: all 0.2s ease;
}

.breakdown-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.comparison-card {
  transition: all 0.3s ease;
}

.comparison-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.exchange-card {
  transition: all 0.3s ease;
}

.exchange-warning {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .comparison-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .assessment-content {
    padding: 1rem;
  }

  .total-value-card {
    padding: 1rem;
  }

  .breakdown-item, .comparison-card, .exchange-card {
    padding: 0.75rem;
  }
}
</style>
