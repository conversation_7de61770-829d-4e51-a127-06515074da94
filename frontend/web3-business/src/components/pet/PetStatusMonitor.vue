<template>
  <div class="pet-status-monitor bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 监控器头部 -->
    <div class="monitor-header bg-gradient-to-r from-orange-500 to-red-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold flex items-center space-x-2">
          <van-icon name="eye-o" />
          <span>状态监控</span>
        </h3>
        <div class="flex items-center space-x-2">
          <div class="status-indicator flex items-center space-x-1">
            <div class="w-2 h-2 rounded-full" :class="overallStatusColor"></div>
            <span class="text-sm">{{ overallStatusText }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控器内容 -->
    <div class="monitor-content p-6">
      <!-- 实时状态卡片 -->
      <div class="status-cards grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- 健康状态卡片 -->
        <div class="status-card bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span class="text-red-500 text-xl">❤️</span>
              <span class="font-medium text-red-700">健康状态</span>
            </div>
            <div class="status-value text-2xl font-bold text-red-600">
              {{ healthPercentage }}%
            </div>
          </div>

          <div class="progress-bar w-full bg-red-200 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full transition-all duration-500"
              :class="healthProgressClass"
              :style="{ width: `${healthPercentage}%` }"
            />
          </div>

          <div class="status-info flex justify-between text-sm text-red-600">
            <span>{{ pet.health }}/{{ pet.maxHealth }}</span>
            <span>{{ getHealthStatusText() }}</span>
          </div>

          <div class="status-actions mt-3">
            <van-button
              size="small"
              type="primary"
              :disabled="!canFeed"
              @click="handleFeed"
              class="w-full bg-red-500 border-red-500"
            >
              {{ feedButtonText }}
            </van-button>
          </div>
        </div>

        <!-- 快乐状态卡片 -->
        <div class="status-card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span class="text-yellow-500 text-xl">😊</span>
              <span class="font-medium text-yellow-700">快乐状态</span>
            </div>
            <div class="status-value text-2xl font-bold text-yellow-600">
              {{ happinessPercentage }}%
            </div>
          </div>

          <div class="progress-bar w-full bg-yellow-200 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full transition-all duration-500"
              :class="happinessProgressClass"
              :style="{ width: `${happinessPercentage}%` }"
            />
          </div>

          <div class="status-info flex justify-between text-sm text-yellow-600">
            <span>{{ pet.happiness }}/{{ pet.maxHappiness }}</span>
            <span>{{ getHappinessStatusText() }}</span>
          </div>

          <div class="status-actions mt-3">
            <van-button
              size="small"
              type="primary"
              :disabled="!canPlay"
              @click="handlePlay"
              class="w-full bg-yellow-500 border-yellow-500"
            >
              {{ playButtonText }}
            </van-button>
          </div>
        </div>

        <!-- 能量状态卡片 -->
        <div class="status-card bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span class="text-blue-500 text-xl">⚡</span>
              <span class="font-medium text-blue-700">能量状态</span>
            </div>
            <div class="status-value text-2xl font-bold text-blue-600">
              {{ energyPercentage }}%
            </div>
          </div>

          <div class="progress-bar w-full bg-blue-200 rounded-full h-3 mb-2">
            <div
              class="h-3 rounded-full transition-all duration-500"
              :class="energyProgressClass"
              :style="{ width: `${energyPercentage}%` }"
            />
          </div>

          <div class="status-info flex justify-between text-sm text-blue-600">
            <span>{{ pet.energy }}/{{ pet.maxEnergy }}</span>
            <span>{{ getEnergyStatusText() }}</span>
          </div>

          <div class="status-actions mt-3">
            <van-button
              size="small"
              type="primary"
              :disabled="!canRest"
              @click="handleRest"
              class="w-full bg-blue-500 border-blue-500"
            >
              {{ restButtonText }}
            </van-button>
          </div>
        </div>
      </div>

      <!-- 活动面板 -->
      <div class="activity-panel mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-purple-500">🎯</span>
          <span>活动面板</span>
        </h4>

        <div class="activity-grid grid grid-cols-2 md:grid-cols-4 gap-4">
          <!-- 训练活动 -->
          <div class="activity-card bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div class="activity-icon text-3xl mb-2">💪</div>
            <div class="activity-name font-medium text-purple-700 mb-2">训练</div>
            <div class="activity-info text-xs text-purple-600 mb-3">
              提升属性和经验
            </div>
            <van-button
              size="small"
              type="primary"
              :disabled="!canTrain"
              @click="handleTrain"
              class="w-full bg-purple-500 border-purple-500"
            >
              {{ trainButtonText }}
            </van-button>
            <div class="cooldown-info text-xs text-gray-500 mt-1">
              {{ getTrainCooldownText() }}
            </div>
          </div>

          <!-- 探索活动 -->
          <div class="activity-card bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div class="activity-icon text-3xl mb-2">🗺️</div>
            <div class="activity-name font-medium text-green-700 mb-2">探索</div>
            <div class="activity-info text-xs text-green-600 mb-3">
              发现新物品
            </div>
            <van-button
              size="small"
              type="primary"
              :disabled="!canExplore"
              @click="handleExplore"
              class="w-full bg-green-500 border-green-500"
            >
              {{ exploreButtonText }}
            </van-button>
            <div class="cooldown-info text-xs text-gray-500 mt-1">
              {{ getExploreCooldownText() }}
            </div>
          </div>

          <!-- 学习活动 -->
          <div class="activity-card bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
            <div class="activity-icon text-3xl mb-2">📚</div>
            <div class="activity-name font-medium text-indigo-700 mb-2">学习</div>
            <div class="activity-info text-xs text-indigo-600 mb-3">
              学习新技能
            </div>
            <van-button
              size="small"
              type="primary"
              :disabled="!canStudy"
              @click="handleStudy"
              class="w-full bg-indigo-500 border-indigo-500"
            >
              {{ studyButtonText }}
            </van-button>
            <div class="cooldown-info text-xs text-gray-500 mt-1">
              {{ getStudyCooldownText() }}
            </div>
          </div>

          <!-- 社交活动 -->
          <div class="activity-card bg-pink-50 border border-pink-200 rounded-lg p-4 text-center">
            <div class="activity-icon text-3xl mb-2">👥</div>
            <div class="activity-name font-medium text-pink-700 mb-2">社交</div>
            <div class="activity-info text-xs text-pink-600 mb-3">
              与其他萌宠互动
            </div>
            <van-button
              size="small"
              type="primary"
              :disabled="!canSocialize"
              @click="handleSocialize"
              class="w-full bg-pink-500 border-pink-500"
            >
              {{ socializeButtonText }}
            </van-button>
            <div class="cooldown-info text-xs text-gray-500 mt-1">
              {{ getSocializeCooldownText() }}
            </div>
          </div>
        </div>
      </div>

      <!-- 状态历史 -->
      <div class="status-history">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-gray-500">📊</span>
          <span>状态历史</span>
        </h4>

        <div class="history-chart bg-gray-50 rounded-lg p-4 mb-4">
          <!-- 简化的状态趋势图 -->
          <div class="chart-container h-32 flex items-end justify-between space-x-1">
            <div
              v-for="(point, index) in statusHistory"
              :key="index"
              class="chart-bar bg-gradient-to-t from-blue-400 to-blue-600 rounded-t flex-1 transition-all duration-300"
              :style="{ height: `${point.health}%` }"
              :title="`${formatTime(point.timestamp)}: 健康${point.health}% 快乐${point.happiness}% 能量${point.energy}%`"
            />
          </div>

          <div class="chart-legend flex justify-between text-xs text-gray-500 mt-2">
            <span>24小时前</span>
            <span>12小时前</span>
            <span>现在</span>
          </div>
        </div>

        <!-- 最近活动记录 -->
        <div class="recent-activities">
          <h5 class="font-medium text-gray-700 mb-3">最近活动</h5>
          <div class="activities-list space-y-2 max-h-40 overflow-y-auto">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-record bg-white border border-gray-200 rounded-lg p-3 flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div class="activity-icon text-lg">{{ activity.icon }}</div>
                <div>
                  <div class="text-sm font-medium">{{ activity.name }}</div>
                  <div class="text-xs text-gray-500">{{ formatTime(activity.timestamp) }}</div>
                </div>
              </div>
              <div class="activity-result text-sm" :class="activity.resultClass">
                {{ activity.result }}
              </div>
            </div>
          </div>

          <div v-if="recentActivities.length === 0" class="text-center py-8 text-gray-500">
            暂无活动记录
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import type { Pet } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
}

interface Emits {
  (e: 'feed'): void
  (e: 'play'): void
  (e: 'rest'): void
  (e: 'train'): void
}

interface StatusHistoryPoint {
  timestamp: number
  health: number
  happiness: number
  energy: number
}

interface ActivityRecord {
  id: string
  name: string
  icon: string
  timestamp: number
  result: string
  resultClass: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const statusHistory = ref<StatusHistoryPoint[]>([])
const recentActivities = ref<ActivityRecord[]>([])
const currentTime = ref(Date.now())

// 定时器
let statusUpdateTimer: number | null = null

// 计算属性 - 状态百分比
const healthPercentage = computed(() => {
  return Math.round((props.pet.health / props.pet.maxHealth) * 100)
})

const happinessPercentage = computed(() => {
  return Math.round((props.pet.happiness / props.pet.maxHappiness) * 100)
})

const energyPercentage = computed(() => {
  return Math.round((props.pet.energy / props.pet.maxEnergy) * 100)
})

// 计算属性 - 整体状态
const overallStatusColor = computed(() => {
  const avgStatus = (healthPercentage.value + happinessPercentage.value + energyPercentage.value) / 3

  if (avgStatus >= 80) return 'bg-green-500 animate-pulse'
  if (avgStatus >= 60) return 'bg-yellow-500'
  if (avgStatus >= 40) return 'bg-orange-500'
  return 'bg-red-500 animate-pulse'
})

const overallStatusText = computed(() => {
  const avgStatus = (healthPercentage.value + happinessPercentage.value + energyPercentage.value) / 3

  if (avgStatus >= 80) return '状态良好'
  if (avgStatus >= 60) return '状态一般'
  if (avgStatus >= 40) return '需要关注'
  return '状态危险'
})

// 计算属性 - 进度条样式
const healthProgressClass = computed(() => {
  if (healthPercentage.value >= 80) return 'bg-green-500'
  if (healthPercentage.value >= 60) return 'bg-yellow-500'
  if (healthPercentage.value >= 40) return 'bg-orange-500'
  return 'bg-red-500'
})

const happinessProgressClass = computed(() => {
  if (happinessPercentage.value >= 80) return 'bg-green-500'
  if (happinessPercentage.value >= 60) return 'bg-yellow-500'
  if (happinessPercentage.value >= 40) return 'bg-orange-500'
  return 'bg-red-500'
})

const energyProgressClass = computed(() => {
  if (energyPercentage.value >= 80) return 'bg-green-500'
  if (energyPercentage.value >= 60) return 'bg-yellow-500'
  if (energyPercentage.value >= 40) return 'bg-orange-500'
  return 'bg-red-500'
})

// 计算属性 - 活动可用性
const canFeed = computed(() => {
  const lastFeedTime = props.pet.lastFeedTime || 0
  const cooldown = 30 * 60 * 1000 // 30分钟冷却
  return currentTime.value - lastFeedTime >= cooldown && props.pet.health < props.pet.maxHealth
})

const canPlay = computed(() => {
  const lastPlayTime = props.pet.lastPlayTime || 0
  const cooldown = 60 * 60 * 1000 // 1小时冷却
  return currentTime.value - lastPlayTime >= cooldown && props.pet.energy >= 20
})

const canRest = computed(() => {
  const lastRestTime = props.pet.lastRestTime || 0
  const cooldown = 2 * 60 * 60 * 1000 // 2小时冷却
  return currentTime.value - lastRestTime >= cooldown && props.pet.energy < props.pet.maxEnergy
})

const canTrain = computed(() => {
  const lastTrainTime = props.pet.lastTrainTime || 0
  const cooldown = 4 * 60 * 60 * 1000 // 4小时冷却
  return currentTime.value - lastTrainTime >= cooldown && props.pet.energy >= 30
})

const canExplore = computed(() => {
  return props.pet.energy >= 40 && props.pet.health >= 50
})

const canStudy = computed(() => {
  return props.pet.energy >= 25 && props.pet.happiness >= 60
})

const canSocialize = computed(() => {
  return props.pet.happiness >= 30 && props.pet.health >= 40
})

// 计算属性 - 按钮文本
const feedButtonText = computed(() => {
  if (!canFeed.value) {
    const lastFeedTime = props.pet.lastFeedTime || 0
    const cooldown = 30 * 60 * 1000
    const remaining = Math.max(0, cooldown - (currentTime.value - lastFeedTime))
    if (remaining > 0) {
      const minutes = Math.ceil(remaining / (60 * 1000))
      return `${minutes}分钟后`
    }
    return '已满'
  }
  return '喂食'
})

const playButtonText = computed(() => {
  if (!canPlay.value) {
    const lastPlayTime = props.pet.lastPlayTime || 0
    const cooldown = 60 * 60 * 1000
    const remaining = Math.max(0, cooldown - (currentTime.value - lastPlayTime))
    if (remaining > 0) {
      const hours = Math.ceil(remaining / (60 * 60 * 1000))
      return `${hours}小时后`
    }
    return '能量不足'
  }
  return '玩耍'
})

const restButtonText = computed(() => {
  if (!canRest.value) {
    const lastRestTime = props.pet.lastRestTime || 0
    const cooldown = 2 * 60 * 60 * 1000
    const remaining = Math.max(0, cooldown - (currentTime.value - lastRestTime))
    if (remaining > 0) {
      const hours = Math.ceil(remaining / (60 * 60 * 1000))
      return `${hours}小时后`
    }
    return '已满'
  }
  return '休息'
})

const trainButtonText = computed(() => {
  if (!canTrain.value) {
    const lastTrainTime = props.pet.lastTrainTime || 0
    const cooldown = 4 * 60 * 60 * 1000
    const remaining = Math.max(0, cooldown - (currentTime.value - lastTrainTime))
    if (remaining > 0) {
      const hours = Math.ceil(remaining / (60 * 60 * 1000))
      return `${hours}小时后`
    }
    return '能量不足'
  }
  return '训练'
})

const exploreButtonText = computed(() => canExplore.value ? '探索' : '状态不足')
const studyButtonText = computed(() => canStudy.value ? '学习' : '状态不足')
const socializeButtonText = computed(() => canSocialize.value ? '社交' : '状态不足')

// 方法 - 状态文本
const getHealthStatusText = () => {
  if (healthPercentage.value >= 80) return '健康'
  if (healthPercentage.value >= 60) return '良好'
  if (healthPercentage.value >= 40) return '一般'
  if (healthPercentage.value >= 20) return '虚弱'
  return '危险'
}

const getHappinessStatusText = () => {
  if (happinessPercentage.value >= 80) return '开心'
  if (happinessPercentage.value >= 60) return '满足'
  if (happinessPercentage.value >= 40) return '平静'
  if (happinessPercentage.value >= 20) return '沮丧'
  return '伤心'
}

const getEnergyStatusText = () => {
  if (energyPercentage.value >= 80) return '充沛'
  if (energyPercentage.value >= 60) return '良好'
  if (energyPercentage.value >= 40) return '一般'
  if (energyPercentage.value >= 20) return '疲惫'
  return '精疲力竭'
}

// 方法 - 冷却时间文本
const getTrainCooldownText = () => {
  if (canTrain.value) return '可用'
  const lastTrainTime = props.pet.lastTrainTime || 0
  const cooldown = 4 * 60 * 60 * 1000
  const remaining = Math.max(0, cooldown - (currentTime.value - lastTrainTime))
  if (remaining > 0) {
    const hours = Math.ceil(remaining / (60 * 60 * 1000))
    return `${hours}小时后可用`
  }
  return '能量不足'
}

const getExploreCooldownText = () => canExplore.value ? '可用' : '状态不足'
const getStudyCooldownText = () => canStudy.value ? '可用' : '状态不足'
const getSocializeCooldownText = () => canSocialize.value ? '可用' : '状态不足'

// 方法 - 事件处理
const handleFeed = () => {
  emit('feed')
  addActivityRecord('喂食', '🍎', '健康度 +20', 'text-green-600')
}

const handlePlay = () => {
  emit('play')
  addActivityRecord('玩耍', '🎾', '快乐度 +15', 'text-yellow-600')
}

const handleRest = () => {
  emit('rest')
  addActivityRecord('休息', '😴', '能量 +30', 'text-blue-600')
}

const handleTrain = () => {
  emit('train')
  addActivityRecord('训练', '💪', '经验 +50', 'text-purple-600')
}

const handleExplore = () => {
  addActivityRecord('探索', '🗺️', '发现物品', 'text-green-600')
}

const handleStudy = () => {
  addActivityRecord('学习', '📚', '智力 +5', 'text-indigo-600')
}

const handleSocialize = () => {
  addActivityRecord('社交', '👥', '魅力 +3', 'text-pink-600')
}

// 方法 - 工具函数
const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const addActivityRecord = (name: string, icon: string, result: string, resultClass: string) => {
  const record: ActivityRecord = {
    id: Date.now().toString(),
    name,
    icon,
    timestamp: Date.now(),
    result,
    resultClass
  }

  recentActivities.value.unshift(record)
  if (recentActivities.value.length > 20) {
    recentActivities.value = recentActivities.value.slice(0, 20)
  }
}

const updateStatusHistory = () => {
  const point: StatusHistoryPoint = {
    timestamp: Date.now(),
    health: healthPercentage.value,
    happiness: happinessPercentage.value,
    energy: energyPercentage.value
  }

  statusHistory.value.push(point)

  // 保持最近24小时的数据
  const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
  statusHistory.value = statusHistory.value.filter(p => p.timestamp > oneDayAgo)

  // 限制数据点数量
  if (statusHistory.value.length > 48) {
    statusHistory.value = statusHistory.value.slice(-48)
  }
}

// 生命周期
onMounted(() => {
  // 初始化状态历史
  updateStatusHistory()

  // 设置定时器更新时间和状态
  statusUpdateTimer = window.setInterval(() => {
    currentTime.value = Date.now()
    updateStatusHistory()
  }, 60000) // 每分钟更新一次
})

onUnmounted(() => {
  if (statusUpdateTimer) {
    clearInterval(statusUpdateTimer)
  }
})
</script>

<style scoped>
.pet-status-monitor {
  transition: all 0.3s ease;
}

.status-card {
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.activity-card {
  transition: all 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.chart-bar {
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.activity-record {
  transition: all 0.2s ease;
}

.activity-record:hover {
  background-color: #f9fafb;
  transform: translateX(4px);
}

.activities-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.activities-list::-webkit-scrollbar {
  width: 4px;
}

.activities-list::-webkit-scrollbar-track {
  background: transparent;
}

.activities-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-cards {
    grid-template-columns: 1fr;
  }

  .activity-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 640px) {
  .monitor-content {
    padding: 1rem;
  }

  .status-card, .activity-card {
    padding: 0.75rem;
  }

  .activity-grid {
    gap: 0.75rem;
  }
}
</style>
