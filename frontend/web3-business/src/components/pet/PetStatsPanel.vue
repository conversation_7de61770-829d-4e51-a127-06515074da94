<template>
  <div class="pet-stats-panel bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 面板头部 -->
    <div class="panel-header bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold flex items-center space-x-2">
          <van-icon name="chart-trending-o" />
          <span>属性面板</span>
        </h3>
        <div class="flex items-center space-x-2">
          <van-button
            size="small"
            type="primary"
            @click="toggleDetailView"
            class="bg-white text-blue-500 hover:bg-gray-100"
          >
            {{ showDetailed ? '简化' : '详细' }}
          </van-button>
          <van-button
            size="small"
            type="primary"
            @click="refreshStats"
            class="bg-white text-blue-500 hover:bg-gray-100"
          >
            刷新
          </van-button>
        </div>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content p-6">
      <!-- 基础属性 -->
      <div class="basic-stats mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-blue-500">⚡</span>
          <span>基础属性</span>
        </h4>

        <div class="stats-grid grid grid-cols-2 md:grid-cols-3 gap-4">
          <div
            v-for="(stat, key) in basicStats"
            :key="key"
            class="stat-item bg-gray-50 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:bg-gray-100 hover:shadow-md"
            @click="handleStatClick(key, stat.value)"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="stat-icon text-lg">{{ stat.icon }}</span>
                <span class="stat-name text-sm font-medium text-gray-600">{{ stat.name }}</span>
              </div>
              <div class="stat-change" :class="getStatChangeClass(key)">
                {{ getStatChangeText(key) }}
              </div>
            </div>

            <div class="stat-value text-2xl font-bold" :class="stat.colorClass">
              {{ stat.value }}
            </div>

            <!-- 详细模式下显示更多信息 -->
            <div v-if="showDetailed" class="stat-details mt-2 space-y-1">
              <div class="flex justify-between text-xs text-gray-500">
                <span>基础值:</span>
                <span>{{ pet.baseStats[key as keyof typeof pet.baseStats] }}</span>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>装备加成:</span>
                <span>+{{ equipmentBonus[key as keyof typeof equipmentBonus] || 0 }}</span>
              </div>
              <div class="flex justify-between text-xs text-gray-500">
                <span>等级加成:</span>
                <span>+{{ levelBonus[key as keyof typeof levelBonus] || 0 }}</span>
              </div>
            </div>

            <!-- 属性进度条 -->
            <div v-if="showDetailed" class="stat-progress mt-3">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 rounded-full transition-all duration-500"
                  :class="stat.progressClass"
                  :style="{ width: `${getStatProgress(stat.value)}%` }"
                />
              </div>
              <div class="text-xs text-gray-500 mt-1 text-center">
                {{ stat.value }}/{{ getStatMax(key) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 战斗力评估 -->
      <div class="combat-power mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-red-500">⚔️</span>
          <span>战斗力评估</span>
        </h4>

        <div class="power-display bg-gradient-to-r from-red-500 to-orange-500 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm opacity-90 mb-1">总战斗力</div>
              <div class="text-3xl font-bold">{{ totalCombatPower }}</div>
            </div>
            <div class="power-rank text-right">
              <div class="text-sm opacity-90 mb-1">评级</div>
              <div class="text-xl font-bold">{{ combatRank }}</div>
            </div>
          </div>

          <div v-if="showDetailed" class="power-breakdown mt-4 grid grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
              <span>攻击力:</span>
              <span>{{ attackPower }}</span>
            </div>
            <div class="flex justify-between">
              <span>防御力:</span>
              <span>{{ defensePower }}</span>
            </div>
            <div class="flex justify-between">
              <span>敏捷度:</span>
              <span>{{ agilityPower }}</span>
            </div>
            <div class="flex justify-between">
              <span>魔法力:</span>
              <span>{{ magicPower }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性对比 -->
      <div v-if="showDetailed" class="stats-comparison mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-green-500">📊</span>
          <span>属性对比</span>
        </h4>

        <div class="comparison-chart bg-gray-50 rounded-lg p-4">
          <!-- 雷达图样式的属性对比 -->
          <div class="radar-chart relative w-full h-48 flex items-center justify-center">
            <svg viewBox="0 0 200 200" class="w-full h-full">
              <!-- 背景网格 -->
              <g class="grid-lines" stroke="#e5e7eb" stroke-width="1" fill="none">
                <polygon points="100,20 171,65 171,135 100,180 29,135 29,65" />
                <polygon points="100,40 151,75 151,125 100,160 49,125 49,75" />
                <polygon points="100,60 131,85 131,115 100,140 69,115 69,85" />
              </g>

              <!-- 属性数据 -->
              <polygon
                :points="radarPoints"
                fill="rgba(59, 130, 246, 0.3)"
                stroke="#3b82f6"
                stroke-width="2"
              />

              <!-- 属性点 -->
              <g v-for="(point, index) in radarPointsArray" :key="index">
                <circle
                  :cx="point.x"
                  :cy="point.y"
                  r="3"
                  fill="#3b82f6"
                  stroke="white"
                  stroke-width="2"
                />
              </g>
            </svg>

            <!-- 属性标签 -->
            <div class="absolute inset-0">
              <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4 text-xs font-medium">力量</div>
              <div class="absolute top-1/4 right-0 transform translate-x-4 -translate-y-2 text-xs font-medium">智力</div>
              <div class="absolute bottom-1/4 right-0 transform translate-x-4 translate-y-2 text-xs font-medium">敏捷</div>
              <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-4 text-xs font-medium">魅力</div>
              <div class="absolute bottom-1/4 left-0 transform -translate-x-4 translate-y-2 text-xs font-medium">体力</div>
              <div class="absolute top-1/4 left-0 transform -translate-x-4 -translate-y-2 text-xs font-medium">幸运</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性历史 -->
      <div v-if="showDetailed && statHistory.length > 0" class="stats-history">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-purple-500">📈</span>
          <span>属性变化历史</span>
        </h4>

        <div class="history-list space-y-2 max-h-40 overflow-y-auto">
          <div
            v-for="record in statHistory.slice(0, 10)"
            :key="record.id"
            class="history-item bg-gray-50 rounded-lg p-3 flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div class="change-icon text-lg">{{ record.icon }}</div>
              <div>
                <div class="text-sm font-medium">{{ record.description }}</div>
                <div class="text-xs text-gray-500">{{ formatTime(record.timestamp) }}</div>
              </div>
            </div>
            <div class="change-value" :class="record.changeClass">
              {{ record.change }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { Pet, PetStats } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
  showDetailed?: boolean
}

interface Emits {
  (e: 'stat-click', statName: string, value: number): void
}

interface StatHistoryRecord {
  id: string
  timestamp: number
  statName: string
  oldValue: number
  newValue: number
  change: string
  changeClass: string
  description: string
  icon: string
}

const props = withDefaults(defineProps<Props>(), {
  showDetailed: false
})

const emit = defineEmits<Emits>()

// 响应式状态
const showDetailed = ref(props.showDetailed)
const statHistory = ref<StatHistoryRecord[]>([])
const previousStats = ref<PetStats>({ ...props.pet.stats })

// 计算属性 - 基础属性配置
const basicStats = computed(() => ({
  strength: {
    name: '力量',
    icon: '💪',
    value: props.pet.stats.strength,
    colorClass: 'text-red-600',
    progressClass: 'bg-red-500'
  },
  intelligence: {
    name: '智力',
    icon: '🧠',
    value: props.pet.stats.intelligence,
    colorClass: 'text-blue-600',
    progressClass: 'bg-blue-500'
  },
  agility: {
    name: '敏捷',
    icon: '⚡',
    value: props.pet.stats.agility,
    colorClass: 'text-yellow-600',
    progressClass: 'bg-yellow-500'
  },
  charm: {
    name: '魅力',
    icon: '✨',
    value: props.pet.stats.charm,
    colorClass: 'text-pink-600',
    progressClass: 'bg-pink-500'
  },
  vitality: {
    name: '体力',
    icon: '❤️',
    value: props.pet.stats.vitality,
    colorClass: 'text-green-600',
    progressClass: 'bg-green-500'
  },
  luck: {
    name: '幸运',
    icon: '🍀',
    value: props.pet.stats.luck,
    colorClass: 'text-purple-600',
    progressClass: 'bg-purple-500'
  }
}))

// 计算属性 - 装备加成
const equipmentBonus = computed(() => {
  const bonus: Partial<PetStats> = {
    strength: 0,
    intelligence: 0,
    agility: 0,
    charm: 0,
    vitality: 0,
    luck: 0
  }

  props.pet.equipment.forEach(equipment => {
    if (equipment.stats) {
      Object.entries(equipment.stats).forEach(([stat, value]) => {
        if (value && stat in bonus) {
          bonus[stat as keyof PetStats] = (bonus[stat as keyof PetStats] || 0) + value
        }
      })
    }
  })

  return bonus
})

// 计算属性 - 等级加成
const levelBonus = computed(() => {
  const rarityMultipliers = {
    common: 1.0,
    uncommon: 1.2,
    rare: 1.5,
    epic: 2.0,
    legendary: 3.0,
    mythical: 5.0
  }

  const multiplier = rarityMultipliers[props.pet.rarity]
  const baseBonus = Math.floor((props.pet.level - 1) * 2 * multiplier)

  return {
    strength: baseBonus,
    intelligence: baseBonus,
    agility: baseBonus,
    charm: baseBonus,
    vitality: baseBonus,
    luck: Math.floor(baseBonus * 0.5)
  }
})

// 计算属性 - 战斗力
const attackPower = computed(() => {
  return Math.floor(props.pet.stats.strength * 1.5 + props.pet.stats.agility * 0.8)
})

const defensePower = computed(() => {
  return Math.floor(props.pet.stats.vitality * 1.2 + props.pet.stats.strength * 0.5)
})

const agilityPower = computed(() => {
  return Math.floor(props.pet.stats.agility * 1.3 + props.pet.stats.luck * 0.3)
})

const magicPower = computed(() => {
  return Math.floor(props.pet.stats.intelligence * 1.4 + props.pet.stats.charm * 0.6)
})

const totalCombatPower = computed(() => {
  return attackPower.value + defensePower.value + agilityPower.value + magicPower.value
})

const combatRank = computed(() => {
  const power = totalCombatPower.value
  if (power >= 1000) return 'S'
  if (power >= 800) return 'A'
  if (power >= 600) return 'B'
  if (power >= 400) return 'C'
  if (power >= 200) return 'D'
  return 'E'
})

// 计算属性 - 雷达图
const radarPoints = computed(() => {
  const stats = props.pet.stats
  const maxStat = 200 // 假设最大属性值为200
  const center = 100
  const radius = 60

  const points = [
    // 力量 (顶部)
    {
      x: center,
      y: center - (stats.strength / maxStat) * radius
    },
    // 智力 (右上)
    {
      x: center + Math.cos(Math.PI / 6) * (stats.intelligence / maxStat) * radius,
      y: center - Math.sin(Math.PI / 6) * (stats.intelligence / maxStat) * radius
    },
    // 敏捷 (右下)
    {
      x: center + Math.cos(-Math.PI / 6) * (stats.agility / maxStat) * radius,
      y: center - Math.sin(-Math.PI / 6) * (stats.agility / maxStat) * radius
    },
    // 魅力 (底部)
    {
      x: center,
      y: center + (stats.charm / maxStat) * radius
    },
    // 体力 (左下)
    {
      x: center - Math.cos(-Math.PI / 6) * (stats.vitality / maxStat) * radius,
      y: center - Math.sin(-Math.PI / 6) * (stats.vitality / maxStat) * radius
    },
    // 幸运 (左上)
    {
      x: center - Math.cos(Math.PI / 6) * (stats.luck / maxStat) * radius,
      y: center - Math.sin(Math.PI / 6) * (stats.luck / maxStat) * radius
    }
  ]

  return points.map(p => `${p.x},${p.y}`).join(' ')
})

const radarPointsArray = computed(() => {
  const stats = props.pet.stats
  const maxStat = 200
  const center = 100
  const radius = 60

  return [
    { x: center, y: center - (stats.strength / maxStat) * radius },
    { x: center + Math.cos(Math.PI / 6) * (stats.intelligence / maxStat) * radius, y: center - Math.sin(Math.PI / 6) * (stats.intelligence / maxStat) * radius },
    { x: center + Math.cos(-Math.PI / 6) * (stats.agility / maxStat) * radius, y: center - Math.sin(-Math.PI / 6) * (stats.agility / maxStat) * radius },
    { x: center, y: center + (stats.charm / maxStat) * radius },
    { x: center - Math.cos(-Math.PI / 6) * (stats.vitality / maxStat) * radius, y: center - Math.sin(-Math.PI / 6) * (stats.vitality / maxStat) * radius },
    { x: center - Math.cos(Math.PI / 6) * (stats.luck / maxStat) * radius, y: center - Math.sin(Math.PI / 6) * (stats.luck / maxStat) * radius }
  ]
})

// 方法
const toggleDetailView = () => {
  showDetailed.value = !showDetailed.value
}

const refreshStats = () => {
  // 触发属性刷新逻辑
  emit('stat-click', 'refresh', 0)
}

const handleStatClick = (statName: string, value: number) => {
  emit('stat-click', statName, value)
}

const getStatChangeClass = (statName: string) => {
  const current = props.pet.stats[statName as keyof PetStats]
  const previous = previousStats.value[statName as keyof PetStats]

  if (current > previous) return 'text-green-500 text-xs'
  if (current < previous) return 'text-red-500 text-xs'
  return 'text-gray-400 text-xs'
}

const getStatChangeText = (statName: string) => {
  const current = props.pet.stats[statName as keyof PetStats]
  const previous = previousStats.value[statName as keyof PetStats]
  const change = current - previous

  if (change > 0) return `+${change}`
  if (change < 0) return `${change}`
  return '—'
}

const getStatProgress = (value: number) => {
  const maxStat = 200 // 假设最大属性值
  return Math.min((value / maxStat) * 100, 100)
}

const getStatMax = (statName: string) => {
  // 根据等级和稀有度计算属性上限
  const baseMax = 100
  const levelBonus = props.pet.level * 2
  const rarityMultiplier = {
    common: 1.0,
    uncommon: 1.2,
    rare: 1.5,
    epic: 2.0,
    legendary: 3.0,
    mythical: 5.0
  }[props.pet.rarity]

  return Math.floor((baseMax + levelBonus) * rarityMultiplier)
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

const addStatHistoryRecord = (statName: string, oldValue: number, newValue: number, description: string) => {
  const change = newValue - oldValue
  const record: StatHistoryRecord = {
    id: Date.now().toString(),
    timestamp: Date.now(),
    statName,
    oldValue,
    newValue,
    change: change > 0 ? `+${change}` : `${change}`,
    changeClass: change > 0 ? 'text-green-500 font-medium' : change < 0 ? 'text-red-500 font-medium' : 'text-gray-500',
    description,
    icon: change > 0 ? '📈' : change < 0 ? '📉' : '➖'
  }

  statHistory.value.unshift(record)
  if (statHistory.value.length > 50) {
    statHistory.value = statHistory.value.slice(0, 50)
  }
}

// 监听属性变化
watch(() => props.pet.stats, (newStats, oldStats) => {
  if (oldStats) {
    Object.entries(newStats).forEach(([statName, newValue]) => {
      const oldValue = oldStats[statName as keyof PetStats]
      if (newValue !== oldValue) {
        const statDisplayName = basicStats.value[statName as keyof typeof basicStats.value]?.name || statName
        addStatHistoryRecord(statName, oldValue, newValue, `${statDisplayName}属性变化`)
      }
    })
  }
  previousStats.value = { ...newStats }
}, { deep: true })
</script>

<style scoped>
.pet-stats-panel {
  transition: all 0.3s ease;
}

.stat-item {
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.power-display {
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
}

.radar-chart svg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.history-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.history-list::-webkit-scrollbar {
  width: 4px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .power-breakdown {
    grid-template-columns: 1fr;
  }

  .radar-chart {
    height: 200px;
  }
}

@media (max-width: 640px) {
  .panel-content {
    padding: 1rem;
  }

  .stats-grid {
    gap: 0.75rem;
  }

  .stat-item {
    padding: 0.75rem;
  }
}
</style>