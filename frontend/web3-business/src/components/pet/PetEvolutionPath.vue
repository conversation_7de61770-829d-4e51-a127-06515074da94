<template>
  <div class="pet-evolution-path p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold">进化路径</h3>
      <van-icon name="cross" @click="$emit('close')" class="cursor-pointer" />
    </div>

    <div class="evolution-content">
      <div class="current-pet text-center mb-6">
        <PetDisplay :pet="pet" size="medium" />
        <div class="mt-2">
          <div class="font-medium">{{ pet.name }}</div>
          <div class="text-sm text-gray-500">当前形态</div>
        </div>
      </div>

      <div class="evolution-paths space-y-4">
        <div class="evolution-option bg-gray-50 rounded-lg p-4">
          <div class="text-center">
            <div class="text-4xl mb-2">🦋</div>
            <div class="font-medium">进化形态</div>
            <div class="text-sm text-gray-500 mb-3">需要等级 20</div>
            <van-button
              type="primary"
              size="small"
              :disabled="pet.level < 20"
              @click="$emit('evolve', 'butterfly')"
            >
              {{ pet.level >= 20 ? '进化' : `还需 ${20 - pet.level} 级` }}
            </van-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PetDisplay from './PetDisplay.vue'
import type { Pet } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
}

interface Emits {
  (e: 'close'): void
  (e: 'evolve', path: string): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
