<template>
  <div class="pet-exchange-history">
    <!-- 统计信息 -->
    <van-cell-group class="mb-4">
      <van-cell title="总兑换次数" :value="stats.confirmedTransactions" />
      <van-cell title="总获得代币" :value="formatTokenAmount(stats.totalTokensEarned)" />
      <van-cell title="平均每只萌宠" :value="formatTokenAmount(stats.averageTokensPerPet)" />
      <van-cell title="成功率" :value="`${stats.successRate}%`" />
    </van-cell-group>

    <!-- 稀有度统计 -->
    <van-collapse v-model="showRarityStats" class="mb-4">
      <van-collapse-item title="按稀有度统计" name="rarity">
        <div class="space-y-2 p-2">
          <div
            v-for="(stat, rarity) in stats.rarityStats"
            :key="rarity"
            class="flex justify-between items-center"
          >
            <span class="flex items-center">
              <van-tag :type="getRarityTagType(rarity)" size="small" class="mr-2">
                {{ getRarityText(rarity) }}
              </van-tag>
              <span>{{ stat.count }} 只</span>
            </span>
            <span class="text-sm">{{ formatTokenAmount(stat.totalTokens.toString()) }}</span>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>

    <!-- 交易历史列表 -->
    <div class="mb-4">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-semibold">交易历史</h3>
        <van-button
          v-if="history.transactions.length > 0"
          size="small"
          type="danger"
          plain
          @click="showClearDialog = true"
        >
          清空历史
        </van-button>
      </div>

      <!-- 筛选器 -->
      <van-cell-group class="mb-4">
        <van-field
          v-model="searchQuery"
          placeholder="搜索萌宠名称..."
          left-icon="search"
          clearable
        />
        <van-field
          v-model="statusFilter"
          label="状态筛选"
          placeholder="选择状态"
          readonly
          is-link
          @click="showStatusPicker = true"
        />
      </van-cell-group>
    </div>

    <!-- 交易列表 -->
    <div v-if="filteredTransactions.length > 0" class="space-y-3">
      <van-card
        v-for="transaction in paginatedTransactions"
        :key="transaction.id"
        :title="transaction.petName"
        :desc="`等级 ${transaction.petLevel} | ${getRarityText(transaction.petRarity)}`"
        class="transaction-card"
        @click="showTransactionDetail(transaction)"
      >
        <template #tags>
          <van-tag
            :type="getStatusTagType(transaction.status)"
            size="small"
          >
            {{ getStatusText(transaction.status) }}
          </van-tag>
        </template>

        <template #footer>
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-600">
              {{ formatDate(transaction.timestamp) }}
            </span>
            <span class="font-semibold text-primary-600">
              {{ formatTokenAmount(transaction.tokenAmount) }}
            </span>
          </div>
        </template>
      </van-card>

      <!-- 分页 -->
      <van-pagination
        v-if="totalPages > 1"
        v-model="currentPage"
        :total-items="filteredTransactions.length"
        :items-per-page="pageSize"
        :show-page-size="false"
        class="mt-4"
      />
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else-if="!isLoading"
      description="暂无兑换记录"
      image="search"
    />

    <!-- 加载状态 -->
    <van-loading v-if="isLoading" class="flex justify-center py-8" />

    <!-- 状态选择器 -->
    <van-popup v-model:show="showStatusPicker" position="bottom">
      <van-picker
        :columns="statusOptions"
        @confirm="onStatusConfirm"
        @cancel="showStatusPicker = false"
      />
    </van-popup>

    <!-- 交易详情对话框 -->
    <van-dialog
      v-model:show="showDetailDialog"
      :title="`交易详情 - ${selectedTransaction?.petName}`"
      :show-cancel-button="false"
      confirm-button-text="关闭"
    >
      <template #message>
        <div v-if="selectedTransaction" class="text-left space-y-3 p-4">
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span class="text-gray-600">萌宠名称:</span>
              <span class="ml-2">{{ selectedTransaction.petName }}</span>
            </div>
            <div>
              <span class="text-gray-600">等级:</span>
              <span class="ml-2">{{ selectedTransaction.petLevel }}</span>
            </div>
            <div>
              <span class="text-gray-600">稀有度:</span>
              <span class="ml-2">{{ getRarityText(selectedTransaction.petRarity) }}</span>
            </div>
            <div>
              <span class="text-gray-600">状态:</span>
              <van-tag
                :type="getStatusTagType(selectedTransaction.status)"
                size="small"
                class="ml-2"
              >
                {{ getStatusText(selectedTransaction.status) }}
              </van-tag>
            </div>
            <div class="col-span-2">
              <span class="text-gray-600">代币数量:</span>
              <span class="ml-2 font-semibold">{{ formatTokenAmount(selectedTransaction.tokenAmount) }}</span>
            </div>
            <div class="col-span-2">
              <span class="text-gray-600">交易时间:</span>
              <span class="ml-2">{{ formatFullDate(selectedTransaction.timestamp) }}</span>
            </div>
            <div v-if="selectedTransaction.transactionHash" class="col-span-2">
              <span class="text-gray-600">交易哈希:</span>
              <div class="mt-1">
                <van-button
                  size="mini"
                  type="primary"
                  plain
                  @click="copyToClipboard(selectedTransaction.transactionHash)"
                >
                  {{ selectedTransaction.transactionHash.slice(0, 20) }}...
                </van-button>
              </div>
            </div>
            <div v-if="selectedTransaction.blockNumber" class="col-span-2">
              <span class="text-gray-600">区块号:</span>
              <span class="ml-2">{{ selectedTransaction.blockNumber }}</span>
            </div>
            <div v-if="selectedTransaction.gasUsed" class="col-span-2">
              <span class="text-gray-600">Gas 使用:</span>
              <span class="ml-2">{{ selectedTransaction.gasUsed }}</span>
            </div>
          </div>
        </div>
      </template>
    </van-dialog>

    <!-- 清空确认对话框 -->
    <van-dialog
      v-model:show="showClearDialog"
      title="确认清空"
      message="确定要清空所有兑换历史记录吗？此操作不可撤销。"
      show-cancel-button
      confirm-button-text="确认清空"
      cancel-button-text="取消"
      @confirm="clearHistory"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showToast } from 'vant'
import { ethers } from 'ethers'
import { tokenExchangeService } from '../../services/token-exchange.service'
import type { ExchangeHistory, ExchangeTransaction } from '../../services/token-exchange.service'

// 响应式数据
const isLoading = ref(false)
const history = ref<ExchangeHistory>({
  transactions: [],
  totalExchanged: '0',
  totalPetsExchanged: 0,
  lastExchangeTime: 0
})
const stats = ref<any>({})
const showRarityStats = ref<string[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showStatusPicker = ref(false)
const showDetailDialog = ref(false)
const showClearDialog = ref(false)
const selectedTransaction = ref<ExchangeTransaction | null>(null)

// 状态选项
const statusOptions = [
  { text: '全部', value: '' },
  { text: '待确认', value: 'pending' },
  { text: '已确认', value: 'confirmed' },
  { text: '失败', value: 'failed' }
]

// 计算属性
const filteredTransactions = computed(() => {
  let filtered = history.value.transactions

  // 按名称搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tx =>
      tx.petName.toLowerCase().includes(query)
    )
  }

  // 按状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(tx => tx.status === statusFilter.value)
  }

  return filtered
})

const totalPages = computed(() => {
  return Math.ceil(filteredTransactions.value.length / pageSize.value)
})

const paginatedTransactions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTransactions.value.slice(start, end)
})

// 方法
const loadHistory = () => {
  isLoading.value = true
  try {
    history.value = tokenExchangeService.getExchangeHistory()
    stats.value = tokenExchangeService.getExchangeStats()
  } catch (error) {
    console.error('加载兑换历史失败:', error)
    showToast('加载历史记录失败')
  } finally {
    isLoading.value = false
  }
}

const formatTokenAmount = (amount: string): string => {
  try {
    const formatted = ethers.formatEther(amount)
    return `${parseFloat(formatted).toFixed(6)} MTK`
  } catch {
    return '0 MTK'
  }
}

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

const formatFullDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

const getRarityText = (rarity: string): string => {
  const rarityMap: Record<string, string> = {
    'common': '普通',
    'uncommon': '稀有',
    'rare': '珍稀',
    'epic': '史诗',
    'legendary': '传说',
    'mythical': '神话'
  }
  return rarityMap[rarity] || rarity
}

const getRarityTagType = (rarity: string): string => {
  const typeMap: Record<string, string> = {
    'common': 'default',
    'uncommon': 'primary',
    'rare': 'success',
    'epic': 'warning',
    'legendary': 'danger',
    'mythical': 'primary'
  }
  return typeMap[rarity] || 'default'
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': '待确认',
    'confirmed': '已确认',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'confirmed': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || 'default'
}

const showTransactionDetail = (transaction: ExchangeTransaction) => {
  selectedTransaction.value = transaction
  showDetailDialog.value = true
}

const onStatusConfirm = ({ selectedOptions }: any) => {
  statusFilter.value = selectedOptions[0].value
  showStatusPicker.value = false
  currentPage.value = 1 // 重置到第一页
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    showToast('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败')
  }
}

const clearHistory = () => {
  try {
    tokenExchangeService.clearExchangeHistory()
    loadHistory()
    showToast('历史记录已清空')
  } catch (error) {
    console.error('清空历史失败:', error)
    showToast('清空失败')
  }
  showClearDialog.value = false
}

const refreshHistory = () => {
  loadHistory()
}

// 生命周期
onMounted(() => {
  loadHistory()
})

// 暴露方法给父组件
defineExpose({
  refreshHistory
})
</script>

<style scoped>
.pet-exchange-history {
  padding: 16px;
}

.transaction-card {
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.transaction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.text-primary-600 {
  color: #7c3aed;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.gap-2 {
  gap: 0.5rem;
}
</style>