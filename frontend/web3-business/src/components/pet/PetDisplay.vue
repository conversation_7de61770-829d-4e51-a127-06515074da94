<template>
  <div
    class="pet-display"
    :class="[sizeClass, { interactive }]"
    @click="handleClick"
    @mouseenter="handleHover"
    @mouseleave="handleLeave"
  >
    <!-- 稀有度光环 -->
    <div
      v-if="showRarityGlow"
      class="rarity-glow"
      :class="rarityClass"
    ></div>

    <!-- 宠物图像 -->
    <div class="pet-image">
      <img
        :src="petImageSrc"
        :alt="pet.name"
        @error="handleImageError"
      />
    </div>

    <!-- 宠物交互组件 -->
    <PetInteractionSimple
      v-if="interactive && enableInteraction"
      :pet="pet"
      :size="size"
      @feed="$emit('feed', pet)"
      @play="$emit('play', pet)"
      @train="$emit('train', pet)"
      @rest="$emit('rest', pet)"
      @pet="$emit('pet', pet)"
      @talk="$emit('talk', pet)"
    />

    <!-- 宠物状态指示器 -->
    <div v-if="showStats" class="pet-stats">
      <div class="stat-bar health">
        <div class="stat-fill" :style="{ width: `${healthPercentage}%` }"></div>
      </div>
      <div class="stat-bar happiness">
        <div class="stat-fill" :style="{ width: `${happinessPercentage}%` }"></div>
      </div>
      <div class="stat-bar energy">
        <div class="stat-fill" :style="{ width: `${energyPercentage}%` }"></div>
      </div>
    </div>

    <!-- 宠物等级 -->
    <div v-if="showLevel" class="pet-level">
      <span>Lv.{{ pet.level }} </span>
    </div>

    <!-- 宠物名称 -->
    <div v-if="showName" class="pet-name">
      <span>{{ pet.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Pet } from '../../types/typesWithoutCircular'
// import PetInteractionSimple from './PetInteractionSimple.vue'

// 定义组件属性
interface Props {
  pet: Pet
  size?: 'small' | 'medium' | 'large' | 'huge'
  interactive?: boolean // 交互
  showStats?: boolean // 展示状态
  showRarityGlow?: boolean
  showLevel?: boolean
  showName?: boolean
  showInteractionButtons?: boolean
  enableInteraction?: boolean
}

// 定义事件
interface Emits {
  (e: 'click', pet: Pet): void
  (e: 'hover', pet: Pet): void
  (e: 'leave', pet: Pet): void
  (e: 'feed', pet: Pet): void
  (e: 'play', pet: Pet): void
  (e: 'train', pet: Pet): void
  (e: 'rest', pet: Pet): void
  (e: 'pet', pet: Pet): void
  (e: 'talk', pet: Pet): void
  (e: 'interaction', type: string, pet: Pet): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  interactive: true,
  showStats: false,
  showRarityGlow: false,
  showLevel: true,
  showName: true,
  showInteractionButtons: false,
  enableInteraction: true
})

const emit = defineEmits<Emits>()

// 计算属性
const sizeClass = computed(() => `size-${props.size}`)

const rarityClass = computed(() => `rarity-${props.pet.rarity}`)

const petImageSrc = computed(() => {
  // 如果有自定义头像，使用自定义头像
  if (props.pet.avatar) return props.pet.avatar

  // 否则根据宠物类型和品种选择默认图片
  const type = props.pet.type || 'cat'
  const pattern = props.pet.appearance?.pattern || 'default'
  return `/images/pets/${type}/${pattern}.svg`
})

const healthPercentage = computed(() => {
  return (props.pet.health / props.pet.maxHealth) * 100
})

const happinessPercentage = computed(() => {
  return (props.pet.happiness / props.pet.maxHappiness) * 100
})

const energyPercentage = computed(() => {
  return (props.pet.energy / props.pet.maxEnergy) * 100
})

// 方法
const handleClick = () => {
  if (props.interactive) {
    emit('click', props.pet)
  }
}

const handleHover = () => {
  if (props.interactive) {
    emit('hover', props.pet)
  }
}

const handleLeave = () => {
  if (props.interactive) {
    emit('leave', props.pet)
  }
}

const handleImageError = (event: Event) => {
  // 图片加载失败时，使用默认图片
  const target = event.target as HTMLImageElement
  const type = props.pet.type || 'cat'

  // 首先尝试该类型的默认图片
  if (!target.src.includes('/default.svg')) {
    target.src = `/images/pets/${type}/default.svg`
  } else {
    // 如果类型默认图片也失败，使用通用默认图片
    target.src = '/images/pets/default-pet.svg'
  }
}

// 定义组件选项
defineOptions({
  name: 'PetDisplay'
})
</script>

<style scoped>
.pet-display {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.pet-display.interactive {
  cursor: pointer;
}

.pet-display.interactive:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* 尺寸样式 */
.pet-display.size-small {
  width: 100px;
  height: 100px;
}

.pet-display.size-medium {
  width: 150px;
  height: 150px;
}

.pet-display.size-large {
  width: 200px;
  height: 200px;
}

.pet-display.size-huge {
  width: 300px;
  height: 300px;
}

/* 稀有度光环 */
.rarity-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 20px;
  opacity: 0.5;
  z-index: -1;
  animation: pulse 2s infinite;
}

.rarity-common {
  box-shadow: 0 0 20px rgba(107, 114, 128, 0.5);
}

.rarity-uncommon {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.rarity-rare {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.rarity-epic {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.rarity-legendary {
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

.rarity-mythical {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

/* 宠物图像 */
.pet-image {
  width: 80%;
  height: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 宠物状态指示器 */
.pet-stats {
  position: absolute;
  bottom: 5px;
  left: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.stat-bar {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.health .stat-fill {
  background-color: #34d399;
}

.happiness .stat-fill {
  background-color: #fbbf24;
}

.energy .stat-fill {
  background-color: #60a5fa;
}

/* 宠物等级 */
.pet-level {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 宠物名称 */
.pet-name {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: 600;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 交互组件覆盖层 */
.pet-interaction-overlay {
  position: absolute;
  inset: 0;
  z-index: 10;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .pet-display.size-huge {
    width: 250px;
    height: 250px;
  }
}
</style>
