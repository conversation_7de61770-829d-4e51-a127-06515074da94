<template>
  <div
    class="pet-animation-container relative"
    :class="containerClasses"
  >
    <!-- Lottie 动画层 -->
    <div
      v-if="useLottie && lottieAnimationData"
      class="lottie-layer absolute inset-0"
    >
      <Vue3Lottie
        :animationData="lottieAnimationData"
        :height="lottieSize"
        :width="lottieSize"
        :loop="lottieLoop"
        :autoPlay="lottieAutoPlay"
        :speed="animationSpeed"
        @onComplete="handleAnimationComplete"
        @onLoopComplete="handleLoopComplete"
      />
    </div>

    <!-- CSS 动画层 -->
    <div
      v-else
      class="css-animation-layer relative w-full h-full"
      :class="cssAnimationClasses"
    >
      <!-- 萌宠主体 -->
      <div
        class="pet-sprite relative z-10"
        :class="spriteClasses"
        v-motion
        :initial="motionConfig.initial"
        :animate="motionConfig.animate"
        :variants="motionConfig.variants"
      >
        <slot name="pet-content">
          <!-- 使用优化的渲染器 -->
          <PetRenderer
            :pet="pet"
            :size="size"
            :animation="animation"
            :effects="['rarity', 'status', 'animation']"
            :optimized="false"
            :useCanvas="animation === 'levelup' || animation === 'evolution'"
          />
        </slot>
      </div>

      <!-- 粒子效果层 -->
      <div
        v-if="showParticles"
        class="particles-layer absolute inset-0 pointer-events-none"
      >
        <div
          v-for="particle in particles"
          :key="particle.id"
          class="particle absolute"
          :class="particle.classes"
          :style="particle.style"
        />
      </div>

      <!-- 轨迹效果层 -->
      <div
        v-if="showTrail"
        class="trail-layer absolute inset-0 pointer-events-none"
      >
        <div
          v-for="trail in trailPoints"
          :key="trail.id"
          class="trail-point absolute w-2 h-2 rounded-full"
          :class="trailClasses"
          :style="trail.style"
        />
      </div>
    </div>

    <!-- 特效叠加层 -->
    <div class="effects-overlay absolute inset-0 pointer-events-none">
      <!-- 升级特效 -->
      <div
        v-if="showLevelUpEffect"
        class="level-up-effect absolute inset-0 flex items-center justify-center"
      >
        <div class="level-up-text text-yellow-400 font-bold text-2xl animate-bounce">
          LEVEL UP!
        </div>
        <div class="level-up-sparkles absolute inset-0">
          <div
            v-for="sparkle in levelUpSparkles"
            :key="sparkle.id"
            class="sparkle absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping"
            :style="sparkle.style"
          />
        </div>
      </div>

      <!-- 进化特效 -->
      <div
        v-if="showEvolutionEffect"
        class="evolution-effect absolute inset-0 flex items-center justify-center"
      >
        <div class="evolution-light absolute inset-0 bg-white opacity-80 animate-pulse" />
        <div class="evolution-text text-purple-400 font-bold text-3xl animate-pulse z-10">
          EVOLUTION!
        </div>
      </div>

      <!-- 心情气泡 -->
      <div
        v-if="showMoodBubble"
        class="mood-bubble absolute -top-8 left-1/2 transform -translate-x-1/2"
      >
        <div
          class="bubble bg-white rounded-full p-2 shadow-lg animate-bounce"
          :class="moodBubbleClasses"
        >
          <span class="text-lg">{{ moodEmoji }}</span>
        </div>
        <div class="bubble-tail absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import { useMotion } from '@vueuse/motion'
import { Vue3Lottie } from 'vue3-lottie'
import PetRenderer from './PetRenderer.vue'
import type { Pet, PetRarity } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
  animation: 'idle' | 'walk' | 'run' | 'jump' | 'play' | 'eat' | 'sleep' | 'happy' | 'sad' | 'levelup' | 'evolution'
  size?: 'small' | 'medium' | 'large'
  loop?: boolean
  autoPlay?: boolean
  speed?: number
  useLottie?: boolean
  showParticles?: boolean
  showTrail?: boolean
  customClass?: string
}

interface Emits {
  (e: 'animationComplete'): void
  (e: 'loopComplete'): void
}

const props = withDefaults(defineProps<Props>(), {
  animation: 'idle',
  size: 'medium',
  loop: true,
  autoPlay: true,
  speed: 1,
  useLottie: false,
  showParticles: false,
  showTrail: false,
  customClass: ''
})

const emit = defineEmits<Emits>()

// 响应式状态
const particles = ref<Array<{
  id: string
  classes: string
  style: Record<string, string>
}>>([])

const trailPoints = ref<Array<{
  id: string
  style: Record<string, string>
}>>([])

const levelUpSparkles = ref<Array<{
  id: string
  style: Record<string, string>
}>>([])

const showLevelUpEffect = ref(false)
const showEvolutionEffect = ref(false)
const showMoodBubble = ref(false)

let particleInterval: number | null = null
let trailInterval: number | null = null

// 计算属性
const containerClasses = computed(() => {
  const sizes = {
    small: 'w-16 h-16',
    medium: 'w-24 h-24',
    large: 'w-32 h-32'
  }
  return `${sizes[props.size]} ${props.customClass}`
})

const lottieSize = computed(() => {
  const sizes = {
    small: 64,
    medium: 96,
    large: 128
  }
  return sizes[props.size]
})

const lottieLoop = computed(() => props.loop)
const lottieAutoPlay = computed(() => props.autoPlay)
const animationSpeed = computed(() => props.speed)

const lottieAnimationData = ref<any>(null)

// 动态加载Lottie动画数据
const loadLottieAnimation = async () => {
  try {
    // 根据萌宠类型和动画类型构建路径
    const animationPath = `/animations/${props.animation}.json`
    const petTypePath = `/animations/${props.pet.type}/${props.animation}.json`
    const petSpecificPath = `/animations/${props.pet.type}/${props.pet.appearance.color}_${props.animation}.json`

    // 尝试加载特定萌宠的动画
    try {
      const response = await fetch(petSpecificPath)
      if (response.ok) {
        lottieAnimationData.value = await response.json()
        return
      }
    } catch (error) {
      console.warn(`Failed to load pet specific animation: ${petSpecificPath}`)
    }

    // 尝试加载萌宠类型的动画
    try {
      const response = await fetch(petTypePath)
      if (response.ok) {
        lottieAnimationData.value = await response.json()
        return
      }
    } catch (error) {
      console.warn(`Failed to load pet type animation: ${petTypePath}`)
    }

    // 加载通用动画
    const response = await fetch(animationPath)
    if (response.ok) {
      lottieAnimationData.value = await response.json()
    } else {
      // 如果所有尝试都失败，加载默认的空闲动画
      const defaultResponse = await fetch('/animations/pet-idle.json')
      if (defaultResponse.ok) {
        lottieAnimationData.value = await defaultResponse.json()
      }
    }
  } catch (error) {
    console.error('Failed to load Lottie animation:', error)
    lottieAnimationData.value = null
  }
}

const cssAnimationClasses = computed(() => {
  const animations = {
    idle: 'animate-pulse',
    walk: 'animate-bounce',
    run: 'animate-bounce',
    jump: 'animate-bounce',
    play: 'animate-spin',
    eat: 'animate-pulse',
    sleep: 'animate-pulse',
    happy: 'animate-bounce',
    sad: 'animate-pulse',
    levelup: 'animate-ping',
    evolution: 'animate-spin'
  }
  return animations[props.animation]
})

const spriteClasses = computed(() => {
  let classes = 'transition-all duration-500'

  // 根据动画类型添加特殊样式
  switch (props.animation) {
    case 'sleep':
      classes += ' opacity-70 grayscale'
      break
    case 'happy':
      classes += ' brightness-110'
      break
    case 'sad':
      classes += ' opacity-80 saturate-50'
      break
    case 'levelup':
      classes += ' brightness-125 saturate-150'
      break
    case 'evolution':
      classes += ' brightness-150 saturate-200'
      break
  }

  return classes
})

const trailClasses = computed(() => {
  const colors = {
    common: 'bg-gray-400',
    uncommon: 'bg-green-400',
    rare: 'bg-blue-400',
    epic: 'bg-purple-400',
    legendary: 'bg-yellow-400',
    mythical: 'bg-red-400'
  }
  return `${colors[props.pet.rarity]} animate-ping`
})

const moodEmoji = computed(() => {
  const emojis = {
    happy: '😊',
    content: '😌',
    sad: '😢',
    angry: '😠',
    excited: '🤩',
    tired: '😴',
    playful: '😄'
  }
  return emojis[props.pet.mood as keyof typeof emojis] || '😌'
})

const moodBubbleClasses = computed(() => {
  const colors = {
    happy: 'bg-yellow-100 border-yellow-300',
    content: 'bg-green-100 border-green-300',
    sad: 'bg-blue-100 border-blue-300',
    angry: 'bg-red-100 border-red-300',
    excited: 'bg-purple-100 border-purple-300',
    tired: 'bg-gray-100 border-gray-300',
    playful: 'bg-pink-100 border-pink-300'
  }
  return `border-2 ${colors[props.pet.mood as keyof typeof colors] || 'bg-green-100 border-green-300'}`
})

// Motion 配置
const motionConfig = computed(() => {
  const configs = {
    idle: {
      initial: { scale: 1, rotate: 0 },
      animate: {
        scale: [1, 1.05, 1],
        rotate: [0, 1, -1, 0],
        transition: {
          duration: 3000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    walk: {
      initial: { x: 0, rotate: 0 },
      animate: {
        x: [0, 10, 0, -10, 0],
        rotate: [0, 5, 0, -5, 0],
        transition: {
          duration: 2000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    run: {
      initial: { x: 0, scaleX: 1 },
      animate: {
        x: [0, 20, 0, -20, 0],
        scaleX: [1, 1.1, 1, 1.1, 1],
        transition: {
          duration: 1000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    jump: {
      initial: { y: 0, scale: 1 },
      animate: {
        y: [0, -20, 0],
        scale: [1, 0.9, 1.1, 1],
        transition: {
          duration: 800,
          repeat: Infinity,
          ease: 'easeOut'
        }
      },
      variants: {}
    },
    play: {
      initial: { rotate: 0, scale: 1 },
      animate: {
        rotate: [0, 360],
        scale: [1, 1.2, 1],
        transition: {
          duration: 1500,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    eat: {
      initial: { scaleY: 1, y: 0 },
      animate: {
        scaleY: [1, 0.9, 1],
        y: [0, 2, 0],
        transition: {
          duration: 1000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    sleep: {
      initial: { scale: 1, opacity: 1 },
      animate: {
        scale: [1, 0.95, 1],
        opacity: [1, 0.8, 1],
        transition: {
          duration: 4000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    happy: {
      initial: { y: 0, rotate: 0 },
      animate: {
        y: [0, -10, 0],
        rotate: [0, 10, -10, 0],
        transition: {
          duration: 600,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    sad: {
      initial: { y: 0, opacity: 1 },
      animate: {
        y: [0, 5, 0],
        opacity: [1, 0.7, 1],
        transition: {
          duration: 2000,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      },
      variants: {}
    },
    levelup: {
      initial: { scale: 1, rotate: 0 },
      animate: {
        scale: [1, 1.5, 1.2, 1],
        rotate: [0, 360, 0],
        transition: {
          duration: 2000,
          repeat: 1,
          ease: 'easeOut'
        }
      },
      variants: {}
    },
    evolution: {
      initial: { scale: 1, opacity: 1 },
      animate: {
        scale: [1, 0.5, 2, 1],
        opacity: [1, 0, 1, 1],
        transition: {
          duration: 3000,
          repeat: 1,
          ease: 'easeInOut'
        }
      },
      variants: {}
    }
  }

  return configs[props.animation] || configs.idle
})

// 方法
const handleAnimationComplete = () => {
  emit('animationComplete')

  // 特殊动画完成后的处理
  if (props.animation === 'levelup') {
    showLevelUpEffect.value = false
  } else if (props.animation === 'evolution') {
    showEvolutionEffect.value = false
  }
}

const handleLoopComplete = () => {
  emit('loopComplete')
}

const generateParticles = () => {
  if (!props.showParticles) return

  const particleCount = 5
  const newParticles: Array<{
    id: string
    classes: string
    style: Record<string, string>
  }> = []

  for (let i = 0; i < particleCount; i++) {
    const particle = {
      id: `particle-${Date.now()}-${i}`,
      classes: getParticleClasses(),
      style: getParticleStyle()
    }
    newParticles.push(particle)
  }

  particles.value = newParticles

  // 清理旧粒子
  setTimeout(() => {
    particles.value = particles.value.filter(p =>
      !newParticles.some(np => np.id === p.id)
    )
  }, 2000)
}

const generateTrail = () => {
  if (!props.showTrail) return

  const trail = {
    id: `trail-${Date.now()}`,
    style: {
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      animationDelay: '0s'
    }
  }

  trailPoints.value.push(trail)

  // 限制轨迹点数量
  if (trailPoints.value.length > 10) {
    trailPoints.value.shift()
  }

  // 清理旧轨迹点
  setTimeout(() => {
    trailPoints.value = trailPoints.value.filter(t => t.id !== trail.id)
  }, 1000)
}

const getParticleClasses = () => {
  const colors = ['bg-yellow-400', 'bg-blue-400', 'bg-green-400', 'bg-purple-400', 'bg-pink-400']
  const animations = ['animate-ping', 'animate-pulse', 'animate-bounce']

  const color = colors[Math.floor(Math.random() * colors.length)]
  const animation = animations[Math.floor(Math.random() * animations.length)]

  return `w-2 h-2 rounded-full ${color} ${animation}`
}

const getParticleStyle = () => {
  return {
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    animationDelay: `${Math.random() * 1000}ms`,
    animationDuration: `${1000 + Math.random() * 2000}ms`
  }
}

const generateLevelUpSparkles = () => {
  const sparkleCount = 8
  const sparkles = []

  for (let i = 0; i < sparkleCount; i++) {
    const angle = (i / sparkleCount) * 2 * Math.PI
    const radius = 40
    const x = 50 + Math.cos(angle) * radius
    const y = 50 + Math.sin(angle) * radius

    sparkles.push({
      id: `sparkle-${i}`,
      style: {
        left: `${x}%`,
        top: `${y}%`,
        animationDelay: `${i * 100}ms`
      }
    })
  }

  levelUpSparkles.value = sparkles
}

const triggerLevelUpEffect = () => {
  showLevelUpEffect.value = true
  generateLevelUpSparkles()

  setTimeout(() => {
    showLevelUpEffect.value = false
  }, 3000)
}

const triggerEvolutionEffect = () => {
  showEvolutionEffect.value = true

  setTimeout(() => {
    showEvolutionEffect.value = false
  }, 4000)
}

const showMoodBubbleTemporary = () => {
  showMoodBubble.value = true

  setTimeout(() => {
    showMoodBubble.value = false
  }, 3000)
}

// 生命周期
onMounted(() => {
  // 加载Lottie动画
  if (props.useLottie) {
    loadLottieAnimation()
  }

  // 根据动画类型设置特效
  if (props.animation === 'levelup') {
    triggerLevelUpEffect()
  } else if (props.animation === 'evolution') {
    triggerEvolutionEffect()
  }

  // 启动粒子效果
  if (props.showParticles) {
    particleInterval = window.setInterval(generateParticles, 1000)
  }

  // 启动轨迹效果
  if (props.showTrail) {
    trailInterval = window.setInterval(generateTrail, 100)
  }

  // 显示心情气泡
  if (props.pet.mood !== 'content') {
    setTimeout(showMoodBubbleTemporary, 1000)
  }
})

onUnmounted(() => {
  if (particleInterval) {
    clearInterval(particleInterval)
  }
  if (trailInterval) {
    clearInterval(trailInterval)
  }
})

// 监听动画变化
watch(() => props.animation, (newAnimation) => {
  if (newAnimation === 'levelup') {
    triggerLevelUpEffect()
  } else if (newAnimation === 'evolution') {
    triggerEvolutionEffect()
  }

  // 重新加载Lottie动画
  if (props.useLottie) {
    loadLottieAnimation()
  }
})

// 监听萌宠心情变化
watch(() => props.pet.mood, (newMood) => {
  if (newMood !== 'content') {
    showMoodBubbleTemporary()
  }
})

// 暴露方法给父组件
defineExpose({
  triggerLevelUpEffect,
  triggerEvolutionEffect,
  showMoodBubbleTemporary
})
</script>

<style scoped>
.pet-animation-container {
  position: relative;
  overflow: visible;
}

.particle {
  pointer-events: none;
  z-index: 20;
}

.trail-point {
  pointer-events: none;
  z-index: 15;
  opacity: 0.6;
}

.level-up-effect {
  z-index: 30;
  animation: fadeInOut 3s ease-in-out;
}

.evolution-effect {
  z-index: 30;
  animation: evolutionPulse 4s ease-in-out;
}

.mood-bubble {
  z-index: 25;
  animation: bubbleFloat 3s ease-in-out;
}

.sparkle {
  z-index: 35;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes evolutionPulse {
  0%, 100% { opacity: 0; transform: scale(0.5); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes bubbleFloat {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px) scale(0.8);
  }
  20%, 80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px) scale(0.8);
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .level-up-text {
    font-size: 1.25rem;
  }

  .evolution-text {
    font-size: 1.5rem;
  }

  .mood-bubble {
    transform: translateX(-50%) scale(0.8);
  }
}
</style>
