<template>
  <div class="pet-skills-traits bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 组件头部 -->
    <div class="component-header bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold flex items-center space-x-2">
          <van-icon name="star-o" />
          <span>技能与特质</span>
        </h3>
        <div class="flex items-center space-x-2">
          <van-button
            size="small"
            type="primary"
            @click="toggleView"
            class="bg-white text-indigo-500 hover:bg-gray-100"
          >
            {{ viewMode === 'skills' ? '特质' : '技能' }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 组件内容 -->
    <div class="component-content p-6">
      <!-- 技能视图 -->
      <div v-if="viewMode === 'skills'" class="skills-view">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-800 flex items-center space-x-2">
            <span class="text-blue-500">⚡</span>
            <span>已学技能</span>
            <span class="text-sm text-gray-500">({{ pet.skills.length }})</span>
          </h4>
          <van-button size="small" @click="showAvailableSkills = true">
            学习新技能
          </van-button>
        </div>

        <!-- 已学技能列表 -->
        <div v-if="pet.skills.length > 0" class="skills-grid grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div
            v-for="skill in pet.skills"
            :key="skill.id"
            class="skill-card bg-blue-50 border border-blue-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            @click="selectedSkill = skill; showSkillDetail = true"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="skill-icon text-2xl">{{ skill.icon }}</span>
                <div>
                  <h5 class="font-medium text-blue-800">{{ skill.name }}</h5>
                  <div class="text-sm text-blue-600">{{ skill.type }}</div>
                </div>
              </div>
              <div class="skill-level bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                Lv.{{ skill.level }}
              </div>
            </div>

            <div class="skill-description text-sm text-gray-600 mb-3">
              {{ skill.description }}
            </div>

            <!-- 技能效果 -->
            <div class="skill-effects space-y-1">
              <div
                v-for="effect in skill.effects"
                :key="effect.type"
                class="effect-item text-xs bg-white rounded px-2 py-1 flex justify-between"
              >
                <span>{{ effect.description }}</span>
                <span class="text-green-600 font-medium">{{ formatEffectValue(effect) }}</span>
              </div>
            </div>

            <!-- 技能经验条 -->
            <div class="skill-exp mt-3">
              <div class="flex justify-between text-xs text-gray-500 mb-1">
                <span>经验值</span>
                <span>{{ skill.experience }}/{{ skill.maxExperience }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 bg-blue-500 rounded-full transition-all duration-300"
                  :style="{ width: `${(skill.experience / skill.maxExperience) * 100}%` }"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 空技能提示 -->
        <div v-else class="empty-skills text-center py-12">
          <van-empty description="还没有学会任何技能" />
          <van-button type="primary" @click="showAvailableSkills = true" class="mt-4">
            学习第一个技能
          </van-button>
        </div>
      </div>

      <!-- 特质视图 -->
      <div v-else class="traits-view">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-800 flex items-center space-x-2">
            <span class="text-purple-500">✨</span>
            <span>天赋特质</span>
            <span class="text-sm text-gray-500">({{ pet.traits.length }})</span>
          </h4>
        </div>

        <!-- 特质列表 -->
        <div v-if="pet.traits.length > 0" class="traits-grid grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div
            v-for="trait in pet.traits"
            :key="trait.id"
            class="trait-card bg-purple-50 border border-purple-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            @click="selectedTrait = trait; showTraitDetail = true"
          >
            <div class="flex items-start justify-between mb-2">
              <div class="flex items-center space-x-2">
                <span class="trait-icon text-2xl">{{ trait.icon }}</span>
                <div>
                  <h5 class="font-medium text-purple-800">{{ trait.name }}</h5>
                  <div class="text-sm" :class="getRarityTextClass(trait.rarity)">
                    {{ getRarityText(trait.rarity) }}
                  </div>
                </div>
              </div>
              <div class="trait-level bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                Lv.{{ trait.level }}
              </div>
            </div>

            <div class="trait-description text-sm text-gray-600 mb-3">
              {{ trait.description }}
            </div>

            <!-- 特质效果 -->
            <div class="trait-effects space-y-1">
              <div
                v-for="effect in trait.effects"
                :key="effect.type"
                class="effect-item text-xs bg-white rounded px-2 py-1 flex justify-between"
              >
                <span>{{ effect.description }}</span>
                <span class="text-green-600 font-medium">{{ formatEffectValue(effect) }}</span>
              </div>
            </div>

            <!-- 升级按钮 -->
            <div class="trait-actions mt-3">
              <van-button
                size="mini"
                type="primary"
                :disabled="!canUpgradeTrait(trait)"
                @click.stop="handleUpgradeTrait(trait.id)"
                class="w-full bg-purple-500 border-purple-500"
              >
                {{ getUpgradeButtonText(trait) }}
              </van-button>
            </div>
          </div>
        </div>

        <!-- 空特质提示 -->
        <div v-else class="empty-traits text-center py-12">
          <van-empty description="还没有觉醒任何特质" />
          <div class="text-sm text-gray-500 mt-2">
            特质会在萌宠成长过程中随机觉醒
          </div>
        </div>
      </div>

      <!-- 技能组合效果 -->
      <div v-if="skillCombos.length > 0" class="skill-combos mt-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-orange-500">🔥</span>
          <span>技能组合</span>
        </h4>

        <div class="combos-grid grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="combo in skillCombos"
            :key="combo.id"
            class="combo-card bg-orange-50 border border-orange-200 rounded-lg p-4"
          >
            <div class="flex items-center space-x-2 mb-2">
              <span class="combo-icon text-xl">{{ combo.icon }}</span>
              <h5 class="font-medium text-orange-800">{{ combo.name }}</h5>
            </div>

            <div class="combo-description text-sm text-gray-600 mb-3">
              {{ combo.description }}
            </div>

            <div class="combo-requirements text-xs text-orange-600">
              需要技能: {{ combo.requiredSkills.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可学习技能弹窗 -->
    <van-popup
      v-model:show="showAvailableSkills"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div class="available-skills p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">可学习技能</h3>
          <van-icon name="cross" @click="showAvailableSkills = false" class="cursor-pointer" />
        </div>

        <div class="skills-list space-y-4 max-h-96 overflow-y-auto">
          <div
            v-for="skill in availableSkills"
            :key="skill.id"
            class="skill-item bg-gray-50 rounded-lg p-4 flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <span class="text-2xl">{{ skill.icon }}</span>
              <div>
                <h4 class="font-medium">{{ skill.name }}</h4>
                <div class="text-sm text-gray-600">{{ skill.description }}</div>
                <div class="text-xs text-gray-500 mt-1">
                  学习条件: {{ getSkillRequirements(skill) }}
                </div>
              </div>
            </div>

            <van-button
              size="small"
              type="primary"
              :disabled="!canLearnSkill(skill)"
              @click="handleLearnSkill(skill.id)"
            >
              {{ getLearnButtonText(skill) }}
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 技能详情弹窗 -->
    <van-popup
      v-model:show="showSkillDetail"
      position="center"
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div v-if="selectedSkill" class="skill-detail p-6">
        <div class="detail-header text-center mb-4">
          <div class="skill-icon text-4xl mb-2">{{ selectedSkill.icon }}</div>
          <h3 class="text-lg font-bold text-blue-800">{{ selectedSkill.name }}</h3>
          <div class="text-sm text-gray-500">{{ selectedSkill.type }} · Lv.{{ selectedSkill.level }}</div>
        </div>

        <div class="skill-info space-y-4">
          <div>
            <h4 class="font-medium mb-2">技能描述</h4>
            <p class="text-sm text-gray-600">{{ selectedSkill.description }}</p>
          </div>

          <div>
            <h4 class="font-medium mb-2">技能效果</h4>
            <div class="effects-list space-y-2">
              <div
                v-for="effect in selectedSkill.effects"
                :key="effect.type"
                class="effect-item bg-gray-50 rounded p-2 text-sm"
              >
                {{ effect.description }}: <span class="text-green-600 font-medium">{{ formatEffectValue(effect) }}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-medium mb-2">升级进度</h4>
            <div class="progress-info">
              <div class="flex justify-between text-sm text-gray-500 mb-1">
                <span>经验值</span>
                <span>{{ selectedSkill.experience }}/{{ selectedSkill.maxExperience }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 bg-blue-500 rounded-full"
                  :style="{ width: `${(selectedSkill.experience / selectedSkill.maxExperience) * 100}%` }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 特质详情弹窗 -->
    <van-popup
      v-model:show="showTraitDetail"
      position="center"
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div v-if="selectedTrait" class="trait-detail p-6">
        <div class="detail-header text-center mb-4">
          <div class="trait-icon text-4xl mb-2">{{ selectedTrait.icon }}</div>
          <h3 class="text-lg font-bold text-purple-800">{{ selectedTrait.name }}</h3>
          <div class="text-sm" :class="getRarityTextClass(selectedTrait.rarity)">
            {{ getRarityText(selectedTrait.rarity) }} · Lv.{{ selectedTrait.level }}
          </div>
        </div>

        <div class="trait-info space-y-4">
          <div>
            <h4 class="font-medium mb-2">特质描述</h4>
            <p class="text-sm text-gray-600">{{ selectedTrait.description }}</p>
          </div>

          <div>
            <h4 class="font-medium mb-2">特质效果</h4>
            <div class="effects-list space-y-2">
              <div
                v-for="effect in selectedTrait.effects"
                :key="effect.type"
                class="effect-item bg-gray-50 rounded p-2 text-sm"
              >
                {{ effect.description }}: <span class="text-green-600 font-medium">{{ formatEffectValue(effect) }}</span>
              </div>
            </div>
          </div>

          <div class="trait-actions">
            <van-button
              type="primary"
              :disabled="!canUpgradeTrait(selectedTrait)"
              @click="handleUpgradeTrait(selectedTrait.id); showTraitDetail = false"
              class="w-full bg-purple-500 border-purple-500"
            >
              {{ getUpgradeButtonText(selectedTrait) }}
            </van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { Pet, PetSkill, PetTrait, PetRarity } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
}

interface Emits {
  (e: 'learn-skill', skillId: string): void
  (e: 'upgrade-trait', traitId: string): void
}

interface SkillCombo {
  id: string
  name: string
  icon: string
  description: string
  requiredSkills: string[]
  effects: any[]
}

interface AvailableSkill {
  id: string
  name: string
  icon: string
  description: string
  type: string
  requirements: {
    level: number
    stats?: Record<string, number>
    traits?: string[]
  }
  cost: {
    experience: number
    items?: string[]
  }
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const viewMode = ref<'skills' | 'traits'>('skills')
const showAvailableSkills = ref(false)
const showSkillDetail = ref(false)
const showTraitDetail = ref(false)
const selectedSkill = ref<PetSkill | null>(null)
const selectedTrait = ref<PetTrait | null>(null)

// 计算属性 - 技能组合
const skillCombos = computed<SkillCombo[]>(() => {
  const combos: SkillCombo[] = []
  const skillNames = props.pet.skills.map(s => s.name)

  // 检查各种技能组合
  if (skillNames.includes('火球术') && skillNames.includes('冰箭术')) {
    combos.push({
      id: 'fire-ice-combo',
      name: '冰火双重奏',
      icon: '🔥❄️',
      description: '同时释放火球和冰箭，造成额外伤害',
      requiredSkills: ['火球术', '冰箭术'],
      effects: []
    })
  }

  if (skillNames.includes('治疗术') && skillNames.includes('护盾术')) {
    combos.push({
      id: 'heal-shield-combo',
      name: '神圣守护',
      description: '治疗的同时获得护盾保护',
      icon: '✨🛡️',
      requiredSkills: ['治疗术', '护盾术'],
      effects: []
    })
  }

  return combos
})

// 计算属性 - 可学习技能
const availableSkills = computed<AvailableSkill[]>(() => {
  const learnedSkillIds = props.pet.skills.map(s => s.id)

  const allSkills: AvailableSkill[] = [
    {
      id: 'fireball',
      name: '火球术',
      icon: '🔥',
      description: '发射火球攻击敌人',
      type: '攻击技能',
      requirements: { level: 5, stats: { intelligence: 20 } },
      cost: { experience: 100 }
    },
    {
      id: 'heal',
      name: '治疗术',
      icon: '💚',
      description: '恢复自身或队友的生命值',
      type: '治疗技能',
      requirements: { level: 3, stats: { intelligence: 15, charm: 10 } },
      cost: { experience: 80 }
    },
    {
      id: 'shield',
      name: '护盾术',
      icon: '🛡️',
      description: '为自己或队友提供护盾保护',
      type: '防御技能',
      requirements: { level: 8, stats: { vitality: 25 } },
      cost: { experience: 120 }
    },
    {
      id: 'speed-boost',
      name: '疾风步',
      icon: '💨',
      description: '大幅提升移动速度',
      type: '辅助技能',
      requirements: { level: 6, stats: { agility: 30 } },
      cost: { experience: 90 }
    },
    {
      id: 'lucky-strike',
      name: '幸运一击',
      icon: '🍀',
      description: '攻击有概率造成暴击伤害',
      type: '被动技能',
      requirements: { level: 10, stats: { luck: 40 } },
      cost: { experience: 150 }
    }
  ]

  return allSkills.filter(skill => !learnedSkillIds.includes(skill.id))
})

// 方法
const toggleView = () => {
  viewMode.value = viewMode.value === 'skills' ? 'traits' : 'skills'
}

const handleLearnSkill = (skillId: string) => {
  emit('learn-skill', skillId)
  showAvailableSkills.value = false
}

const handleUpgradeTrait = (traitId: string) => {
  emit('upgrade-trait', traitId)
}

const canLearnSkill = (skill: AvailableSkill): boolean => {
  // 检查等级要求
  if (props.pet.level < skill.requirements.level) {
    return false
  }

  // 检查属性要求
  if (skill.requirements.stats) {
    for (const [stat, required] of Object.entries(skill.requirements.stats)) {
      if (props.pet.stats[stat as keyof typeof props.pet.stats] < required) {
        return false
      }
    }
  }

  // 检查经验要求
  if (props.pet.experience < skill.cost.experience) {
    return false
  }

  return true
}

const canUpgradeTrait = (trait: PetTrait): boolean => {
  // 简单的升级条件检查
  const requiredExp = trait.level * 100
  return props.pet.experience >= requiredExp && trait.level < 10
}

const getLearnButtonText = (skill: AvailableSkill): string => {
  if (!canLearnSkill(skill)) {
    if (props.pet.level < skill.requirements.level) {
      return `需要${skill.requirements.level}级`
    }
    if (props.pet.experience < skill.cost.experience) {
      return '经验不足'
    }
    return '条件不足'
  }
  return '学习'
}

const getUpgradeButtonText = (trait: PetTrait): string => {
  if (!canUpgradeTrait(trait)) {
    if (trait.level >= 10) {
      return '已满级'
    }
    return '经验不足'
  }
  return '升级'
}

const getSkillRequirements = (skill: AvailableSkill): string => {
  const requirements = []

  requirements.push(`${skill.requirements.level}级`)

  if (skill.requirements.stats) {
    for (const [stat, value] of Object.entries(skill.requirements.stats)) {
      const statNames = {
        strength: '力量',
        intelligence: '智力',
        agility: '敏捷',
        charm: '魅力',
        vitality: '体力',
        luck: '幸运'
      }
      requirements.push(`${statNames[stat as keyof typeof statNames]}${value}`)
    }
  }

  requirements.push(`${skill.cost.experience}经验`)

  return requirements.join(', ')
}

const getRarityText = (rarity: PetRarity): string => {
  const rarityTexts = {
    common: '普通',
    uncommon: '优秀',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return rarityTexts[rarity]
}

const getRarityTextClass = (rarity: PetRarity): string => {
  const classes = {
    common: 'text-gray-600',
    uncommon: 'text-green-600',
    rare: 'text-blue-600',
    epic: 'text-purple-600',
    legendary: 'text-yellow-600',
    mythical: 'text-red-600'
  }
  return classes[rarity]
}

const formatEffectValue = (effect: any): string => {
  if (effect.type === 'stat_bonus') {
    return `+${effect.value}`
  }
  if (effect.type === 'percentage_bonus') {
    return `+${effect.value}%`
  }
  if (effect.type === 'special_ability') {
    return effect.value
  }
  return effect.value?.toString() || ''
}
</script>

<style scoped>
.pet-skills-traits {
  transition: all 0.3s ease;
}

.skill-card, .trait-card {
  transition: all 0.2s ease;
}

.skill-card:hover, .trait-card:hover {
  transform: translateY(-2px);
}

.combo-card {
  transition: all 0.3s ease;
}

.combo-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.skills-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.skills-list::-webkit-scrollbar {
  width: 4px;
}

.skills-list::-webkit-scrollbar-track {
  background: transparent;
}

.skills-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .skills-grid, .traits-grid, .combos-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .component-content {
    padding: 1rem;
  }

  .skill-card, .trait-card, .combo-card {
    padding: 0.75rem;
  }
}
</style>
