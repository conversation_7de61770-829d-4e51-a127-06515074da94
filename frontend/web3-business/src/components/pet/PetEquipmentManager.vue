<template>
  <div class="pet-equipment-manager bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 管理器头部 -->
    <div class="manager-header bg-gradient-to-r from-green-500 to-teal-600 text-white p-4">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold flex items-center space-x-2">
          <van-icon name="bag-o" />
          <span>装备管理</span>
        </h3>
        <div class="flex items-center space-x-2">
          <van-button
            size="small"
            type="primary"
            @click="toggleView"
            class="bg-white text-green-500 hover:bg-gray-100"
          >
            {{ viewMode === 'equipped' ? '背包' : '已装备' }}
          </van-button>
          <van-button
            size="small"
            type="primary"
            @click="sortEquipment"
            class="bg-white text-green-500 hover:bg-gray-100"
          >
            排序
          </van-button>
        </div>
      </div>
    </div>

    <!-- 管理器内容 -->
    <div class="manager-content p-6">
      <!-- 装备槽位视图 -->
      <div v-if="viewMode === 'equipped'" class="equipment-slots">
        <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center space-x-2">
          <span class="text-green-500">⚔️</span>
          <span>装备槽位</span>
        </h4>

        <!-- 装备槽位网格 -->
        <div class="slots-grid grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div
            v-for="slot in equipmentSlots"
            :key="slot.type"
            class="equipment-slot relative bg-gray-50 rounded-lg p-4 border-2 border-dashed transition-all duration-200"
            :class="getSlotClasses(slot)"
            @click="handleSlotClick(slot)"
          >
            <!-- 槽位图标 -->
            <div class="slot-icon text-center mb-2">
              <div class="text-2xl">{{ getSlotIcon(slot.type) }}</div>
              <div class="text-xs text-gray-500 mt-1">{{ getSlotName(slot.type) }}</div>
            </div>

            <!-- 已装备的物品 -->
            <div v-if="slot.equipped" class="equipped-item">
              <div class="item-image relative mb-2">
                <img
                  :src="getEquipmentImageSrc(slot.equipped)"
                  :alt="slot.equipped.name"
                  class="w-full h-12 object-contain rounded"
                  @error="handleImageError"
                />
                <!-- 稀有度边框 -->
                <div
                  class="absolute inset-0 rounded border-2"
                  :class="getRarityBorderClass(slot.equipped.rarity)"
                />
              </div>

              <div class="item-info text-center">
                <div class="item-name text-xs font-medium truncate" :title="slot.equipped.name">
                  {{ slot.equipped.name }}
                </div>
                <div class="item-level text-xs text-gray-500">
                  Lv.{{ slot.equipped.level }}
                </div>
              </div>

              <!-- 卸下按钮 -->
              <van-button
                size="mini"
                type="danger"
                @click.stop="handleUnequip(slot.equipped!)"
                class="absolute top-1 right-1 w-6 h-6 p-0"
              >
                ×
              </van-button>
            </div>

            <!-- 空槽位提示 -->
            <div v-else class="empty-slot text-center">
              <div class="text-gray-400 text-xs">
                {{ slot.locked ? `${slot.unlockLevel}级解锁` : '点击装备' }}
              </div>
            </div>

            <!-- 锁定状态 -->
            <div v-if="slot.locked" class="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
              <van-icon name="lock" class="text-white text-lg" />
            </div>
          </div>
        </div>

        <!-- 套装加成显示 -->
        <div v-if="setBonuses.length > 0" class="set-bonuses">
          <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center space-x-2">
            <span class="text-purple-500">✨</span>
            <span>套装效果</span>
          </h4>

          <div class="set-list space-y-3">
            <div
              v-for="setBonus in setBonuses"
              :key="setBonus.setName"
              class="set-item bg-purple-50 rounded-lg p-4 border border-purple-200"
            >
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-purple-800">{{ setBonus.setName }}</h5>
                <span class="text-sm text-purple-600">
                  {{ setBonus.currentPieces }}/{{ setBonus.requiredPieces }}
                </span>
              </div>

              <div class="set-effects space-y-1">
                <div
                  v-for="effect in setBonus.effects"
                  :key="effect.type"
                  class="effect-item text-sm"
                  :class="setBonus.currentPieces >= setBonus.requiredPieces ? 'text-green-600' : 'text-gray-500'"
                >
                  {{ effect.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 背包视图 -->
      <div v-else class="inventory-view">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-800 flex items-center space-x-2">
            <span class="text-blue-500">🎒</span>
            <span>背包物品</span>
            <span class="text-sm text-gray-500">({{ filteredInventory.length }})</span>
          </h4>

          <!-- 筛选器 -->
          <div class="filters flex items-center space-x-2">
            <van-dropdown-menu>
              <van-dropdown-item v-model="filterType" :options="typeFilterOptions" />
              <van-dropdown-item v-model="filterRarity" :options="rarityFilterOptions" />
            </van-dropdown-menu>
          </div>
        </div>

        <!-- 物品网格 -->
        <div class="inventory-grid grid grid-cols-3 md:grid-cols-6 gap-3">
          <div
            v-for="item in filteredInventory"
            :key="item.id"
            class="inventory-item relative bg-gray-50 rounded-lg p-3 border-2 border-transparent cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-md"
            @click="handleItemClick(item)"
          >
            <!-- 物品图像 -->
            <div class="item-image relative mb-2">
              <img
                :src="getEquipmentImageSrc(item)"
                :alt="item.name"
                class="w-full h-12 object-contain rounded"
                @error="handleImageError"
              />
              <!-- 稀有度边框 -->
              <div
                class="absolute inset-0 rounded border-2"
                :class="getRarityBorderClass(item.rarity)"
              />

              <!-- 数量标识 -->
              <div v-if="getItemQuantity(item) > 1" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {{ getItemQuantity(item) }}
              </div>
            </div>

            <!-- 物品信息 -->
            <div class="item-info text-center">
              <div class="item-name text-xs font-medium truncate" :title="item.name">
                {{ item.name }}
              </div>
              <div class="item-level text-xs text-gray-500">
                Lv.{{ item.level }}
              </div>
            </div>

            <!-- 装备按钮 -->
            <van-button
              v-if="canEquipItem(item)"
              size="mini"
              type="primary"
              @click.stop="handleEquip(item)"
              class="absolute top-1 right-1 w-6 h-6 p-0"
            >
              +
            </van-button>

            <!-- 不可装备标识 -->
            <div v-else-if="!canEquipItem(item)" class="absolute top-1 right-1 text-red-500">
              <van-icon name="warning-o" size="12" />
            </div>
          </div>
        </div>

        <!-- 空背包提示 -->
        <div v-if="filteredInventory.length === 0" class="empty-inventory text-center py-12">
          <van-empty description="背包空空如也" />
          <van-button type="primary" @click="$router.push('/shop')" class="mt-4">
            去商店看看
          </van-button>
        </div>
      </div>
    </div>

    <!-- 装备详情弹窗 -->
    <van-popup
      v-model:show="showEquipmentDetail"
      position="center"
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div v-if="selectedEquipment" class="equipment-detail p-6">
        <div class="detail-header text-center mb-4">
          <img
            :src="getEquipmentImageSrc(selectedEquipment)"
            :alt="selectedEquipment.name"
            class="w-16 h-16 mx-auto mb-2 rounded"
            @error="handleImageError"
          />
          <h3 class="text-lg font-bold" :class="getRarityTextClass(selectedEquipment.rarity)">
            {{ selectedEquipment.name }}
          </h3>
          <div class="text-sm text-gray-500">{{ selectedEquipment.description }}</div>
        </div>

        <!-- 装备属性 -->
        <div v-if="selectedEquipment.stats" class="equipment-stats mb-4">
          <h4 class="font-medium mb-2">属性加成</h4>
          <div class="stats-list space-y-1">
            <div
              v-for="(value, stat) in selectedEquipment.stats"
              :key="stat"
              class="stat-item flex justify-between text-sm"
            >
              <span>{{ getStatName(stat) }}:</span>
              <span class="text-green-600 font-medium">+{{ value }}</span>
            </div>
          </div>
        </div>

        <!-- 装备需求 -->
        <div v-if="selectedEquipment.requirements.length > 0" class="equipment-requirements mb-4">
          <h4 class="font-medium mb-2">装备需求</h4>
          <div class="requirements-list space-y-1">
            <div
              v-for="req in selectedEquipment.requirements"
              :key="`${req.type}-${req.target}`"
              class="requirement-item text-sm"
              :class="checkRequirement(req) ? 'text-green-600' : 'text-red-600'"
            >
              {{ getRequirementText(req) }}
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions flex space-x-2">
          <van-button
            v-if="isEquipped(selectedEquipment)"
            type="danger"
            @click="handleUnequip(selectedEquipment)"
            class="flex-1"
          >
            卸下装备
          </van-button>
          <van-button
            v-else-if="canEquipItem(selectedEquipment)"
            type="primary"
            @click="handleEquip(selectedEquipment)"
            class="flex-1"
          >
            装备
          </van-button>
          <van-button @click="$emit('view-equipment', selectedEquipment)" class="flex-1">
            详细信息
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { showToast } from 'vant'
import type { Pet, Equipment, Item, EquipmentType, PetRarity, EquipmentRequirement, SetBonus } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
  inventory: Item[]
}

interface Emits {
  (e: 'equip-item', equipment: Equipment): void
  (e: 'unequip-item', equipment: Equipment): void
  (e: 'view-equipment', equipment: Equipment): void
}

interface EquipmentSlot {
  type: EquipmentType
  equipped: Equipment | null
  locked: boolean
  unlockLevel: number
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const viewMode = ref<'equipped' | 'inventory'>('equipped')
const showEquipmentDetail = ref(false)
const selectedEquipment = ref<Equipment | null>(null)
const filterType = ref('all')
const filterRarity = ref('all')
const sortBy = ref('level')

// 计算属性 - 装备槽位
const equipmentSlots = computed<EquipmentSlot[]>(() => {
  const slots = [
    { type: 'weapon' as EquipmentType, unlockLevel: 1 },
    { type: 'armor' as EquipmentType, unlockLevel: 1 },
    { type: 'helmet' as EquipmentType, unlockLevel: 5 },
    { type: 'boots' as EquipmentType, unlockLevel: 8 },
    { type: 'gloves' as EquipmentType, unlockLevel: 10 },
    { type: 'accessory' as EquipmentType, unlockLevel: 15 },
    { type: 'ring' as EquipmentType, unlockLevel: 20 },
    { type: 'necklace' as EquipmentType, unlockLevel: 25 }
  ]

  return slots.map(slot => {
    const equipped = props.pet.equipment.find(eq => eq.type === slot.type) || null
    return {
      type: slot.type,
      equipped,
      locked: props.pet.level < slot.unlockLevel,
      unlockLevel: slot.unlockLevel
    }
  })
})

// 计算属性 - 套装加成
const setBonuses = computed<SetBonus[]>(() => {
  const setItems = new Map<string, Equipment[]>()

  // 按套装分组
  props.pet.equipment.forEach(equipment => {
    if (equipment.setBonus) {
      const setName = equipment.setBonus.setName
      if (!setItems.has(setName)) {
        setItems.set(setName, [])
      }
      setItems.get(setName)!.push(equipment)
    }
  })

  // 计算套装加成
  const bonuses: SetBonus[] = []
  setItems.forEach((items, setName) => {
    const setBonus = items[0].setBonus!
    bonuses.push({
      ...setBonus,
      currentPieces: items.length
    })
  })

  return bonuses
})

// 计算属性 - 筛选后的背包物品
const filteredInventory = computed(() => {
  let items = props.inventory.filter(item => item.type && Object.values(['weapon', 'armor', 'helmet', 'boots', 'gloves', 'accessory', 'ring', 'necklace']).includes(item.type))

  // 类型筛选
  if (filterType.value !== 'all') {
    items = items.filter(item => item.type === filterType.value)
  }

  // 稀有度筛选
  if (filterRarity.value !== 'all') {
    items = items.filter(item => item.rarity === filterRarity.value)
  }

  // 排序
  items.sort((a, b) => {
    switch (sortBy.value) {
      case 'level':
        return b.level - a.level
      case 'rarity':
        const rarityOrder = { common: 1, uncommon: 2, rare: 3, epic: 4, legendary: 5, mythical: 6 }
        return rarityOrder[b.rarity] - rarityOrder[a.rarity]
      case 'name':
        return a.name.localeCompare(b.name)
      default:
        return 0
    }
  })

  return items as Equipment[]
})

// 计算属性 - 筛选选项
const typeFilterOptions = computed(() => [
  { text: '全部', value: 'all' },
  { text: '武器', value: 'weapon' },
  { text: '护甲', value: 'armor' },
  { text: '头盔', value: 'helmet' },
  { text: '靴子', value: 'boots' },
  { text: '手套', value: 'gloves' },
  { text: '饰品', value: 'accessory' },
  { text: '戒指', value: 'ring' },
  { text: '项链', value: 'necklace' }
])

const rarityFilterOptions = computed(() => [
  { text: '全部', value: 'all' },
  { text: '普通', value: 'common' },
  { text: '优秀', value: 'uncommon' },
  { text: '稀有', value: 'rare' },
  { text: '史诗', value: 'epic' },
  { text: '传说', value: 'legendary' },
  { text: '神话', value: 'mythical' }
])

// 方法
const toggleView = () => {
  viewMode.value = viewMode.value === 'equipped' ? 'inventory' : 'equipped'
}

const sortEquipment = () => {
  const sortOptions = ['level', 'rarity', 'name']
  const currentIndex = sortOptions.indexOf(sortBy.value)
  sortBy.value = sortOptions[(currentIndex + 1) % sortOptions.length]

  const sortNames = { level: '等级', rarity: '稀有度', name: '名称' }
  showToast(`按${sortNames[sortBy.value as keyof typeof sortNames]}排序`)
}

const handleSlotClick = (slot: EquipmentSlot) => {
  if (slot.locked) {
    showToast(`${slot.unlockLevel}级解锁此槽位`)
    return
  }

  if (slot.equipped) {
    selectedEquipment.value = slot.equipped
    showEquipmentDetail.value = true
  } else {
    // 切换到背包视图并筛选对应类型
    viewMode.value = 'inventory'
    filterType.value = slot.type
  }
}

const handleItemClick = (item: Equipment) => {
  selectedEquipment.value = item
  showEquipmentDetail.value = true
}

const handleEquip = (equipment: Equipment) => {
  emit('equip-item', equipment)
  showEquipmentDetail.value = false
}

const handleUnequip = (equipment: Equipment) => {
  emit('unequip-item', equipment)
  showEquipmentDetail.value = false
}

const canEquipItem = (equipment: Equipment): boolean => {
  // 检查等级需求
  const levelReq = equipment.requirements.find(req => req.type === 'level')
  if (levelReq && props.pet.level < (levelReq.value as number)) {
    return false
  }

  // 检查稀有度需求
  const rarityReq = equipment.requirements.find(req => req.type === 'rarity')
  if (rarityReq) {
    const rarityOrder = { common: 1, uncommon: 2, rare: 3, epic: 4, legendary: 5, mythical: 6 }
    const requiredRarity = rarityOrder[rarityReq.value as PetRarity]
    const petRarity = rarityOrder[props.pet.rarity]
    if (petRarity < requiredRarity) {
      return false
    }
  }

  // 检查种族需求
  const speciesReq = equipment.requirements.find(req => req.type === 'species')
  if (speciesReq && props.pet.type !== speciesReq.value) {
    return false
  }

  // 检查槽位是否解锁
  const slot = equipmentSlots.value.find(s => s.type === equipment.type)
  if (slot?.locked) {
    return false
  }

  return true
}

const isEquipped = (equipment: Equipment): boolean => {
  return props.pet.equipment.some(eq => eq.id === equipment.id)
}

const checkRequirement = (requirement: EquipmentRequirement): boolean => {
  switch (requirement.type) {
    case 'level':
      return props.pet.level >= (requirement.value as number)
    case 'rarity':
      const rarityOrder = { common: 1, uncommon: 2, rare: 3, epic: 4, legendary: 5, mythical: 6 }
      const requiredRarity = rarityOrder[requirement.value as PetRarity]
      const petRarity = rarityOrder[props.pet.rarity]
      return petRarity >= requiredRarity
    case 'species':
      return props.pet.type === requirement.value
    case 'stat':
      const statValue = props.pet.stats[requirement.target as keyof typeof props.pet.stats]
      return statValue >= (requirement.value as number)
    default:
      return true
  }
}

const getSlotClasses = (slot: EquipmentSlot) => {
  let classes = ''

  if (slot.locked) {
    classes += 'border-gray-300 bg-gray-100 cursor-not-allowed'
  } else if (slot.equipped) {
    classes += 'border-green-300 bg-green-50 cursor-pointer hover:border-green-400'
  } else {
    classes += 'border-gray-300 cursor-pointer hover:border-blue-300 hover:bg-blue-50'
  }

  return classes
}

const getSlotIcon = (type: EquipmentType): string => {
  const icons = {
    weapon: '⚔️',
    armor: '🛡️',
    helmet: '⛑️',
    boots: '👢',
    gloves: '🧤',
    accessory: '💎',
    ring: '💍',
    necklace: '📿'
  }
  return icons[type] || '❓'
}

const getSlotName = (type: EquipmentType): string => {
  const names = {
    weapon: '武器',
    armor: '护甲',
    helmet: '头盔',
    boots: '靴子',
    gloves: '手套',
    accessory: '饰品',
    ring: '戒指',
    necklace: '项链'
  }
  return names[type] || type
}

const getRarityBorderClass = (rarity: PetRarity): string => {
  const classes = {
    common: 'border-gray-400',
    uncommon: 'border-green-400',
    rare: 'border-blue-400',
    epic: 'border-purple-400',
    legendary: 'border-yellow-400',
    mythical: 'border-red-400'
  }
  return classes[rarity]
}

const getRarityTextClass = (rarity: PetRarity): string => {
  const classes = {
    common: 'text-gray-600',
    uncommon: 'text-green-600',
    rare: 'text-blue-600',
    epic: 'text-purple-600',
    legendary: 'text-yellow-600',
    mythical: 'text-red-600'
  }
  return classes[rarity]
}

const getEquipmentImageSrc = (equipment: Equipment): string => {
  return `/images/equipment/${equipment.type}/${equipment.id}.png`
}

const getItemQuantity = (item: Item): number => {
  return item.quantity || 1
}

const getStatName = (stat: string): string => {
  const names = {
    strength: '力量',
    intelligence: '智力',
    agility: '敏捷',
    charm: '魅力',
    vitality: '体力',
    luck: '幸运'
  }
  return names[stat as keyof typeof names] || stat
}

const getRequirementText = (requirement: EquipmentRequirement): string => {
  switch (requirement.type) {
    case 'level':
      return `等级 ${requirement.value}`
    case 'rarity':
      const rarityNames = { common: '普通', uncommon: '优秀', rare: '稀有', epic: '史诗', legendary: '传说', mythical: '神话' }
      return `稀有度 ${rarityNames[requirement.value as PetRarity]}`
    case 'species':
      return `种族 ${requirement.value}`
    case 'stat':
      return `${getStatName(requirement.target)} ${requirement.value}`
    default:
      return `${requirement.type} ${requirement.value}`
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/equipment/default.png'
}
</script>

<style scoped>
.pet-equipment-manager {
  transition: all 0.3s ease;
}

.equipment-slot {
  min-height: 120px;
  transition: all 0.2s ease;
}

.equipment-slot:hover:not(.cursor-not-allowed) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.inventory-item {
  min-height: 100px;
  transition: all 0.2s ease;
}

.inventory-item:hover {
  transform: translateY(-2px);
}

.set-item {
  transition: all 0.3s ease;
}

.set-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.equipment-detail {
  max-height: 80vh;
  overflow-y: auto;
}

/* 自定义滚动条 */
.equipment-detail {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.equipment-detail::-webkit-scrollbar {
  width: 4px;
}

.equipment-detail::-webkit-scrollbar-track {
  background: transparent;
}

.equipment-detail::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .slots-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .inventory-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {
  .manager-content {
    padding: 1rem;
  }

  .equipment-slot {
    min-height: 100px;
    padding: 0.75rem;
  }

  .inventory-item {
    min-height: 80px;
    padding: 0.5rem;
  }
}
</style>
