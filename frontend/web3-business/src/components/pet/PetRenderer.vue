<template>
  <div
    class="pet-renderer"
    :class="[sizeClass, { 'optimized': optimized }]"
    ref="rendererRef"
  >
    <canvas
      v-if="useCanvas"
      ref="canvasRef"
      class="pet-canvas"
      :width="canvasSize"
      :height="canvasSize"
    ></canvas>

    <div v-else class="pet-image-container">
      <img
        :src="petImageSrc"
        :alt="pet.name"
        class="pet-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <div
        v-for="effect in activeEffects"
        :key="effect.id"
        class="pet-effect"
        :class="effect.classes"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import type { Pet, PetRarity } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
  size?: 'tiny' | 'small' | 'medium' | 'large' | 'huge'
  animation?: string
  useCanvas?: boolean
  optimized?: boolean
  effects?: string[]
}

interface Emits {
  (e: 'loaded'): void
  (e: 'error', error: Error): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  animation: 'idle',
  useCanvas: false,
  optimized: false,
  effects: () => []
})

const emit = defineEmits<Emits>()

// 引用
const rendererRef = ref<HTMLDivElement | null>(null)
const canvasRef = ref<HTMLCanvasElement | null>(null)
const animationFrameId = ref<number | null>(null)
const imageLoaded = ref(false)
const activeEffects = ref<Array<{ id: string, classes: string }>>([])

// 计算属性
const sizeClass = computed(() => {
  const sizes = {
    tiny: 'w-8 h-8',
    small: 'w-16 h-16',
    medium: 'w-24 h-24',
    large: 'w-32 h-32',
    huge: 'w-48 h-48'
  }
  return sizes[props.size]
})

const canvasSize = computed(() => {
  const sizes = {
    tiny: 32,
    small: 64,
    medium: 96,
    large: 128,
    huge: 192
  }
  return sizes[props.size]
})

const petImageSrc = computed(() => {
  const { type, appearance } = props.pet
  // 首先尝试具体的图片，如果不存在则使用默认图片
  if (appearance?.color && appearance?.pattern) {
    return `/images/pets/${type}/${appearance.color}_${appearance.pattern}.svg`
  }
  return `/images/pets/${type}/default.svg`
})

// 方法
const handleImageLoad = () => {
  imageLoaded.value = true
  emit('loaded')

  // 应用效果
  applyEffects()
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  const type = props.pet.type || 'cat'

  // 首先尝试该类型的默认图片
  if (!target.src.includes('/default.svg')) {
    target.src = `/images/pets/${type}/default.svg`
  } else {
    // 如果类型默认图片也失败，使用通用默认图片
    target.src = '/images/pets/default-pet.svg'
    console.error('Failed to load pet image:', petImageSrc.value)
    emit('error', new Error(`Failed to load image: ${petImageSrc.value}`))
  }
}

const applyEffects = () => {
  activeEffects.value = []

  // 应用稀有度效果
  if (props.effects.includes('rarity')) {
    const rarityEffect = {
      id: 'rarity-effect',
      classes: `rarity-effect rarity-${props.pet.rarity}`
    }
    activeEffects.value.push(rarityEffect)
  }

  // 应用状态效果
  if (props.effects.includes('status') && props.pet.status !== 'healthy') {
    const statusEffect = {
      id: 'status-effect',
      classes: `status-effect status-${props.pet.status}`
    }
    activeEffects.value.push(statusEffect)
  }

  // 应用动画效果
  if (props.effects.includes('animation') && props.animation !== 'idle') {
    const animationEffect = {
      id: 'animation-effect',
      classes: `animation-effect animation-${props.animation}`
    }
    activeEffects.value.push(animationEffect)
  }
}

// Canvas 渲染
const initCanvas = () => {
  if (!props.useCanvas || !canvasRef.value) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 加载萌宠图像
  const petImage = new Image()
  petImage.src = petImageSrc.value
  petImage.onload = () => {
    // 开始动画循环
    startAnimationLoop(ctx, petImage)
  }
}

const startAnimationLoop = (ctx: CanvasRenderingContext2D, petImage: HTMLImageElement) => {
  let frame = 0
  const frameCount = 60

  const animate = () => {
    // 清除画布
    ctx.clearRect(0, 0, canvasRef.value!.width, canvasRef.value!.height)

    // 计算动画参数
    const progress = frame / frameCount
    const scale = 1 + Math.sin(progress * Math.PI * 2) * 0.05

    // 绘制萌宠
    const centerX = canvasRef.value!.width / 2
    const centerY = canvasRef.value!.height / 2
    const size = Math.min(canvasRef.value!.width, canvasRef.value!.height) * 0.8

    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.scale(scale, scale)
    ctx.drawImage(
      petImage,
      -size / 2,
      -size / 2,
      size,
      size
    )
    ctx.restore()

    // 应用特效
    applyCanvasEffects(ctx)

    // 更新帧计数
    frame = (frame + 1) % frameCount

    // 继续动画循环
    animationFrameId.value = requestAnimationFrame(animate)
  }

  // 开始动画
  animate()
}

const applyCanvasEffects = (ctx: CanvasRenderingContext2D) => {
  if (!canvasRef.value) return

  // 应用稀有度光晕
  if (props.effects.includes('rarity')) {
    const rarityColors: Record<string, string> = {
      common: 'rgba(150, 150, 150, 0.3)',
      uncommon: 'rgba(75, 175, 75, 0.3)',
      rare: 'rgba(75, 75, 175, 0.3)',
      epic: 'rgba(175, 75, 175, 0.3)',
      legendary: 'rgba(255, 215, 0, 0.3)',
      mythical: 'rgba(255, 0, 0, 0.3)'
    }

    const color = rarityColors[props.pet.rarity] || rarityColors.common

    ctx.save()
    ctx.fillStyle = color
    ctx.globalAlpha = 0.5 + Math.sin(Date.now() / 1000) * 0.2
    ctx.beginPath()
    ctx.arc(
      canvasRef.value.width / 2,
      canvasRef.value.height / 2,
      canvasRef.value.width * 0.45,
      0,
      Math.PI * 2
    )
    ctx.fill()
    ctx.restore()
  }
}

const stopAnimationLoop = () => {
  if (animationFrameId.value !== null) {
    cancelAnimationFrame(animationFrameId.value)
    animationFrameId.value = null
  }
}

// 生命周期
onMounted(() => {
  if (props.useCanvas) {
    initCanvas()
  }
})

onUnmounted(() => {
  stopAnimationLoop()
})

// 监听属性变化
watch(() => props.pet, () => {
  if (props.useCanvas) {
    initCanvas()
  } else {
    applyEffects()
  }
}, { deep: true })

watch(() => props.animation, () => {
  applyEffects()
})

watch(() => props.effects, () => {
  applyEffects()
}, { deep: true })

// 暴露方法
defineExpose({
  refresh: () => {
    if (props.useCanvas) {
      initCanvas()
    } else {
      applyEffects()
    }
  }
})
</script>

<style scoped>
.pet-renderer {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.pet-canvas {
  width: 100%;
  height: 100%;
}

.pet-image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.pet-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.pet-effect {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

/* 稀有度效果 */
.rarity-effect {
  border-radius: 8px;
  mix-blend-mode: overlay;
}

.rarity-common {
  background: radial-gradient(circle, rgba(150, 150, 150, 0.3) 0%, transparent 70%);
}

.rarity-uncommon {
  background: radial-gradient(circle, rgba(75, 175, 75, 0.3) 0%, transparent 70%);
  animation: pulse 2s infinite;
}

.rarity-rare {
  background: radial-gradient(circle, rgba(75, 75, 175, 0.3) 0%, transparent 70%);
  animation: pulse 1.5s infinite;
}

.rarity-epic {
  background: radial-gradient(circle, rgba(175, 75, 175, 0.3) 0%, transparent 70%);
  animation: pulse 1s infinite;
}

.rarity-legendary {
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  animation: glow 2s infinite;
}

.rarity-mythical {
  background: radial-gradient(circle, rgba(255, 0, 0, 0.3) 0%, transparent 70%);
  animation: glow 1s infinite;
}

/* 状态效果 */
.status-effect {
  border-radius: 8px;
  mix-blend-mode: multiply;
}

.status-sick {
  background-color: rgba(0, 255, 0, 0.2);
  animation: pulse 2s infinite;
}

.status-tired {
  background-color: rgba(100, 100, 100, 0.2);
  animation: pulse 3s infinite;
}

.status-hungry {
  background-color: rgba(255, 165, 0, 0.2);
  animation: pulse 1.5s infinite;
}

.status-happy {
  background-color: rgba(255, 255, 0, 0.2);
  animation: pulse 1s infinite;
}

/* 动画效果 */
.animation-effect {
  border-radius: 8px;
}

.animation-happy {
  animation: bounce 0.5s infinite alternate;
}

.animation-sad {
  animation: fade 2s infinite alternate;
}

.animation-play {
  animation: rotate 2s infinite linear;
}

.animation-sleep {
  background-color: rgba(0, 0, 0, 0.1);
  animation: breathe 4s infinite;
}

.animation-levelup {
  background: radial-gradient(circle, rgba(255, 255, 0, 0.5) 0%, transparent 70%);
  animation: expand 1s infinite;
}

.animation-evolution {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  animation: flash 0.5s infinite;
}

/* 优化模式 */
.optimized .pet-effect {
  display: none;
}

/* 动画关键帧 */
@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.8); }
}

@keyframes bounce {
  0% { transform: translateY(0); }
  100% { transform: translateY(-10%); }
}

@keyframes fade {
  0% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(0.95); }
}

@keyframes expand {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.5; }
  100% { transform: scale(0.8); opacity: 0.8; }
}

@keyframes flash {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}
</style>
