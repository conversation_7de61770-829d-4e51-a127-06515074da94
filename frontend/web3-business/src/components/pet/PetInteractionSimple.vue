<template>
  <div class="pet-interaction-simple">
    <div class="interaction-buttons">
      <button
        v-for="action in actions"
        :key="action.type"
        @click="handleAction(action.type)"
        class="action-btn"
      >
        {{ action.icon }} {{ action.label }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Pet } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
  size?: 'small' | 'medium' | 'large'
}

interface Emits {
  (e: 'feed', pet: Pet): void
  (e: 'play', pet: Pet): void
  (e: 'train', pet: Pet): void
  (e: 'rest', pet: Pet): void
  (e: 'pet', pet: Pet): void
  (e: 'talk', pet: Pet): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium'
})

const emit = defineEmits<Emits>()

const actions = [
  { type: 'feed', label: '喂食', icon: '🍎' },
  { type: 'play', label: '玩耍', icon: '🎾' },
  { type: 'train', label: '训练', icon: '💪' },
  { type: 'rest', label: '休息', icon: '😴' },
  { type: 'pet', label: '抚摸', icon: '👋' },
  { type: 'talk', label: '对话', icon: '💬' }
]

const handleAction = (type: string) => {
  ;(emit as any)(type, props.pet)
}
</script>

<style scoped>
.pet-interaction-simple {
  padding: 10px;
}

.interaction-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-size: 12px;
}

.action-btn:hover {
  background: #0056b3;
}
</style>
