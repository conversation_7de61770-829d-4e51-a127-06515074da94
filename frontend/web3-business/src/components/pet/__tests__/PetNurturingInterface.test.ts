import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import PetNurturingInterface from '@/components/pet/PetNurturingInterface.vue'
import { mountComponent, createMockPet, createMockItem } from '@/test/utils'
import { usePetStore } from '@/stores/pet'
import { useGameStore } from '@/stores/game'

describe('PetNurturingInterface', () => {
  let mockPet: any
  let mockItems: any[]

  beforeEach(() => {
    mockPet = createMockPet({
      name: 'Buddy',
      health: 70,
      happiness: 60,
      experience: 50,
    })

    mockItems = [
      createMockItem({
        id: 'food-1',
        name: 'Pet Food',
        type: 'food',
        effect: { health: 20, happiness: 10, experience: 5 }
      }),
      createMockItem({
        id: 'toy-1',
        name: 'Ball',
        type: 'toy',
        effect: { health: 0, happiness: 25, experience: 10 }
      }),
    ]

    vi.clearAllMocks()
  })

  it('renders nurturing interface with pet and actions', () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    expect(wrapper.find('[data-testid="nurturing-interface"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="pet-display"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="action-buttons"]').exists()).toBe(true)
  })

  it('shows available nurturing actions', () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    expect(wrapper.find('[data-testid="feed-action"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="play-action"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="train-action"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="rest-action"]').exists()).toBe(true)
  })

  it('handles feed action correctly', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const petStore = usePetStore()
    const feedSpy = vi.spyOn(petStore, 'feedPet')

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    await feedButton.trigger('click')

    expect(feedSpy).toHaveBeenCalledWith(mockPet.id)
  })

  it('handles play action correctly', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const petStore = usePetStore()
    const playSpy = vi.spyOn(petStore, 'playWithPet')

    const playButton = wrapper.find('[data-testid="play-action"]')
    await playButton.trigger('click')

    expect(playSpy).toHaveBeenCalledWith(mockPet.id)
  })

  it('handles train action correctly', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const petStore = usePetStore()
    const trainSpy = vi.spyOn(petStore, 'trainPet')

    const trainButton = wrapper.find('[data-testid="train-action"]')
    await trainButton.trigger('click')

    expect(trainSpy).toHaveBeenCalledWith(mockPet.id)
  })

  it('shows cooldown timers for actions', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const gameStore = useGameStore()
    gameStore.actionCooldowns = {
      [mockPet.id]: {
        feed: Date.now() + 60000, // 1 minute cooldown
        play: 0,
        train: 0,
        rest: 0,
      }
    }

    await nextTick()

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    expect(feedButton.attributes('disabled')).toBeDefined()
    expect(wrapper.find('[data-testid="feed-cooldown"]').exists()).toBe(true)
  })

  it('shows item selection modal when using items', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const useItemButton = wrapper.find('[data-testid="use-item-action"]')
    await useItemButton.trigger('click')

    expect(wrapper.find('[data-testid="item-selection-modal"]').exists()).toBe(true)
  })

  it('applies item effects when item is used', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const petStore = usePetStore()
    const useItemSpy = vi.spyOn(petStore, 'useItemOnPet')

    // Open item selection
    const useItemButton = wrapper.find('[data-testid="use-item-action"]')
    await useItemButton.trigger('click')

    // Select an item
    const foodItem = wrapper.find('[data-testid="item-food-1"]')
    await foodItem.trigger('click')

    expect(useItemSpy).toHaveBeenCalledWith(mockPet.id, 'food-1')
  })

  it('shows action effects with animations', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    await feedButton.trigger('click')

    expect(wrapper.find('[data-testid="action-effect"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="health-increase"]').exists()).toBe(true)
  })

  it('prevents actions when pet stats are at maximum', async () => {
    const maxStatsPet = createMockPet({
      health: 100,
      maxHealth: 100,
      happiness: 100,
      maxHappiness: 100,
    })

    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: maxStatsPet }
    })

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    expect(feedButton.attributes('disabled')).toBeDefined()
  })

  it('shows energy cost for actions', () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const trainButton = wrapper.find('[data-testid="train-action"]')
    expect(wrapper.find('[data-testid="train-energy-cost"]').text()).toContain('10')
  })

  it('handles insufficient energy gracefully', async () => {
    const lowEnergyPet = createMockPet({ energy: 5 })
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: lowEnergyPet }
    })

    const trainButton = wrapper.find('[data-testid="train-action"]')
    expect(trainButton.attributes('disabled')).toBeDefined()
    expect(wrapper.find('[data-testid="insufficient-energy"]').exists()).toBe(true)
  })

  it('updates pet display after actions', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const petStore = usePetStore()
    vi.spyOn(petStore, 'feedPet').mockImplementation(() => {
      mockPet.health = Math.min(mockPet.health + 20, mockPet.maxHealth)
    })

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    await feedButton.trigger('click')
    await nextTick()

    // Pet display should reflect updated stats
    expect(wrapper.find('[data-testid="pet-health"]').text()).toContain('90')
  })

  it('shows level up notification when pet levels up', async () => {
    const nearLevelUpPet = createMockPet({
      level: 1,
      experience: 95,
      maxExperience: 100,
    })

    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: nearLevelUpPet }
    })

    const petStore = usePetStore()
    vi.spyOn(petStore, 'trainPet').mockImplementation(() => {
      nearLevelUpPet.experience = 100
      nearLevelUpPet.level = 2
    })

    const trainButton = wrapper.find('[data-testid="train-action"]')
    await trainButton.trigger('click')
    await nextTick()

    expect(wrapper.find('[data-testid="level-up-notification"]').exists()).toBe(true)
  })

  it('is accessible with proper keyboard navigation', async () => {
    const wrapper = mountComponent(PetNurturingInterface, {
      props: { pet: mockPet }
    })

    const feedButton = wrapper.find('[data-testid="feed-action"]')
    await feedButton.trigger('keydown.enter')

    const petStore = usePetStore()
    expect(vi.spyOn(petStore, 'feedPet')).toHaveBeenCalled()
  })
})