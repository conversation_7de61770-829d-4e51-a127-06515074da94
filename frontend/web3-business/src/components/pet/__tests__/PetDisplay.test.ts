import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import PetDisplay from '@/components/pet/PetDisplay.vue'
import { mountComponent, createMockPet } from '@/test/utils'

describe('PetDisplay', () => {
  let mockPet: any

  beforeEach(() => {
    mockPet = createMockPet({
      name: '<PERSON>lu<PERSON>',
      level: 5,
      rarity: 'rare',
      health: 80,
      happiness: 90,
    })
    vi.clearAllMocks()
  })

  it('renders pet information correctly', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet }
    })

    expect(wrapper.find('[data-testid="pet-name"]').text()).toBe('Fluffy')
    expect(wrapper.find('[data-testid="pet-level"]').text()).toContain('5')
    expect(wrapper.find('[data-testid="pet-rarity"]').text()).toContain('rare')
  })

  it('displays pet stats with progress bars', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, showStats: true }
    })

    expect(wrapper.find('[data-testid="health-bar"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="happiness-bar"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="experience-bar"]').exists()).toBe(true)
  })

  it('applies correct rarity styling', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet }
    })

    const petCard = wrapper.find('[data-testid="pet-card"]')
    expect(petCard.classes()).toContain('rarity-rare')
  })

  it('shows pet image with correct species', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet }
    })

    const petImage = wrapper.find('[data-testid="pet-image"]')
    expect(petImage.exists()).toBe(true)
    expect(petImage.attributes('alt')).toContain('cat')
  })

  it('handles different size props', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, size: 'large' }
    })

    const petCard = wrapper.find('[data-testid="pet-card"]')
    expect(petCard.classes()).toContain('size-large')
  })

  it('shows interactive elements when interactive prop is true', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, interactive: true }
    })

    expect(wrapper.find('[data-testid="pet-actions"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="feed-button"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="play-button"]').exists()).toBe(true)
  })

  it('emits pet-click event when pet is clicked', async () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, interactive: true }
    })

    const petCard = wrapper.find('[data-testid="pet-card"]')
    await petCard.trigger('click')

    expect(wrapper.emitted('pet-click')).toBeTruthy()
    expect(wrapper.emitted('pet-click')?.[0]).toEqual([mockPet])
  })

  it('emits action events when action buttons are clicked', async () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, interactive: true }
    })

    const feedButton = wrapper.find('[data-testid="feed-button"]')
    await feedButton.trigger('click')

    expect(wrapper.emitted('pet-action')).toBeTruthy()
    expect(wrapper.emitted('pet-action')?.[0]).toEqual(['feed', mockPet])
  })

  it('shows low health warning when health is below threshold', () => {
    const lowHealthPet = createMockPet({ health: 20, maxHealth: 100 })
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: lowHealthPet, showStats: true }
    })

    expect(wrapper.find('[data-testid="low-health-warning"]').exists()).toBe(true)
  })

  it('shows evolution indicator when pet can evolve', () => {
    const evolvablePet = createMockPet({
      level: 10,
      experience: 1000,
      maxExperience: 1000
    })
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: evolvablePet }
    })

    expect(wrapper.find('[data-testid="evolution-indicator"]').exists()).toBe(true)
  })

  it('displays equipment count when pet has equipment', () => {
    const equippedPet = createMockPet({
      equipment: [
        { id: '1', name: 'Collar', type: 'accessory' },
        { id: '2', name: 'Toy', type: 'toy' }
      ]
    })
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: equippedPet }
    })

    expect(wrapper.find('[data-testid="equipment-count"]').text()).toContain('2')
  })

  it('handles missing pet data gracefully', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: null }
    })

    expect(wrapper.find('[data-testid="no-pet-message"]').exists()).toBe(true)
  })

  it('updates display when pet data changes', async () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet }
    })

    expect(wrapper.find('[data-testid="pet-level"]').text()).toContain('5')

    const updatedPet = { ...mockPet, level: 6 }
    await wrapper.setProps({ pet: updatedPet })

    expect(wrapper.find('[data-testid="pet-level"]').text()).toContain('6')
  })

  it('is accessible with proper ARIA attributes', () => {
    const wrapper = mountComponent(PetDisplay, {
      props: { pet: mockPet, interactive: true }
    })

    const petCard = wrapper.find('[data-testid="pet-card"]')
    expect(petCard.attributes('role')).toBe('button')
    expect(petCard.attributes('aria-label')).toContain('Fluffy')
    expect(petCard.attributes('tabindex')).toBe('0')
  })
})
