<template>
  <div
    class="pet-interaction relative"
    :class="containerClasses"
    @click="handleClick"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
  >
    <!-- 萌宠动画组件 -->
    <PetAnimation
      ref="petAnimationRef"
      :pet="pet"
      :animation="currentAnimation"
      :size="size"
      :loop="animationLoop"
      :auto-play="true"
      :speed="animationSpeed"
      :show-particles="showParticles"
      :show-trail="showTrail"
      @animation-complete="handleAnimationComplete"
      @loop-complete="handleLoopComplete"
    >
      <template #pet-content>
        <!-- Slot for pet content -->
        <slot name="pet-content"></slot>
      </template>
    </PetAnimation>

    <!-- 交互按钮层 -->
    <div
      v-if="showInteractionButtons"
      class="interaction-buttons absolute -bottom-12 left-1/2 transform -translate-x-1/2 flex space-x-2"
    >
      <button
        v-for="action in availableActions"
        :key="action.type"
        class="action-button px-3 py-1 rounded-full text-xs font-medium transition-all duration-200"
        :class="getActionButtonClasses(action)"
        :disabled="!action.enabled"
        @click.stop="handleActionClick(action)"
      >
        <span class="mr-1">{{ action.icon }}</span>
        {{ action.label }}
      </button>
    </div>

    <!-- 交互反馈层 -->
    <div class="interaction-feedback absolute inset-0 pointer-events-none">
      <!-- 点击波纹效果 -->
      <div
        v-for="ripple in ripples"
        :key="ripple.id"
        class="ripple absolute rounded-full border-2 border-white opacity-50"
        :style="ripple.style"
      />

      <!-- 浮动文字效果 -->
      <div
        v-for="floatingText in floatingTexts"
        :key="floatingText.id"
        class="floating-text absolute font-bold text-sm pointer-events-none"
        :class="floatingText.classes"
        :style="floatingText.style"
      >
        {{ floatingText.text }}
      </div>

      <!-- 心形效果 -->
      <div
        v-for="heart in hearts"
        :key="heart.id"
        class="heart absolute text-red-500 text-lg pointer-events-none"
        :style="heart.style"
      >
        ❤️
      </div>
    </div>

    <!-- 状态提示层 -->
    <div
      v-if="statusMessage"
      class="status-message absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs whitespace-nowrap"
    >
      {{ statusMessage }}
    </div>

    <!-- 冷却时间指示器 -->
    <div
      v-if="showCooldown && cooldownProgress > 0"
      class="cooldown-indicator absolute inset-0 flex items-center justify-center"
    >
      <div class="cooldown-circle relative w-8 h-8">
        <svg class="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
          <circle
            cx="16"
            cy="16"
            r="14"
            stroke="rgba(255,255,255,0.3)"
            stroke-width="2"
            fill="none"
          />
          <circle
            cx="16"
            cy="16"
            r="14"
            stroke="white"
            stroke-width="2"
            fill="none"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="circumference * (1 - cooldownProgress)"
            class="transition-all duration-100"
          />
        </svg>
        <div class="absolute inset-0 flex items-center justify-center text-white text-xs font-bold">
          {{ Math.ceil(cooldownTime / 1000) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import PetAnimation from './PetAnimation.vue'
import type { Pet, PetRarity } from '../../types/typesWithoutCircular'

interface InteractionAction {
  type: 'feed' | 'play' | 'train' | 'rest' | 'pet' | 'talk'
  label: string
  icon: string
  enabled: boolean
  cooldown: number
  cost?: number
}

interface Props {
  pet: Pet
  size?: 'small' | 'medium' | 'large'
  showStats?: boolean
  showRarityGlow?: boolean
  showInteractionButtons?: boolean
  showParticles?: boolean
  showTrail?: boolean
  showCooldown?: boolean
  interactionCooldown?: number
  customClass?: string
}

interface Emits {
  (e: 'feed', pet: Pet): void
  (e: 'play', pet: Pet): void
  (e: 'train', pet: Pet): void
  (e: 'rest', pet: Pet): void
  (e: 'pet', pet: Pet): void
  (e: 'talk', pet: Pet): void
  (e: 'interaction', type: 'feed' | 'play' | 'train' | 'rest' | 'pet' | 'talk', pet: Pet): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  showStats: true,
  showRarityGlow: true,
  showInteractionButtons: true,
  showParticles: false,
  showTrail: false,
  showCooldown: true,
  interactionCooldown: 1000,
  customClass: ''
})

const emit = defineEmits<Emits>()

// 响应式状态
const petAnimationRef = ref<InstanceType<typeof PetAnimation> | null>(null)
const currentAnimation = ref<string>('idle')
const animationLoop = ref(true)
const animationSpeed = ref(1)
const statusMessage = ref('')
const cooldownTime = ref(0)
const cooldownProgress = ref(0)

const ripples = ref<Array<{
  id: string
  style: Record<string, string>
}>>([])

const floatingTexts = ref<Array<{
  id: string
  text: string
  classes: string
  style: Record<string, string>
}>>([])

const hearts = ref<Array<{
  id: string
  style: Record<string, string>
}>>([])

let cooldownTimer: number | null = null
let statusTimer: number | null = null

// 计算属性
const containerClasses = computed(() => {
  const sizes = {
    small: 'w-20 h-20',
    medium: 'w-28 h-28',
    large: 'w-36 h-36'
  }
  return `${sizes[props.size]} cursor-pointer select-none ${props.customClass}`
})

const availableActions = computed((): InteractionAction[] => {
  const now = Date.now()

  return [
    {
      type: 'feed',
      label: '喂食',
      icon: '🍎',
      enabled: now - props.pet.lastFeedTime > 30 * 60 * 1000, // 30分钟冷却
      cooldown: 30 * 60 * 1000
    },
    {
      type: 'play',
      label: '玩耍',
      icon: '🎾',
      enabled: props.pet.energy > 20 && now - props.pet.lastPlayTime > 15 * 60 * 1000, // 15分钟冷却
      cooldown: 15 * 60 * 1000
    },
    {
      type: 'train',
      label: '训练',
      icon: '💪',
      enabled: props.pet.energy > 30 && now - props.pet.lastTrainTime > 60 * 60 * 1000, // 1小时冷却
      cooldown: 60 * 60 * 1000
    },
    {
      type: 'rest',
      label: '休息',
      icon: '😴',
      enabled: props.pet.energy < props.pet.maxEnergy * 0.8,
      cooldown: 0
    },
    {
      type: 'pet',
      label: '抚摸',
      icon: '👋',
      enabled: true,
      cooldown: 5 * 1000 // 5秒冷却
    },
    {
      type: 'talk',
      label: '对话',
      icon: '💬',
      enabled: true,
      cooldown: 10 * 1000 // 10秒冷却
    }
  ]
})

const circumference = computed(() => 2 * Math.PI * 14) // r=14 的圆周长

// 方法
const handleClick = (event: MouseEvent) => {
  createRippleEffect(event)
  playInteractionAnimation('happy')

  // 随机显示浮动文字
  const messages = ['好可爱!', '真棒!', '继续!', '太好了!']
  const randomMessage = messages[Math.floor(Math.random() * messages.length)]
  showFloatingText(randomMessage, 'text-yellow-400', event)
}

const handleTouchStart = (event: TouchEvent) => {
  const touch = event.touches[0]
  createRippleEffect(touch)
}

const handleTouchEnd = () => {
  playInteractionAnimation('happy')
}

const handleMouseDown = () => {
  currentAnimation.value = 'play'
}

const handleMouseUp = () => {
  currentAnimation.value = 'idle'
}

const handleMouseLeave = () => {
  currentAnimation.value = 'idle'
}

const handlePetClick = () => {
  emit('pet', props.pet)
  emit('interaction', 'pet', props.pet)

  // 创建心形效果
  createHeartEffect()
  showFloatingText('+1 快乐', 'text-pink-400')

  // 播放快乐动画
  playInteractionAnimation('happy')
}

const handlePetHover = () => {
  if (currentAnimation.value === 'idle') {
    currentAnimation.value = 'happy'
  }
}

const handlePetLeave = () => {
  if (currentAnimation.value === 'happy') {
    currentAnimation.value = 'idle'
  }
}

const handleActionClick = (action: InteractionAction) => {
  if (!action.enabled) {
    showStatusMessage(`${action.label}冷却中...`)
    return
  }

  // 发射对应事件
  emit(action.type, props.pet)
  emit('interaction', action.type, props.pet)

  // 播放对应动画
  playActionAnimation(action.type)

  // 显示反馈
  showActionFeedback(action)

  // 开始冷却
  if (action.cooldown > 0) {
    startCooldown(action.cooldown)
  }
}

const handleAnimationComplete = () => {
  // 动画完成后回到空闲状态
  if (currentAnimation.value !== 'idle') {
    setTimeout(() => {
      currentAnimation.value = 'idle'
    }, 500)
  }
}

const handleLoopComplete = () => {
  // 循环完成处理
}

const createRippleEffect = (event: MouseEvent | Touch) => {
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const ripple = {
    id: `ripple-${Date.now()}`,
    style: {
      left: `${x}px`,
      top: `${y}px`,
      width: '0px',
      height: '0px',
      animation: 'ripple-expand 0.6s ease-out'
    }
  }

  ripples.value.push(ripple)

  // 清理波纹效果
  setTimeout(() => {
    ripples.value = ripples.value.filter(r => r.id !== ripple.id)
  }, 600)
}

const showFloatingText = (text: string, colorClass: string, event?: MouseEvent | Touch) => {
  let x = '50%'
  let y = '20%'

  if (event) {
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    x = `${((event.clientX - rect.left) / rect.width) * 100}%`
    y = `${((event.clientY - rect.top) / rect.height) * 100}%`
  }

  const floatingText = {
    id: `text-${Date.now()}`,
    text,
    classes: `${colorClass} animate-bounce`,
    style: {
      left: x,
      top: y,
      transform: 'translate(-50%, -50%)',
      animation: 'float-up 2s ease-out forwards'
    }
  }

  floatingTexts.value.push(floatingText)

  // 清理浮动文字
  setTimeout(() => {
    floatingTexts.value = floatingTexts.value.filter(ft => ft.id !== floatingText.id)
  }, 2000)
}

const createHeartEffect = () => {
  const heartCount = 3

  for (let i = 0; i < heartCount; i++) {
    const heart = {
      id: `heart-${Date.now()}-${i}`,
      style: {
        left: `${45 + Math.random() * 10}%`,
        top: `${45 + Math.random() * 10}%`,
        animation: `heart-float 2s ease-out ${i * 200}ms forwards`
      }
    }

    hearts.value.push(heart)

    // 清理心形效果
    setTimeout(() => {
      hearts.value = hearts.value.filter(h => h.id !== heart.id)
    }, 2200 + i * 200)
  }
}

const playInteractionAnimation = (animation: string) => {
  currentAnimation.value = animation
  animationLoop.value = false

  // 动画播放完后回到空闲状态
  setTimeout(() => {
    currentAnimation.value = 'idle'
    animationLoop.value = true
  }, 1500)
}

const playActionAnimation = (actionType: string) => {
  const animationMap = {
    feed: 'eat',
    play: 'play',
    train: 'run',
    rest: 'sleep',
    pet: 'happy',
    talk: 'happy'
  }

  const animation = animationMap[actionType as keyof typeof animationMap] || 'idle'
  playInteractionAnimation(animation)
}

const showActionFeedback = (action: InteractionAction) => {
  const feedbackMap = {
    feed: { text: '+20 健康', color: 'text-green-400' },
    play: { text: '+10 快乐', color: 'text-yellow-400' },
    train: { text: '+5 经验', color: 'text-blue-400' },
    rest: { text: '+30 能量', color: 'text-purple-400' },
    pet: { text: '+1 快乐', color: 'text-pink-400' },
    talk: { text: '好开心!', color: 'text-cyan-400' }
  }

  const feedback = feedbackMap[action.type as keyof typeof feedbackMap]
  if (feedback) {
    showFloatingText(feedback.text, feedback.color)
  }

  showStatusMessage(`${action.label}成功!`)
}

const showStatusMessage = (message: string) => {
  statusMessage.value = message

  if (statusTimer) {
    clearTimeout(statusTimer)
  }

  statusTimer = window.setTimeout(() => {
    statusMessage.value = ''
  }, 2000)
}

const startCooldown = (duration: number) => {
  cooldownTime.value = duration
  cooldownProgress.value = 1

  const startTime = Date.now()

  cooldownTimer = window.setInterval(() => {
    const elapsed = Date.now() - startTime
    const remaining = Math.max(0, duration - elapsed)

    cooldownTime.value = remaining
    cooldownProgress.value = remaining / duration

    if (remaining <= 0) {
      if (cooldownTimer) {
        clearInterval(cooldownTimer)
        cooldownTimer = null
      }
    }
  }, 100)
}

const getActionButtonClasses = (action: InteractionAction) => {
  let classes = 'transition-all duration-200 '

  if (action.enabled) {
    classes += 'bg-blue-500 hover:bg-blue-600 text-white hover:scale-105 active:scale-95'
  } else {
    classes += 'bg-gray-400 text-gray-600 cursor-not-allowed opacity-50'
  }

  return classes
}

// 生命周期
onMounted(() => {
  // 初始化动画
  currentAnimation.value = 'idle'
})

onUnmounted(() => {
  if (cooldownTimer) {
    clearInterval(cooldownTimer)
  }
  if (statusTimer) {
    clearTimeout(statusTimer)
  }
})

// 监听萌宠状态变化
watch(() => props.pet.health, (newHealth, oldHealth) => {
  if (newHealth > oldHealth) {
    showFloatingText('+健康', 'text-green-400')
  }
})

watch(() => props.pet.happiness, (newHappiness, oldHappiness) => {
  if (newHappiness > oldHappiness) {
    showFloatingText('+快乐', 'text-yellow-400')
  }
})

watch(() => props.pet.level, (newLevel, oldLevel) => {
  if (newLevel > oldLevel) {
    petAnimationRef.value?.triggerLevelUpEffect()
    showFloatingText('升级!', 'text-purple-400')
  }
})

// 暴露方法给父组件
defineExpose({
  playAnimation: playInteractionAnimation,
  showMessage: showStatusMessage,
  createHeartEffect,
  triggerLevelUp: () => petAnimationRef.value?.triggerLevelUpEffect(),
  triggerEvolution: () => petAnimationRef.value?.triggerEvolutionEffect()
})
</script>

<style scoped>
.pet-interaction {
  position: relative;
  user-select: none;
}

.action-button {
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ripple {
  pointer-events: none;
  z-index: 10;
}

.floating-text {
  pointer-events: none;
  z-index: 20;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.heart {
  pointer-events: none;
  z-index: 15;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.status-message {
  z-index: 25;
  animation: fadeInOut 2s ease-in-out;
}

.cooldown-indicator {
  z-index: 30;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
}

@keyframes ripple-expand {
  0% {
    width: 0px;
    height: 0px;
    opacity: 0.8;
  }
  100% {
    width: 60px;
    height: 60px;
    margin-left: -30px;
    margin-top: -30px;
    opacity: 0;
  }
}

@keyframes float-up {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -150%) scale(1.2);
  }
}

@keyframes heart-float {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-30px) scale(1.5);
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .interaction-buttons {
    bottom: -8px;
    flex-wrap: wrap;
    max-width: 200px;
  }

  .action-button {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
  }

  .status-message {
    font-size: 0.625rem;
    top: -6px;
  }
}
</style>
