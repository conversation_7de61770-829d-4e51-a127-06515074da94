<template>
  <div class="pet-navigation">
    <van-tabbar v-model="activeTab" @change="handleTabChange">
      <van-tabbar-item icon="home-o" to="/">
        首页
      </van-tabbar-item>
      <van-tabbar-item icon="friends-o" to="/pets">
        我的萌宠
        <template #badge v-if="petCount > 0">
          {{ petCount }}
        </template>
      </van-tabbar-item>
      <van-tabbar-item icon="add-o" to="/pet/create">
        创建萌宠
      </van-tabbar-item>
      <van-tabbar-item icon="shop-o" to="/shop">
        商店
      </van-tabbar-item>
      <van-tabbar-item icon="setting-o" to="/settings">
        设置
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePetStore } from '../../stores/pet'

const route = useRoute()
const router = useRouter()
const petStore = usePetStore()

// 响应式状态
const activeTab = ref(0)

// 计算属性
const petCount = computed(() => petStore.petCount)

// 根据当前路由设置活动标签
const setActiveTabFromRoute = () => {
  const path = route.path
  if (path === '/') {
    activeTab.value = 0
  } else if (path === '/pets') {
    activeTab.value = 1
  } else if (path === '/pet/create') {
    activeTab.value = 2
  } else if (path === '/shop') {
    activeTab.value = 3
  } else if (path === '/settings') {
    activeTab.value = 4
  }
}

// 方法
const handleTabChange = (index: number) => {
  const routes = ['/', '/pets', '/pet/create', '/shop', '/settings']
  router.push(routes[index])
}

// 监听路由变化
watch(() => route.path, setActiveTabFromRoute, { immediate: true })
</script>

<style scoped>
.pet-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}
</style>
