<template>
  <div class="pet-basic-info-card bg-white rounded-xl shadow-lg overflow-hidden">
    <!-- 卡片头部 -->
    <div class="card-header relative p-6 bg-gradient-to-r from-purple-500 to-pink-500 text-white">
      <!-- 稀有度背景效果 -->
      <div
        class="absolute inset-0 opacity-20"
        :class="rarityBackgroundClass"
      />

      <div class="relative z-10 flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- 萌宠头像 -->
          <div class="pet-avatar relative">
            <PetDisplay
              :pet="pet"
              size="large"
              :show-stats="false"
              :show-rarity-glow="true"
              class="border-4 border-white border-opacity-50 rounded-full"
            />

            <!-- 等级徽章 -->
            <div class="level-badge absolute -bottom-2 -right-2 bg-yellow-400 text-yellow-900 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
              {{ pet.level }}
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="pet-info">
            <div class="flex items-center space-x-2 mb-2">
              <h2 class="text-2xl font-bold">{{ pet.name }}</h2>
              <van-icon name="edit" @click="$emit('edit-name')" class="cursor-pointer hover:text-yellow-300" />
            </div>

            <div class="flex items-center space-x-4 text-sm opacity-90">
              <span class="flex items-center space-x-1">
                <van-icon name="star" />
                <span>{{ rarityText }}</span>
              </span>
              <span class="flex items-center space-x-1">
                <van-icon name="user" />
                <span>{{ typeText }}</span>
              </span>
              <span class="flex items-center space-x-1">
                <van-icon name="clock" />
                <span>{{ ageText }}</span>
              </span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-col space-y-2">
          <van-button
            size="small"
            type="primary"
            @click="$emit('view-evolution')"
            class="bg-white text-purple-500 hover:bg-gray-100"
          >
            进化路径
          </van-button>
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content p-6">
      <!-- 经验值进度 -->
      <div class="experience-section mb-6">
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm font-medium text-gray-600">经验值</span>
          <span class="text-sm text-gray-500">{{ pet.experience }}/{{ pet.maxExperience }}</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
          <div
            class="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transition-all duration-500"
            :style="{ width: `${experiencePercentage}%` }"
          />
        </div>
        <div class="text-xs text-gray-500 mt-1">
          距离下一级还需 {{ nextLevelExp }} 经验
        </div>
      </div>

      <!-- 生命状态条 -->
      <div class="status-bars space-y-4 mb-6">
        <!-- 健康度 -->
        <div class="status-bar">
          <div class="flex justify-between items-center mb-1">
            <div class="flex items-center space-x-2">
              <span class="text-red-500">❤️</span>
              <span class="text-sm font-medium">健康度</span>
            </div>
            <span class="text-sm text-gray-500">{{ pet.health }}/{{ pet.maxHealth }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-full bg-red-500 rounded-full transition-all duration-300"
              :style="{ width: `${healthPercentage}%` }"
            />
          </div>
        </div>

        <!-- 快乐度 -->
        <div class="status-bar">
          <div class="flex justify-between items-center mb-1">
            <div class="flex items-center space-x-2">
              <span class="text-yellow-500">😊</span>
              <span class="text-sm font-medium">快乐度</span>
            </div>
            <span class="text-sm text-gray-500">{{ pet.happiness }}/{{ pet.maxHappiness }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-full bg-yellow-500 rounded-full transition-all duration-300"
              :style="{ width: `${happinessPercentage}%` }"
            />
          </div>
        </div>

        <!-- 能量值 -->
        <div class="status-bar">
          <div class="flex justify-between items-center mb-1">
            <div class="flex items-center space-x-2">
              <span class="text-blue-500">⚡</span>
              <span class="text-sm font-medium">能量值</span>
            </div>
            <span class="text-sm text-gray-500">{{ pet.energy }}/{{ pet.maxEnergy }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-full bg-blue-500 rounded-full transition-all duration-300"
              :style="{ width: `${energyPercentage}%` }"
            />
          </div>
        </div>
      </div>

      <!-- 萌宠状态和心情 -->
      <div class="status-mood-section grid grid-cols-2 gap-4 mb-6">
        <!-- 当前状态 -->
        <div class="status-card bg-gray-50 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="status-icon text-2xl">{{ statusIcon }}</div>
            <div>
              <div class="text-sm font-medium text-gray-600">状态</div>
              <div class="text-lg font-bold" :class="statusTextClass">{{ statusText }}</div>
            </div>
          </div>
        </div>

        <!-- 当前心情 -->
        <div class="mood-card bg-gray-50 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <div class="mood-icon text-2xl">{{ moodIcon }}</div>
            <div>
              <div class="text-sm font-medium text-gray-600">心情</div>
              <div class="text-lg font-bold" :class="moodTextClass">{{ moodText }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成长阶段 -->
      <div class="growth-stage-section mb-6">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-lg font-semibold text-gray-800">成长阶段</h3>
          <span class="text-sm text-gray-500">{{ generationText }}</span>
        </div>

        <div class="growth-stages flex items-center justify-between">
          <div
            v-for="(stage, index) in growthStages"
            :key="stage.name"
            class="stage-item flex flex-col items-center"
            :class="{ 'active': index <= currentStageIndex, 'current': index === currentStageIndex }"
          >
            <div class="stage-icon w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300">
              {{ stage.icon }}
            </div>
            <span class="stage-name text-xs mt-1">{{ stage.name }}</span>
          </div>
        </div>
      </div>

      <!-- 特殊标记和成就 -->
      <div v-if="pet.achievements && pet.achievements.length > 0" class="achievements-section">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">成就徽章</h3>
        <div class="achievements-grid grid grid-cols-4 gap-2">
          <div
            v-for="achievement in pet.achievements.slice(0, 8)"
            :key="achievement.id"
            class="achievement-badge bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg p-2 text-center"
            :title="achievement.description"
          >
            <div class="text-lg">{{ achievement.icon }}</div>
            <div class="text-xs text-white font-medium truncate">{{ achievement.name }}</div>
          </div>
        </div>
        <div v-if="pet.achievements.length > 8" class="text-center mt-2">
          <span class="text-sm text-gray-500">还有 {{ pet.achievements.length - 8 }} 个成就...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PetDisplay from './PetDisplay.vue'
import type { Pet, PetRarity, PetType, PetStatus, PetMood, GrowthStage } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet
}

interface Emits {
  (e: 'edit-name'): void
  (e: 'view-evolution'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性 - 稀有度相关
const rarityBackgroundClass = computed(() => {
  const rarityClasses = {
    common: 'bg-gray-400',
    uncommon: 'bg-green-400',
    rare: 'bg-blue-400',
    epic: 'bg-purple-400',
    legendary: 'bg-yellow-400',
    mythical: 'bg-red-400'
  }
  return rarityClasses[props.pet.rarity]
})

const rarityText = computed(() => {
  const rarityTexts = {
    common: '普通',
    uncommon: '优秀',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说',
    mythical: '神话'
  }
  return rarityTexts[props.pet.rarity]
})

const typeText = computed(() => {
  const typeTexts = {
    cat: '猫咪',
    dog: '狗狗',
    rabbit: '兔子',
    bird: '小鸟',
    fish: '鱼儿',
    dragon: '龙',
    unicorn: '独角兽',
    phoenix: '凤凰'
  }
  return typeTexts[props.pet.type as keyof typeof typeTexts] || props.pet.type
})

// 计算属性 - 年龄和时间
const ageText = computed(() => {
  const now = Date.now()
  const age = now - props.pet.birthTime
  const days = Math.floor(age / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天出生'
  if (days === 1) return '1天大'
  if (days < 30) return `${days}天大`

  const months = Math.floor(days / 30)
  if (months < 12) return `${months}个月大`

  const years = Math.floor(months / 12)
  return `${years}岁`
})

const generationText = computed(() => {
  return `第${props.pet.generation || 1}代`
})

// 计算属性 - 进度条
const experiencePercentage = computed(() => {
  return Math.min((props.pet.experience / props.pet.maxExperience) * 100, 100)
})

const nextLevelExp = computed(() => {
  return Math.max(0, props.pet.maxExperience - props.pet.experience)
})

const healthPercentage = computed(() => {
  return (props.pet.health / props.pet.maxHealth) * 100
})

const happinessPercentage = computed(() => {
  return (props.pet.happiness / props.pet.maxHappiness) * 100
})

const energyPercentage = computed(() => {
  return (props.pet.energy / props.pet.maxEnergy) * 100
})

// 计算属性 - 状态和心情
const statusIcon = computed(() => {
  const statusIcons = {
    healthy: '💚',
    sick: '🤒',
    injured: '🩹',
    tired: '😴',
    excited: '🤩',
    training: '💪',
    resting: '😌'
  }
  return statusIcons[props.pet.status as keyof typeof statusIcons] || '💚'
})

const statusText = computed(() => {
  const statusTexts = {
    healthy: '健康',
    sick: '生病',
    injured: '受伤',
    tired: '疲惫',
    excited: '兴奋',
    training: '训练中',
    resting: '休息中'
  }
  return statusTexts[props.pet.status as keyof typeof statusTexts] || '健康'
})

const statusTextClass = computed(() => {
  const statusClasses = {
    healthy: 'text-green-600',
    sick: 'text-red-600',
    injured: 'text-orange-600',
    tired: 'text-gray-600',
    excited: 'text-yellow-600',
    training: 'text-blue-600',
    resting: 'text-purple-600'
  }
  return statusClasses[props.pet.status as keyof typeof statusClasses] || 'text-green-600'
})

const moodIcon = computed(() => {
  const moodIcons = {
    happy: '😊',
    content: '😌',
    sad: '😢',
    angry: '😠',
    excited: '🤩',
    tired: '😴',
    playful: '😄',
    lonely: '😔'
  }
  return moodIcons[props.pet.mood as keyof typeof moodIcons] || '😌'
})

const moodText = computed(() => {
  const moodTexts = {
    happy: '开心',
    content: '满足',
    sad: '伤心',
    angry: '生气',
    excited: '兴奋',
    tired: '疲惫',
    playful: '顽皮',
    lonely: '孤独'
  }
  return moodTexts[props.pet.mood as keyof typeof moodTexts] || '满足'
})

const moodTextClass = computed(() => {
  const moodClasses = {
    happy: 'text-yellow-600',
    content: 'text-green-600',
    sad: 'text-blue-600',
    angry: 'text-red-600',
    excited: 'text-orange-600',
    tired: 'text-gray-600',
    playful: 'text-pink-600',
    lonely: 'text-purple-600'
  }
  return moodClasses[props.pet.mood as keyof typeof moodClasses] || 'text-green-600'
})

// 计算属性 - 成长阶段
const growthStages = computed(() => [
  { name: '幼体', icon: '🥚' },
  { name: '幼年', icon: '🐣' },
  { name: '少年', icon: '🐤' },
  { name: '成年', icon: '🐦' },
  { name: '成熟', icon: '🦅' }
])

const currentStageIndex = computed(() => {
  const stageMap = {
    egg: 0,
    baby: 1,
    child: 2,
    adult: 3,
    elder: 4
  }
  return stageMap[props.pet.growthStage as keyof typeof stageMap] || 2
})
</script>

<style scoped>
.pet-basic-info-card {
  transition: all 0.3s ease;
}

.pet-basic-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.level-badge {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-bar, .status-card, .mood-card {
  transition: all 0.3s ease;
}

.status-card:hover, .mood-card:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.growth-stages {
  position: relative;
}

.growth-stages::before {
  content: '';
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  height: 2px;
  background: #e5e7eb;
  z-index: 0;
}

.stage-item {
  position: relative;
  z-index: 1;
}

.stage-item .stage-icon {
  background: #e5e7eb;
  color: #9ca3af;
}

.stage-item.active .stage-icon {
  background: #3b82f6;
  color: white;
}

.stage-item.current .stage-icon {
  background: #f59e0b;
  color: white;
  animation: pulse 2s infinite;
}

.stage-item.current .stage-name {
  color: #f59e0b;
  font-weight: 600;
}

.achievement-badge {
  transition: all 0.3s ease;
}

.achievement-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .card-header {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .pet-info h2 {
    font-size: 1.25rem;
  }

  .status-mood-section {
    grid-template-columns: 1fr;
  }

  .achievements-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
