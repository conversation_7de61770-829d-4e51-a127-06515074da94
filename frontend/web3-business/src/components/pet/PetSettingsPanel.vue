<template>
  <div class="pet-settings-panel p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold">萌宠设置</h3>
      <van-icon name="cross" @click="$emit('close')" class="cursor-pointer" />
    </div>

    <div class="settings-content space-y-4">
      <div class="setting-item">
        <van-cell title="导出萌宠数据" is-link @click="$emit('export')" />
      </div>

      <div class="setting-item">
        <van-cell title="重置萌宠" is-link @click="$emit('reset')" />
      </div>

      <div class="setting-item">
        <van-cell title="萌宠信息" :value="`等级 ${pet?.level || 0}`" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Pet } from '../../types/typesWithoutCircular'

interface Props {
  pet: Pet | null
}

interface Emits {
  (e: 'close'): void
  (e: 'export'): void
  (e: 'reset'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
