<template>
  <div class="pet-nurturing-interface">
    <div v-if="currentPet" class="nurturing-container">
      <!-- 萌宠显示区域 -->
      <div class="pet-display-section">
        <div class="pet-avatar">
          <img
            :src="currentPet.avatar"
            :alt="currentPet.name"
            class="pet-image"
            @error="handleImageError"
          />
          <div class="pet-mood-indicator" :class="`mood-${currentPet.mood}`">
            {{ getMoodEmoji(currentPet.mood) }}
          </div>
        </div>

        <!-- 萌宠状态栏 -->
        <div class="pet-status-bars">
          <div class="status-bar">
            <span class="status-label">健康度</span>
            <div class="progress-bar">
              <div
                class="progress-fill health"
                :style="{ width: `${(currentPet.health / currentPet.maxHealth) * 100}%` }"
              ></div>
            </div>
            <span class="status-value">{{ currentPet.health }}/{{ currentPet.maxHealth }}</span>
          </div>

          <div class="status-bar">
            <span class="status-label">快乐度</span>
            <div class="progress-bar">
              <div
                class="progress-fill happiness"
                :style="{ width: `${(currentPet.happiness / currentPet.maxHappiness) * 100}%` }"
              ></div>
            </div>
            <span class="status-value">{{ currentPet.happiness }}/{{ currentPet.maxHappiness }}</span>
          </div>

          <div class="status-bar">
            <span class="status-label">能量值</span>
            <div class="progress-bar">
              <div
                class="progress-fill energy"
                :style="{ width: `${(currentPet.energy / currentPet.maxEnergy) * 100}%` }"
              ></div>
            </div>
            <span class="status-value">{{ currentPet.energy }}/{{ currentPet.maxEnergy }}</span>
          </div>

          <div class="status-bar">
            <span class="status-label">经验值</span>
            <div class="progress-bar">
              <div
                class="progress-fill experience"
                :style="{ width: `${(currentPet.experience / currentPet.maxExperience) * 100}%` }"
              ></div>
            </div>
            <span class="status-value">{{ currentPet.experience }}/{{ currentPet.maxExperience }}</span>
          </div>
        </div>
      </div>

      <!-- 养成动作区域 -->
      <div class="nurturing-actions-section">
        <!-- 喂食动作 -->
        <div class="action-category">
          <h4 class="category-title">🍎 喂食</h4>
          <div class="action-buttons">
            <button
              v-for="action in feedingActions"
              :key="action.id"
              @click="performAction('feed', action.id)"
              :disabled="!action.available || nurturing.isProcessing"
              :class="['action-btn', 'feed-btn', { disabled: !action.available }]"
              :title="action.available ? action.description : action.reason"
            >
              <div class="btn-content">
                <span class="btn-name">{{ action.name }}</span>
                <span v-if="action.cooldownRemaining > 0" class="btn-cooldown">
                  {{ formatCooldown(action.cooldownRemaining) }}
                </span>
              </div>
            </button>
          </div>
        </div>

        <!-- 训练动作 -->
        <div class="action-category">
          <h4 class="category-title">💪 训练</h4>
          <div class="action-buttons">
            <button
              v-for="action in trainingActions"
              :key="action.id"
              @click="performAction('train', action.id)"
              :disabled="!action.available || nurturing.isProcessing"
              :class="['action-btn', 'train-btn', { disabled: !action.available }]"
              :title="action.available ? action.description : action.reason"
            >
              <div class="btn-content">
                <span class="btn-name">{{ action.name }}</span>
                <span v-if="action.cooldownRemaining > 0" class="btn-cooldown">
                  {{ formatCooldown(action.cooldownRemaining) }}
                </span>
              </div>
            </button>
          </div>
        </div>

        <!-- 玩耍动作 -->
        <div class="action-category">
          <h4 class="category-title">🎾 玩耍</h4>
          <div class="action-buttons">
            <button
              v-for="action in playActions"
              :key="action.id"
              @click="performAction('play', action.id)"
              :disabled="!action.available || nurturing.isProcessing"
              :class="['action-btn', 'play-btn', { disabled: !action.available }]"
              :title="action.available ? action.description : action.reason"
            >
              <div class="btn-content">
                <span class="btn-name">{{ action.name }}</span>
                <span v-if="action.cooldownRemaining > 0" class="btn-cooldown">
                  {{ formatCooldown(action.cooldownRemaining) }}
                </span>
              </div>
            </button>
          </div>
        </div>

        <!-- 其他动作 -->
        <div class="action-category">
          <h4 class="category-title">😴 其他</h4>
          <div class="action-buttons">
            <button
              @click="performAction('rest')"
              :disabled="currentPet.status === 'sleeping' || nurturing.isProcessing"
              class="action-btn rest-btn"
            >
              <div class="btn-content">
                <span class="btn-name">休息</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- 萌宠信息区域 -->
      <div class="pet-info-section">
        <div class="pet-basic-info">
          <h3 class="pet-name">{{ currentPet.name }}</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">等级:</span>
              <span class="info-value">{{ currentPet.level }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">稀有度:</span>
              <span class="info-value" :class="`rarity-${currentPet.rarity}`">
                {{ getRarityText(currentPet.rarity) }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">成长阶段:</span>
              <span class="info-value">{{ getGrowthStageText(currentPet.growthStage) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">心情:</span>
              <span class="info-value" :class="`mood-${currentPet.mood}`">
                {{ getMoodText(currentPet.mood) }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">状态:</span>
              <span class="info-value" :class="`status-${currentPet.status}`">
                {{ getStatusText(currentPet.status) }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">代币价值:</span>
              <span class="info-value token-value">
                {{ formatTokenValue(petStore.currentPetTokenValue) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 成长里程碑 -->
        <div class="milestones-section">
          <h4 class="section-title">🏆 成长里程碑</h4>
          <div class="milestone-list">
            <div
              v-for="milestone in milestones"
              :key="milestone.id"
              :class="['milestone-item', { achieved: milestone.achieved }]"
            >
              <div class="milestone-icon">
                {{ milestone.achieved ? '✅' : '⏳' }}
              </div>
              <div class="milestone-content">
                <div class="milestone-name">{{ milestone.name }}</div>
                <div class="milestone-description">{{ milestone.description }}</div>
                <div class="milestone-progress">
                  <div class="progress-bar small">
                    <div
                      class="progress-fill milestone"
                      :style="{ width: `${Math.min(100, (milestone.progress / milestone.requirement) * 100)}%` }"
                    ></div>
                  </div>
                  <span class="progress-text">{{ milestone.progress }}/{{ milestone.requirement }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动历史 -->
      <div v-if="nurturing.actionHistory.length > 0" class="activity-history">
        <h4 class="section-title">📝 最近活动</h4>
        <div class="history-list">
          <div
            v-for="record in nurturing.actionHistory.slice(-5)"
            :key="record.timestamp"
            class="history-item"
          >
            <span class="history-time">{{ formatTime(record.timestamp) }}</span>
            <span class="history-action">{{ record.action }}</span>
            <span class="history-result">{{ record.result }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 无萌宠状态 -->
    <div v-else class="no-pet-state">
      <div class="no-pet-content">
        <div class="no-pet-icon">🐾</div>
        <h3>还没有萌宠</h3>
        <p>创建你的第一只萌宠开始养成之旅吧！</p>
        <button @click="createRandomPet" class="create-pet-btn">
          🎲 随机生成萌宠
        </button>
      </div>
    </div>

    <!-- 升级动画弹窗 -->
    <div v-if="showLevelUpModal" class="level-up-modal">
      <div class="modal-overlay" @click="closeLevelUpModal"></div>
      <div class="modal-content">
        <div class="level-up-animation">
          <div class="level-up-icon">🎉</div>
          <h2>恭喜升级！</h2>
          <p>{{ currentPet?.name }} 升级到了 {{ levelUpResult?.newLevel }} 级！</p>

          <div v-if="levelUpResult?.statGains" class="stat-gains">
            <h4>属性提升:</h4>
            <div class="stat-gain-grid">
              <div
                v-for="(gain, stat) in levelUpResult.statGains"
                :key="stat"
                class="stat-gain-item"
              >
                <span class="stat-name">{{ getStatName(stat) }}</span>
                <span class="stat-gain">+{{ gain }}</span>
              </div>
            </div>
          </div>

          <div v-if="levelUpResult?.unlockedFeatures?.length" class="unlocked-features">
            <h4>解锁功能:</h4>
            <ul>
              <li v-for="feature in levelUpResult.unlockedFeatures" :key="feature">
                {{ getFeatureName(feature) }}
              </li>
            </ul>
          </div>

          <button @click="closeLevelUpModal" class="modal-close-btn">
            确定
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="nurturing.isProcessing" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>处理中...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { usePetStore } from '../../stores/pet'
import { usePetNurturing } from '../../composables/usePetNurturing'
import { PetMood, PetStatus, PetRarity, GrowthStage } from '../../types/typesWithoutCircular'
import type { LevelUpResult } from '../../utils/petGrowthSystem'

const petStore = usePetStore()
const nurturing = usePetNurturing()

const showLevelUpModal = ref(false)
const levelUpResult = ref<LevelUpResult | null>(null)
let autoGrowthCleanup: (() => void) | null = null

const currentPet = computed(() => petStore.currentPet)
const milestones = computed(() => {
  if (!currentPet.value) return []
  return petStore.getPetMilestones(currentPet.value.id)
})

// 分类动作
const feedingActions = computed(() =>
  nurturing.availableActions.filter(action => action.type === 'feed')
)

const trainingActions = computed(() =>
  nurturing.availableActions.filter(action => action.type === 'train')
)

const playActions = computed(() =>
  nurturing.availableActions.filter(action => action.type === 'play')
)

// 执行养成动作
const performAction = async (type: string, actionId?: string) => {
  if (!currentPet.value || nurturing.isProcessing) return

  try {
    let success = false

    switch (type) {
      case 'feed':
        success = await nurturing.feedPet(currentPet.value.id, actionId)
        break
      case 'train':
        success = await nurturing.trainPet(currentPet.value.id, actionId!)
        break
      case 'play':
        success = await nurturing.playWithPet(currentPet.value.id, actionId)
        break
      case 'rest':
        success = await nurturing.restPet(currentPet.value.id)
        break
    }

    if (success) {
      // 检查升级
      const result = petStore.checkLevelUp(currentPet.value.id)
      if (result && result.success) {
        levelUpResult.value = result
        showLevelUpModal.value = true
      }
    }
  } catch (error) {
    console.error('执行动作失败:', error)
    // 这里可以添加错误提示
  }
}

const createRandomPet = () => {
  petStore.generateRandomPet()
}

const closeLevelUpModal = () => {
  showLevelUpModal.value = false
  levelUpResult.value = null
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/pets/default.png' // 默认图片
}

// 格式化方法
const formatCooldown = (milliseconds: number): string => {
  const minutes = Math.ceil(milliseconds / (60 * 1000))
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}小时${remainingMinutes}分钟`
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatTokenValue = (value: string): string => {
  const num = parseFloat(value) / 1e18
  return num.toFixed(4) + ' TOKEN'
}

const getRarityText = (rarity: PetRarity): string => {
  const rarityTexts = {
    [PetRarity.COMMON]: '普通',
    [PetRarity.UNCOMMON]: '不凡',
    [PetRarity.RARE]: '稀有',
    [PetRarity.EPIC]: '史诗',
    [PetRarity.LEGENDARY]: '传说',
    [PetRarity.MYTHICAL]: '神话'
  }
  return rarityTexts[rarity] || '未知'
}

const getGrowthStageText = (stage: GrowthStage): string => {
  const stageTexts = {
    [GrowthStage.BABY]: '幼体',
    [GrowthStage.CHILD]: '儿童',
    [GrowthStage.TEEN]: '青少年',
    [GrowthStage.ADULT]: '成年',
    [GrowthStage.ELDER]: '长者',
    [GrowthStage.LEGENDARY_FORM]: '传说形态'
  }
  return stageTexts[stage] || '未知'
}

const getMoodText = (mood: PetMood): string => {
  const moodTexts = {
    [PetMood.HAPPY]: '开心',
    [PetMood.CONTENT]: '满足',
    [PetMood.PLAYFUL]: '顽皮',
    [PetMood.TIRED]: '疲惫',
    [PetMood.SAD]: '伤心',
    [PetMood.SICK]: '生病',
    [PetMood.EXCITED]: '兴奋',
    [PetMood.FOCUSED]: '专注'
  }
  return moodTexts[mood] || '未知'
}

const getMoodEmoji = (mood: PetMood): string => {
  const moodEmojis = {
    [PetMood.HAPPY]: '😊',
    [PetMood.CONTENT]: '😌',
    [PetMood.PLAYFUL]: '😄',
    [PetMood.TIRED]: '😴',
    [PetMood.SAD]: '😢',
    [PetMood.SICK]: '🤒',
    [PetMood.EXCITED]: '🤩',
    [PetMood.FOCUSED]: '🧐'
  }
  return moodEmojis[mood] || '😐'
}

const getStatusText = (status: PetStatus): string => {
  const statusTexts = {
    [PetStatus.HEALTHY]: '健康',
    [PetStatus.SICK]: '生病',
    [PetStatus.SLEEPING]: '睡觉',
    [PetStatus.PLAYING]: '玩耍中',
    [PetStatus.TRAINING]: '训练中',
    [PetStatus.EATING]: '进食中'
  }
  return statusTexts[status] || '未知'
}

const getStatName = (stat: string): string => {
  const statNames: Record<string, string> = {
    strength: '力量',
    intelligence: '智力',
    agility: '敏捷',
    charm: '魅力',
    vitality: '体质',
    luck: '幸运'
  }
  return statNames[stat] || stat
}

const getFeatureName = (feature: string): string => {
  const featureNames: Record<string, string> = {
    charm_training: '魅力训练',
    comprehensive_training: '综合训练',
    equipment_system: '装备系统',
    skill_learning: '技能学习',
    breeding: '繁殖功能',
    evolution: '进化系统',
    legendary_skills: '传说技能',
    mythical_form: '神话形态',
    max_level: '最高等级'
  }
  return featureNames[feature] || feature
}

// 生命周期
onMounted(() => {
  // 启动自动成长系统
  autoGrowthCleanup = petStore.startAutoGrowthSystem()
})

onUnmounted(() => {
  // 清理自动成长系统
  if (autoGrowthCleanup) {
    autoGrowthCleanup()
  }
})
</script>

<style scoped>
.pet-nurturing-interface {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.nurturing-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.pet-display-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pet-avatar {
  position: relative;
  text-align: center;
  margin-bottom: 20px;
}

.pet-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #e5e7eb;
  transition: border-color 0.3s ease;
}

.pet-mood-indicator {
  position: absolute;
  top: -5px;
  right: calc(50% - 70px);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pet-status-bars {
  space-y: 12px;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.status-label {
  min-width: 60px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.progress-bar {
  flex: 1;
  height: 20px;
  background: #f3f4f6;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar.small {
  height: 8px;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-fill.health {
  background: linear-gradient(90deg, #ef4444, #22c55e);
}

.progress-fill.happiness {
  background: linear-gradient(90deg, #f59e0b, #eab308);
}

.progress-fill.energy {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.progress-fill.experience {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.progress-fill.milestone {
  background: linear-gradient(90deg, #10b981, #059669);
}

.status-value {
  min-width: 60px;
  font-size: 12px;
  text-align: right;
  color: #6b7280;
}

.nurturing-actions-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-category {
  margin-bottom: 20px;
}

.category-title {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.action-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.btn-name {
  font-weight: 500;
}

.btn-cooldown {
  font-size: 10px;
  opacity: 0.8;
}

.feed-btn {
  background: #22c55e;
  color: white;
}

.feed-btn:hover:not(.disabled) {
  background: #16a34a;
  transform: translateY(-1px);
}

.train-btn {
  background: #3b82f6;
  color: white;
}

.train-btn:hover:not(.disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.play-btn {
  background: #f59e0b;
  color: white;
}

.play-btn:hover:not(.disabled) {
  background: #d97706;
  transform: translateY(-1px);
}

.rest-btn {
  background: #8b5cf6;
  color: white;
}

.rest-btn:hover:not(.disabled) {
  background: #7c3aed;
  transform: translateY(-1px);
}

.pet-info-section {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.pet-basic-info {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pet-name {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.token-value {
  color: #10b981;
  font-weight: 600;
}

/* 稀有度颜色 */
.rarity-common { color: #6b7280; }
.rarity-uncommon { color: #10b981; }
.rarity-rare { color: #3b82f6; }
.rarity-epic { color: #8b5cf6; }
.rarity-legendary { color: #f59e0b; }
.rarity-mythical { color: #ef4444; }

/* 心情颜色 */
.mood-happy { color: #22c55e; }
.mood-content { color: #10b981; }
.mood-playful { color: #f59e0b; }
.mood-tired { color: #6b7280; }
.mood-sad { color: #ef4444; }
.mood-sick { color: #dc2626; }
.mood-excited { color: #8b5cf6; }
.mood-focused { color: #3b82f6; }

/* 状态颜色 */
.status-healthy { color: #22c55e; }
.status-sick { color: #ef4444; }
.status-sleeping { color: #6b7280; }
.status-playing { color: #f59e0b; }
.status-training { color: #3b82f6; }
.status-eating { color: #10b981; }

.milestones-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.milestone-list {
  max-height: 300px;
  overflow-y: auto;
}

.milestone-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.milestone-item.achieved {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.milestone-item:not(.achieved) {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.milestone-icon {
  font-size: 18px;
  margin-top: 2px;
}

.milestone-content {
  flex: 1;
}

.milestone-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.milestone-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.milestone-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
  min-width: 50px;
}

.activity-history {
  grid-column: 1 / -1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
  font-size: 12px;
}

.history-time {
  color: #6b7280;
  min-width: 80px;
}

.history-action {
  flex: 1;
  color: #374151;
  margin: 0 10px;
}

.history-result {
  color: #10b981;
  font-weight: 500;
  min-width: 80px;
  text-align: right;
}

.no-pet-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.no-pet-content {
  text-align: center;
  padding: 40px;
}

.no-pet-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-pet-content h3 {
  margin: 0 0 10px 0;
  color: #374151;
  font-size: 24px;
}

.no-pet-content p {
  margin: 0 0 30px 0;
  color: #6b7280;
  font-size: 16px;
}

.create-pet-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-pet-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.level-up-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.level-up-animation {
  text-align: center;
}

.level-up-icon {
  font-size: 48px;
  margin-bottom: 15px;
  animation: bounce 1s infinite;
}

.level-up-animation h2 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 24px;
}

.level-up-animation p {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 16px;
}

.stat-gains h4 {
  margin: 0 0 15px 0;
  color: #374151;
  font-size: 16px;
}

.stat-gain-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.stat-gain-item {
  background: #f0fdf4;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-name {
  font-size: 12px;
  color: #374151;
}

.stat-gain {
  font-size: 14px;
  font-weight: 600;
  color: #16a34a;
}

.unlocked-features {
  margin-bottom: 20px;
}

.unlocked-features h4 {
  margin: 0 0 10px 0;
  color: #374151;
  font-size: 16px;
}

.unlocked-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.unlocked-features li {
  background: #eff6ff;
  padding: 6px 12px;
  border-radius: 4px;
  margin-bottom: 4px;
  color: #1d4ed8;
  font-size: 14px;
}

.modal-close-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.modal-close-btn:hover {
  background: #2563eb;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

.loading-overlay p {
  color: #6b7280;
  font-size: 14px;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-30px,0);
  }
  70% {
    transform: translate3d(0,-15px,0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nurturing-container {
    grid-template-columns: 1fr;
  }

  .pet-info-section {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    justify-content: center;
  }

  .stat-gain-grid {
    grid-template-columns: 1fr;
  }
}
</style>