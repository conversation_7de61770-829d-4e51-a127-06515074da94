<template>
  <nav class="responsive-navigation" :class="{ 'nav-open': isMenuOpen }">
    <!-- 移动端顶部导航栏 -->
    <div class="mobile-header">
      <div class="mobile-header-content">
        <!-- Logo -->
        <router-link to="/" class="nav-logo" @click="closeMenu">
          <img src="/favicon.ico" alt="萌宠世界" class="logo-image" />
          <span class="logo-text">萌宠世界</span>
        </router-link>

        <!-- 移动端菜单按钮 -->
        <button
          class="mobile-menu-button"
          @click="toggleMenu"
          :aria-expanded="isMenuOpen"
          aria-label="切换菜单"
        >
          <div class="hamburger" :class="{ active: isMenuOpen }">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
    </div>

    <!-- 导航菜单 -->
    <div class="nav-menu" :class="{ 'menu-open': isMenuOpen }">
      <div class="nav-content">
        <!-- 主要导航链接 -->
        <div class="nav-links">
          <router-link
            v-for="link in mainLinks"
            :key="link.path"
            :to="link.path"
            class="nav-link"
            :class="{ active: isActiveRoute(link.path) }"
            @click="closeMenu"
          >
            <span class="nav-icon">{{ link.icon }}</span>
            <span class="nav-text">{{ link.text }}</span>
            <span v-if="link.badge" class="nav-badge">{{ link.badge }}</span>
          </router-link>
        </div>

        <!-- 次要导航链接 -->
        <div class="nav-secondary">
          <router-link
            v-for="link in secondaryLinks"
            :key="link.path"
            :to="link.path"
            class="nav-link secondary"
            :class="{ active: isActiveRoute(link.path) }"
            @click="closeMenu"
          >
            <span class="nav-icon">{{ link.icon }}</span>
            <span class="nav-text">{{ link.text }}</span>
          </router-link>
        </div>

        <!-- 钱包状态 -->
        <div class="nav-wallet">
          <WalletStatus compact />
        </div>

        <!-- 主题切换 -->
        <div class="nav-theme">
          <button
            class="theme-toggle"
            @click="toggleTheme"
            :title="`切换到${getNextThemeText()}主题`"
          >
            <span class="theme-icon">{{ getThemeIcon() }}</span>
            <span class="theme-text">{{ getCurrentThemeText() }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="isMenuOpen"
      class="mobile-overlay"
      @click="closeMenu"
    ></div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalTheme } from '../../composables/useTheme'
import { usePetStore } from '../../stores/pet'
import WalletStatus from '../WalletStatus.vue'

const route = useRoute()
const theme = useGlobalTheme()
const petStore = usePetStore()

const isMenuOpen = ref(false)

// 主要导航链接
const mainLinks = computed(() => [
  {
    path: '/',
    text: '首页',
    icon: '🏠'
  },
  {
    path: '/pets',
    text: '我的萌宠',
    icon: '🐱',
    badge: petStore.petCount > 0 ? petStore.petCount.toString() : undefined
  },
  {
    path: '/pet/create',
    text: '创建萌宠',
    icon: '✨'
  },
  {
    path: '/shop',
    text: '商店',
    icon: '🛒'
  },
  {
    path: '/token-management',
    text: '代币管理',
    icon: '💰'
  }
])

// 次要导航链接
const secondaryLinks = [
  {
    path: '/tutorial',
    text: '游戏教程',
    icon: '📚'
  },
  {
    path: '/introduction',
    text: '游戏介绍',
    icon: '📖'
  },
  {
    path: '/settings',
    text: '设置',
    icon: '⚙️'
  },
  {
    path: '/about',
    text: '关于',
    icon: 'ℹ️'
  }
]

// 方法
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

const isActiveRoute = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

const toggleTheme = () => {
  theme.toggleTheme()
}

const getCurrentThemeText = () => {
  const themeTexts = {
    light: '浅色',
    dark: '深色',
    auto: '自动'
  }
  return themeTexts[theme.themeConfig.value.mode]
}

const getNextThemeText = () => {
  const nextThemes = {
    light: '深色',
    dark: '自动',
    auto: '浅色'
  }
  return nextThemes[theme.themeConfig.value.mode]
}

const getThemeIcon = () => {
  const themeIcons = {
    light: '☀️',
    dark: '🌙',
    auto: '🔄'
  }
  return themeIcons[theme.themeConfig.value.mode]
}

// 监听路由变化，关闭菜单
const handleRouteChange = () => {
  closeMenu()
}

// 监听窗口大小变化
const handleResize = () => {
  if (window.innerWidth >= 1024) {
    closeMenu()
  }
}

// 监听 ESC 键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isMenuOpen.value) {
    closeMenu()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.responsive-navigation {
  position: relative;
  z-index: 50;
}

/* 移动端顶部导航栏 */
.mobile-header {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4rem;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 60;
}

.mobile-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: inherit;
  font-weight: 600;
  font-size: 1.125rem;
}

.logo-image {
  width: 2rem;
  height: 2rem;
}

.logo-text {
  display: none;
}

/* 汉堡菜单按钮 */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.hamburger {
  position: relative;
  width: 1.5rem;
  height: 1.125rem;
}

.hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background-color: #374151;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.hamburger span:nth-child(1) {
  top: 0;
}

.hamburger span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.hamburger span:nth-child(3) {
  bottom: 0;
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg);
  top: 50%;
  margin-top: -1px;
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 50%;
  margin-bottom: -1px;
}

/* 导航菜单 */
.nav-menu {
  position: fixed;
  top: 4rem;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  z-index: 55;
}

.nav-menu.menu-open {
  transform: translateX(0);
}

.nav-content {
  padding: 2rem 1rem;
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 导航链接 */
.nav-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  text-decoration: none;
  color: #374151;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  background-color: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.nav-link.active {
  background-color: #a855f7;
  color: white;
}

.nav-icon {
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
}

.nav-text {
  flex: 1;
}

.nav-badge {
  background-color: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 0.5rem;
  min-width: 1.25rem;
  text-align: center;
}

.nav-link.active .nav-badge {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 次要导航 */
.nav-secondary {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-link.secondary {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}

/* 钱包状态 */
.nav-wallet {
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.75rem;
}

/* 主题切换 */
.nav-theme {
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: #a855f7;
}

.theme-icon {
  font-size: 1.25rem;
}

.theme-text {
  flex: 1;
  text-align: left;
}

/* 移动端遮罩 */
.mobile-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
}

/* 桌面端样式 */
@media (min-width: 640px) {
  .logo-text {
    display: block;
  }
}

@media (min-width: 1024px) {
  .mobile-header {
    position: static;
    height: auto;
    background: none;
    backdrop-filter: none;
    border-bottom: none;
  }

  .mobile-header-content {
    padding: 1rem 2rem;
  }

  .mobile-menu-button {
    display: none;
  }

  .nav-menu {
    position: static;
    transform: none;
    background: none;
    backdrop-filter: none;
    overflow: visible;
  }

  .nav-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    max-width: none;
    gap: 2rem;
  }

  .nav-links {
    flex-direction: row;
    gap: 0.5rem;
  }

  .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
  }

  .nav-secondary {
    flex-direction: row;
    padding-top: 0;
    border-top: none;
    gap: 0.5rem;
  }

  .nav-wallet {
    padding: 0;
    background: none;
    border-radius: 0;
  }

  .nav-theme {
    padding-top: 0;
    border-top: none;
  }

  .theme-toggle {
    width: auto;
    padding: 0.5rem 1rem;
  }

  .mobile-overlay {
    display: none;
  }
}

/* 深色主题适配 */
:global(.theme-dark) .mobile-header {
  background-color: rgba(17, 24, 39, 0.95);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

:global(.theme-dark) .nav-menu {
  background-color: rgba(17, 24, 39, 0.98);
}

:global(.theme-dark) .nav-link {
  color: #d1d5db;
}

:global(.theme-dark) .nav-link:hover {
  background-color: rgba(168, 85, 247, 0.2);
}

:global(.theme-dark) .nav-secondary {
  border-top-color: rgba(255, 255, 255, 0.1);
}

:global(.theme-dark) .nav-wallet {
  background-color: rgba(255, 255, 255, 0.05);
}

:global(.theme-dark) .nav-theme {
  border-top-color: rgba(255, 255, 255, 0.1);
}

:global(.theme-dark) .theme-toggle {
  color: #d1d5db;
  border-color: rgba(255, 255, 255, 0.1);
}

:global(.theme-dark) .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

:global(.theme-dark) .hamburger span {
  background-color: #d1d5db;
}
</style>
