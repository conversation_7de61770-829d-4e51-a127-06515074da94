<template>
  <nav class="desktop-navigation">
    <div class="nav-container">
      <!-- Logo 和品牌 -->
      <div class="logo-area">
        <router-link to="/" class="logo-link">
          <div class="logo-icon">🐾</div>
          <div class="logo-text">萌宠养成</div>
        </router-link>
      </div>

      <!-- 主导航菜单 -->
      <div class="main-nav">
        <router-link to="/" class="nav-item" :class="{ 'active': $route.path === '/' }">
          首页
        </router-link>
        <router-link to="/pets" class="nav-item" :class="{ 'active': $route.path === '/pets' || $route.path.startsWith('/pet/') }">
          萌宠世界
        </router-link>
        <router-link to="/shop" class="nav-item" :class="{ 'active': $route.path === '/shop' }">
          游戏商店
        </router-link>
        <router-link to="/tutorial" class="nav-item" :class="{ 'active': $route.path === '/tutorial' }">
          教程
        </router-link>
        <router-link to="/introduction" class="nav-item" :class="{ 'active': $route.path === '/introduction' }">
          游戏介绍
        </router-link>
      </div>

      <!-- 右侧操作区 -->
      <div class="actions-area">
        <router-link to="/pet/create" class="create-button">
          创建萌宠
        </router-link>
        <div class="wallet-area">
          <WalletStatus />
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          @click="showMobileMenu = !showMobileMenu"
          class="mobile-menu-btn md:hidden"
          aria-label="菜单"
        >
          <van-icon :name="showMobileMenu ? 'cross' : 'bars'" class="text-xl" />
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <transition name="slide-down">
      <div v-show="showMobileMenu" class="mobile-menu md:hidden">
        <router-link
          v-for="item in mobileMenuItems"
          :key="item.path"
          :to="item.path"
          class="mobile-menu-item"
          :class="{ 'active': $route.path === item.path }"
          @click="showMobileMenu = false"
        >
          <van-icon :name="item.icon" class="mr-2" />
          {{ item.name }}
        </router-link>

        <!-- 移动端钱包状态 -->
        <div class="mobile-wallet">
          <WalletStatus />
        </div>
      </div>
    </transition>
  </nav>
</template>

<script lang="ts">
export default {
  name: 'DesktopNavigation'
}
</script>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { usePetStore } from '../../stores/pet'
import WalletStatus from '../WalletStatus.vue'

const router = useRouter()
const petStore = usePetStore()

// 响应式状态
const showMobileMenu = ref(false)

// 计算属性
const petCount = computed(() => petStore.petCount)

const mobileMenuItems = computed(() => [
  { path: '/', name: '首页', icon: 'home-o' },
  { path: '/pets', name: '萌宠世界', icon: 'friends-o', badge: petCount.value > 0 ? petCount.value : null },
  { path: '/pet/create', name: '创建萌宠', icon: 'add-o' },
  { path: '/shop', name: '游戏商店', icon: 'shop-o' },
  { path: '/tutorial', name: '教程', icon: 'guide-o' },
  { path: '/introduction', name: '游戏介绍', icon: 'info-o' },
  { path: '/settings', name: '设置', icon: 'setting-o' }
])
</script>

<style scoped>
.desktop-navigation {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  height: 60px;
}

/* Logo 区域 */
.logo-area {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-icon {
  font-size: 1.75rem;
  margin-right: 0.5rem;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1d1d1f;
}

/* 主导航菜单 */
.main-nav {
  display: none;

  @media (min-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }
}

.nav-item {
  padding: 0.5rem 0.75rem;
  color: #1d1d1f;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
  position: relative;
}

.nav-item:hover {
  color: #0071e3;
}

.nav-item.active {
  color: #0071e3;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0071e3;
  border-radius: 1px;
}

/* 右侧操作区 */
.actions-area {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.create-button {
  display: none;

  @media (min-width: 768px) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: #0071e3;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.2s ease;
  }
}

.create-button:hover {
  background-color: #0077ed;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wallet-area {
  display: none;

  @media (min-width: 768px) {
    display: block;
  }
}

.mobile-menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  background-color: white;
  z-index: 40;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: #1d1d1f;
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 500;
}

.mobile-menu-item.active {
  background-color: rgba(0, 113, 227, 0.05);
  color: #0071e3;
}

.mobile-wallet {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  max-height: 600px;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
}
</style>
