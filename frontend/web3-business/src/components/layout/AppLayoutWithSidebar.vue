<template>
  <div class="app-layout-with-sidebar">
    <!-- 侧边栏遮罩 -->
    <div
      v-if="showSidebar"
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 侧边栏 -->
    <nav
      class="sidebar"
      :class="{ 'sidebar-open': showSidebar }"
    >
      <div class="sidebar-header">
        <div class="brand-section">
          <div class="brand-icon-wrapper">
            <span class="brand-icon">🐾</span>
            <div class="icon-sparkles">
              <span class="sparkle sparkle-1">✨</span>
              <span class="sparkle sparkle-2">⭐</span>
              <span class="sparkle sparkle-3">💫</span>
            </div>
          </div>
          <span class="brand-text">萌宠世界</span>
        </div>
        <button class="close-btn" @click="closeSidebar">
          <van-icon name="cross" />
        </button>
      </div>

      <div class="sidebar-content">
        <div class="nav-section">
          <h3 class="nav-section-title">主要功能</h3>
          <div class="nav-links">
            <router-link to="/" class="nav-link" @click="closeSidebar">
              <span class="link-icon">🏠</span>
              <span class="link-text">首页</span>
            </router-link>
            <router-link to="/pets" class="nav-link" @click="closeSidebar">
              <span class="link-icon">🐾</span>
              <span class="link-text">我的萌宠</span>
            </router-link>
            <router-link to="/pet/create" class="nav-link" @click="closeSidebar">
              <span class="link-icon">✨</span>
              <span class="link-text">创建萌宠</span>
            </router-link>
            <router-link to="/shop" class="nav-link" @click="closeSidebar">
              <span class="link-icon">🛒</span>
              <span class="link-text">商店</span>
            </router-link>
          </div>
        </div>

        <div class="nav-section">
          <h3 class="nav-section-title">代币管理</h3>
          <div class="nav-links">
            <router-link to="/token-management" class="nav-link" @click="closeSidebar">
              <span class="link-icon">💎</span>
              <span class="link-text">代币管理</span>
            </router-link>
            <router-link to="/token-exchange" class="nav-link" @click="closeSidebar">
              <span class="link-icon">🔄</span>
              <span class="link-text">代币兑换</span>
            </router-link>
          </div>
        </div>

        <div class="nav-section">
          <h3 class="nav-section-title">帮助与设置</h3>
          <div class="nav-links">
            <router-link to="/tutorial" class="nav-link" @click="closeSidebar">
              <span class="link-icon">📚</span>
              <span class="link-text">游戏教程</span>
            </router-link>
            <router-link to="/introduction" class="nav-link" @click="closeSidebar">
              <span class="link-icon">🎯</span>
              <span class="link-text">游戏介绍</span>
            </router-link>
            <router-link to="/settings" class="nav-link" @click="closeSidebar">
              <span class="link-icon">⚙️</span>
              <span class="link-text">设置</span>
            </router-link>
            <router-link to="/about" class="nav-link" @click="closeSidebar">
              <span class="link-icon">ℹ️</span>
              <span class="link-text">关于</span>
            </router-link>
          </div>
        </div>
      </div>

      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-avatar">
            <span class="avatar-emoji">😊</span>
          </div>
          <div class="user-details">
            <div class="user-name">萌宠玩家</div>
            <div class="user-status">在线</div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-wrapper">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-content">
          <button class="menu-btn" @click="openSidebar">
            <van-icon name="wap-nav" />
          </button>

          <div class="header-brand">
            <router-link to="/" class="brand-link">
              <span class="brand-icon">🐾</span>
              <span class="brand-text">萌宠世界</span>
            </router-link>
          </div>

          <div class="header-actions">
            <!-- 桌面端导航链接 -->
            <div class="desktop-nav">
              <router-link to="/" class="nav-link">首页</router-link>
              <router-link to="/pets" class="nav-link">萌宠</router-link>
              <router-link to="/shop" class="nav-link">商店</router-link>
              <router-link to="/token-management" class="nav-link">代币</router-link>
            </div>

            <div class="user-section">
              <div class="user-avatar">
                <span class="avatar-emoji">😊</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 主内容 -->
      <main class="main-content">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const showSidebar = ref(false)

const openSidebar = () => {
  showSidebar.value = true
}

const closeSidebar = () => {
  showSidebar.value = false
}
</script>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'AppLayoutWithSidebar'
})
</script>

<style scoped>
.app-layout-with-sidebar {
  min-height: 100vh;
  display: flex;
  background: #f8fafc;
}

/* 侧边栏遮罩 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  top: 0;
  left: -320px;
  width: 320px;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 999;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar-open {
  left: 0;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-section {
  display: flex;
  align-items: center;
}

.brand-icon-wrapper {
  position: relative;
  margin-right: 0.75rem;
}

.brand-icon {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

.icon-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 0.8rem;
  animation: sparkle 3s infinite;
}

.sparkle-1 {
  top: -5px;
  right: -5px;
  animation-delay: 0s;
}

.sparkle-2 {
  bottom: -5px;
  left: -5px;
  animation-delay: 1s;
}

.sparkle-3 {
  top: 50%;
  right: -10px;
  animation-delay: 2s;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.sidebar-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.nav-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(4px);
}

.nav-link.router-link-active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.link-icon {
  font-size: 1.25rem;
  transition: transform 0.3s ease;
}

.nav-link:hover .link-icon {
  transform: scale(1.2);
}

.link-text {
  font-size: 0.95rem;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(255, 234, 167, 0.3);
}

.avatar-emoji {
  font-size: 1.2rem;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: white;
}

.user-status {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 主内容区域 */
.main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.menu-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #374151;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-btn:hover {
  background: #f3f4f6;
  color: #667eea;
}

.header-brand {
  flex: 1;
  margin-left: 1rem;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #374151;
  font-weight: 700;
  font-size: 1.25rem;
}

.brand-link .brand-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.desktop-nav {
  display: none;
  gap: 1rem;
}

.desktop-nav .nav-link {
  text-decoration: none;
  color: #6b7280;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.desktop-nav .nav-link:hover {
  color: #667eea;
  background: #f3f4f6;
}

.desktop-nav .nav-link.router-link-active {
  color: #667eea;
  background: #eef2ff;
}

.user-section .user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-section .user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* 主内容 */
.main-content {
  flex: 1;
  background: #f8fafc;
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .desktop-nav {
    display: flex;
  }

  .menu-btn {
    display: none;
  }

  .sidebar {
    display: none;
  }

  .sidebar-overlay {
    display: none;
  }
}

@media (max-width: 767px) {
  .header-content {
    height: 60px;
  }

  .brand-link .brand-text {
    display: none;
  }

  .brand-link .brand-icon {
    font-size: 1.8rem;
  }
}
</style>
