<template>
  <div
    class="responsive-container"
    :class="[
      containerClass,
      paddingClass,
      { 'full-height': fullHeight }
    ]"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  fullHeight?: boolean
  centered?: boolean
}

// 定义组件选项
defineOptions({
  name: 'ResponsiveContainer'
})

const props = withDefaults(defineProps<Props>(), {
  maxWidth: 'xl',
  padding: 'md',
  fullHeight: false,
  centered: true
})

const containerClass = computed(() => {
  const classes = []

  // 最大宽度
  switch (props.maxWidth) {
    case 'sm':
      classes.push('max-w-sm')
      break
    case 'md':
      classes.push('max-w-md')
      break
    case 'lg':
      classes.push('max-w-4xl')
      break
    case 'xl':
      classes.push('max-w-6xl')
      break
    case '2xl':
      classes.push('max-w-7xl')
      break
    case 'full':
      classes.push('max-w-full')
      break
  }

  // 居中
  if (props.centered) {
    classes.push('mx-auto')
  }

  return classes.join(' ')
})

const paddingClass = computed(() => {
  switch (props.padding) {
    case 'none':
      return 'p-0'
    case 'sm':
      return 'p-2 sm:p-4'
    case 'md':
      return 'p-4 sm:p-6 lg:p-8'
    case 'lg':
      return 'p-6 sm:p-8 lg:p-12'
    case 'xl':
      return 'p-8 sm:p-12 lg:p-16'
    default:
      return 'p-4 sm:p-6 lg:p-8'
  }
})
</script>

<style scoped>
.responsive-container {
  width: 100%;
}

.full-height {
  min-height: 100vh;
}

/* 响应式网格系统 */
.responsive-container :deep(.grid-responsive) {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-container :deep(.grid-responsive) {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-container :deep(.grid-responsive) {
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container :deep(.grid-responsive) {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-container :deep(.grid-responsive) {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 响应式弹性布局 */
.responsive-container :deep(.flex-responsive) {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .responsive-container :deep(.flex-responsive) {
    flex-direction: row;
    gap: 2rem;
  }
}

/* 响应式文本大小 */
.responsive-container :deep(.text-responsive-sm) {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .responsive-container :deep(.text-responsive-sm) {
    font-size: 1rem;
  }
}

.responsive-container :deep(.text-responsive-base) {
  font-size: 1rem;
}

@media (min-width: 640px) {
  .responsive-container :deep(.text-responsive-base) {
    font-size: 1.125rem;
  }
}

.responsive-container :deep(.text-responsive-lg) {
  font-size: 1.125rem;
}

@media (min-width: 640px) {
  .responsive-container :deep(.text-responsive-lg) {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container :deep(.text-responsive-lg) {
    font-size: 1.5rem;
  }
}

.responsive-container :deep(.text-responsive-xl) {
  font-size: 1.5rem;
}

@media (min-width: 640px) {
  .responsive-container :deep(.text-responsive-xl) {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container :deep(.text-responsive-xl) {
    font-size: 2.5rem;
  }
}

/* 响应式间距 */
.responsive-container :deep(.space-responsive) > * + * {
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .responsive-container :deep(.space-responsive) > * + * {
    margin-top: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container :deep(.space-responsive) > * + * {
    margin-top: 2rem;
  }
}
</style>
