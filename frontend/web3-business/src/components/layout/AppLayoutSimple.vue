<template>
  <div class="app-layout-simple">
    <!-- 简化的导航栏 -->
    <nav class="simple-nav">
      <div class="nav-container">
        <router-link to="/" class="nav-brand">
          <span class="brand-icon">🐾</span>
          <span class="brand-text">萌宠游戏</span>
        </router-link>

        <div class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
          <router-link to="/pets" class="nav-link">我的萌宠</router-link>
          <router-link to="/shop" class="nav-link">商店</router-link>
          <router-link to="/token-management" class="nav-link">代币</router-link>
          <router-link to="/about" class="nav-link">关于</router-link>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 底部导航 (移动端) -->
    <nav class="bottom-nav">
      <router-link to="/" class="bottom-nav-item">
        <span class="nav-icon">🏠</span>
        <span class="nav-text">首页</span>
      </router-link>
      <router-link to="/pets" class="bottom-nav-item">
        <span class="nav-icon">🐾</span>
        <span class="nav-text">萌宠</span>
      </router-link>
      <router-link to="/shop" class="bottom-nav-item">
        <span class="nav-icon">🛒</span>
        <span class="nav-text">商店</span>
      </router-link>
      <router-link to="/token-management" class="bottom-nav-item">
        <span class="nav-icon">💎</span>
        <span class="nav-text">代币</span>
      </router-link>
      <router-link to="/about" class="bottom-nav-item">
        <span class="nav-icon">ℹ️</span>
        <span class="nav-text">关于</span>
      </router-link>
    </nav>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'AppLayoutSimple',
  setup() {
    return {}
  }
})
</script>

<style scoped>
.app-layout-simple {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* 顶部导航样式 */
.simple-nav {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.nav-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #1f2937;
  font-weight: 700;
  font-size: 1.25rem;
}

.brand-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #6b7280;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #667eea;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  min-height: calc(100vh - 60px);
}

/* 底部导航样式 */
.bottom-nav {
  display: none;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #6b7280;
  font-size: 0.75rem;
  padding: 0.5rem;
  transition: color 0.3s ease;
  flex: 1;
}

.bottom-nav-item:hover,
.bottom-nav-item.router-link-active {
  color: #667eea;
}

.nav-icon {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.nav-text {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .bottom-nav {
    display: flex;
  }

  .main-content {
    min-height: calc(100vh - 60px - 70px);
    padding-bottom: 70px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 0.5rem;
  }

  .brand-text {
    display: none;
  }
}
</style>
