<template>
  <div class="app-layout-simple">
    <!-- 萌系导航栏 -->
    <nav class="kawaii-nav">
      <div class="nav-container">
        <!-- 品牌区域 -->
        <router-link to="/" class="nav-brand">
          <div class="brand-icon-wrapper">
            <span class="brand-icon">🐾</span>
            <div class="icon-sparkles">
              <span class="sparkle sparkle-1">✨</span>
              <span class="sparkle sparkle-2">⭐</span>
              <span class="sparkle sparkle-3">💫</span>
            </div>
          </div>
          <span class="brand-text">萌宠世界</span>
        </router-link>

        <!-- 导航链接 -->
        <div class="nav-links">
          <router-link to="/" class="nav-link">
            <span class="link-icon">🏠</span>
            <span class="link-text">首页</span>
            <div class="link-bubble"></div>
          </router-link>
          <router-link to="/pets" class="nav-link">
            <span class="link-icon">🐾</span>
            <span class="link-text">我的萌宠</span>
            <div class="link-bubble"></div>
          </router-link>
          <router-link to="/pet/create" class="nav-link">
            <span class="link-icon">✨</span>
            <span class="link-text">创建萌宠</span>
            <div class="link-bubble"></div>
          </router-link>
          <router-link to="/shop" class="nav-link">
            <span class="link-icon">🛒</span>
            <span class="link-text">商店</span>
            <div class="link-bubble"></div>
          </router-link>
          <router-link to="/token-management" class="nav-link">
            <span class="link-icon">💎</span>
            <span class="link-text">代币</span>
            <div class="link-bubble"></div>
          </router-link>
          <router-link to="/about" class="nav-link">
            <span class="link-icon">ℹ️</span>
            <span class="link-text">关于</span>
            <div class="link-bubble"></div>
          </router-link>
        </div>

        <!-- 用户信息区域 -->
        <div class="nav-user">
          <div class="user-avatar">
            <span class="avatar-emoji">😊</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 萌系底部导航 (移动端) -->
    <nav class="kawaii-bottom-nav">
      <router-link to="/" class="bottom-nav-item">
        <div class="nav-item-content">
          <span class="nav-icon">🏠</span>
          <span class="nav-text">首页</span>
          <div class="nav-ripple"></div>
        </div>
      </router-link>
      <router-link to="/pets" class="bottom-nav-item">
        <div class="nav-item-content">
          <span class="nav-icon">🐾</span>
          <span class="nav-text">萌宠</span>
          <div class="nav-ripple"></div>
        </div>
      </router-link>
      <router-link to="/pet/create" class="bottom-nav-item create-btn">
        <div class="nav-item-content">
          <span class="nav-icon">✨</span>
          <span class="nav-text">创建</span>
          <div class="nav-ripple"></div>
        </div>
      </router-link>
      <router-link to="/shop" class="bottom-nav-item">
        <div class="nav-item-content">
          <span class="nav-icon">🛒</span>
          <span class="nav-text">商店</span>
          <div class="nav-ripple"></div>
        </div>
      </router-link>
      <router-link to="/token-management" class="bottom-nav-item">
        <div class="nav-item-content">
          <span class="nav-icon">💎</span>
          <span class="nav-text">代币</span>
          <div class="nav-ripple"></div>
        </div>
      </router-link>
    </nav>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'AppLayoutSimple',
  setup() {
    return {}
  }
})
</script>

<style scoped>
.app-layout-simple {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* 萌系顶部导航样式 */
.kawaii-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* 品牌区域 */
.nav-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: 1.25rem;
  transition: all 0.3s ease;
}

.nav-brand:hover {
  transform: scale(1.05);
}

.brand-icon-wrapper {
  position: relative;
  margin-right: 0.75rem;
}

.brand-icon {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

.icon-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 0.8rem;
  animation: sparkle 3s infinite;
}

.sparkle-1 {
  top: -5px;
  right: -5px;
  animation-delay: 0s;
}

.sparkle-2 {
  bottom: -5px;
  left: -5px;
  animation-delay: 1s;
}

.sparkle-3 {
  top: 50%;
  right: -10px;
  animation-delay: 2s;
}

.brand-text {
  background: linear-gradient(45deg, #fff, #ffeaa7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航链接 */
.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.nav-link.router-link-active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.link-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.nav-link:hover .link-icon {
  transform: scale(1.2) rotate(10deg);
}

.link-text {
  font-size: 0.9rem;
}

.link-bubble {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.nav-link:hover .link-bubble {
  width: 100%;
  height: 100%;
}

/* 用户区域 */
.nav-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 234, 167, 0.3);
}

.user-avatar:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px rgba(255, 234, 167, 0.5);
}

.avatar-emoji {
  font-size: 1.2rem;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  min-height: calc(100vh - 70px);
  position: relative;
}

/* 萌系底部导航样式 */
.kawaii-bottom-nav {
  display: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-top: 3px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  box-shadow: 0 -4px 20px rgba(102, 126, 234, 0.3);
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
  flex: 1;
  position: relative;
}

.nav-item-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.bottom-nav-item:hover,
.bottom-nav-item.router-link-active {
  color: white;
  transform: translateY(-3px);
}

.bottom-nav-item.router-link-active .nav-icon {
  animation: bounce 1s ease-in-out;
}

.bottom-nav-item.create-btn {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  border-radius: 50%;
  margin: -10px 5px 0;
  width: 60px;
  height: 60px;
  box-shadow: 0 4px 15px rgba(255, 234, 167, 0.4);
}

.bottom-nav-item.create-btn:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 234, 167, 0.6);
}

.bottom-nav-item.create-btn .nav-icon {
  font-size: 1.5rem;
}

.bottom-nav-item.create-btn .nav-text {
  color: #2d3436;
  font-weight: 600;
}

.nav-icon {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
  transition: all 0.3s ease;
}

.nav-text {
  font-weight: 500;
  font-size: 0.7rem;
}

.nav-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: 1;
}

.bottom-nav-item:active .nav-ripple {
  width: 50px;
  height: 50px;
}

/* 动画关键帧 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 4px 25px rgba(102, 126, 234, 0.5);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .kawaii-bottom-nav {
    display: flex;
  }

  .main-content {
    min-height: calc(100vh - 70px - 80px);
    padding-bottom: 80px;
  }

  .nav-container {
    height: 60px;
  }

  .brand-icon {
    font-size: 1.8rem;
  }

  .brand-text {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 0.5rem;
    height: 55px;
  }

  .brand-text {
    display: none;
  }

  .brand-icon {
    font-size: 1.5rem;
  }

  .user-avatar {
    width: 35px;
    height: 35px;
  }

  .avatar-emoji {
    font-size: 1rem;
  }

  .bottom-nav-item.create-btn {
    width: 50px;
    height: 50px;
    margin: -8px 3px 0;
  }

  .bottom-nav-item.create-btn .nav-icon {
    font-size: 1.3rem;
  }

  .nav-text {
    font-size: 0.65rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .app-layout-simple {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
