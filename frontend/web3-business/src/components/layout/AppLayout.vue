<template>
  <div class="app-layout" :class="themeClass">
    <!-- 响应式导航 -->
    <ResponsiveNavigation />

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 页面加载指示器 -->
      <div v-if="isLoading" class="page-loading">
        <LoadingSpinner
          type="pulse"
          size="large"
          :text="loadingText"
          color="primary"
        />
      </div>

      <!-- 页面内容 -->
      <div v-else class="page-content">
        <slot />
      </div>
    </main>

    <!-- 全局通知 -->
    <Teleport to="body">
      <div v-if="showNotification" class="global-notification">
        <div class="notification-content" :class="notificationClass">
          <span class="notification-icon">{{ notificationIcon }}</span>
          <span class="notification-message">{{ notificationMessage }}</span>
          <button
            class="notification-close"
            @click="hideNotification"
            aria-label="关闭通知"
          >
            ×
          </button>
        </div>
      </div>
    </Teleport>

    <!-- 返回顶部按钮 -->
    <Transition name="fade">
      <button
        v-if="showBackToTop"
        class="back-to-top"
        @click="scrollToTop"
        aria-label="返回顶部"
      >
        ↑
      </button>
    </Transition>

    <!-- 全局模态框容器 -->
    <Teleport to="body">
      <div v-if="showModal" class="modal-overlay" @click="closeModal">
        <div class="modal-content" @click.stop>
          <slot name="modal" />
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, provide } from 'vue'
import { useGlobalTheme } from '../../composables/useTheme'
import ResponsiveNavigation from './ResponsiveNavigation.vue'
import LoadingSpinner from '../common/LoadingSpinner.vue'

interface Props {
  loading?: boolean
  loadingText?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  loadingText: '加载中...'
})

const theme = useGlobalTheme()
const isLoading = ref(props.loading)
const scrollY = ref(0)

// 通知系统
const showNotification = ref(false)
const notificationMessage = ref('')
const notificationType = ref<'success' | 'error' | 'warning' | 'info'>('info')

// 模态框系统
const showModal = ref(false)

// 计算属性
const themeClass = computed(() => theme.themeClass.value)

const showBackToTop = computed(() => scrollY.value > 300)

const notificationClass = computed(() => `notification-${notificationType.value}`)

const notificationIcon = computed(() => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  }
  return icons[notificationType.value]
})

// 方法
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const showNotificationMessage = (
  message: string,
  type: 'success' | 'error' | 'warning' | 'info' = 'info',
  duration = 3000
) => {
  notificationMessage.value = message
  notificationType.value = type
  showNotification.value = true

  if (duration > 0) {
    setTimeout(() => {
      hideNotification()
    }, duration)
  }
}

const hideNotification = () => {
  showNotification.value = false
}

const openModal = () => {
  showModal.value = true
  document.body.style.overflow = 'hidden'
}

const closeModal = () => {
  showModal.value = false
  document.body.style.overflow = ''
}

const setLoading = (loading: boolean, text?: string) => {
  isLoading.value = loading
  if (text) {
    props.loadingText = text
  }
}

// 滚动监听
const handleScroll = () => {
  scrollY.value = window.scrollY
}

// 键盘事件监听
const handleKeydown = (event: KeyboardEvent) => {
  // ESC 键关闭模态框
  if (event.key === 'Escape' && showModal.value) {
    closeModal()
  }

  // ESC 键关闭通知
  if (event.key === 'Escape' && showNotification.value) {
    hideNotification()
  }
}

// 提供全局方法给子组件
provide('layout', {
  showNotification: showNotificationMessage,
  hideNotification,
  openModal,
  closeModal,
  setLoading
})

// 生命周期
onMounted(() => {
  // 初始化主题
  theme.initTheme()

  // 添加事件监听
  window.addEventListener('scroll', handleScroll, { passive: true })
  document.addEventListener('keydown', handleKeydown)

  // 设置初始滚动位置
  handleScroll()
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener('keydown', handleKeydown)

  // 恢复 body 滚动
  document.body.style.overflow = ''
})

// 定义组件选项
defineOptions({
  name: 'AppLayout'
})

// 暴露方法给父组件
defineExpose({
  showNotification: showNotificationMessage,
  hideNotification,
  openModal,
  closeModal,
  setLoading
})
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-gradient, linear-gradient(135deg, #faf5ff 0%, #f0f9ff 100%));
  transition: background 0.3s ease;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding-top: 4rem; /* 为移动端导航栏留出空间 */
}

.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 2rem;
}

.page-content {
  min-height: calc(100vh - 4rem);
}

/* 全局通知 */
.global-notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 100;
  animation: slideInRight 0.3s ease;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-width: 400px;
  font-weight: 500;
}

.notification-success {
  background-color: rgba(34, 197, 94, 0.9);
  color: white;
}

.notification-error {
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
}

.notification-warning {
  background-color: rgba(245, 158, 11, 0.9);
  color: white;
}

.notification-info {
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
}

.notification-icon {
  font-size: 1.25rem;
}

.notification-message {
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.notification-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #a855f7, #0ea5e9);
  color: white;
  border: none;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(168, 85, 247, 0.3);
  transition: all 0.3s ease;
  z-index: 90;
}

.back-to-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(168, 85, 247, 0.4);
}

.back-to-top:active {
  transform: translateY(0);
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 200;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: slideInUp 0.3s ease;
}

/* 动画 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 桌面端适配 */
@media (min-width: 1024px) {
  .main-content {
    padding-top: 0;
  }

  .page-content {
    min-height: 100vh;
  }

  .global-notification {
    top: 2rem;
    right: 2rem;
  }
}

/* 深色主题适配 */
:global(.theme-dark) .app-layout {
  background: var(--bg-gradient-dark, linear-gradient(135deg, #1f2937 0%, #111827 100%));
}

:global(.theme-dark) .modal-content {
  background-color: #1f2937;
  color: #f9fafb;
}

/* 无动画模式 */
:global(.no-animations) * {
  animation-duration: 0s !important;
  transition-duration: 0s !important;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .notification-content {
    border: 2px solid currentColor;
  }

  .back-to-top {
    border: 2px solid white;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .back-to-top {
    transition: none;
  }

  .notification-content,
  .modal-overlay,
  .modal-content {
    animation: none;
  }
}

/* 响应式调整 */
@media (max-width: 640px) {
  .global-notification {
    top: 5rem;
    left: 1rem;
    right: 1rem;
  }

  .notification-content {
    max-width: none;
  }

  .back-to-top {
    bottom: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .modal-content {
    margin: 0;
    border-radius: 0.5rem;
    max-width: 95vw;
    max-height: 85vh;
  }
}
</style>
