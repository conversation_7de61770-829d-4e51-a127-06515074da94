<template>
  <div class="app-layout">
    <!-- 顶部导航 -->
    <DesktopNavigation />

    <!-- 主内容区域 -->
    <main class="main-content">
      <slot />
    </main>

    <!-- 移动端底部导航 -->
    <PetNavigation class="md:hidden" />

    <!-- 背景装饰效果 -->
    <div class="bg-decorations">
      <div class="decoration decoration-1"></div>
      <div class="decoration decoration-2"></div>
      <div class="decoration decoration-3"></div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'AppLayout'
}
</script>

<script setup lang="ts">
import DesktopNavigation from './DesktopNavigation.vue'
import PetNavigation from '../pet/PetNavigation.vue'
</script>

<style scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fbfbfd;
  position: relative;
  overflow-x: hidden;
}

.main-content {
  flex: 1;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem;
  padding-bottom: 80px; /* 为移动端底部导航留出空间 */
  z-index: 1;
  position: relative;
}

/* 背景装饰 */
.bg-decorations {
  position: fixed;
  inset: 0;
  z-index: 0;
  overflow: hidden;
  pointer-events: none;
}

.decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.2;
}

.decoration-1 {
  top: -10%;
  right: -10%;
  width: 40vw;
  height: 40vw;
  background: linear-gradient(135deg, #a78bfa, #3b82f6);
  animation: float-slow 30s infinite alternate ease-in-out;
}

.decoration-2 {
  bottom: -15%;
  left: -15%;
  width: 50vw;
  height: 50vw;
  background: linear-gradient(135deg, #f472b6, #8b5cf6);
  animation: float-slow 25s infinite alternate-reverse ease-in-out;
}

.decoration-3 {
  top: 40%;
  right: 20%;
  width: 30vw;
  height: 30vw;
  background: linear-gradient(135deg, #60a5fa, #34d399);
  animation: float-slow 20s infinite alternate ease-in-out;
}

@keyframes float-slow {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(5%, 5%);
  }
}

/* 桌面端样式 */
@media (min-width: 768px) {
  .main-content {
    padding-bottom: 1.5rem; /* 桌面端不需要底部导航空间 */
  }
}
</style>
