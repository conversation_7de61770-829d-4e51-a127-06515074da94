<template>
  <div class="help-button-container">
    <van-button
      round
      type="primary"
      size="small"
      icon="question-o"
      @click="showHelp"
      class="floating-help-button"
    >
      帮助
    </van-button>

    <van-popup
      v-model:show="showHelpPopup"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
    >
      <div class="help-popup-content">
        <h3 class="help-title">游戏帮助</h3>

        <van-tabs v-model:active="activeTab">
          <van-tab title="快速指南">
            <div class="tab-content">
              <QuickGuide />
            </div>
          </van-tab>

          <van-tab title="常见问题">
            <div class="tab-content">
              <FAQ />
            </div>
          </van-tab>

          <van-tab title="游戏规则">
            <div class="tab-content">
              <GameRules />
            </div>
          </van-tab>
        </van-tabs>

        <div class="help-actions">
          <van-button
            type="primary"
            block
            @click="goToTutorial"
          >
            查看完整教程
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import QuickGuide from './help/QuickGuide.vue'
import FAQ from './help/FAQ.vue'
import GameRules from './help/GameRules.vue'

const router = useRouter()
const showHelpPopup = ref(false)
const activeTab = ref(0)

const showHelp = () => {
  showHelpPopup.value = true
}

const goToTutorial = () => {
  showHelpPopup.value = false
  router.push('/tutorial')
}
</script>

<style scoped>
.help-button-container {
  @apply fixed bottom-6 right-6 z-40;
}

.floating-help-button {
  @apply shadow-lg;
}

.help-popup-content {
  @apply flex flex-col h-full;
}

.help-title {
  @apply text-xl font-semibold text-center py-4 border-b border-gray-200;
}

.tab-content {
  @apply p-4 overflow-y-auto;
  height: calc(100% - 100px);
}

.help-actions {
  @apply p-4 border-t border-gray-200 mt-auto;
}
</style>