<template>
  <div v-if="shouldShow" class="onboarding-guide">
    <!-- 遮罩层 -->
    <div class="overlay" @click="handleOverlayClick"></div>

    <!-- 高亮区域 -->
    <div
      v-if="currentStep.target"
      class="highlight"
      :style="highlightStyle"
    ></div>

    <!-- 提示卡片 -->
    <div class="guide-card" :style="cardStyle">
      <div class="card-header">
        <h4 class="card-title">{{ currentStep.title }}</h4>
        <van-icon
          name="cross"
          class="close-icon"
          @click="closeGuide"
        />
      </div>

      <div class="card-content">
        <p class="card-description">{{ currentStep.description }}</p>

        <!-- 操作提示 -->
        <div v-if="currentStep.action" class="action-hint">
          <van-icon name="info-o" class="hint-icon" />
          <span class="hint-text">{{ currentStep.action }}</span>
        </div>
      </div>

      <div class="card-footer">
        <div class="step-indicator">
          {{ currentStepIndex + 1 }} / {{ steps.length }}
        </div>

        <div class="card-actions">
          <van-button
            v-if="currentStepIndex > 0"
            size="small"
            plain
            @click="previousStep"
          >
            上一步
          </van-button>

          <van-button
            v-if="!isLastStep"
            size="small"
            type="primary"
            @click="nextStep"
          >
            下一步
          </van-button>

          <van-button
            v-if="isLastStep"
            size="small"
            type="primary"
            @click="completeGuide"
          >
            完成
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

export interface GuideStep {
  id: string
  title: string
  description: string
  target?: string // CSS选择器
  action?: string // 操作提示
  position?: 'top' | 'bottom' | 'left' | 'right'
}

// Make sure the component is properly exported
defineExports({ GuideStep })

interface Props {
  steps: GuideStep[]
  show: boolean
  allowSkip?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowSkip: true
})

const emit = defineEmits<{
  complete: []
  skip: []
  stepChange: [step: number]
}>()

// 状态
const currentStepIndex = ref(0)
const highlightStyle = ref({})
const cardStyle = ref({})

// 计算属性
const shouldShow = computed(() => props.show && props.steps.length > 0)
const currentStep = computed(() => props.steps[currentStepIndex.value])
const isLastStep = computed(() => currentStepIndex.value === props.steps.length - 1)

// 方法
const updateHighlight = async () => {
  await nextTick()

  if (!currentStep.value.target) {
    highlightStyle.value = {}
    cardStyle.value = { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
    return
  }

  const targetElement = document.querySelector(currentStep.value.target)
  if (!targetElement) {
    console.warn(`Target element not found: ${currentStep.value.target}`)
    return
  }

  const rect = targetElement.getBoundingClientRect()
  const padding = 8

  // 设置高亮样式
  highlightStyle.value = {
    top: `${rect.top - padding}px`,
    left: `${rect.left - padding}px`,
    width: `${rect.width + padding * 2}px`,
    height: `${rect.height + padding * 2}px`
  }

  // 计算卡片位置
  const cardPosition = calculateCardPosition(rect)
  cardStyle.value = cardPosition
}

const calculateCardPosition = (targetRect: DOMRect) => {
  const cardWidth = 280
  const cardHeight = 200
  const margin = 16
  const position = currentStep.value.position || 'bottom'

  let top = 0
  let left = 0

  switch (position) {
    case 'top':
      top = targetRect.top - cardHeight - margin
      left = targetRect.left + (targetRect.width - cardWidth) / 2
      break
    case 'bottom':
      top = targetRect.bottom + margin
      left = targetRect.left + (targetRect.width - cardWidth) / 2
      break
    case 'left':
      top = targetRect.top + (targetRect.height - cardHeight) / 2
      left = targetRect.left - cardWidth - margin
      break
    case 'right':
      top = targetRect.top + (targetRect.height - cardHeight) / 2
      left = targetRect.right + margin
      break
  }

  // 确保卡片在视窗内
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  if (left < margin) left = margin
  if (left + cardWidth > viewportWidth - margin) left = viewportWidth - cardWidth - margin
  if (top < margin) top = margin
  if (top + cardHeight > viewportHeight - margin) top = viewportHeight - cardHeight - margin

  return {
    top: `${top}px`,
    left: `${left}px`,
    width: `${cardWidth}px`
  }
}

const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    currentStepIndex.value++
    emit('stepChange', currentStepIndex.value)
    updateHighlight()
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    emit('stepChange', currentStepIndex.value)
    updateHighlight()
  }
}

const completeGuide = () => {
  emit('complete')
}

const closeGuide = () => {
  if (props.allowSkip) {
    emit('skip')
  }
}

const handleOverlayClick = () => {
  if (props.allowSkip) {
    closeGuide()
  }
}

// 监听窗口大小变化
const handleResize = () => {
  updateHighlight()
}

// 生命周期
onMounted(() => {
  if (shouldShow.value) {
    updateHighlight()
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听步骤变化
watch(() => props.show, (newShow) => {
  if (newShow) {
    currentStepIndex.value = 0
    updateHighlight()
  }
})

watch(() => currentStepIndex.value, () => {
  updateHighlight()
})
</script>

<style scoped>
.onboarding-guide {
  @apply fixed inset-0 z-50;
}

.overlay {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.highlight {
  @apply absolute border-2 border-blue-500 rounded-lg bg-white bg-opacity-10 pointer-events-none;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}

.guide-card {
  @apply absolute bg-white rounded-lg shadow-lg p-4 max-w-xs;
  transition: all 0.3s ease;
}

.card-header {
  @apply flex items-center justify-between mb-3;
}

.card-title {
  @apply text-lg font-semibold text-gray-800;
}

.close-icon {
  @apply text-gray-400 cursor-pointer hover:text-gray-600;
}

.card-content {
  @apply mb-4;
}

.card-description {
  @apply text-gray-600 text-sm leading-relaxed mb-3;
}

.action-hint {
  @apply flex items-start space-x-2 bg-blue-50 rounded-lg p-2;
}

.hint-icon {
  @apply text-blue-500 flex-shrink-0 mt-0.5;
}

.hint-text {
  @apply text-blue-700 text-xs;
}

.card-footer {
  @apply flex items-center justify-between;
}

.step-indicator {
  @apply text-xs text-gray-500;
}

.card-actions {
  @apply flex space-x-2;
}
</style>
