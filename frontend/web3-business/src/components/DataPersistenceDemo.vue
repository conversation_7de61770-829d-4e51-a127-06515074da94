<template>
  <div class="data-persistence-demo p-4">
    <h2 class="text-xl font-bold mb-4">数据持久化演示</h2>

    <!-- 状态显示 -->
    <div class="status-section mb-6">
      <h3 class="text-lg font-semibold mb-2">持久化状态</h3>
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-gray-100 p-3 rounded">
          <div class="text-sm text-gray-600">初始化状态</div>
          <div class="font-medium" :class="status.isInitialized ? 'text-green-600' : 'text-red-600'">
            {{ status.isInitialized ? '已初始化' : '未初始化' }}
          </div>
        </div>
        <div class="bg-gray-100 p-3 rounded">
          <div class="text-sm text-gray-600">最后保存时间</div>
          <div class="font-medium">
            {{ status.lastSaved ? new Date(status.lastSaved).toLocaleString() : '从未保存' }}
          </div>
        </div>
        <div class="bg-gray-100 p-3 rounded">
          <div class="text-sm text-gray-600">最后备份时间</div>
          <div class="font-medium">
            {{ status.lastBackup ? new Date(status.lastBackup).toLocaleString() : '从未备份' }}
          </div>
        </div>
        <div class="bg-gray-100 p-3 rounded">
          <div class="text-sm text-gray-600">存储统计</div>
          <div class="font-medium">
            {{ storageStats.itemCount }} 项 / {{ Math.round(storageStats.totalSize / 1024) }}KB
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions-section mb-6">
      <h3 class="text-lg font-semibold mb-2">操作</h3>
      <div class="flex flex-wrap gap-2">
        <button
          @click="initializePersistence"
          :disabled="status.isInitialized || status.isLoading"
          class="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
        >
          {{ status.isLoading ? '初始化中...' : '初始化持久化' }}
        </button>

        <button
          @click="saveAllData"
          :disabled="!status.isInitialized || status.isSaving"
          class="px-4 py-2 bg-green-500 text-white rounded disabled:bg-gray-300"
        >
          {{ status.isSaving ? '保存中...' : '手动保存' }}
        </button>

        <button
          @click="loadAllData"
          :disabled="!status.isInitialized || status.isLoading"
          class="px-4 py-2 bg-yellow-500 text-white rounded disabled:bg-gray-300"
        >
          {{ status.isLoading ? '加载中...' : '重新加载' }}
        </button>

        <button
          @click="exportBackup"
          :disabled="!status.isInitialized"
          class="px-4 py-2 bg-purple-500 text-white rounded disabled:bg-gray-300"
        >
          导出备份
        </button>

        <button
          @click="triggerFileImport"
          :disabled="!status.isInitialized"
          class="px-4 py-2 bg-indigo-500 text-white rounded disabled:bg-gray-300"
        >
          导入备份
        </button>

        <button
          @click="clearAllData"
          :disabled="!status.isInitialized"
          class="px-4 py-2 bg-red-500 text-white rounded disabled:bg-gray-300"
        >
          清除所有数据
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      @change="handleFileImport"
      class="hidden"
    />

    <!-- 错误显示 -->
    <div v-if="status.error" class="error-section mb-4">
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {{ status.error }}
      </div>
    </div>

    <!-- 操作结果显示 -->
    <div v-if="lastResult" class="result-section mb-4">
      <div
        class="px-4 py-3 rounded"
        :class="lastResult.success ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'"
      >
        {{ lastResult.message }}
        <div v-if="lastResult.details" class="text-sm mt-1">
          {{ lastResult.details }}
        </div>
      </div>
    </div>

    <!-- 数据完整性检查 -->
    <div class="integrity-section">
      <h3 class="text-lg font-semibold mb-2">数据完整性</h3>
      <button
        @click="checkDataIntegrity"
        class="px-4 py-2 bg-gray-500 text-white rounded"
      >
        检查数据完整性
      </button>
      <div v-if="integrityResult !== null" class="mt-2">
        <span
          :class="integrityResult ? 'text-green-600' : 'text-red-600'"
          class="font-medium"
        >
          {{ integrityResult ? '数据完整' : '数据不完整或损坏' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { usePersistence } from '../composables/usePersistence'
import { showToast } from 'vant'

// 使用持久化组合式函数
const persistence = usePersistence()
const { status } = persistence

// 本地状态
const lastResult = ref<{ success: boolean; message: string; details?: string } | null>(null)
const integrityResult = ref<boolean | null>(null)
const fileInput = ref<HTMLInputElement>()

// 计算属性
const storageStats = computed(() => {
  try {
    return persistence.getStorageStats()
  } catch {
    return { itemCount: 0, totalSize: 0, lastModified: 0, version: '0.0.0' }
  }
})

// 方法
const initializePersistence = async () => {
  try {
    const success = await persistence.initialize()
    lastResult.value = {
      success,
      message: success ? '持久化系统初始化成功' : '持久化系统初始化失败'
    }

    if (success) {
      showToast({ message: '持久化系统已启动', type: 'success' })
    } else {
      showToast({ message: '初始化失败', type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '初始化失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  }
}

const saveAllData = async () => {
  try {
    const success = await persistence.saveAll()
    lastResult.value = {
      success,
      message: success ? '数据保存成功' : '数据保存失败'
    }

    if (success) {
      showToast({ message: '数据已保存', type: 'success' })
    } else {
      showToast({ message: '保存失败', type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '保存失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  }
}

const loadAllData = async () => {
  try {
    const success = await persistence.loadAll()
    lastResult.value = {
      success,
      message: success ? '数据加载成功' : '数据加载失败'
    }

    if (success) {
      showToast({ message: '数据已加载', type: 'success' })
    } else {
      showToast({ message: '加载失败', type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '加载失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  }
}

const exportBackup = async () => {
  try {
    const result = await persistence.exportBackup()
    lastResult.value = {
      success: result.success,
      message: result.message
    }

    if (result.success) {
      showToast({ message: '备份导出成功', type: 'success' })
    } else {
      showToast({ message: result.message, type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '导出失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  }
}

const triggerFileImport = () => {
  fileInput.value?.click()
}

const handleFileImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  try {
    const result = await persistence.importBackup(file)
    lastResult.value = {
      success: result.success,
      message: result.message,
      details: result.success
        ? `恢复项目: ${result.restoredItems.join(', ')}`
        : `跳过项目: ${result.skippedItems.join(', ')}`
    }

    if (result.success) {
      showToast({ message: '备份导入成功', type: 'success' })
    } else {
      showToast({ message: result.message, type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '导入失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  } finally {
    // 清除文件输入
    if (target) target.value = ''
  }
}

const clearAllData = async () => {
  if (!confirm('确定要清除所有数据吗？此操作不可恢复！')) {
    return
  }

  try {
    const success = await persistence.clearAllData()
    lastResult.value = {
      success,
      message: success ? '所有数据已清除' : '清除数据失败'
    }

    if (success) {
      showToast({ message: '数据已清除', type: 'success' })
    } else {
      showToast({ message: '清除失败', type: 'fail' })
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : '清除失败'
    lastResult.value = { success: false, message }
    showToast({ message, type: 'fail' })
  }
}

const checkDataIntegrity = async () => {
  try {
    integrityResult.value = await persistence.checkDataIntegrity()

    const message = integrityResult.value ? '数据完整性检查通过' : '发现数据问题'
    showToast({
      message,
      type: integrityResult.value ? 'success' : 'fail'
    })
  } catch (error) {
    integrityResult.value = false
    const message = error instanceof Error ? error.message : '检查失败'
    showToast({ message, type: 'fail' })
  }
}

// 生命周期
onMounted(() => {
  // 组件挂载时不自动初始化，让用户手动操作
})
</script>

<style scoped>
.data-persistence-demo {
  max-width: 800px;
  margin: 0 auto;
}
</style>
