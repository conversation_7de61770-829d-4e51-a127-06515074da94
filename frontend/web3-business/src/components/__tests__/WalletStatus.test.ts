import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import WalletStatus from '@/components/WalletStatus.vue'
import { mountComponent } from '@/test/utils'
import { useWalletStore } from '@/stores/wallet'

describe('WalletStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows disconnected state when wallet not connected', () => {
    const wrapper = mountComponent(WalletStatus)

    expect(wrapper.find('[data-testid="wallet-disconnected"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="connect-button"]').exists()).toBe(true)
  })

  it('shows connected state with wallet info', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    // Mock connected state
    walletStore.isConnected = true
    walletStore.address = '******************************************'
    walletStore.balance = '1.5'

    await nextTick()

    expect(wrapper.find('[data-testid="wallet-connected"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="wallet-address"]').text()).toContain('0x1234...7890')
    expect(wrapper.find('[data-testid="wallet-balance"]').text()).toContain('1.5')
  })

  it('truncates long wallet addresses', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.address = '******************************************'

    await nextTick()

    const addressElement = wrapper.find('[data-testid="wallet-address"]')
    expect(addressElement.text()).toBe('0x1234...7890')
  })

  it('shows network information', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.chainId = 11155111 // Sepolia testnet

    await nextTick()

    expect(wrapper.find('[data-testid="network-info"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="network-name"]').text()).toContain('Sepolia')
  })

  it('shows warning for wrong network', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.chainId = 1 // Mainnet (wrong network)

    await nextTick()

    expect(wrapper.find('[data-testid="network-warning"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="switch-network-button"]').exists()).toBe(true)
  })

  it('handles disconnect action', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    const disconnectSpy = vi.spyOn(walletStore, 'disconnect')

    await nextTick()

    const disconnectButton = wrapper.find('[data-testid="disconnect-button"]')
    await disconnectButton.trigger('click')

    expect(disconnectSpy).toHaveBeenCalled()
  })

  it('handles network switch action', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.chainId = 1 // Wrong network
    const switchNetworkSpy = vi.spyOn(walletStore, 'switchNetwork')

    await nextTick()

    const switchButton = wrapper.find('[data-testid="switch-network-button"]')
    await switchButton.trigger('click')

    expect(switchNetworkSpy).toHaveBeenCalledWith(11155111) // Sepolia
  })

  it('refreshes balance when refresh button clicked', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    const refreshSpy = vi.spyOn(walletStore, 'refreshBalance')

    await nextTick()

    const refreshButton = wrapper.find('[data-testid="refresh-balance-button"]')
    await refreshButton.trigger('click')

    expect(refreshSpy).toHaveBeenCalled()
  })

  it('shows loading state during balance refresh', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.isLoading = true

    await nextTick()

    expect(wrapper.find('[data-testid="balance-loading"]').exists()).toBe(true)
  })

  it('copies address to clipboard when clicked', async () => {
    const wrapper = mountComponent(WalletStatus)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.address = '******************************************'

    // Mock clipboard API
    const writeTextSpy = vi.fn()
    Object.assign(navigator, {
      clipboard: { writeText: writeTextSpy }
    })

    await nextTick()

    const addressElement = wrapper.find('[data-testid="wallet-address"]')
    await addressElement.trigger('click')

    expect(writeTextSpy).toHaveBeenCalledWith('******************************************')
  })
})