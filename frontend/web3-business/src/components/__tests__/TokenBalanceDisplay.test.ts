import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import TokenBalanceDisplay from '@/components/TokenBalanceDisplay.vue'
import { mountComponent } from '@/test/utils'
import { useWalletStore } from '@/stores/wallet'

describe('TokenBalanceDisplay', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('displays token balance correctly', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.tokenBalance = '123.456'

    await nextTick()

    expect(wrapper.find('[data-testid="token-balance"]').text()).toContain('123.456')
    expect(wrapper.find('[data-testid="token-symbol"]').text()).toContain('MTK')
  })

  it('shows loading state when balance is being fetched', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.isLoading = true

    await nextTick()

    expect(wrapper.find('[data-testid="balance-loading"]').exists()).toBe(true)
  })

  it('shows zero balance when not connected', () => {
    const wrapper = mountComponent(TokenBalanceDisplay)

    expect(wrapper.find('[data-testid="token-balance"]').text()).toContain('0.00')
  })

  it('formats large numbers correctly', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.tokenBalance = '1234567.89'

    await nextTick()

    expect(wrapper.find('[data-testid="token-balance"]').text()).toContain('1,234,567.89')
  })

  it('handles refresh balance action', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    const refreshSpy = vi.spyOn(walletStore, 'refreshTokenBalance')

    await nextTick()

    const refreshButton = wrapper.find('[data-testid="refresh-balance"]')
    await refreshButton.trigger('click')

    expect(refreshSpy).toHaveBeenCalled()
  })

  it('shows error state when balance fetch fails', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.error = 'Failed to fetch balance'

    await nextTick()

    expect(wrapper.find('[data-testid="balance-error"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="balance-error"]').text()).toContain('Failed to fetch balance')
  })

  it('updates display when balance changes', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.tokenBalance = '100.00'

    await nextTick()
    expect(wrapper.find('[data-testid="token-balance"]').text()).toContain('100.00')

    walletStore.tokenBalance = '200.00'
    await nextTick()
    expect(wrapper.find('[data-testid="token-balance"]').text()).toContain('200.00')
  })

  it('shows USD value when available', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay, {
      props: { showUsdValue: true }
    })
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.tokenBalance = '100.00'
    walletStore.tokenPriceUsd = '2.50'

    await nextTick()

    expect(wrapper.find('[data-testid="usd-value"]').text()).toContain('$250.00')
  })

  it('is accessible with proper ARIA attributes', async () => {
    const wrapper = mountComponent(TokenBalanceDisplay)
    const walletStore = useWalletStore()

    walletStore.isConnected = true
    walletStore.tokenBalance = '123.456'

    await nextTick()

    const balanceElement = wrapper.find('[data-testid="token-balance"]')
    expect(balanceElement.attributes('aria-label')).toContain('Token balance')

    const refreshButton = wrapper.find('[data-testid="refresh-balance"]')
    expect(refreshButton.attributes('aria-label')).toContain('Refresh balance')
  })
})
