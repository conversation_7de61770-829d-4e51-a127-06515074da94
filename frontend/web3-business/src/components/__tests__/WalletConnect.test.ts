import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import WalletConnect from '@/components/WalletConnect.vue'
import { mountComponent, createMockProvider } from '@/test/utils'
import { useWalletStore } from '@/stores/wallet'

describe('WalletConnect', () => {
  let mockProvider: any

  beforeEach(() => {
    mockProvider = createMockProvider()
    vi.clearAllMocks()
  })

  it('renders wallet connection options', () => {
    const wrapper = mountComponent(WalletConnect)

    expect(wrapper.find('[data-testid="wallet-connect-modal"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="metamask-option"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="walletconnect-option"]').exists()).toBe(true)
  })

  it('shows connection modal when not connected', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    await nextTick()
    expect(wrapper.find('[data-testid="wallet-connect-modal"]').isVisible()).toBe(true)
  })

  it('attempts MetaMask connection when clicked', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')

    // Should attempt to connect to MetaMask
    expect(wrapper.emitted('connect-wallet')).toBeTruthy()
  })

  it('handles connection errors gracefully', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    // Mock connection error
    const walletStore = useWalletStore()
    vi.spyOn(walletStore, 'connectWallet').mockRejectedValue(new Error('Connection failed'))

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')
    await nextTick()

    expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true)
  })

  it('displays loading state during connection', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    const walletStore = useWalletStore()
    vi.spyOn(walletStore, 'connectWallet').mockImplementation(() =>
      new Promise(resolve => setTimeout(resolve, 100))
    )

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')

    expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
  })

  it('closes modal after successful connection', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    const walletStore = useWalletStore()
    vi.spyOn(walletStore, 'connectWallet').mockResolvedValue(undefined)

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('click')
    await nextTick()

    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('supports keyboard navigation', async () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    const metamaskButton = wrapper.find('[data-testid="metamask-option"]')
    await metamaskButton.trigger('keydown.enter')

    expect(wrapper.emitted('connect-wallet')).toBeTruthy()
  })

  it('is accessible with proper ARIA attributes', () => {
    const wrapper = mountComponent(WalletConnect, {
      props: { showModal: true }
    })

    const modal = wrapper.find('[data-testid="wallet-connect-modal"]')
    expect(modal.attributes('role')).toBe('dialog')
    expect(modal.attributes('aria-labelledby')).toBeDefined()

    const buttons = wrapper.findAll('button')
    buttons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
    })
  })
})
