<template>
  <div class="error-handling-demo">
    <van-cell-group title="错误处理和用户反馈演示">
      <!-- 错误类型演示 -->
      <van-cell title="网络错误" @click="simulateNetworkError" />
      <van-cell title="合约错误" @click="simulateContractError" />
      <van-cell title="钱包错误" @click="simulateWalletError" />
      <van-cell title="交易错误" @click="simulateTransactionError" />
      <van-cell title="未知错误" @click="simulateUnknownError" />
    </van-cell-group>

    <van-cell-group title="消息类型演示">
      <van-cell title="成功消息" @click="showSuccessMessage" />
      <van-cell title="警告消息" @click="showWarningMessage" />
      <van-cell title="信息消息" @click="showInfoMessage" />
      <van-cell title="加载消息" @click="showLoadingMessage" />
    </van-cell-group>

    <van-cell-group title="重试机制演示">
      <van-cell title="带重试的操作" @click="executeWithRetry" />
      <van-cell title="网络重试操作" @click="executeNetworkOperation" />
    </van-cell-group>

    <van-cell-group title="交易跟踪演示">
      <van-cell title="模拟交易" @click="simulateTransaction" />
      <van-cell title="查看交易状态" @click="showTransactionStatus" />
    </van-cell-group>

    <!-- 错误统计 -->
    <div class="error-stats">
      <van-cell-group title="错误统计">
        <van-cell title="网络错误" :value="errorStats.network" />
        <van-cell title="合约错误" :value="errorStats.contract" />
        <van-cell title="钱包错误" :value="errorStats.wallet" />
        <van-cell title="交易错误" :value="errorStats.transaction" />
        <van-cell title="验证错误" :value="errorStats.validation" />
        <van-cell title="未知错误" :value="errorStats.unknown" />
      </van-cell>
    </div>

    <!-- 网络状态 -->
    <div class="network-status">
      <van-cell-group title="网络状态">
        <van-cell title="当前状态" :value="networkStatusText" />
        <van-cell title="RTT" :value="`${networkInfo.rtt}ms`" />
        <van-cell title="连接类型" :value="networkInfo.effectiveType" />
        <van-cell title="最后检查" :value="formatTime(networkInfo.lastChecked)" />
      </van-cell>
    </div>

    <!-- 交易状态组件 -->
    <TransactionStatus
      :auto-refresh="true"
      :show-stats="true"
      @retry="handleTransactionRetry"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useErrorHandler } from '@/composables/useErrorHandler'
import { networkMonitor, NetworkStatus } from '@/services/network-monitor.service'
import { transactionTracker } from '@/services/transaction-tracker.service'
import TransactionStatus from './TransactionStatus.vue'

const {
  handleError,
  showSuccess,
  showWarning,
  showInfo,
  showLoading,
  executeWithRetry,
  executeNetworkOperation,
  executeTransaction,
  getErrorStats
} = useErrorHandler()

// 响应式数据
const errorStats = ref(getErrorStats())
const refreshTimer = ref<number | null>(null)

// 网络状态
const networkStatus = computed(() => networkMonitor.status.value)
const networkInfo = computed(() => networkMonitor.info.value)

const networkStatusText = computed(() => {
  const statusMap = {
    [NetworkStatus.ONLINE]: '在线',
    [NetworkStatus.OFFLINE]: '离线',
    [NetworkStatus.SLOW]: '网络较慢',
    [NetworkStatus.UNSTABLE]: '网络不稳定'
  }
  return statusMap[networkStatus.value]
})

/**
 * 模拟网络错误
 */
const simulateNetworkError = () => {
  const error = { code: 'NETWORK_ERROR', message: 'Network connection failed' }
  handleError(error, { action: 'simulate_network_error' })
  updateStats()
}

/**
 * 模拟合约错误
 */
const simulateContractError = () => {
  const error = {
    code: 'CALL_EXCEPTION',
    reason: 'TradingNotEnabled',
    message: 'execution reverted: TradingNotEnabled'
  }
  handleError(error, { action: 'simulate_contract_error' })
  updateStats()
}

/**
 * 模拟钱包错误
 */
const simulateWalletError = () => {
  const error = { code: 4001, message: 'User rejected the request' }
  handleError(error, { action: 'simulate_wallet_error' })
  updateStats()
}

/**
 * 模拟交易错误
 */
const simulateTransactionError = () => {
  const error = { code: 'INSUFFICIENT_FUNDS', message: 'insufficient funds' }
  handleError(error, { action: 'simulate_transaction_error' })
  updateStats()
}

/**
 * 模拟未知错误
 */
const simulateUnknownError = () => {
  const error = new Error('Something unexpected happened')
  handleError(error, { action: 'simulate_unknown_error' })
  updateStats()
}

/**
 * 显示成功消息
 */
const showSuccessMessage = () => {
  showSuccess('操作成功完成！')
}

/**
 * 显示警告消息
 */
const showWarningMessage = () => {
  showWarning('请注意：这是一个警告消息')
}

/**
 * 显示信息消息
 */
const showInfoMessage = () => {
  showInfo('这是一条信息提示')
}

/**
 * 显示加载消息
 */
const showLoadingMessage = () => {
  const closeLoading = showLoading('正在处理中...')

  // 3秒后关闭加载
  setTimeout(() => {
    closeLoading()
    showSuccess('处理完成！')
  }, 3000)
}

/**
 * 执行带重试的操作
 */
const executeWithRetryDemo = async () => {
  let attempts = 0

  const operation = async () => {
    attempts++
    if (attempts < 3) {
      throw new Error(`模拟失败 (尝试 ${attempts})`)
    }
    return '操作成功'
  }

  const result = await executeWithRetry(operation, { action: 'retry_demo' })
  if (result) {
    showSuccess(`重试成功: ${result}`)
  }
}

/**
 * 执行网络操作
 */
const executeNetworkOperationDemo = async () => {
  const operation = async () => {
    // 模拟网络请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (Math.random() < 0.3) {
      throw new Error('网络请求失败')
    }

    return '网络请求成功'
  }

  const result = await executeNetworkOperation(operation, { action: 'network_demo' })
  if (result) {
    showSuccess(`网络操作成功: ${result}`)
  }
}

/**
 * 模拟交易
 */
const simulateTransaction = async () => {
  // 创建模拟交易Promise
  const mockTransaction = new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() < 0.7) {
        resolve({
          hash: `0x${Math.random().toString(16).substr(2, 64)}`,
          blockNumber: Math.floor(Math.random() * 1000000),
          gasUsed: Math.floor(Math.random() * 100000),
          status: 1
        })
      } else {
        reject(new Error('交易失败'))
      }
    }, 2000)
  })

  const result = await executeTransaction(
    mockTransaction,
    'demo_transfer',
    '演示转账交易',
    { amount: '1.0', to: '0x1234...5678' }
  )

  if (result) {
    showSuccess('交易提交成功！')
  }
}

/**
 * 显示交易状态
 */
const showTransactionStatus = () => {
  const transactions = transactionTracker.getAllTransactions()
  const activeTransactions = transactionTracker.getActiveTransactions()

  showInfo(`总交易数: ${transactions.length}, 活跃交易: ${activeTransactions.length}`)
}

/**
 * 处理交易重试
 */
const handleTransactionRetry = (transaction: any) => {
  showInfo(`重试交易: ${transaction.description}`)
  // 这里可以实现具体的重试逻辑
}

/**
 * 更新错误统计
 */
const updateStats = () => {
  errorStats.value = getErrorStats()
}

/**
 * 格式化时间
 */
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

/**
 * 开始定时刷新
 */
const startRefresh = () => {
  refreshTimer.value = window.setInterval(() => {
    updateStats()
  }, 5000)
}

/**
 * 停止定时刷新
 */
const stopRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  startRefresh()
})

onUnmounted(() => {
  stopRefresh()
})
</script>

<style scoped>
.error-handling-demo {
  @apply space-y-4 p-4;
}

.error-stats,
.network-status {
  @apply mt-4;
}

.van-cell {
  @apply cursor-pointer;
}

.van-cell:hover {
  @apply bg-gray-50;
}
</style>
