<template>
  <div class="loading-spinner" :class="[sizeClass, typeClass]">
    <div v-if="type === 'spinner'" class="spinner">
      <div class="spinner-circle"></div>
    </div>

    <div v-else-if="type === 'dots'" class="dots">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
    </div>

    <div v-else-if="type === 'pulse'" class="pulse">
      <div class="pulse-circle"></div>
    </div>

    <div v-else-if="type === 'bars'" class="bars">
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
      <div class="bar"></div>
    </div>

    <div v-if="showText && text" class="loading-text">
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'spinner' | 'dots' | 'pulse' | 'bars'
  size?: 'small' | 'medium' | 'large'
  text?: string
  showText?: boolean
  color?: 'primary' | 'secondary' | 'white'
}

// 定义组件选项
defineOptions({
  name: 'LoadingSpinner'
})

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'medium',
  text: '加载中...',
  showText: true,
  color: 'primary'
})

const sizeClass = computed(() => `size-${props.size}`)
const typeClass = computed(() => `type-${props.type}`)
</script>

<style scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

/* 尺寸样式 */
.loading-spinner.size-small {
  --spinner-size: 1.5rem;
  --text-size: 0.875rem;
}

.loading-spinner.size-medium {
  --spinner-size: 2rem;
  --text-size: 1rem;
}

.loading-spinner.size-large {
  --spinner-size: 3rem;
  --text-size: 1.125rem;
}

/* 旋转加载器 */
.spinner {
  width: var(--spinner-size);
  height: var(--spinner-size);
}

.spinner-circle {
  width: 100%;
  height: 100%;
  border: 2px solid rgba(168, 85, 247, 0.2);
  border-top: 2px solid #a855f7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 点状加载器 */
.dots {
  display: flex;
  gap: 0.25rem;
}

.dot {
  width: calc(var(--spinner-size) / 4);
  height: calc(var(--spinner-size) / 4);
  background-color: #a855f7;
  border-radius: 50%;
  animation: bounce-dots 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

/* 脉冲加载器 */
.pulse {
  width: var(--spinner-size);
  height: var(--spinner-size);
}

.pulse-circle {
  width: 100%;
  height: 100%;
  background-color: #a855f7;
  border-radius: 50%;
  animation: pulse-scale 1.5s ease-in-out infinite;
}

/* 条状加载器 */
.bars {
  display: flex;
  gap: 0.125rem;
  align-items: end;
  height: var(--spinner-size);
}

.bar {
  width: calc(var(--spinner-size) / 6);
  background-color: #a855f7;
  border-radius: 2px;
  animation: bars-scale 1.2s ease-in-out infinite;
}

.bar:nth-child(1) { animation-delay: -0.9s; }
.bar:nth-child(2) { animation-delay: -0.6s; }
.bar:nth-child(3) { animation-delay: -0.3s; }
.bar:nth-child(4) { animation-delay: 0s; }

/* 加载文本 */
.loading-text {
  font-size: var(--text-size);
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}

/* 动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce-dots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bars-scale {
  0%, 40%, 100% {
    height: 20%;
  }
  20% {
    height: 100%;
  }
}

/* 颜色变体 */
.loading-spinner.type-spinner.color-white .spinner-circle {
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: white;
}

.loading-spinner.color-white .dot,
.loading-spinner.color-white .pulse-circle,
.loading-spinner.color-white .bar {
  background-color: white;
}

.loading-spinner.color-white .loading-text {
  color: white;
}

.loading-spinner.color-secondary .spinner-circle {
  border-color: rgba(14, 165, 233, 0.2);
  border-top-color: #0ea5e9;
}

.loading-spinner.color-secondary .dot,
.loading-spinner.color-secondary .pulse-circle,
.loading-spinner.color-secondary .bar {
  background-color: #0ea5e9;
}
</style>
