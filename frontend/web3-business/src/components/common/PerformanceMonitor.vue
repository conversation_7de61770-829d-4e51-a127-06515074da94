<template>
  <div v-if="showMonitor" class="performance-monitor">
    <van-card
      title="性能监控"
      :desc="`FPS: ${performanceData.fps} | 内存: ${performanceData.memoryUsage}%`"
      class="mb-4"
    >
      <template #footer>
        <van-button size="small" @click="toggleDetails">
          {{ showDetails ? '隐藏详情' : '显示详情' }}
        </van-button>
        <van-button size="small" type="warning" @click="cleanup" class="ml-2">
          清理资源
        </van-button>
      </template>
    </van-card>

    <van-collapse v-if="showDetails" v-model="activeCollapse">
      <!-- 内存信息 -->
      <van-collapse-item title="内存使用" name="memory">
        <div class="space-y-2">
          <div class="flex justify-between">
            <span>已使用:</span>
            <span>{{ formatBytes(memoryStats.memoryInfo?.usedJSHeapSize || 0) }}</span>
          </div>
          <div class="flex justify-between">
            <span>总计:</span>
            <span>{{ formatBytes(memoryStats.memoryInfo?.totalJSHeapSize || 0) }}</span>
          </div>
          <div class="flex justify-between">
            <span>限制:</span>
            <span>{{ formatBytes(memoryStats.memoryInfo?.jsHeapSizeLimit || 0) }}</span>
          </div>
          <div class="flex justify-between">
            <span>峰值使用率:</span>
            <span>{{ memoryStats.peakUsage.toFixed(2) }}%</span>
          </div>
          <div class="flex justify-between">
            <span>平均使用率:</span>
            <span>{{ memoryStats.averageUsage.toFixed(2) }}%</span>
          </div>
          <div class="flex justify-between">
            <span>活动资源:</span>
            <span>{{ memoryStats.resourceCount }}</span>
          </div>
        </div>
      </van-collapse-item>

      <!-- 缓存信息 -->
      <van-collapse-item title="请求缓存" name="cache">
        <div class="space-y-2">
          <div class="flex justify-between">
            <span>缓存大小:</span>
            <span>{{ cacheStats.cacheSize }}</span>
          </div>
          <div class="flex justify-between">
            <span>命中率:</span>
            <span>{{ cacheStats.hitRate }}%</span>
          </div>
          <div class="flex justify-between">
            <span>错误率:</span>
            <span>{{ cacheStats.errorRate }}%</span>
          </div>
          <div class="flex justify-between">
            <span>总请求:</span>
            <span>{{ cacheStats.stats.totalRequests }}</span>
          </div>
          <div class="flex justify-between">
            <span>平均响应时间:</span>
            <span>{{ cacheStats.stats.averageResponseTime.toFixed(2) }}ms</span>
          </div>
        </div>
      </van-collapse-item>

      <!-- 动画信息 -->
      <van-collapse-item title="动画性能" name="animation">
        <div class="space-y-2">
          <div class="flex justify-between">
            <span>性能模式:</span>
            <span class="capitalize">{{ animationStats.performanceMode }}</span>
          </div>
          <div class="flex justify-between">
            <span>动画启用:</span>
            <span>{{ animationStats.animationsEnabled ? '是' : '否' }}</span>
          </div>
          <div class="flex justify-between">
            <span>减少动画:</span>
            <span>{{ animationStats.prefersReducedMotion ? '是' : '否' }}</span>
          </div>
          <div class="flex justify-between">
            <span>活动动画:</span>
            <span>{{ animationStats.activeAnimationsCount }}</span>
          </div>
          <div class="flex justify-between">
            <span>当前FPS:</span>
            <span>{{ animationStats.performanceMetrics.fps }}</span>
          </div>
          <div class="flex justify-between">
            <span>丢帧数:</span>
            <span>{{ animationStats.performanceMetrics.frameDrops }}</span>
          </div>
        </div>
      </van-collapse-item>

      <!-- 资源列表 -->
      <van-collapse-item title="活动资源" name="resources">
        <div class="space-y-2 max-h-40 overflow-y-auto">
          <div
            v-for="resource in resources"
            :key="resource.id"
            class="flex justify-between items-center p-2 bg-gray-50 rounded"
          >
            <div>
              <div class="font-medium">{{ resource.type }}</div>
              <div class="text-sm text-gray-500">
                {{ formatTime(Date.now() - resource.created) }} ago
              </div>
            </div>
            <van-button
              size="mini"
              type="danger"
              @click="cleanupResource(resource.id)"
            >
              清理
            </van-button>
          </div>
          <div v-if="resources.length === 0" class="text-center text-gray-500 py-4">
            暂无活动资源
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useMemoryManager } from '@/utils/memoryManager'
import { useRequestCache } from '@/utils/requestCache'
import { useGlobalAnimation } from '@/composables/useAnimation'

interface Props {
  show?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  show: false
})

const showMonitor = ref(props.show)
const showDetails = ref(false)
const activeCollapse = ref<string[]>([])

// 使用性能监控工具
const memoryManager = useMemoryManager()
const requestCache = useRequestCache()
const animation = useGlobalAnimation()

// 性能数据
const performanceData = computed(() => {
  const memInfo = memoryManager.stats.value.memoryInfo
  return {
    fps: animation.performanceMetrics.value.fps,
    memoryUsage: memInfo ? memInfo.usage.toFixed(1) : '0'
  }
})

// 统计数据
const memoryStats = computed(() => memoryManager.stats.value)
const cacheStats = computed(() => ({
  ...requestCache,
  stats: requestCache.stats.value
}))
const animationStats = computed(() => ({
  performanceMode: animation.performanceMode.value,
  animationsEnabled: animation.animationsEnabled.value,
  prefersReducedMotion: animation.prefersReducedMotion.value,
  activeAnimationsCount: animation.activeAnimationsCount.value,
  performanceMetrics: animation.performanceMetrics.value
}))
const resources = computed(() => memoryManager.resources.value)

// 更新定时器
let updateTimer: number | null = null

const startUpdating = () => {
  updateTimer = setInterval(() => {
    memoryManager.updateStats()
  }, 1000)
}

const stopUpdating = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 方法
const toggleDetails = () => {
  showDetails.value = !showDetails.value
  if (showDetails.value) {
    activeCollapse.value = ['memory', 'cache', 'animation']
  } else {
    activeCollapse.value = []
  }
}

const cleanup = () => {
  memoryManager.cleanup()
  requestCache.clearCache()
  animation.stopNonCriticalAnimations()
}

const cleanupResource = (id: string) => {
  memoryManager.unregister(id)
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (ms: number): string => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

// 生命周期
onMounted(() => {
  if (showMonitor.value) {
    startUpdating()
  }
})

onUnmounted(() => {
  stopUpdating()
})

// 监听显示状态变化
watch(() => props.show, (newValue) => {
  showMonitor.value = newValue
  if (newValue) {
    startUpdating()
  } else {
    stopUpdating()
  }
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 16px;
}

@media (max-width: 768px) {
  .performance-monitor {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
}
</style>
