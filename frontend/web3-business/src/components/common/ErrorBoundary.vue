<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">
        <van-icon name="warning-o" size="48" color="#ff6b6b" />
      </div>

      <div class="error-content">
        <h3 class="error-title">出现了一些问题</h3>
        <p class="error-message">{{ errorMessage }}</p>

        <div class="error-actions">
          <van-button
            type="primary"
            size="small"
            @click="retry"
            :loading="retrying"
          >
            重试
          </van-button>

          <van-button
            type="default"
            size="small"
            @click="goHome"
          >
            返回首页
          </van-button>

          <van-button
            type="default"
            size="small"
            @click="showDetails = !showDetails"
          >
            {{ showDetails ? '隐藏' : '显示' }}详情
          </van-button>
        </div>

        <van-collapse v-model="showDetails" class="error-details">
          <van-collapse-item name="details" title="错误详情">
            <div class="error-stack">
              <p><strong>错误类型:</strong> {{ errorInfo?.type || 'Unknown' }}</p>
              <p><strong>时间:</strong> {{ errorInfo?.timestamp?.toLocaleString() }}</p>
              <p v-if="errorInfo?.code"><strong>错误代码:</strong> {{ errorInfo.code }}</p>
              <p v-if="errorInfo?.context"><strong>上下文:</strong></p>
              <pre v-if="errorInfo?.context">{{ JSON.stringify(errorInfo.context, null, 2) }}</pre>
              <p v-if="errorStack"><strong>堆栈信息:</strong></p>
              <pre v-if="errorStack" class="stack-trace">{{ errorStack }}</pre>
            </div>
          </van-collapse-item>
        </van-collapse>
      </div>
    </div>
  </div>

  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog } from 'vant'
import { errorHandler, type AppError } from '@/services/error-handler.service'

interface Props {
  fallback?: string
  onError?: (error: AppError) => void
  showRetry?: boolean
  showHome?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fallback: '组件加载失败',
  showRetry: true,
  showHome: true
})

const emit = defineEmits<{
  error: [error: AppError]
  retry: []
}>()

const router = useRouter()

const hasError = ref(false)
const errorMessage = ref('')
const errorStack = ref('')
const errorInfo = ref<AppError | null>(null)
const showDetails = ref(false)
const retrying = ref(false)
const retryCount = ref(0)
const maxRetries = 3

// 捕获子组件错误
onErrorCaptured((error: any, instance, info) => {
  console.error('ErrorBoundary caught error:', error, info)

  // 创建错误信息
  const appError = errorHandler.handleError(error, {
    component: instance?.$?.type?.name || 'Unknown',
    errorInfo: info,
    retryCount: retryCount.value
  })

  // 设置错误状态
  hasError.value = true
  errorMessage.value = appError.message
  errorStack.value = error.stack || ''
  errorInfo.value = appError

  // 触发事件
  emit('error', appError)
  props.onError?.(appError)

  // 阻止错误继续向上传播
  return false
})

/**
 * 重试操作
 */
const retry = async () => {
  if (retryCount.value >= maxRetries) {
    showDialog({
      title: '重试次数已达上限',
      message: '已达到最大重试次数，请刷新页面或联系技术支持',
      confirmButtonText: '刷新页面',
      cancelButtonText: '取消'
    }).then(() => {
      window.location.reload()
    })
    return
  }

  retrying.value = true
  retryCount.value++

  try {
    // 等待一段时间后重置错误状态
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 重置错误状态
    hasError.value = false
    errorMessage.value = ''
    errorStack.value = ''
    errorInfo.value = null
    showDetails.value = false

    emit('retry')

    // 强制重新渲染
    await nextTick()

  } catch (error) {
    errorHandler.handleError(error, { action: 'retry' })
  } finally {
    retrying.value = false
  }
}

/**
 * 返回首页
 */
const goHome = () => {
  router.push('/')
}

/**
 * 重置错误状态
 */
const reset = () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  errorInfo.value = null
  showDetails.value = false
  retryCount.value = 0
}

// 暴露方法给父组件
defineExpose({
  reset,
  retry
})
</script>

<style scoped>
.error-boundary {
  @apply min-h-screen flex items-center justify-center p-4 bg-gray-50;
}

.error-container {
  @apply max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center;
}

.error-icon {
  @apply mb-4;
}

.error-content {
  @apply space-y-4;
}

.error-title {
  @apply text-lg font-semibold text-gray-800 mb-2;
}

.error-message {
  @apply text-gray-600 text-sm leading-relaxed;
}

.error-actions {
  @apply flex flex-wrap gap-2 justify-center;
}

.error-details {
  @apply mt-4 text-left;
}

.error-stack {
  @apply space-y-2 text-xs;
}

.error-stack p {
  @apply text-gray-700;
}

.error-stack strong {
  @apply font-medium text-gray-900;
}

.stack-trace {
  @apply bg-gray-100 p-2 rounded text-xs overflow-x-auto whitespace-pre-wrap;
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .error-container {
    @apply mx-4;
  }

  .error-actions {
    @apply flex-col;
  }

  .error-actions .van-button {
    @apply w-full;
  }
}
</style>
