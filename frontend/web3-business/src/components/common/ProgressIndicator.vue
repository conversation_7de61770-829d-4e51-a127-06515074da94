<template>
  <div class="progress-indicator" :class="[sizeClass, typeClass]">
    <!-- 线性进度条 -->
    <div v-if="type === 'linear'" class="progress-linear">
      <div class="progress-track">
        <div
          class="progress-fill"
          :style="{ width: `${percentage}%` }"
        ></div>
      </div>
      <div v-if="showPercentage" class="progress-text">
        {{ Math.round(percentage) }}%
      </div>
    </div>

    <!-- 圆形进度条 -->
    <div v-else-if="type === 'circular'" class="progress-circular">
      <svg class="progress-svg" :width="circularSize" :height="circularSize">
        <circle
          class="progress-track-circle"
          :cx="circularSize / 2"
          :cy="circularSize / 2"
          :r="radius"
          fill="none"
          :stroke-width="strokeWidth"
        />
        <circle
          class="progress-fill-circle"
          :cx="circularSize / 2"
          :cy="circularSize / 2"
          :r="radius"
          fill="none"
          :stroke-width="strokeWidth"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashoffset"
        />
      </svg>
      <div v-if="showPercentage" class="progress-text-circular">
        {{ Math.round(percentage) }}%
      </div>
    </div>

    <!-- 步骤进度条 -->
    <div v-else-if="type === 'steps'" class="progress-steps">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="progress-step"
        :class="{
          'completed': index < currentStep,
          'active': index === currentStep,
          'pending': index > currentStep
        }"
      >
        <div class="step-indicator">
          <span v-if="index < currentStep">✓</span>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="step-label">{{ step.label }}</div>
      </div>
    </div>

    <!-- 标签 -->
    <div v-if="label && type !== 'steps'" class="progress-label">
      {{ label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Step {
  label: string
  completed?: boolean
}

interface Props {
  type?: 'linear' | 'circular' | 'steps'
  percentage?: number
  size?: 'small' | 'medium' | 'large'
  showPercentage?: boolean
  label?: string
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  steps?: Step[]
  currentStep?: number
}

// 定义组件选项
defineOptions({
  name: 'ProgressIndicator'
})

const props = withDefaults(defineProps<Props>(), {
  type: 'linear',
  percentage: 0,
  size: 'medium',
  showPercentage: true,
  color: 'primary',
  steps: () => [],
  currentStep: 0
})

const sizeClass = computed(() => `size-${props.size}`)
const typeClass = computed(() => `type-${props.type}`)

// 圆形进度条计算
const circularSize = computed(() => {
  const sizes = { small: 60, medium: 80, large: 120 }
  return sizes[props.size]
})

const strokeWidth = computed(() => {
  const widths = { small: 4, medium: 6, large: 8 }
  return widths[props.size]
})

const radius = computed(() => {
  return (circularSize.value - strokeWidth.value) / 2
})

const circumference = computed(() => {
  return 2 * Math.PI * radius.value
})

const strokeDashoffset = computed(() => {
  return circumference.value - (props.percentage / 100) * circumference.value
})
</script>

<style scoped>
.progress-indicator {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 尺寸样式 */
.progress-indicator.size-small {
  --progress-height: 0.5rem;
  --text-size: 0.75rem;
}

.progress-indicator.size-medium {
  --progress-height: 0.75rem;
  --text-size: 0.875rem;
}

.progress-indicator.size-large {
  --progress-height: 1rem;
  --text-size: 1rem;
}

/* 线性进度条 */
.progress-linear {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-track {
  flex: 1;
  height: var(--progress-height);
  background-color: #e5e7eb;
  border-radius: calc(var(--progress-height) / 2);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #a855f7, #0ea5e9);
  border-radius: calc(var(--progress-height) / 2);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--text-size);
  font-weight: 600;
  color: #374151;
  min-width: 3rem;
  text-align: right;
}

/* 圆形进度条 */
.progress-circular {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-track-circle {
  stroke: #e5e7eb;
}

.progress-fill-circle {
  stroke: url(#gradient);
  stroke-linecap: round;
  transition: stroke-dashoffset 0.3s ease;
}

.progress-text-circular {
  position: absolute;
  font-size: var(--text-size);
  font-weight: 600;
  color: #374151;
}

/* 步骤进度条 */
.progress-steps {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  position: relative;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 1.25rem;
  left: 60%;
  right: -40%;
  height: 2px;
  background-color: #e5e7eb;
  z-index: -1;
}

.progress-step.completed:not(:last-child)::after {
  background: linear-gradient(90deg, #a855f7, #0ea5e9);
}

.step-indicator {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  border: 2px solid #e5e7eb;
  background-color: white;
  color: #6b7280;
  transition: all 0.3s ease;
}

.progress-step.completed .step-indicator {
  background: linear-gradient(135deg, #a855f7, #0ea5e9);
  border-color: #a855f7;
  color: white;
}

.progress-step.active .step-indicator {
  border-color: #a855f7;
  color: #a855f7;
  box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1);
}

.step-label {
  font-size: var(--text-size);
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

.progress-step.completed .step-label {
  color: #374151;
}

.progress-step.active .step-label {
  color: #a855f7;
  font-weight: 600;
}

/* 标签 */
.progress-label {
  font-size: var(--text-size);
  color: #374151;
  font-weight: 500;
  text-align: center;
}

/* 颜色变体 */
.progress-indicator.color-success .progress-fill {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.progress-indicator.color-warning .progress-fill {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.progress-indicator.color-error .progress-fill {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

.progress-indicator.color-secondary .progress-fill {
  background: linear-gradient(90deg, #0ea5e9, #38bdf8);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .progress-steps {
    flex-direction: column;
    gap: 1.5rem;
  }

  .progress-step:not(:last-child)::after {
    top: 60%;
    left: 1.25rem;
    right: auto;
    bottom: -40%;
    width: 2px;
    height: auto;
  }

  .progress-step {
    flex-direction: row;
    width: 100%;
    text-align: left;
  }

  .step-label {
    text-align: left;
  }
}
</style>
