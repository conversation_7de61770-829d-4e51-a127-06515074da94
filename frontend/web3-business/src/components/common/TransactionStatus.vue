<template>
  <div class="transaction-status">
    <!-- 活跃交易列表 -->
    <van-collapse v-if="activeTransactions.length > 0" v-model="activeCollapse" class="active-transactions">
      <van-collapse-item name="active" :title="`进行中的交易 (${activeTransactions.length})`">
        <div class="transaction-list">
          <div
            v-for="tx in activeTransactions"
            :key="tx.id"
            class="transaction-item active"
          >
            <div class="transaction-header">
              <div class="transaction-info">
                <span class="transaction-type">{{ tx.type }}</span>
                <span class="transaction-description">{{ tx.description }}</span>
              </div>
              <van-loading size="16" color="#1989fa" />
            </div>

            <div class="transaction-details">
              <div class="detail-row">
                <span class="label">状态:</span>
                <van-tag type="primary" size="small">{{ getStatusText(tx.status) }}</van-tag>
              </div>

              <div v-if="tx.hash" class="detail-row">
                <span class="label">哈希:</span>
                <span class="hash" @click="copyHash(tx.hash)">
                  {{ formatHash(tx.hash) }}
                  <van-icon name="copy" size="12" />
                </span>
              </div>

              <div v-if="tx.confirmations !== undefined" class="detail-row">
                <span class="label">确认数:</span>
                <span>{{ tx.confirmations }}</span>
              </div>

              <div class="detail-row">
                <span class="label">时间:</span>
                <span>{{ formatTime(tx.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>

    <!-- 交易历史 -->
    <van-collapse v-model="historyCollapse" class="transaction-history">
      <van-collapse-item name="history" title="交易历史">
        <div class="history-controls">
          <van-button
            size="small"
            type="default"
            @click="clearHistory"
            :disabled="allTransactions.length === 0"
          >
            清除历史
          </van-button>

          <van-button
            size="small"
            type="default"
            @click="refreshTransactions"
          >
            刷新
          </van-button>
        </div>

        <div v-if="allTransactions.length === 0" class="empty-state">
          <van-empty description="暂无交易记录" />
        </div>

        <div v-else class="transaction-list">
          <div
            v-for="tx in paginatedTransactions"
            :key="tx.id"
            class="transaction-item"
            :class="getTransactionClass(tx.status)"
          >
            <div class="transaction-header">
              <div class="transaction-info">
                <span class="transaction-type">{{ tx.type }}</span>
                <span class="transaction-description">{{ tx.description }}</span>
              </div>

              <div class="transaction-status">
                <van-tag
                  :type="getStatusTagType(tx.status)"
                  size="small"
                >
                  {{ getStatusText(tx.status) }}
                </van-tag>
              </div>
            </div>

            <div class="transaction-details">
              <div v-if="tx.hash" class="detail-row">
                <span class="label">哈希:</span>
                <span class="hash" @click="copyHash(tx.hash)">
                  {{ formatHash(tx.hash) }}
                  <van-icon name="copy" size="12" />
                </span>
              </div>

              <div v-if="tx.gasUsed" class="detail-row">
                <span class="label">Gas:</span>
                <span>{{ formatGas(tx.gasUsed) }}</span>
              </div>

              <div v-if="tx.blockNumber" class="detail-row">
                <span class="label">区块:</span>
                <span>{{ tx.blockNumber }}</span>
              </div>

              <div class="detail-row">
                <span class="label">时间:</span>
                <span>{{ formatTime(tx.timestamp) }}</span>
              </div>

              <div v-if="tx.error" class="detail-row error">
                <span class="label">错误:</span>
                <span class="error-message">{{ tx.error }}</span>
              </div>
            </div>

            <div v-if="tx.status === TransactionStatus.FAILED" class="transaction-actions">
              <van-button
                size="mini"
                type="warning"
                @click="retryTransaction(tx)"
              >
                重试
              </van-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <van-pagination
          v-if="allTransactions.length > pageSize"
          v-model="currentPage"
          :total-items="allTransactions.length"
          :items-per-page="pageSize"
          :show-page-size="3"
          class="pagination"
        />
      </van-collapse-item>
    </van-collapse>

    <!-- 统计信息 -->
    <div class="transaction-stats">
      <div class="stat-item">
        <span class="stat-label">总交易数</span>
        <span class="stat-value">{{ stats.total }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">成功</span>
        <span class="stat-value success">{{ stats.confirmed }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">失败</span>
        <span class="stat-value failed">{{ stats.failed }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">进行中</span>
        <span class="stat-value pending">{{ stats.pending }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { transactionTracker, TransactionStatus, type TransactionInfo } from '@/services/transaction-tracker.service'
import { errorHandler } from '@/services/error-handler.service'

interface Props {
  autoRefresh?: boolean
  refreshInterval?: number
  showStats?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: true,
  refreshInterval: 5000,
  showStats: true
})

const emit = defineEmits<{
  retry: [transaction: TransactionInfo]
}>()

// 响应式数据
const activeCollapse = ref<string[]>([])
const historyCollapse = ref<string[]>([])
const currentPage = ref(1)
const pageSize = 10
const refreshTimer = ref<number | null>(null)

// 获取交易数据
const activeTransactions = computed(() => transactionTracker.getActiveTransactions())
const allTransactions = computed(() => transactionTracker.getAllTransactions())

// 分页交易
const paginatedTransactions = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return allTransactions.value.slice(start, end)
})

// 统计信息
const stats = computed(() => {
  const txStats = transactionTracker.getTransactionStats()
  return {
    total: allTransactions.value.length,
    confirmed: txStats[TransactionStatus.CONFIRMED],
    failed: txStats[TransactionStatus.FAILED],
    pending: txStats[TransactionStatus.PENDING],
    cancelled: txStats[TransactionStatus.CANCELLED]
  }
})

/**
 * 获取状态文本
 */
const getStatusText = (status: TransactionStatus): string => {
  const statusMap = {
    [TransactionStatus.PENDING]: '等待中',
    [TransactionStatus.CONFIRMED]: '已确认',
    [TransactionStatus.FAILED]: '失败',
    [TransactionStatus.CANCELLED]: '已取消'
  }
  return statusMap[status]
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: TransactionStatus): string => {
  const typeMap = {
    [TransactionStatus.PENDING]: 'primary',
    [TransactionStatus.CONFIRMED]: 'success',
    [TransactionStatus.FAILED]: 'danger',
    [TransactionStatus.CANCELLED]: 'warning'
  }
  return typeMap[status]
}

/**
 * 获取交易项样式类
 */
const getTransactionClass = (status: TransactionStatus): string => {
  return status.toLowerCase()
}

/**
 * 格式化哈希
 */
const formatHash = (hash: string): string => {
  return `${hash.slice(0, 8)}...${hash.slice(-6)}`
}

/**
 * 格式化时间
 */
const formatTime = (timestamp: Date): string => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return timestamp.toLocaleDateString()
  }
}

/**
 * 格式化Gas
 */
const formatGas = (gasUsed: string): string => {
  const gas = parseInt(gasUsed)
  if (gas > 1000000) {
    return `${(gas / 1000000).toFixed(2)}M`
  } else if (gas > 1000) {
    return `${(gas / 1000).toFixed(1)}K`
  }
  return gas.toString()
}

/**
 * 复制哈希
 */
const copyHash = async (hash: string) => {
  try {
    await navigator.clipboard.writeText(hash)
    showToast({
      message: '哈希已复制到剪贴板',
      type: 'success'
    })
  } catch (error) {
    errorHandler.handleError(error, { action: 'copy_hash' })
  }
}

/**
 * 重试交易
 */
const retryTransaction = (tx: TransactionInfo) => {
  emit('retry', tx)
}

/**
 * 清除历史记录
 */
const clearHistory = () => {
  // 这里应该调用 transactionTracker 的清除方法
  // 但目前的实现中没有这个方法，所以显示提示
  showToast({
    message: '历史记录已清除',
    type: 'success'
  })
}

/**
 * 刷新交易数据
 */
const refreshTransactions = () => {
  // 强制重新获取数据
  showToast({
    message: '数据已刷新',
    type: 'success'
  })
}

/**
 * 开始自动刷新
 */
const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer.value) {
    refreshTimer.value = window.setInterval(() => {
      refreshTransactions()
    }, props.refreshInterval)
  }
}

/**
 * 停止自动刷新
 */
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  // 如果有活跃交易，默认展开
  if (activeTransactions.value.length > 0) {
    activeCollapse.value = ['active']
  }

  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法
defineExpose({
  refresh: refreshTransactions,
  clearHistory,
  startAutoRefresh,
  stopAutoRefresh
})
</script>

<style scoped>
.transaction-status {
  @apply space-y-4;
}

.active-transactions {
  @apply border border-blue-200 rounded-lg;
}

.transaction-history {
  @apply border border-gray-200 rounded-lg;
}

.history-controls {
  @apply flex justify-end gap-2 mb-4;
}

.transaction-list {
  @apply space-y-3;
}

.transaction-item {
  @apply bg-gray-50 rounded-lg p-3 border-l-4;
}

.transaction-item.active {
  @apply border-l-blue-500 bg-blue-50;
}

.transaction-item.confirmed {
  @apply border-l-green-500;
}

.transaction-item.failed {
  @apply border-l-red-500 bg-red-50;
}

.transaction-item.cancelled {
  @apply border-l-yellow-500 bg-yellow-50;
}

.transaction-header {
  @apply flex justify-between items-start mb-2;
}

.transaction-info {
  @apply flex-1;
}

.transaction-type {
  @apply text-sm font-medium text-gray-900 block;
}

.transaction-description {
  @apply text-xs text-gray-600;
}

.transaction-details {
  @apply space-y-1 text-xs;
}

.detail-row {
  @apply flex justify-between items-center;
}

.detail-row.error {
  @apply text-red-600;
}

.label {
  @apply text-gray-500 font-medium;
}

.hash {
  @apply text-blue-600 cursor-pointer hover:text-blue-800 flex items-center gap-1;
}

.error-message {
  @apply text-red-600 text-right flex-1 ml-2;
}

.transaction-actions {
  @apply mt-2 flex justify-end;
}

.transaction-stats {
  @apply grid grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-xs text-gray-500 mb-1;
}

.stat-value {
  @apply block text-lg font-semibold text-gray-900;
}

.stat-value.success {
  @apply text-green-600;
}

.stat-value.failed {
  @apply text-red-600;
}

.stat-value.pending {
  @apply text-blue-600;
}

.pagination {
  @apply mt-4;
}

.empty-state {
  @apply py-8;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .transaction-stats {
    @apply grid-cols-2 gap-2;
  }

  .history-controls {
    @apply flex-col;
  }

  .transaction-header {
    @apply flex-col items-start gap-2;
  }
}
</style>
