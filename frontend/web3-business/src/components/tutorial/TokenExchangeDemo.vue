<template>
  <div class="token-exchange-demo">
    <div class="demo-container">
      <!-- 萌宠信息 -->
      <div class="pet-card">
        <div class="pet-header">
          <div class="pet-avatar">
            <img src="/images/pets/cat/orange_striped.svg" alt="萌宠" class="pet-image" />
            <div class="pet-rarity" :class="`rarity-${petRarity}`">{{ rarityText }}</div>
          </div>
          <div class="pet-info">
            <h4 class="pet-name">{{ petName }}</h4>
            <div class="pet-level">
              <span class="level-label">等级</span>
              <span class="level-value">{{ petLevel }}</span>
            </div>
          </div>
        </div>

        <div class="pet-attributes">
          <div class="attribute-item">
            <div class="attribute-label">健康度</div>
            <div class="attribute-bar">
              <div class="bar-fill health" :style="{ width: `${petHealth}%` }"></div>
            </div>
            <div class="attribute-value">{{ petHealth }}/100</div>
          </div>

          <div class="attribute-item">
            <div class="attribute-label">装备</div>
            <div class="equipment-slots">
              <div
                v-for="(equipped, index) in equipmentSlots"
                :key="index"
                class="equipment-slot"
                :class="{ 'equipped': equipped }"
              >
                {{ equipped ? '🧩' : '' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 兑换计算 -->
      <div class="exchange-calculator">
        <div class="calculator-title">代币兑换计算</div>

        <div class="calculation-factors">
          <div class="factor-item">
            <div class="factor-label">基础价值</div>
            <div class="factor-value">{{ baseValue }} 代币</div>
          </div>

          <div class="factor-item">
            <div class="factor-label">稀有度加成</div>
            <div class="factor-value">×{{ rarityMultiplier }}</div>
          </div>

          <div class="factor-item">
            <div class="factor-label">健康度加成</div>
            <div class="factor-value">×{{ healthMultiplier }}</div>
          </div>

          <div class="factor-item">
            <div class="factor-label">装备加成</div>
            <div class="factor-value">×{{ equipmentMultiplier }}</div>
          </div>

          <div class="factor-divider"></div>

          <div class="factor-item total">
            <div class="factor-label">预计获得</div>
            <div class="factor-value">{{ totalValue }} 代币</div>
          </div>
        </div>
      </div>

      <!-- 兑换操作 -->
      <div class="exchange-actions">
        <div class="action-title">兑换操作</div>

        <div class="action-steps">
          <div class="step-item" :class="{ 'active': currentStep >= 1, 'completed': currentStep > 1 }">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">确认兑换</div>
              <div class="step-desc">确认将萌宠兑换成代币</div>
            </div>
          </div>

          <div class="step-item" :class="{ 'active': currentStep >= 2, 'completed': currentStep > 2 }">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">签名交易</div>
              <div class="step-desc">在钱包中确认交易</div>
            </div>
          </div>

          <div class="step-item" :class="{ 'active': currentStep >= 3, 'completed': currentStep > 3 }">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">交易确认</div>
              <div class="step-desc">等待区块链确认</div>
            </div>
          </div>

          <div class="step-item" :class="{ 'active': currentStep >= 4 }">
            <div class="step-number">4</div>
            <div class="step-content">
              <div class="step-title">兑换完成</div>
              <div class="step-desc">代币已发送到钱包</div>
            </div>
          </div>
        </div>

        <div class="action-button">
          <van-button
            v-if="currentStep === 0"
            type="primary"
            block
            @click="startExchange"
          >
            开始兑换演示
          </van-button>

          <van-button
            v-else-if="currentStep < 4"
            type="primary"
            block
            :loading="isProcessing"
            @click="nextStep"
          >
            {{ stepButtonText }}
          </van-button>

          <div v-else class="exchange-complete">
            <van-icon name="success" class="complete-icon" />
            <span class="complete-text">兑换成功！</span>
          </div>
        </div>
      </div>

      <!-- 演示状态 -->
      <div class="demo-status">
        <div class="progress-container">
          <div class="progress-label">演示进度</div>
          <van-progress :percentage="demoProgress" :show-pivot="false" />
        </div>
      </div>
    </div>

    <!-- 完成按钮 -->
    <div class="demo-actions">
      <van-button
        type="primary"
        size="small"
        @click="completeDemoHandler"
        :disabled="demoProgress < 100"
      >
        {{ demoProgress < 100 ? '请完成演示' : '完成演示' }}
      </van-button>
    </div>

    <!-- 确认弹窗 -->
    <van-dialog
      v-model:show="showConfirmDialog"
      title="确认兑换"
      show-cancel-button
      @confirm="confirmExchange"
    >
      <div class="confirm-content">
        <p class="confirm-warning">
          <van-icon name="warning-o" class="warning-icon" />
          兑换后萌宠将永久消失，此操作不可逆！
        </p>
        <p class="confirm-detail">
          你将获得 <span class="token-amount">{{ totalValue }}</span> 代币
        </p>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'

// 定义事件
const emit = defineEmits<{
  'demo-complete': []
}>()

// 萌宠状态
const petName = ref('小橘')
const petLevel = ref(8)
const petRarity = ref('rare') // common, uncommon, rare, epic, legendary
const petHealth = ref(85)
const equipmentSlots = ref([true, true, false, false])

// 兑换状态
const currentStep = ref(0)
const isProcessing = ref(false)
const showConfirmDialog = ref(false)
const demoProgress = ref(0)

// 计算属性
const rarityText = computed(() => {
  const rarityMap: Record<string, string> = {
    common: '普通',
    uncommon: '罕见',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  }
  return rarityMap[petRarity.value] || '普通'
})

const baseValue = computed(() => {
  return petLevel.value * 10
})

const rarityMultiplier = computed(() => {
  const multipliers: Record<string, number> = {
    common: 1.0,
    uncommon: 1.5,
    rare: 2.0,
    epic: 3.0,
    legendary: 5.0
  }
  return multipliers[petRarity.value] || 1.0
})

const healthMultiplier = computed(() => {
  if (petHealth.value >= 80) return 1.2
  if (petHealth.value >= 50) return 1.0
  if (petHealth.value >= 20) return 0.8
  return 0.5
})

const equipmentMultiplier = computed(() => {
  const equippedCount = equipmentSlots.value.filter(slot => slot).length
  return 1 + (equippedCount * 0.1)
})

const totalValue = computed(() => {
  const value = baseValue.value * rarityMultiplier.value * healthMultiplier.value * equipmentMultiplier.value
  return Math.floor(value)
})

const stepButtonText = computed(() => {
  switch (currentStep.value) {
    case 1: return '确认兑换'
    case 2: return '签名交易'
    case 3: return '等待确认'
    default: return '下一步'
  }
})

// 方法
const startExchange = () => {
  currentStep.value = 1
  updateProgress()
}

const nextStep = () => {
  if (currentStep.value === 1) {
    showConfirmDialog.value = true
    return
  }

  isProcessing.value = true

  setTimeout(() => {
    currentStep.value++
    isProcessing.value = false
    updateProgress()

    if (currentStep.value === 4) {
      showToast({
        message: '兑换成功！',
        icon: 'success'
      })
    }
  }, 1500)
}

const confirmExchange = () => {
  currentStep.value = 2
  updateProgress()
}

const updateProgress = () => {
  demoProgress.value = Math.min(100, Math.floor((currentStep.value / 4) * 100))
}

const completeDemoHandler = () => {
  if (demoProgress.value < 100) {
    showToast('请先完成演示')
    return
  }

  showToast('演示完成！')
  emit('demo-complete')
}
</script>

<style scoped>
.token-exchange-demo {
  @apply space-y-4;
}

.demo-container {
  @apply space-y-4;
}

.pet-card {
  @apply bg-white rounded-lg border border-gray-200 p-3 space-y-3;
}

.pet-header {
  @apply flex items-center space-x-3;
}

.pet-avatar {
  @apply relative;
}

.pet-image {
  @apply w-12 h-12 rounded-full bg-gray-100 p-1;
}

.pet-rarity {
  @apply absolute -top-1 -right-1 text-xs font-semibold text-white px-2 py-0.5 rounded-full;
}

.rarity-common {
  @apply bg-gray-500;
}

.rarity-uncommon {
  @apply bg-green-500;
}

.rarity-rare {
  @apply bg-blue-500;
}

.rarity-epic {
  @apply bg-purple-500;
}

.rarity-legendary {
  @apply bg-yellow-500;
}

.pet-info {
  @apply flex-1;
}

.pet-name {
  @apply font-semibold text-gray-800 mb-1;
}

.pet-level {
  @apply flex items-center space-x-2;
}

.level-label {
  @apply text-xs text-gray-500;
}

.level-value {
  @apply text-sm font-semibold bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full;
}

.pet-attributes {
  @apply space-y-3;
}

.attribute-item {
  @apply flex items-center space-x-2;
}

.attribute-label {
  @apply text-xs text-gray-500 w-12;
}

.attribute-bar {
  @apply flex-1 h-4 bg-gray-100 rounded-full overflow-hidden;
}

.bar-fill {
  @apply h-full rounded-full;
}

.bar-fill.health {
  @apply bg-green-500;
}

.attribute-value {
  @apply text-xs text-gray-500 w-16 text-right;
}

.equipment-slots {
  @apply flex-1 flex space-x-1;
}

.equipment-slot {
  @apply w-6 h-6 bg-gray-100 rounded flex items-center justify-center text-sm;
}

.equipment-slot.equipped {
  @apply bg-blue-100;
}

.exchange-calculator {
  @apply bg-white rounded-lg border border-gray-200 p-3;
}

.calculator-title {
  @apply text-sm font-medium text-gray-700 mb-3;
}

.calculation-factors {
  @apply space-y-2;
}

.factor-item {
  @apply flex justify-between items-center text-sm;
}

.factor-label {
  @apply text-gray-600;
}

.factor-value {
  @apply font-medium;
}

.factor-item.total {
  @apply font-semibold text-green-700;
}

.factor-divider {
  @apply border-t border-gray-200 my-2;
}

.exchange-actions {
  @apply bg-white rounded-lg border border-gray-200 p-3 space-y-3;
}

.action-title {
  @apply text-sm font-medium text-gray-700 mb-1;
}

.action-steps {
  @apply space-y-2;
}

.step-item {
  @apply flex items-start space-x-2 p-2 rounded-lg;
}

.step-item.active {
  @apply bg-blue-50;
}

.step-item.completed {
  @apply bg-green-50;
}

.step-number {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium text-white bg-gray-400 flex-shrink-0;
}

.step-item.active .step-number {
  @apply bg-blue-500;
}

.step-item.completed .step-number {
  @apply bg-green-500;
}

.step-content {
  @apply flex-1;
}

.step-title {
  @apply text-sm font-medium text-gray-800;
}

.step-desc {
  @apply text-xs text-gray-500;
}

.action-button {
  @apply mt-4;
}

.exchange-complete {
  @apply flex items-center justify-center space-x-2 bg-green-100 text-green-800 p-3 rounded-lg;
}

.complete-icon {
  @apply text-green-500;
}

.complete-text {
  @apply font-medium;
}

.demo-status {
  @apply mt-4;
}

.progress-container {
  @apply flex items-center space-x-2;
}

.progress-label {
  @apply text-xs text-gray-500;
}

.demo-actions {
  @apply flex justify-end;
}

.confirm-content {
  @apply p-4 space-y-4;
}

.confirm-warning {
  @apply flex items-start space-x-2 text-sm text-red-600 bg-red-50 p-2 rounded-lg;
}

.warning-icon {
  @apply text-red-500 flex-shrink-0 mt-0.5;
}

.confirm-detail {
  @apply text-center text-gray-700;
}

.token-amount {
  @apply font-semibold text-green-600;
}
</style>
