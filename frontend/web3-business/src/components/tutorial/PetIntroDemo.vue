<template>
  <div class="pet-intro-demo">
    <div class="demo-container">
      <!-- 萌宠展示区 -->
      <div class="pet-display">
        <div class="pet-avatar">
          <img src="/images/pets/cat/orange_striped.svg" alt="萌宠" class="pet-image" />
          <div class="pet-rarity" :class="rarityClass">{{ rarityText }}</div>
        </div>

        <div class="pet-info">
          <h4 class="pet-name">{{ petName }}</h4>
          <div class="pet-level">
            <span class="level-label">等级</span>
            <span class="level-value">{{ petLevel }}</span>
          </div>
        </div>
      </div>

      <!-- 属性展示区 -->
      <div class="pet-attributes">
        <div class="attribute-item">
          <div class="attribute-label">健康度</div>
          <div class="attribute-bar">
            <div class="bar-fill health" :style="{ width: `${petHealth}%` }"></div>
          </div>
          <div class="attribute-value">{{ petHealth }}/100</div>
        </div>

        <div class="attribute-item">
          <div class="attribute-label">快乐度</div>
          <div class="attribute-bar">
            <div class="bar-fill happiness" :style="{ width: `${petHappiness}%` }"></div>
          </div>
          <div class="attribute-value">{{ petHappiness }}/100</div>
        </div>

        <div class="attribute-item">
          <div class="attribute-label">经验值</div>
          <div class="attribute-bar">
            <div class="bar-fill experience" :style="{ width: `${(petExperience / maxExperience) * 100}%` }"></div>
          </div>
          <div class="attribute-value">{{ petExperience }}/{{ maxExperience }}</div>
        </div>
      </div>

      <!-- 互动区域 -->
      <div class="pet-interaction">
        <div class="interaction-title">互动演示</div>
        <div class="interaction-buttons">
          <van-button
            type="primary"
            size="small"
            icon="like-o"
            @click="feedPet"
            :disabled="isInteracting"
          >
            喂食
          </van-button>

          <van-button
            type="success"
            size="small"
            icon="fire-o"
            @click="trainPet"
            :disabled="isInteracting"
          >
            训练
          </van-button>

          <van-button
            type="warning"
            size="small"
            icon="smile-o"
            @click="playWithPet"
            :disabled="isInteracting"
          >
            玩耍
          </van-button>
        </div>
      </div>

      <!-- 演示状态 -->
      <div class="demo-status">
        <div v-if="interactionMessage" class="status-message">
          {{ interactionMessage }}
        </div>
        <div class="progress-container">
          <div class="progress-label">演示进度</div>
          <van-progress :percentage="demoProgress" :show-pivot="false" />
        </div>
      </div>
    </div>

    <!-- 完成按钮 -->
    <div class="demo-actions">
      <van-button
        type="primary"
        size="small"
        @click="completeDemoHandler"
        :disabled="demoProgress < 100"
      >
        {{ demoProgress < 100 ? '请完成演示' : '完成演示' }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { showToast } from 'vant'

// 定义事件
const emit = defineEmits<{
  'demo-complete': []
}>()

// 状态
const petName = ref('小橘')
const petLevel = ref(1)
const petRarity = ref('rare') // common, uncommon, rare, epic, legendary
const petHealth = ref(80)
const petHappiness = ref(70)
const petExperience = ref(0)
const maxExperience = ref(100)
const isInteracting = ref(false)
const interactionMessage = ref('')
const interactionCount = ref(0)
const demoProgress = ref(0)

// 计算属性
const rarityClass = computed(() => `rarity-${petRarity.value}`)
const rarityText = computed(() => {
  const rarityMap: Record<string, string> = {
    common: '普通',
    uncommon: '罕见',
    rare: '稀有',
    epic: '史诗',
    legendary: '传说'
  }
  return rarityMap[petRarity.value] || '普通'
})

// 方法
const feedPet = () => {
  if (isInteracting.value) return

  isInteracting.value = true
  interactionMessage.value = '正在喂食...'

  setTimeout(() => {
    petHealth.value = Math.min(100, petHealth.value + 10)
    petExperience.value = Math.min(maxExperience.value, petExperience.value + 5)
    interactionMessage.value = '喂食成功！健康度+10，经验值+5'
    interactionCount.value++
    updateProgress()

    isInteracting.value = false
  }, 1000)
}

const trainPet = () => {
  if (isInteracting.value) return

  isInteracting.value = true
  interactionMessage.value = '正在训练...'

  setTimeout(() => {
    petHealth.value = Math.max(0, petHealth.value - 5)
    petExperience.value = Math.min(maxExperience.value, petExperience.value + 15)
    interactionMessage.value = '训练成功！健康度-5，经验值+15'
    interactionCount.value++
    updateProgress()

    isInteracting.value = false
  }, 1000)
}

const playWithPet = () => {
  if (isInteracting.value) return

  isInteracting.value = true
  interactionMessage.value = '正在玩耍...'

  setTimeout(() => {
    petHappiness.value = Math.min(100, petHappiness.value + 15)
    petExperience.value = Math.min(maxExperience.value, petExperience.value + 3)
    interactionMessage.value = '玩耍成功！快乐度+15，经验值+3'
    interactionCount.value++
    updateProgress()

    isInteracting.value = false
  }, 1000)
}

const updateProgress = () => {
  // 需要3次互动完成演示
  demoProgress.value = Math.min(100, Math.floor((interactionCount.value / 3) * 100))
}

const completeDemoHandler = () => {
  if (demoProgress.value < 100) {
    showToast('请先完成演示')
    return
  }

  showToast('演示完成！')
  emit('demo-complete')
}

// 监听经验值变化，升级
watch(petExperience, (newExp) => {
  if (newExp >= maxExperience.value) {
    petLevel.value++
    petExperience.value = 0
    maxExperience.value = Math.floor(maxExperience.value * 1.5)
    showToast({
      message: `恭喜！萌宠升级到${petLevel.value}级`,
      icon: 'star'
    })
  }
})
</script>

<style scoped>
.pet-intro-demo {
  @apply space-y-4;
}

.demo-container {
  @apply space-y-4;
}

.pet-display {
  @apply flex items-center space-x-4;
}

.pet-avatar {
  @apply relative;
}

.pet-image {
  @apply w-16 h-16 rounded-full bg-gray-100 p-1;
}

.pet-rarity {
  @apply absolute -top-1 -right-1 text-xs font-semibold text-white px-2 py-0.5 rounded-full;
}

.rarity-common {
  @apply bg-gray-500;
}

.rarity-uncommon {
  @apply bg-green-500;
}

.rarity-rare {
  @apply bg-blue-500;
}

.rarity-epic {
  @apply bg-purple-500;
}

.rarity-legendary {
  @apply bg-yellow-500;
}

.pet-info {
  @apply flex-1;
}

.pet-name {
  @apply font-semibold text-gray-800 mb-1;
}

.pet-level {
  @apply flex items-center space-x-2;
}

.level-label {
  @apply text-xs text-gray-500;
}

.level-value {
  @apply text-sm font-semibold bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full;
}

.pet-attributes {
  @apply space-y-3;
}

.attribute-item {
  @apply flex items-center space-x-2;
}

.attribute-label {
  @apply text-xs text-gray-500 w-12;
}

.attribute-bar {
  @apply flex-1 h-4 bg-gray-100 rounded-full overflow-hidden;
}

.bar-fill {
  @apply h-full rounded-full;
  transition: width 0.3s ease;
}

.bar-fill.health {
  @apply bg-green-500;
}

.bar-fill.happiness {
  @apply bg-yellow-500;
}

.bar-fill.experience {
  @apply bg-blue-500;
}

.attribute-value {
  @apply text-xs text-gray-500 w-16 text-right;
}

.pet-interaction {
  @apply bg-gray-50 rounded-lg p-3;
}

.interaction-title {
  @apply text-sm font-medium text-gray-700 mb-2;
}

.interaction-buttons {
  @apply flex space-x-2;
}

.demo-status {
  @apply space-y-2;
}

.status-message {
  @apply text-sm text-gray-600 bg-blue-50 p-2 rounded-lg;
}

.progress-container {
  @apply flex items-center space-x-2;
}

.progress-label {
  @apply text-xs text-gray-500;
}

.demo-actions {
  @apply flex justify-end;
}
</style>
