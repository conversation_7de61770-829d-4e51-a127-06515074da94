<template>
  <div class="game-flow-demo">
    <div class="demo-container">
      <!-- 游戏流程演示 -->
      <div class="flow-steps">
        <div
          v-for="(step, index) in gameFlowSteps"
          :key="index"
          class="flow-step"
          :class="{
            'active': currentStepIndex === index,
            'completed': currentStepIndex > index
          }"
          @click="selectStep(index)"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <h5 class="step-title">{{ step.title }}</h5>
            <p class="step-desc">{{ step.description }}</p>
          </div>
          <div class="step-status">
            <van-icon v-if="currentStepIndex > index" name="success" class="status-icon completed" />
            <van-icon v-else-if="currentStepIndex === index" name="play-circle-o" class="status-icon active" />
            <van-icon v-else name="clock-o" class="status-icon" />
          </div>
        </div>
      </div>

      <!-- 步骤详情 -->
      <div class="step-details">
        <div class="details-header">
          <h4 class="details-title">{{ currentStep.title }}</h4>
          <div class="details-subtitle">{{ currentStep.subtitle }}</div>
        </div>

        <div class="details-content">
          <div class="details-image">
            <div class="image-placeholder" :class="`step-${currentStepIndex + 1}`">
              <div class="placeholder-icon">{{ currentStep.icon }}</div>
            </div>
          </div>

          <div class="details-description">
            <p v-for="(point, i) in currentStep.points" :key="i" class="description-point">
              <van-icon name="arrow" class="point-icon" />
              {{ point }}
            </p>
          </div>

          <div v-if="currentStep.tips" class="details-tips">
            <div class="tip-header">
              <van-icon name="info-o" class="tip-icon" />
              <span class="tip-title">小贴士</span>
            </div>
            <p class="tip-content">{{ currentStep.tips }}</p>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation-buttons">
        <van-button
          plain
          size="small"
          icon="arrow-left"
          @click="previousStep"
          :disabled="currentStepIndex === 0"
        >
          上一步
        </van-button>

        <div class="step-indicator">
          {{ currentStepIndex + 1 }}/{{ gameFlowSteps.length }}
        </div>

        <van-button
          type="primary"
          size="small"
          @click="nextStep"
          :disabled="currentStepIndex === gameFlowSteps.length - 1"
        >
          下一步
          <template #right-icon>
            <van-icon name="arrow" />
          </template>
        </van-button>
      </div>

      <!-- 演示状态 -->
      <div class="demo-status">
        <div class="progress-container">
          <div class="progress-label">演示进度</div>
          <van-progress :percentage="demoProgress" :show-pivot="false" />
        </div>
      </div>
    </div>

    <!-- 完成按钮 -->
    <div class="demo-actions">
      <van-button
        type="primary"
        size="small"
        @click="completeDemoHandler"
        :disabled="demoProgress < 100"
      >
        {{ demoProgress < 100 ? '请完成演示' : '完成演示' }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showToast } from 'vant'

// 定义事件
const emit = defineEmits<{
  'demo-complete': []
}>()

// 游戏流程步骤
const gameFlowSteps = [
  {
    title: '连接钱包',
    subtitle: '开始游戏的第一步',
    description: '连接你的Web3钱包',
    icon: '👛',
    points: [
      '点击首页右上角的"连接钱包"按钮',
      '选择MetaMask或WalletConnect',
      '在钱包中确认连接请求',
      '确保连接到正确的测试网络'
    ],
    tips: '首次连接时，请仔细检查授权请求，确保安全连接。'
  },
  {
    title: '创建萌宠',
    subtitle: '开始你的养成之旅',
    description: '创建或领养你的第一只萌宠',
    icon: '🐱',
    points: [
      '首次进入游戏会自动引导创建萌宠',
      '为你的萌宠取一个可爱的名字',
      '系统会随机生成萌宠的基础属性',
      '不同稀有度的萌宠有不同的成长潜力'
    ],
    tips: '稀有度越高的萌宠，成长潜力和最终价值越高。'
  },
  {
    title: '养成萌宠',
    subtitle: '提升萌宠属性',
    description: '通过各种方式养成萌宠',
    icon: '🍼',
    points: [
      '定期喂食萌宠提升健康度',
      '训练萌宠获得更多经验值',
      '与萌宠互动提升快乐度',
      '让萌宠休息恢复健康度'
    ],
    tips: '平衡各种养成活动，保持萌宠的健康度和快乐度。'
  },
  {
    title: '装备道具',
    subtitle: '增强萌宠属性',
    description: '为萌宠装备各种道具',
    icon: '🎒',
    points: [
      '在商店购买各种装备和道具',
      '为萌宠装备合适的道具',
      '不同装备提供不同的属性加成',
      '稀有装备可以显著提升萌宠价值'
    ],
    tips: '根据萌宠的特点选择合适的装备，可以事半功倍。'
  },
  {
    title: '兑换代币',
    subtitle: '收获养成成果',
    description: '将萌宠兑换成代币',
    icon: '💰',
    points: [
      '当萌宠达到理想状态时可以兑换',
      '系统会根据萌宠属性计算代币价值',
      '确认兑换并支付Gas费用',
      '代币会直接发送到你的钱包'
    ],
    tips: '兑换是不可逆操作，萌宠兑换后将永久消失，请谨慎考虑。'
  }
]

// 状态
const currentStepIndex = ref(0)
const viewedSteps = ref<number[]>([0])
const demoProgress = ref(0)

// 计算属性
const currentStep = computed(() => gameFlowSteps[currentStepIndex.value])

// 方法
const selectStep = (index: number) => {
  // 只允许查看已解锁的步骤
  const maxAllowedIndex = Math.max(...viewedSteps.value)
  if (index <= maxAllowedIndex) {
    currentStepIndex.value = index
    updateProgress()
  }
}

const nextStep = () => {
  if (currentStepIndex.value < gameFlowSteps.length - 1) {
    currentStepIndex.value++
    if (!viewedSteps.value.includes(currentStepIndex.value)) {
      viewedSteps.value.push(currentStepIndex.value)
    }
    updateProgress()
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    updateProgress()
  }
}

const updateProgress = () => {
  // 计算已查看的步骤百分比
  const uniqueViewedSteps = [...new Set(viewedSteps.value)]
  demoProgress.value = Math.min(100, Math.floor((uniqueViewedSteps.length / gameFlowSteps.length) * 100))
}

const completeDemoHandler = () => {
  if (demoProgress.value < 100) {
    showToast('请先浏览所有步骤')
    return
  }

  showToast('演示完成！')
  emit('demo-complete')
}
</script>

<style scoped>
.game-flow-demo {
  @apply space-y-4;
}

.demo-container {
  @apply space-y-4;
}

.flow-steps {
  @apply space-y-2;
}

.flow-step {
  @apply flex items-center space-x-3 p-3 rounded-lg bg-gray-50 cursor-pointer transition-colors duration-200;
}

.flow-step:hover {
  @apply bg-gray-100;
}

.flow-step.active {
  @apply bg-blue-50;
}

.flow-step.completed {
  @apply bg-green-50;
}

.step-number {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium text-white bg-gray-400 flex-shrink-0;
}

.flow-step.active .step-number {
  @apply bg-blue-500;
}

.flow-step.completed .step-number {
  @apply bg-green-500;
}

.step-content {
  @apply flex-1;
}

.step-title {
  @apply text-sm font-medium text-gray-800;
}

.step-desc {
  @apply text-xs text-gray-500;
}

.step-status {
  @apply flex-shrink-0;
}

.status-icon {
  @apply text-gray-400;
}

.status-icon.active {
  @apply text-blue-500;
}

.status-icon.completed {
  @apply text-green-500;
}

.step-details {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.details-header {
  @apply mb-4 pb-3 border-b border-gray-200;
}

.details-title {
  @apply text-lg font-semibold text-gray-800;
}

.details-subtitle {
  @apply text-sm text-gray-500;
}

.details-content {
  @apply space-y-4;
}

.details-image {
  @apply flex justify-center mb-4;
}

.image-placeholder {
  @apply w-20 h-20 rounded-full flex items-center justify-center;
}

.step-1 {
  @apply bg-gradient-to-br from-blue-400 to-blue-600;
}

.step-2 {
  @apply bg-gradient-to-br from-pink-400 to-purple-600;
}

.step-3 {
  @apply bg-gradient-to-br from-green-400 to-green-600;
}

.step-4 {
  @apply bg-gradient-to-br from-yellow-400 to-orange-600;
}

.step-5 {
  @apply bg-gradient-to-br from-red-400 to-red-600;
}

.placeholder-icon {
  @apply text-4xl text-white;
}

.details-description {
  @apply space-y-2;
}

.description-point {
  @apply flex items-start space-x-2 text-sm text-gray-600;
}

.point-icon {
  @apply text-blue-500 flex-shrink-0 mt-0.5;
}

.details-tips {
  @apply bg-blue-50 rounded-lg p-3;
}

.tip-header {
  @apply flex items-center space-x-1 mb-1;
}

.tip-icon {
  @apply text-blue-500;
}

.tip-title {
  @apply text-sm font-medium text-blue-700;
}

.tip-content {
  @apply text-xs text-blue-600;
}

.navigation-buttons {
  @apply flex items-center justify-between;
}

.step-indicator {
  @apply text-xs text-gray-500;
}

.demo-status {
  @apply mt-4;
}

.progress-container {
  @apply flex items-center space-x-2;
}

.progress-label {
  @apply text-xs text-gray-500;
}

.demo-actions {
  @apply flex justify-end;
}
</style>
