<template>
  <div class="game-rules">
    <div class="rules-section">
      <h4 class="section-title">🎯 游戏目标</h4>
      <div class="section-content">
        <p>通过悉心养成萌宠，提升其属性和价值，最终兑换成有价值的代币。</p>
      </div>
    </div>

    <div class="rules-section">
      <h4 class="section-title">📋 基本规则</h4>
      <div class="section-content">
        <div class="rule-item">
          <h5 class="rule-title">1. 萌宠生命周期</h5>
          <ul class="rule-list">
            <li>每只萌宠都有健康度，需要定期照料</li>
            <li>健康度过低会影响萌宠的成长速度</li>
            <li>萌宠可以通过各种方式提升等级和属性</li>
          </ul>
        </div>

        <div class="rule-item">
          <h5 class="rule-title">2. 操作冷却时间</h5>
          <ul class="rule-list">
            <li>喂食：每次间隔3秒</li>
            <li>训练：每次间隔5秒</li>
            <li>装备道具：无冷却时间</li>
            <li>兑换代币：无冷却时间</li>
          </ul>
        </div>

        <div class="rule-item">
          <h5 class="rule-title">3. 稀有度系统</h5>
          <div class="rarity-grid">
            <div class="rarity-item common">
              <span class="rarity-name">普通</span>
              <span class="rarity-multiplier">×1.0</span>
            </div>
            <div class="rarity-item uncommon">
              <span class="rarity-name">罕见</span>
              <span class="rarity-multiplier">×1.5</span>
            </div>
            <div class="rarity-item rare">
              <span class="rarity-name">稀有</span>
              <span class="rarity-multiplier">×2.0</span>
            </div>
            <div class="rarity-item epic">
              <span class="rarity-name">史诗</span>
              <span class="rarity-multiplier">×3.0</span>
            </div>
            <div class="rarity-item legendary">
              <span class="rarity-name">传说</span>
              <span class="rarity-multiplier">×5.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rules-section">
      <h4 class="section-title">💰 代币经济</h4>
      <div class="section-content">
        <div class="rule-item">
          <h5 class="rule-title">兑换公式</h5>
          <div class="formula-box">
            <p class="formula">
              代币价值 = 基础价值 × 稀有度加成 × 健康度加成 × 装备加成
            </p>
          </div>
        </div>

        <div class="rule-item">
          <h5 class="rule-title">价值计算示例</h5>
          <div class="example-box">
            <div class="example-item">
              <span class="example-label">5级普通萌宠</span>
              <span class="example-value">≈ 50 代币</span>
            </div>
            <div class="example-item">
              <span class="example-label">10级传说萌宠</span>
              <span class="example-value">≈ 1000 代币</span>
            </div>
            <div class="example-item">
              <span class="example-label">满装备传说萌宠</span>
              <span class="example-value">≈ 2000+ 代币</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rules-section">
      <h4 class="section-title">🏆 奖励机制</h4>
      <div class="section-content">
        <div class="reward-grid">
          <div class="reward-item">
            <div class="reward-icon">🎁</div>
            <div class="reward-info">
              <h6 class="reward-title">每日登录</h6>
              <p class="reward-desc">连续登录获得游戏币奖励</p>
            </div>
          </div>

          <div class="reward-item">
            <div class="reward-icon">🏅</div>
            <div class="reward-info">
              <h6 class="reward-title">成就系统</h6>
              <p class="reward-desc">完成特定目标解锁奖励</p>
            </div>
          </div>

          <div class="reward-item">
            <div class="reward-icon">🎪</div>
            <div class="reward-info">
              <h6 class="reward-title">活动奖励</h6>
              <p class="reward-desc">参与限时活动获得稀有道具</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rules-section">
      <h4 class="section-title">⚠️ 重要提醒</h4>
      <div class="section-content">
        <div class="warning-box">
          <div class="warning-item">
            <van-icon name="warning-o" class="warning-icon" />
            <span class="warning-text">代币兑换是不可逆操作，萌宠兑换后将永久消失</span>
          </div>
          <div class="warning-item">
            <van-icon name="warning-o" class="warning-icon" />
            <span class="warning-text">所有区块链交易需要支付Gas费用</span>
          </div>
          <div class="warning-item">
            <van-icon name="warning-o" class="warning-icon" />
            <span class="warning-text">请妥善保管钱包私钥，避免资产损失</span>
          </div>
          <div class="warning-item">
            <van-icon name="warning-o" class="warning-icon" />
            <span class="warning-text">定期备份游戏数据，防止意外丢失</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件主要是静态内容展示，不需要复杂的逻辑
</script>

<style scoped>
.game-rules {
  @apply space-y-6;
}

.rules-section {
  @apply bg-gray-50 rounded-lg p-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.section-content {
  @apply space-y-4;
}

.rule-item {
  @apply space-y-2;
}

.rule-title {
  @apply font-semibold text-gray-800;
}

.rule-list {
  @apply ml-4 space-y-1;
}

.rule-list li {
  @apply text-sm text-gray-600 list-disc;
}

.rarity-grid {
  @apply grid grid-cols-1 gap-2 mt-2;
}

.rarity-item {
  @apply flex justify-between items-center px-3 py-2 rounded-lg text-sm font-medium;
}

.rarity-item.common {
  @apply bg-gray-200 text-gray-800;
}

.rarity-item.uncommon {
  @apply bg-green-200 text-green-800;
}

.rarity-item.rare {
  @apply bg-blue-200 text-blue-800;
}

.rarity-item.epic {
  @apply bg-purple-200 text-purple-800;
}

.rarity-item.legendary {
  @apply bg-yellow-200 text-yellow-800;
}

.formula-box {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-3 mt-2;
}

.formula {
  @apply text-sm text-blue-800 font-mono text-center;
}

.example-box {
  @apply space-y-2 mt-2;
}

.example-item {
  @apply flex justify-between items-center bg-white rounded-lg px-3 py-2 text-sm;
}

.example-label {
  @apply text-gray-600;
}

.example-value {
  @apply font-semibold text-green-600;
}

.reward-grid {
  @apply space-y-3;
}

.reward-item {
  @apply flex items-start space-x-3 bg-white rounded-lg p-3;
}

.reward-icon {
  @apply text-2xl flex-shrink-0;
}

.reward-info {
  @apply flex-1;
}

.reward-title {
  @apply font-semibold text-gray-800 mb-1;
}

.reward-desc {
  @apply text-sm text-gray-600;
}

.warning-box {
  @apply space-y-3;
}

.warning-item {
  @apply flex items-start space-x-2 bg-orange-50 border border-orange-200 rounded-lg p-3;
}

.warning-icon {
  @apply text-orange-500 flex-shrink-0 mt-0.5;
}

.warning-text {
  @apply text-sm text-orange-700;
}
</style>