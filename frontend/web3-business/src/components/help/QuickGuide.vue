<template>
  <div class="quick-guide">
    <div class="guide-section">
      <h4 class="section-title">🚀 快速开始</h4>
      <div class="guide-steps">
        <div class="guide-step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h5 class="step-title">连接钱包</h5>
            <p class="step-description">点击"连接钱包"按钮，选择你的Web3钱包进行连接</p>
          </div>
        </div>

        <div class="guide-step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h5 class="step-title">创建萌宠</h5>
            <p class="step-description">首次进入游戏会自动创建你的第一只萌宠</p>
          </div>
        </div>

        <div class="guide-step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h5 class="step-title">养成萌宠</h5>
            <p class="step-description">通过喂食、训练、装备道具来提升萌宠属性</p>
          </div>
        </div>

        <div class="guide-step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h5 class="step-title">兑换代币</h5>
            <p class="step-description">当萌宠达到理想状态时，可以兑换成有价值的代币</p>
          </div>
        </div>
      </div>
    </div>

    <div class="guide-section">
      <h4 class="section-title">💡 重要提示</h4>
      <div class="tips-list">
        <div class="tip-item">
          <van-icon name="info-o" class="tip-icon" />
          <span class="tip-text">萌宠的稀有度越高，兑换价值越大</span>
        </div>
        <div class="tip-item">
          <van-icon name="info-o" class="tip-icon" />
          <span class="tip-text">保持萌宠健康度有助于更好的成长</span>
        </div>
        <div class="tip-item">
          <van-icon name="info-o" class="tip-icon" />
          <span class="tip-text">装备可以显著提升萌宠的综合属性</span>
        </div>
        <div class="tip-item">
          <van-icon name="info-o" class="tip-icon" />
          <span class="tip-text">兑换代币是不可逆操作，请谨慎考虑</span>
        </div>
      </div>
    </div>

    <div class="guide-section">
      <h4 class="section-title">🎯 快捷操作</h4>
      <div class="quick-actions">
        <van-button
          type="primary"
          size="small"
          @click="goToTutorial"
          class="action-button"
        >
          查看完整教程
        </van-button>
        <van-button
          type="success"
          size="small"
          @click="goToShop"
          class="action-button"
        >
          前往商店
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToTutorial = () => {
  router.push('/tutorial')
}

const goToShop = () => {
  router.push('/shop')
}
</script>

<style scoped>
.quick-guide {
  @apply space-y-6;
}

.guide-section {
  @apply bg-gray-50 rounded-lg p-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.guide-steps {
  @apply space-y-4;
}

.guide-step {
  @apply flex items-start space-x-3;
}

.step-number {
  @apply w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold flex-shrink-0;
}

.step-content {
  @apply flex-1;
}

.step-title {
  @apply font-semibold text-gray-800 mb-1;
}

.step-description {
  @apply text-sm text-gray-600 leading-relaxed;
}

.tips-list {
  @apply space-y-3;
}

.tip-item {
  @apply flex items-start space-x-2;
}

.tip-icon {
  @apply text-blue-500 flex-shrink-0 mt-0.5;
}

.tip-text {
  @apply text-sm text-gray-700;
}

.quick-actions {
  @apply flex space-x-3;
}

.action-button {
  @apply flex-1;
}
</style>
