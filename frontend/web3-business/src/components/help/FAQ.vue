<template>
  <div class="faq">
    <van-collapse v-model="activeNames" accordion>
      <van-collapse-item
        v-for="faq in faqList"
        :key="faq.id"
        :title="faq.question"
        :name="faq.id"
        class="faq-item"
      >
        <div class="faq-answer" v-html="faq.answer"></div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const activeNames = ref(['basic'])

const faqList = ref([
  {
    id: 'basic',
    question: '🎮 什么是萌宠养成代币游戏？',
    answer: `
      <p>这是一个基于区块链技术的萌宠养成游戏。你可以：</p>
      <ul>
        <li>创建和养成可爱的虚拟萌宠</li>
        <li>通过各种方式提升萌宠属性</li>
        <li>将养成的萌宠兑换成真实的代币</li>
        <li>使用代币购买道具或进行交易</li>
      </ul>
    `
  },
  {
    id: 'wallet',
    question: '💳 如何连接钱包？',
    answer: `
      <p>支持以下钱包连接方式：</p>
      <ul>
        <li><strong>MetaMask</strong>：最常用的浏览器钱包</li>
        <li><strong>WalletConnect</strong>：支持多种移动端钱包</li>
      </ul>
      <p>确保你的钱包已连接到正确的测试网络。</p>
    `
  },
  {
    id: 'pet-attributes',
    question: '🐱 萌宠属性有哪些？',
    answer: `
      <p>每只萌宠都有以下属性：</p>
      <ul>
        <li><strong>等级</strong>：影响萌宠的整体能力</li>
        <li><strong>稀有度</strong>：普通、稀有、史诗、传说</li>
        <li><strong>健康度</strong>：影响成长速度和状态</li>
        <li><strong>经验值</strong>：用于升级的积累值</li>
        <li><strong>装备</strong>：可以增强萌宠属性的道具</li>
      </ul>
    `
  },
  {
    id: 'nurturing',
    question: '🍼 如何养成萌宠？',
    answer: `
      <p>可以通过以下方式养成萌宠：</p>
      <ul>
        <li><strong>喂食</strong>：增加健康度和经验值</li>
        <li><strong>训练</strong>：快速提升经验值，但会消耗健康度</li>
        <li><strong>装备道具</strong>：永久性提升萌宠属性</li>
        <li><strong>休息</strong>：恢复萌宠的健康度</li>
      </ul>
      <p>注意：不同操作有冷却时间限制。</p>
    `
  },
  {
    id: 'token-exchange',
    question: '💰 如何兑换代币？',
    answer: `
      <p>代币兑换流程：</p>
      <ol>
        <li>确保萌宠达到兑换条件（通常需要一定等级）</li>
        <li>点击"兑换代币"按钮</li>
        <li>查看预估收益和兑换详情</li>
        <li>确认交易并支付Gas费用</li>
        <li>等待区块链确认</li>
      </ol>
      <p><strong>重要</strong>：兑换后萌宠会消失，此操作不可逆！</p>
    `
  },
  {
    id: 'value-calculation',
    question: '📊 萌宠价值如何计算？',
    answer: `
      <p>萌宠价值由以下因素决定：</p>
      <ul>
        <li><strong>基础价值</strong>：基于萌宠等级</li>
        <li><strong>稀有度加成</strong>：传说级别可获得5倍加成</li>
        <li><strong>健康度加成</strong>：健康度越高加成越多</li>
        <li><strong>装备加成</strong>：装备的稀有度和数量</li>
      </ul>
      <p>最终价值 = 基础价值 × 各种加成系数</p>
    `
  },
  {
    id: 'shop',
    question: '🛒 如何使用商店？',
    answer: `
      <p>商店功能：</p>
      <ul>
        <li>使用游戏币或代币购买道具</li>
        <li>道具包括食物、玩具、装备等</li>
        <li>不同道具有不同的效果和稀有度</li>
        <li>购买的道具会自动添加到背包</li>
      </ul>
      <p>建议优先购买能提升萌宠属性的装备。</p>
    `
  },
  {
    id: 'data-safety',
    question: '🔒 数据安全吗？',
    answer: `
      <p>数据安全保障：</p>
      <ul>
        <li>游戏数据存储在本地浏览器中</li>
        <li>支持数据备份和恢复功能</li>
        <li>代币交易通过智能合约保障安全</li>
        <li>不会收集或存储个人敏感信息</li>
      </ul>
      <p>建议定期备份游戏数据以防丢失。</p>
    `
  },
  {
    id: 'troubleshooting',
    question: '🔧 遇到问题怎么办？',
    answer: `
      <p>常见问题解决方案：</p>
      <ul>
        <li><strong>钱包连接失败</strong>：检查网络设置和钱包状态</li>
        <li><strong>交易失败</strong>：确保有足够的Gas费用</li>
        <li><strong>数据丢失</strong>：尝试从备份恢复数据</li>
        <li><strong>页面卡顿</strong>：刷新页面或清除浏览器缓存</li>
      </ul>
      <p>如果问题仍然存在，请联系技术支持。</p>
    `
  }
])
</script>

<style scoped>
.faq {
  @apply space-y-2;
}

.faq-item :deep(.van-collapse-item__title) {
  @apply font-medium text-gray-800;
}

.faq-answer {
  @apply text-sm text-gray-600 leading-relaxed;
}

.faq-answer :deep(p) {
  @apply mb-3;
}

.faq-answer :deep(ul),
.faq-answer :deep(ol) {
  @apply ml-4 mb-3 space-y-1;
}

.faq-answer :deep(li) {
  @apply list-disc;
}

.faq-answer :deep(ol li) {
  @apply list-decimal;
}

.faq-answer :deep(strong) {
  @apply font-semibold text-gray-800;
}
</style>