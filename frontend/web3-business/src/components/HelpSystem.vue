<template>
  <div class="help-system">
    <!-- 帮助按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="question-o"
      @click="toggleHelp"
      class="help-button"
    />

    <!-- 帮助面板 -->
    <van-popup
      v-model:show="showHelp"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="help-panel">
        <div class="help-header">
          <h3 class="help-title">游戏帮助</h3>
          <div class="help-tabs">
            <van-tabs v-model:active="activeTab" type="card">
              <van-tab title="快速指南" name="guide">
                <div class="tab-content">
                  <QuickGuide />
                </div>
              </van-tab>
              <van-tab title="常见问题" name="faq">
                <div class="tab-content">
                  <FAQ />
                </div>
              </van-tab>
              <van-tab title="游戏规则" name="rules">
                <div class="tab-content">
                  <GameRules />
                </div>
              </van-tab>
            </van-tabs>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import QuickGuide from './help/QuickGuide.vue'
import FAQ from './help/FAQ.vue'
import GameRules from './help/GameRules.vue'

// Ensure the component is properly exported
defineExports({})

// 状态
const showHelp = ref(false)
const activeTab = ref('guide')

// 方法
const toggleHelp = () => {
  showHelp.value = !showHelp.value
}
</script>

<style scoped>
.help-system {
  @apply relative;
}

.help-button {
  @apply bg-blue-500 text-white shadow-lg;
}

.help-button :deep(.van-floating-bubble) {
  @apply bg-blue-500;
}

.help-panel {
  @apply h-full flex flex-col;
}

.help-header {
  @apply p-4 border-b border-gray-200;
}

.help-title {
  @apply text-xl font-semibold text-gray-800 mb-4 text-center;
}

.help-tabs {
  @apply flex-1;
}

.tab-content {
  @apply p-4 h-full overflow-y-auto;
}
</style>
