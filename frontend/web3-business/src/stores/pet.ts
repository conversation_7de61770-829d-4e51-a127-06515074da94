import { defineStore } from 'pinia'
import { ref, computed, watch, readonly } from 'vue'
import { PetRarity, PetStatus, PetMood } from '../types/typesWithoutCircular'
import type { Pet, Equipment } from '../types/typesWithoutCircular'
import { PetValidator, PetStatsCalculator, PetSerializer } from '../types/pet'
import type { StatCalculationContext } from '../types/pet'
import { petFactory } from '../utils/petFactory'
import { dataPersistenceService } from '../services/data-persistence.service'
import { petGrowthSystem } from '../utils/petGrowthSystem'
import type { LevelUpResult } from '../utils/petGrowthSystem'

export const usePetStore = defineStore('pet', () => {
  // 状态
  const pets = ref<Pet[]>([])
  const currentPetId = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 工具实例
  const validator = new PetValidator()
  const statsCalculator = new PetStatsCalculator()

  // 计算属性
  const currentPet = computed(() => {
    if (!currentPetId.value) return null
    return pets.value.find(pet => pet.id === currentPetId.value) || null
  })

  const petCount = computed(() => pets.value.length)

  const currentPetStats = computed(() => {
    if (!currentPet.value) return null

    const context: StatCalculationContext = {
      pet: currentPet.value,
      equipment: currentPet.value.equipment,
      buffs: [], // TODO: 实现buff系统
      environment: [] // TODO: 实现环境系统
    }

    return statsCalculator.calculateTotalStats(context)
  })

  const currentPetTokenValue = computed(() => {
    if (!currentPet.value) return '0'
    return statsCalculator.calculateTokenValue(currentPet.value)
  })

  const petsByRarity = computed(() => {
    const grouped: Record<PetRarity, Pet[]> = {
      [PetRarity.COMMON]: [],
      [PetRarity.UNCOMMON]: [],
      [PetRarity.RARE]: [],
      [PetRarity.EPIC]: [],
      [PetRarity.LEGENDARY]: [],
      [PetRarity.MYTHICAL]: []
    }

    pets.value.forEach(pet => {
      grouped[pet.rarity].push(pet)
    })

    return grouped
  })

  // 基础方法
  const addPet = (pet: Pet) => {
    // 验证萌宠数据
    const validation = validator.validatePet(pet)
    if (!validation.isValid) {
      error.value = validation.errors.join(', ')
      throw new Error(`萌宠数据验证失败: ${validation.errors.join(', ')}`)
    }

    pets.value.push(pet)
    if (!currentPetId.value) {
      currentPetId.value = pet.id
    }
    error.value = null
  }

  const updatePet = (petId: string, updates: Partial<Pet>) => {
    const index = pets.value.findIndex(pet => pet.id === petId)
    if (index !== -1) {
      const updatedPet = { ...pets.value[index], ...updates }

      // 验证更新后的萌宠数据
      const validation = validator.validatePet(updatedPet)
      if (!validation.isValid) {
        error.value = validation.errors.join(', ')
        throw new Error(`萌宠更新验证失败: ${validation.errors.join(', ')}`)
      }

      // 更新最后修改时间
      updatedPet.metadata.lastModified = Date.now()

      pets.value[index] = updatedPet
      error.value = null
    }
  }

  const removePet = (petId: string) => {
    const index = pets.value.findIndex(pet => pet.id === petId)
    if (index !== -1) {
      pets.value.splice(index, 1)
      if (currentPetId.value === petId) {
        currentPetId.value = pets.value.length > 0 ? pets.value[0].id : null
      }
    }
  }

  const setCurrentPet = (petId: string | null) => {
    currentPetId.value = petId
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearPets = () => {
    pets.value = []
    currentPetId.value = null
    error.value = null
  }

  // 萌宠生成方法
  const generateRandomPet = (options = {}) => {
    try {
      const pet = petFactory.generateRandomPet(options)
      addPet(pet)
      return pet
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成萌宠失败'
      throw err
    }
  }

  const createCustomPet = (options: any) => {
    try {
      const pet = petFactory.createPet(options)
      addPet(pet)
      return pet
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建萌宠失败'
      throw err
    }
  }

  // 萌宠养成方法
  const feedPet = (petId: string, foodItem?: any) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    const now = Date.now()
    const timeSinceLastFeed = now - pet.lastFeedTime
    const minFeedInterval = 30 * 60 * 1000 // 30分钟

    if (timeSinceLastFeed < minFeedInterval) {
      error.value = '萌宠还不饿，请稍后再喂食'
      return false
    }

    // 计算喂食效果
    const healthGain = Math.min(20, pet.maxHealth - pet.health)
    const happinessGain = Math.min(10, pet.maxHappiness - pet.happiness)
    const experienceGain = 5

    updatePet(petId, {
      health: pet.health + healthGain,
      happiness: pet.happiness + happinessGain,
      experience: pet.experience + experienceGain,
      lastFeedTime: now
    })

    return true
  }

  const trainPet = (petId: string, trainingType: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    if (pet.energy < 20) {
      error.value = '萌宠能量不足，无法训练'
      return false
    }

    const now = Date.now()
    const timeSinceLastTrain = now - pet.lastTrainTime
    const minTrainInterval = 60 * 60 * 1000 // 1小时

    if (timeSinceLastTrain < minTrainInterval) {
      error.value = '萌宠需要休息，请稍后再训练'
      return false
    }

    // 计算训练效果
    const experienceGain = 15
    const energyCost = 20
    const statGain = 2

    const statUpdates: any = {
      experience: pet.experience + experienceGain,
      energy: pet.energy - energyCost,
      lastTrainTime: now
    }

    // 根据训练类型增加对应属性
    switch (trainingType) {
      case 'strength':
        statUpdates.stats = { ...pet.stats, strength: pet.stats.strength + statGain }
        break
      case 'intelligence':
        statUpdates.stats = { ...pet.stats, intelligence: pet.stats.intelligence + statGain }
        break
      case 'agility':
        statUpdates.stats = { ...pet.stats, agility: pet.stats.agility + statGain }
        break
      default:
        // 综合训练，随机提升一个属性
        const stats = ['strength', 'intelligence', 'agility', 'charm', 'vitality']
        const randomStat = stats[Math.floor(Math.random() * stats.length)]
        statUpdates.stats = { ...pet.stats, [randomStat]: pet.stats[randomStat as keyof typeof pet.stats] + statGain }
    }

    updatePet(petId, statUpdates)
    return true
  }

  const restPet = (petId: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    const now = Date.now()
    const energyGain = Math.min(30, pet.maxEnergy - pet.energy)
    const healthGain = Math.min(10, pet.maxHealth - pet.health)

    updatePet(petId, {
      energy: pet.energy + energyGain,
      health: pet.health + healthGain,
      lastRestTime: now
    })

    return true
  }

  // 萌宠装备方法
  const equipItem = (petId: string, equipment: Equipment) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    // 检查是否已装备同类型装备
    const existingIndex = pet.equipment.findIndex(item => item.type === equipment.type)

    if (existingIndex !== -1) {
      // 替换现有装备
      pet.equipment[existingIndex] = equipment
    } else {
      // 添加新装备
      pet.equipment.push(equipment)
    }

    updatePet(petId, { equipment: [...pet.equipment] })
    return true
  }

  const unequipItem = (petId: string, equipmentId: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    const newEquipment = pet.equipment.filter(item => item.id !== equipmentId)
    updatePet(petId, { equipment: newEquipment })
    return true
  }

  // 萌宠序列化方法
  const exportPet = (petId: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return null
    }

    try {
      return PetSerializer.serialize(pet)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '导出萌宠失败'
      return null
    }
  }

  const importPet = (serializedPet: any) => {
    try {
      const pet = PetSerializer.deserialize(serializedPet)
      addPet(pet)
      return pet
    } catch (err) {
      error.value = err instanceof Error ? err.message : '导入萌宠失败'
      throw err
    }
  }

  // 萌宠升级检查 - 使用新的成长系统
  const checkLevelUp = (petId: string): LevelUpResult | null => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) return null

    const levelUpResult = petGrowthSystem.performLevelUp(pet)

    if (levelUpResult.success) {
      // 计算剩余经验
      const remainingExperience = pet.experience - pet.maxExperience

      // 应用升级结果
      const newStats = {
        strength: pet.stats.strength + (levelUpResult.statGains.strength || 0),
        intelligence: pet.stats.intelligence + (levelUpResult.statGains.intelligence || 0),
        agility: pet.stats.agility + (levelUpResult.statGains.agility || 0),
        charm: pet.stats.charm + (levelUpResult.statGains.charm || 0),
        vitality: pet.stats.vitality + (levelUpResult.statGains.vitality || 0),
        luck: pet.stats.luck + (levelUpResult.statGains.luck || 0)
      }

      // 更新成长阶段
      const newGrowthStage = petGrowthSystem.determineGrowthStage(levelUpResult.newLevel)

      updatePet(petId, {
        level: levelUpResult.newLevel,
        experience: remainingExperience,
        maxExperience: levelUpResult.newMaxValues.experience,
        stats: newStats,
        maxHealth: levelUpResult.newMaxValues.health,
        maxHappiness: levelUpResult.newMaxValues.happiness,
        maxEnergy: levelUpResult.newMaxValues.energy,
        health: Math.min(pet.health + 20, levelUpResult.newMaxValues.health),
        happiness: Math.min(pet.happiness + 15, levelUpResult.newMaxValues.happiness),
        energy: Math.min(pet.energy + 10, levelUpResult.newMaxValues.energy),
        growthStage: newGrowthStage,
        mood: PetMood.HAPPY // 升级时心情变好
      })

      // 如果有多余经验，递归检查是否可以继续升级
      if (remainingExperience >= levelUpResult.newMaxValues.experience) {
        return checkLevelUp(petId)
      }
    }

    return levelUpResult
  }

  // 时间相关的成长更新
  const updateTimeBasedGrowth = (petId: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) return

    const timeUpdates = petGrowthSystem.calculateTimeBasedGrowth(pet)

    if (Object.keys(timeUpdates).length > 0) {
      updatePet(petId, timeUpdates)

      // 如果获得了经验，检查升级
      if (timeUpdates.experience) {
        checkLevelUp(petId)
      }
    }
  }

  // 获取萌宠成长里程碑
  const getPetMilestones = (petId: string) => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) return []

    return petGrowthSystem.getGrowthMilestones(pet)
  }

  // 获取萌宠成长事件历史
  const getPetGrowthHistory = (petId: string) => {
    return petGrowthSystem.getGrowthEvents(petId)
  }

  // 检查萌宠是否可以进化
  const canPetEvolve = (petId: string): boolean => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) return false

    return petGrowthSystem.checkEvolutionAvailability(pet)
  }

  // 启动自动成长系统
  const startAutoGrowthSystem = () => {
    // 为所有萌宠启动自动成长
    const intervals: number[] = []

    const updateAllPets = () => {
      pets.value.forEach(pet => {
        updateTimeBasedGrowth(pet.id)
      })
    }

    // 每分钟更新一次
    const mainInterval = setInterval(updateAllPets, 60 * 1000)
    intervals.push(mainInterval)

    // 每10分钟清理过期事件
    const cleanupInterval = setInterval(() => {
      petGrowthSystem.cleanupOldEvents()
    }, 10 * 60 * 1000)
    intervals.push(cleanupInterval)

    return () => {
      intervals.forEach(interval => clearInterval(interval))
    }
  }

  // 萌宠查找方法
  const getPetById = (petId: string): Pet | undefined => {
    return pets.value.find(pet => pet.id === petId)
  }

  // 萌宠名称更新
  const updatePetName = async (petId: string, newName: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    if (!validator.validateName(newName)) {
      error.value = '萌宠名称格式不正确'
      return false
    }

    updatePet(petId, { name: newName })
    return true
  }

  // 萌宠玩耍
  const playWithPet = async (petId: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    if (pet.energy < 15) {
      error.value = '萌宠能量不足，无法玩耍'
      return false
    }

    const now = Date.now()
    const timeSinceLastPlay = now - pet.lastPlayTime
    const minPlayInterval = 45 * 60 * 1000 // 45分钟

    if (timeSinceLastPlay < minPlayInterval) {
      error.value = '萌宠需要休息，请稍后再玩耍'
      return false
    }

    const happinessGain = Math.min(25, pet.maxHappiness - pet.happiness)
    const energyCost = 15
    const experienceGain = 8

    updatePet(petId, {
      happiness: pet.happiness + happinessGain,
      energy: pet.energy - energyCost,
      experience: pet.experience + experienceGain,
      lastPlayTime: now,
      mood: PetMood.HAPPY
    })

    return true
  }

  // 学习技能
  const learnSkill = async (petId: string, skillId: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    // 检查是否已经学会该技能
    if (pet.skills.some(skill => skill.id === skillId)) {
      error.value = '萌宠已经学会了这个技能'
      return false
    }

    // 简单的技能学习逻辑
    const newSkill = {
      id: skillId,
      name: `技能${skillId}`,
      icon: '⚡',
      type: 'active',
      level: 1,
      experience: 0,
      maxExperience: 100,
      description: '新学会的技能',
      effects: [],
      cooldown: 0,
      lastUsed: 0
    }

    const updatedSkills = [...pet.skills, newSkill]
    updatePet(petId, { skills: updatedSkills })
    return true
  }

  // 升级特质
  const upgradeTrait = async (petId: string, traitId: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    const traitIndex = pet.traits.findIndex(trait => trait.id === traitId)
    if (traitIndex === -1) {
      error.value = '特质不存在'
      return false
    }

    const trait = pet.traits[traitIndex]
    if (trait.level >= 10) {
      error.value = '特质已达到最高等级'
      return false
    }

    const requiredExp = trait.level * 100
    if (pet.experience < requiredExp) {
      error.value = '经验不足，无法升级特质'
      return false
    }

    const updatedTraits = [...pet.traits]
    updatedTraits[traitIndex] = {
      ...trait,
      level: trait.level + 1
    }

    updatePet(petId, {
      traits: updatedTraits,
      experience: pet.experience - requiredExp
    })

    return true
  }

  // 计算萌宠价值
  const calculatePetValue = async (petId: string): Promise<string> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return '0'
    }

    return statsCalculator.calculateTokenValue(pet)
  }

  // 兑换代币
  const exchangePetForTokens = async (petId: string, userAddress: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    try {
      // 使用代币兑换服务
      const { tokenExchangeService } = await import('../services/token-exchange.service')

      const transaction = await tokenExchangeService.exchangePetForTokens(
        pet,
        userAddress,
        (step: string) => {
          console.log('兑换进度:', step)
        }
      )

      if (transaction.status === 'confirmed') {
        // 兑换成功，移除萌宠
        removePet(petId)
        return true
      } else {
        error.value = '兑换失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '兑换失败'
      return false
    }
  }

  // 进化萌宠
  const evolvePet = async (petId: string, evolutionPath: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    // 简单的进化逻辑
    updatePet(petId, {
      level: pet.level + 5,
      rarity: pet.rarity === PetRarity.COMMON ? PetRarity.UNCOMMON : pet.rarity,
      mood: PetMood.EXCITED
    })

    return true
  }

  // 重置萌宠
  const resetPet = async (petId: string): Promise<boolean> => {
    const pet = pets.value.find(p => p.id === petId)
    if (!pet) {
      error.value = '萌宠不存在'
      return false
    }

    // 重置到初始状态
    const resetPet = petFactory.generateRandomPet({
      type: pet.type,
      rarity: PetRarity.COMMON,
      name: pet.name
    })

    updatePet(petId, {
      level: 1,
      experience: 0,
      stats: resetPet.stats,
      health: resetPet.health,
      happiness: resetPet.happiness,
      energy: resetPet.energy,
      skills: [],
      traits: [],
      equipment: [],
      rarity: PetRarity.COMMON,
      status: PetStatus.HEALTHY,
      mood: PetMood.CONTENT
    })

    return true
  }

  // 数据持久化方法
  const savePetData = async (): Promise<boolean> => {
    try {
      return await dataPersistenceService.savePetData(pets.value, currentPetId.value)
    } catch (error) {
      console.error('保存萌宠数据失败:', error)
      return false
    }
  }

  const loadPetData = async (): Promise<boolean> => {
    try {
      setLoading(true)
      const data = await dataPersistenceService.loadPetData()

      if (data) {
        pets.value = data.pets
        currentPetId.value = data.currentPetId
        console.log('萌宠数据加载成功')
        return true
      }

      return false
    } catch (error) {
      console.error('加载萌宠数据失败:', error)
      setError('加载萌宠数据失败')
      return false
    } finally {
      setLoading(false)
    }
  }

  // 自动保存监听（防抖）
  let saveTimer: number | null = null
  const debouncedSave = () => {
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    saveTimer = window.setTimeout(() => {
      savePetData()
    }, 1000) // 1秒防抖
  }

  // 监听数据变化自动保存
  watch([pets, currentPetId], () => {
    debouncedSave()
  }, { deep: true })

  return {
    // 状态
    pets: readonly(pets),
    currentPetId: readonly(currentPetId),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // 计算属性
    currentPet,
    petCount,
    currentPetStats,
    currentPetTokenValue,
    petsByRarity,

    // 基础方法
    addPet,
    updatePet,
    removePet,
    setCurrentPet,
    setLoading,
    setError,
    clearPets,

    // 萌宠生成方法
    generateRandomPet,
    createCustomPet,

    // 萌宠养成方法
    feedPet,
    trainPet,
    restPet,

    // 萌宠装备方法
    equipItem,
    unequipItem,

    // 萌宠查找方法
    getPetById,

    // 萌宠交互方法
    updatePetName,
    playWithPet,
    learnSkill,
    upgradeTrait,
    calculatePetValue,
    exchangePetForTokens,
    evolvePet,
    resetPet,

    // 萌宠序列化方法
    exportPet,
    importPet,

    // 萌宠升级方法
    checkLevelUp,

    // 时间相关成长方法
    updateTimeBasedGrowth,
    getPetMilestones,
    getPetGrowthHistory,
    canPetEvolve,
    startAutoGrowthSystem,

    // 工具方法
    validator,
    statsCalculator,

    // 持久化方法
    savePetData,
    loadPetData
  }
})
