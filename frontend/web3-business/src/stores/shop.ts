import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ItemType, PetRarity, EquipmentType } from '../types/typesWithoutCircular'
import type { Item, Equipment } from '../types/typesWithoutCircular'
import { ItemFactory } from '../types/item'
import { EquipmentFactory } from '../types/equipment'
import { useGameStore } from './game'

export interface ShopItem {
  id: string
  name: string
  description: string
  price: number
  currency: 'coins' | 'tokens'
  category: 'food' | 'toy' | 'equipment' | 'decoration' | 'medicine' | 'training'
  type: ItemType | EquipmentType
  rarity: PetRarity
  stock: number
  maxStock: number
  icon: string
  effects?: {
    health?: number
    happiness?: number
    experience?: number
    energy?: number
  }
  requirements?: {
    level?: number
    rarity?: PetRarity
  }
  isLimited: boolean
  refreshTime?: number
}

export interface ShopCategory {
  id: string
  name: string
  icon: string
  description: string
  items: ShopItem[]
}

export interface PurchaseHistory {
  id: string
  itemId: string
  itemName: string
  quantity: number
  totalPrice: number
  currency: 'coins' | 'tokens'
  purchasedAt: Date
}

export const useShopStore = defineStore('shop', () => {
  // 状态
  const shopItems = ref<ShopItem[]>([])
  const categories = ref<ShopCategory[]>([])
  const purchaseHistory = ref<PurchaseHistory[]>([])
  const lastRefreshTime = ref<number>(0)
  const isLoading = ref(false)

  // 获取游戏状态
  const gameStore = useGameStore()

  // 计算属性
  const availableItems = computed(() =>
    shopItems.value.filter(item => item.stock > 0)
  )

  const itemsByCategory = computed(() => {
    const grouped: Record<string, ShopItem[]> = {}
    shopItems.value.forEach(item => {
      if (!grouped[item.category]) {
        grouped[item.category] = []
      }
      grouped[item.category].push(item)
    })
    return grouped
  })

  const limitedTimeItems = computed(() =>
    shopItems.value.filter(item => item.isLimited && item.stock > 0)
  )

  // 初始化商店
  const initializeShop = () => {
    if (shopItems.value.length === 0) {
      generateShopItems()
      generateCategories()
    }
  }

  // 生成商店物品
  const generateShopItems = () => {
    const items: ShopItem[] = []

    // 食物类
    items.push(
      createShopItem('basic_food', '普通饲料', '基础的宠物食物', 10, 'coins', 'food', ItemType.FOOD, PetRarity.COMMON, 99),
      createShopItem('premium_food', '高级饲料', '营养丰富的高级食物', 25, 'coins', 'food', ItemType.FOOD, PetRarity.UNCOMMON, 50),
      createShopItem('deluxe_food', '豪华大餐', '顶级的美味佳肴', 50, 'coins', 'food', ItemType.FOOD, PetRarity.RARE, 20)
    )

    // 玩具类
    items.push(
      createShopItem('basic_toy', '小球', '简单的玩具球', 15, 'coins', 'toy', ItemType.TOY, PetRarity.COMMON, 80),
      createShopItem('magic_toy', '魔法飞盘', '带有魔法效果的飞盘', 40, 'coins', 'toy', ItemType.TOY, PetRarity.UNCOMMON, 30),
      createShopItem('legendary_toy', '传说玩具', '拥有神奇力量的玩具', 100, 'coins', 'toy', ItemType.TOY, PetRarity.EPIC, 5)
    )

    // 装备类
    items.push(
      createShopItem('basic_weapon', '新手武器', '适合初学者的武器', 50, 'coins', 'equipment', EquipmentType.WEAPON, PetRarity.COMMON, 20),
      createShopItem('steel_armor', '钢铁护甲', '坚固的防护装备', 80, 'coins', 'equipment', EquipmentType.ARMOR, PetRarity.UNCOMMON, 15),
      createShopItem('magic_helmet', '魔法头盔', '蕴含魔力的头盔', 120, 'coins', 'equipment', EquipmentType.HELMET, PetRarity.RARE, 8)
    )

    // 药品类
    items.push(
      createShopItem('health_potion', '治疗药水', '恢复健康的药水', 20, 'coins', 'medicine', ItemType.MEDICINE, PetRarity.COMMON, 60),
      createShopItem('energy_drink', '能量饮料', '快速恢复能量', 30, 'coins', 'medicine', ItemType.MEDICINE, PetRarity.UNCOMMON, 40),
      createShopItem('revival_elixir', '复活灵药', '强力的恢复药剂', 100, 'coins', 'medicine', ItemType.MEDICINE, PetRarity.RARE, 10)
    )

    // 训练用品
    items.push(
      createShopItem('training_manual', '训练手册', '提升经验的训练书', 35, 'coins', 'training', ItemType.TRAINING_ITEM, PetRarity.COMMON, 25),
      createShopItem('master_guide', '大师指南', '高级训练教程', 75, 'coins', 'training', ItemType.TRAINING_ITEM, PetRarity.UNCOMMON, 12),
      createShopItem('legendary_scroll', '传说秘籍', '蕴含古老智慧的卷轴', 200, 'coins', 'training', ItemType.TRAINING_ITEM, PetRarity.EPIC, 3)
    )

    // 限时物品
    items.push(
      createShopItem('daily_special', '每日特惠', '限时特价商品', 5, 'coins', 'food', ItemType.FOOD, PetRarity.COMMON, 10, true),
      createShopItem('weekly_premium', '周度精选', '每周限量精品', 150, 'coins', 'equipment', EquipmentType.ACCESSORY, PetRarity.LEGENDARY, 2, true)
    )

    shopItems.value = items
  }

  // 创建商店物品
  const createShopItem = (
    id: string,
    name: string,
    description: string,
    price: number,
    currency: 'coins' | 'tokens',
    category: ShopItem['category'],
    type: ItemType | EquipmentType,
    rarity: PetRarity,
    stock: number,
    isLimited: boolean = false
  ): ShopItem => {
    return {
      id,
      name,
      description,
      price,
      currency,
      category,
      type,
      rarity,
      stock,
      maxStock: stock,
      icon: getItemIcon(category, rarity),
      effects: getItemEffects(category, rarity),
      requirements: getItemRequirements(rarity),
      isLimited,
      refreshTime: isLimited ? Date.now() + 24 * 60 * 60 * 1000 : undefined // 24小时后刷新
    }
  }

  // 获取物品图标
  const getItemIcon = (category: string, rarity: PetRarity): string => {
    const icons = {
      food: '🍖',
      toy: '🎾',
      equipment: '⚔️',
      medicine: '💊',
      training: '📚',
      decoration: '🎨'
    }
    return icons[category as keyof typeof icons] || '📦'
  }

  // 获取物品效果
  const getItemEffects = (category: string, rarity: PetRarity) => {
    const rarityMultiplier = {
      [PetRarity.COMMON]: 1,
      [PetRarity.UNCOMMON]: 1.5,
      [PetRarity.RARE]: 2,
      [PetRarity.EPIC]: 3,
      [PetRarity.LEGENDARY]: 4,
      [PetRarity.MYTHICAL]: 5
    }

    const multiplier = rarityMultiplier[rarity]

    switch (category) {
      case 'food':
        return {
          health: Math.floor(20 * multiplier),
          happiness: Math.floor(15 * multiplier)
        }
      case 'toy':
        return {
          happiness: Math.floor(25 * multiplier),
          energy: Math.floor(-5 * multiplier)
        }
      case 'medicine':
        return {
          health: Math.floor(40 * multiplier),
          energy: Math.floor(10 * multiplier)
        }
      case 'training':
        return {
          experience: Math.floor(50 * multiplier),
          energy: Math.floor(-15 * multiplier)
        }
      default:
        return {}
    }
  }

  // 获取物品需求
  const getItemRequirements = (rarity: PetRarity) => {
    const levelRequirements = {
      [PetRarity.COMMON]: 1,
      [PetRarity.UNCOMMON]: 5,
      [PetRarity.RARE]: 10,
      [PetRarity.EPIC]: 20,
      [PetRarity.LEGENDARY]: 30,
      [PetRarity.MYTHICAL]: 50
    }

    return {
      level: levelRequirements[rarity],
      rarity: rarity === PetRarity.LEGENDARY || rarity === PetRarity.MYTHICAL ? rarity : undefined
    }
  }

  // 生成分类
  const generateCategories = () => {
    categories.value = [
      {
        id: 'food',
        name: '食物',
        icon: '🍖',
        description: '各种美味的宠物食物',
        items: shopItems.value.filter(item => item.category === 'food')
      },
      {
        id: 'toy',
        name: '玩具',
        icon: '🎾',
        description: '有趣的玩具和娱乐用品',
        items: shopItems.value.filter(item => item.category === 'toy')
      },
      {
        id: 'equipment',
        name: '装备',
        icon: '⚔️',
        description: '提升属性的装备道具',
        items: shopItems.value.filter(item => item.category === 'equipment')
      },
      {
        id: 'medicine',
        name: '药品',
        icon: '💊',
        description: '治疗和恢复类药品',
        items: shopItems.value.filter(item => item.category === 'medicine')
      },
      {
        id: 'training',
        name: '训练',
        icon: '📚',
        description: '提升经验的训练用品',
        items: shopItems.value.filter(item => item.category === 'training')
      }
    ]
  }

  // 购买物品
  const purchaseItem = async (itemId: string, quantity: number = 1): Promise<{
    success: boolean
    message: string
    item?: Item | Equipment
  }> => {
    const shopItem = shopItems.value.find(item => item.id === itemId)
    if (!shopItem) {
      return { success: false, message: '商品不存在' }
    }

    // 检查库存
    if (shopItem.stock < quantity) {
      return { success: false, message: '库存不足' }
    }

    // 计算总价
    const totalPrice = shopItem.price * quantity

    // 检查货币余额
    if (shopItem.currency === 'coins') {
      if (gameStore.coins < totalPrice) {
        return { success: false, message: '金币不足' }
      }
    } else {
      // 代币购买逻辑（暂时简化）
      const tokenBalance = parseFloat(gameStore.tokens)
      if (tokenBalance < totalPrice) {
        return { success: false, message: '代币不足' }
      }
    }

    // 检查购买条件
    if (shopItem.requirements?.level && shopItem.requirements.level > 1) {
      // 这里应该检查萌宠等级，暂时跳过
    }

    try {
      // 扣除货币
      if (shopItem.currency === 'coins') {
        gameStore.spendCoins(totalPrice)
      } else {
        // 扣除代币的逻辑
        const newBalance = (parseFloat(gameStore.tokens) - totalPrice).toString()
        gameStore.setTokens(newBalance)
      }

      // 减少库存
      shopItem.stock -= quantity

      // 生成物品
      let purchasedItem: Item | Equipment

      if (Object.values(ItemType).includes(shopItem.type as ItemType)) {
        // 生成道具
        purchasedItem = ItemFactory.generateRandomItem(shopItem.type as ItemType, shopItem.rarity)
        purchasedItem.name = shopItem.name
        purchasedItem.quantity = quantity
      } else {
        // 生成装备
        purchasedItem = EquipmentFactory.generateRandomEquipment(
          shopItem.type as EquipmentType,
          shopItem.rarity,
          1
        )
        purchasedItem.name = shopItem.name
      }

      // 添加到背包
      if ('quantity' in purchasedItem) {
        gameStore.addItem(purchasedItem as any)
      } else {
        // 装备需要特殊处理
        gameStore.addItem({
          id: purchasedItem.id,
          name: purchasedItem.name,
          type: 'equipment',
          quantity: 1,
          description: purchasedItem.description,
          icon: shopItem.icon
        })
      }

      // 记录购买历史
      const purchase: PurchaseHistory = {
        id: Date.now().toString(),
        itemId: shopItem.id,
        itemName: shopItem.name,
        quantity,
        totalPrice,
        currency: shopItem.currency,
        purchasedAt: new Date()
      }
      purchaseHistory.value.unshift(purchase)

      // 限制历史记录数量
      if (purchaseHistory.value.length > 100) {
        purchaseHistory.value = purchaseHistory.value.slice(0, 100)
      }

      return {
        success: true,
        message: `成功购买 ${shopItem.name} x${quantity}`,
        item: purchasedItem
      }
    } catch (error) {
      console.error('购买失败:', error)
      return { success: false, message: '购买失败，请重试' }
    }
  }

  // 刷新商店
  const refreshShop = () => {
    isLoading.value = true

    setTimeout(() => {
      // 刷新限时物品库存
      shopItems.value.forEach(item => {
        if (item.isLimited) {
          item.stock = item.maxStock
          item.refreshTime = Date.now() + 24 * 60 * 60 * 1000
        }
      })

      // 随机调整部分物品价格（±10%）
      shopItems.value.forEach(item => {
        if (Math.random() < 0.3) { // 30%概率调整价格
          const adjustment = 0.9 + Math.random() * 0.2 // 0.9-1.1倍
          item.price = Math.floor(item.price * adjustment)
        }
      })

      lastRefreshTime.value = Date.now()
      isLoading.value = false
    }, 1000)
  }

  // 搜索物品
  const searchItems = (query: string): ShopItem[] => {
    const lowerQuery = query.toLowerCase()
    return shopItems.value.filter(item =>
      item.name.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery) ||
      item.category.toLowerCase().includes(lowerQuery)
    )
  }

  // 按价格排序
  const sortItemsByPrice = (ascending: boolean = true): ShopItem[] => {
    return [...shopItems.value].sort((a, b) =>
      ascending ? a.price - b.price : b.price - a.price
    )
  }

  // 按稀有度排序
  const sortItemsByRarity = (): ShopItem[] => {
    const rarityOrder = {
      [PetRarity.COMMON]: 1,
      [PetRarity.UNCOMMON]: 2,
      [PetRarity.RARE]: 3,
      [PetRarity.EPIC]: 4,
      [PetRarity.LEGENDARY]: 5,
      [PetRarity.MYTHICAL]: 6
    }

    return [...shopItems.value].sort((a, b) =>
      rarityOrder[b.rarity] - rarityOrder[a.rarity]
    )
  }

  // 获取推荐物品
  const getRecommendedItems = (limit: number = 5): ShopItem[] => {
    // 基于用户购买历史和当前需求推荐
    const recentPurchases = purchaseHistory.value.slice(0, 10)
    const preferredCategories = new Set(recentPurchases.map(p => {
      const item = shopItems.value.find(i => i.id === p.itemId)
      return item?.category
    }))

    let recommended = shopItems.value.filter(item =>
      item.stock > 0 && (
        preferredCategories.has(item.category) ||
        item.isLimited ||
        item.rarity === PetRarity.RARE ||
        item.rarity === PetRarity.EPIC
      )
    )

    // 如果推荐不够，添加一些随机物品
    if (recommended.length < limit) {
      const additional = shopItems.value
        .filter(item => item.stock > 0 && !recommended.includes(item))
        .sort(() => Math.random() - 0.5)
        .slice(0, limit - recommended.length)

      recommended = [...recommended, ...additional]
    }

    return recommended.slice(0, limit)
  }

  // 检查是否可以购买
  const canPurchase = (itemId: string, quantity: number = 1): {
    canBuy: boolean
    reason?: string
  } => {
    const item = shopItems.value.find(i => i.id === itemId)
    if (!item) {
      return { canBuy: false, reason: '商品不存在' }
    }

    if (item.stock < quantity) {
      return { canBuy: false, reason: '库存不足' }
    }

    const totalPrice = item.price * quantity
    if (item.currency === 'coins' && gameStore.coins < totalPrice) {
      return { canBuy: false, reason: '金币不足' }
    }

    if (item.currency === 'tokens' && parseFloat(gameStore.tokens) < totalPrice) {
      return { canBuy: false, reason: '代币不足' }
    }

    return { canBuy: true }
  }

  return {
    // 状态
    shopItems,
    categories,
    purchaseHistory,
    lastRefreshTime,
    isLoading,

    // 计算属性
    availableItems,
    itemsByCategory,
    limitedTimeItems,

    // 方法
    initializeShop,
    purchaseItem,
    refreshShop,
    searchItems,
    sortItemsByPrice,
    sortItemsByRarity,
    getRecommendedItems,
    canPurchase
  }
})
