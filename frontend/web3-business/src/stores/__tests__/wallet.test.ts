import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useWalletStore } from '@/stores/wallet'
import { createMockProvider, createMockContract } from '@/test/utils'

describe('Wallet Store', () => {
  let mockProvider: any
  let mockContract: any

  beforeEach(() => {
    setActivePinia(createPinia())
    mockProvider = createMockProvider()
    mockContract = createMockContract()
    vi.clearAllMocks()

    // Mock window.ethereum
    Object.defineProperty(window, 'ethereum', {
      value: mockProvider,
      writable: true,
    })
  })

  it('initializes with default state', () => {
    const store = useWalletStore()

    expect(store.isConnected).toBe(false)
    expect(store.address).toBe('')
    expect(store.balance).toBe('0')
    expect(store.chainId).toBe(0)
    expect(store.provider).toBeNull()
  })

  it('connects to MetaMask successfully', async () => {
    const store = useWalletStore()

    mockProvider.request.mockResolvedValueOnce(['******************************************'])
    mockProvider.request.mockResolvedValueOnce('0x2a') // Chain ID 42
    mockProvider.request.mockResolvedValueOnce('0xde0b6b3a7640000') // 1 ETH

    await store.connectWallet('metamask')

    expect(store.isConnected).toBe(true)
    expect(store.address).toBe('******************************************')
    expect(store.chainId).toBe(42)
    expect(store.balance).toBe('1.0')
  })

  it('handles connection rejection', async () => {
    const store = useWalletStore()

    mockProvider.request.mockRejectedValueOnce(new Error('User rejected'))

    await expect(store.connectWallet('metamask')).rejects.toThrow('User rejected')
    expect(store.isConnected).toBe(false)
  })

  it('disconnects wallet correctly', async () => {
    const store = useWalletStore()

    // First connect
    store.isConnected = true
    store.address = '******************************************'
    store.balance = '1.0'

    await store.disconnect()

    expect(store.isConnected).toBe(false)
    expect(store.address).toBe('')
    expect(store.balance).toBe('0')
    expect(store.provider).toBeNull()
  })

  it('switches network correctly', async () => {
    const store = useWalletStore()
    store.provider = mockProvider

    mockProvider.request.mockResolvedValueOnce(null)

    await store.switchNetwork(11155111) // Sepolia

    expect(mockProvider.request).toHaveBeenCalledWith({
      method: 'wallet_switchEthereumChain',
      params: [{ chainId: '0xaa36a7' }],
    })
  })

  it('adds network if switch fails', async () => {
    const store = useWalletStore()
    store.provider = mockProvider

    // First call fails (network not added)
    mockProvider.request.mockRejectedValueOnce({ code: 4902 })
    // Second call succeeds (add network)
    mockProvider.request.mockResolvedValueOnce(null)

    await store.switchNetwork(11155111)

    expect(mockProvider.request).toHaveBeenCalledWith({
      method: 'wallet_addEthereumChain',
      params: [expect.objectContaining({
        chainId: '0xaa36a7',
        chainName: 'Sepolia Testnet',
      })],
    })
  })

  it('refreshes balance correctly', async () => {
    const store = useWalletStore()
    store.provider = mockProvider
    store.address = '******************************************'

    mockProvider.request.mockResolvedValueOnce('0x1bc16d674ec80000') // 2 ETH

    await store.refreshBalance()

    expect(store.balance).toBe('2.0')
  })

  it('handles account changes', async () => {
    const store = useWalletStore()
    store.isConnected = true

    const accountChangeHandler = mockProvider.on.mock.calls.find(
      call => call[0] === 'accountsChanged'
    )?.[1]

    expect(accountChangeHandler).toBeDefined()

    // Simulate account change
    await accountChangeHandler(['******************************************'])

    expect(store.address).toBe('******************************************')
  })

  it('handles chain changes', async () => {
    const store = useWalletStore()
    store.isConnected = true

    const chainChangeHandler = mockProvider.on.mock.calls.find(
      call => call[0] === 'chainChanged'
    )?.[1]

    expect(chainChangeHandler).toBeDefined()

    // Simulate chain change
    await chainChangeHandler('0x1') // Mainnet

    expect(store.chainId).toBe(1)
  })

  it('handles disconnection events', async () => {
    const store = useWalletStore()
    store.isConnected = true

    const disconnectHandler = mockProvider.on.mock.calls.find(
      call => call[0] === 'disconnect'
    )?.[1]

    expect(disconnectHandler).toBeDefined()

    // Simulate disconnect
    await disconnectHandler()

    expect(store.isConnected).toBe(false)
  })

  it('persists connection state', async () => {
    const store = useWalletStore()

    // Mock localStorage
    const setItemSpy = vi.spyOn(localStorage, 'setItem')

    mockProvider.request.mockResolvedValueOnce(['******************************************'])
    mockProvider.request.mockResolvedValueOnce('0x2a')
    mockProvider.request.mockResolvedValueOnce('0xde0b6b3a7640000')

    await store.connectWallet('metamask')

    expect(setItemSpy).toHaveBeenCalledWith('wallet_connected', 'true')
    expect(setItemSpy).toHaveBeenCalledWith('wallet_type', 'metamask')
  })

  it('restores connection state on initialization', async () => {
    // Mock localStorage with existing connection
    vi.spyOn(localStorage, 'getItem')
      .mockReturnValueOnce('true') // wallet_connected
      .mockReturnValueOnce('metamask') // wallet_type

    mockProvider.request.mockResolvedValueOnce(['******************************************'])
    mockProvider.request.mockResolvedValueOnce('0x2a')
    mockProvider.request.mockResolvedValueOnce('0xde0b6b3a7640000')

    const store = useWalletStore()
    await store.initializeWallet()

    expect(store.isConnected).toBe(true)
    expect(store.address).toBe('******************************************')
  })

  it('handles WalletConnect connection', async () => {
    const store = useWalletStore()

    // Mock WalletConnect provider
    const mockWCProvider = {
      enable: vi.fn().mockResolvedValue(['******************************************']),
      request: vi.fn(),
      on: vi.fn(),
      disconnect: vi.fn(),
    }

    vi.doMock('@walletconnect/ethereum-provider', () => ({
      default: {
        init: vi.fn().mockResolvedValue(mockWCProvider),
      },
    }))

    await store.connectWallet('walletconnect')

    expect(mockWCProvider.enable).toHaveBeenCalled()
  })

  it('validates network compatibility', () => {
    const store = useWalletStore()

    expect(store.isCorrectNetwork(11155111)).toBe(true) // Sepolia
    expect(store.isCorrectNetwork(1)).toBe(false) // Mainnet
    expect(store.isCorrectNetwork(999)).toBe(false) // Unknown
  })

  it('formats addresses correctly', () => {
    const store = useWalletStore()
    const address = '******************************************'

    expect(store.formatAddress(address)).toBe('0x1234...7890')
    expect(store.formatAddress('')).toBe('')
  })

  it('handles loading states correctly', async () => {
    const store = useWalletStore()

    expect(store.isLoading).toBe(false)

    const connectPromise = store.connectWallet('metamask')
    expect(store.isLoading).toBe(true)

    mockProvider.request.mockResolvedValueOnce(['******************************************'])
    mockProvider.request.mockResolvedValueOnce('0x2a')
    mockProvider.request.mockResolvedValueOnce('0xde0b6b3a7640000')

    await connectPromise
    expect(store.isLoading).toBe(false)
  })
})
