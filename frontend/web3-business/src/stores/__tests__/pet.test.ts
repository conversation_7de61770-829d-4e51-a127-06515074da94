import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { usePetStore } from '@/stores/pet'
import { createMockPet, createMockItem, createMockEquipment } from '@/test/utils'

describe('Pet Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('initializes with default state', () => {
    const store = usePetStore()

    expect(store.pets).toEqual([])
    expect(store.currentPet).toBeNull()
    expect(store.inventory).toEqual([])
  })

  it('creates a new pet correctly', async () => {
    const store = usePetStore()

    const newPet = await store.createPet('Fluffy', 'cat')

    expect(store.pets).toHaveLength(1)
    expect(store.currentPet).toEqual(newPet)
    expect(newPet.name).toBe('Fluffy')
    expect(newPet.species).toBe('cat')
    expect(newPet.level).toBe(1)
    expect(newPet.health).toBe(100)
  })

  it('selects current pet correctly', () => {
    const store = usePetStore()
    const mockPet = createMockPet()
    store.pets = [mockPet]

    store.selectPet(mockPet.id)

    expect(store.currentPet).toEqual(mockPet)
  })

  it('feeds pet and updates stats', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ health: 50, happiness: 60 })
    store.pets = [mockPet]
    store.currentPet = mockPet

    await store.feedPet(mockPet.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.health).toBeGreaterThan(50)
    expect(updatedPet.happiness).toBeGreaterThan(60)
    expect(updatedPet.lastFeedTime).toBeInstanceOf(Date)
  })

  it('prevents overfeeding when health is at maximum', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ health: 100, maxHealth: 100 })
    store.pets = [mockPet]

    await store.feedPet(mockPet.id)

    expect(store.pets[0].health).toBe(100) // Should remain at max
  })

  it('trains pet and increases experience', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ experience: 50, strength: 10 })
    store.pets = [mockPet]

    await store.trainPet(mockPet.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.experience).toBeGreaterThan(50)
    expect(updatedPet.strength).toBeGreaterThan(10)
    expect(updatedPet.lastTrainTime).toBeInstanceOf(Date)
  })

  it('levels up pet when experience reaches maximum', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({
      level: 1,
      experience: 95,
      maxExperience: 100
    })
    store.pets = [mockPet]

    await store.trainPet(mockPet.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.level).toBe(2)
    expect(updatedPet.experience).toBeLessThan(95) // Reset after level up
    expect(updatedPet.maxExperience).toBeGreaterThan(100) // Increased for next level
  })

  it('plays with pet and increases happiness', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ happiness: 50 })
    store.pets = [mockPet]

    await store.playWithPet(mockPet.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.happiness).toBeGreaterThan(50)
    expect(updatedPet.lastPlayTime).toBeInstanceOf(Date)
  })

  it('uses item on pet correctly', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ health: 50 })
    const mockItem = createMockItem({
      id: 'health-potion',
      effect: { health: 30, happiness: 0, experience: 0 }
    })

    store.pets = [mockPet]
    store.inventory = [mockItem]

    await store.useItemOnPet(mockPet.id, mockItem.id)

    const updatedPet = store.pets[0]
    const updatedItem = store.inventory[0]

    expect(updatedPet.health).toBe(80) // 50 + 30
    expect(updatedItem.quantity).toBe(0) // Used up
  })

  it('removes item from inventory when quantity reaches zero', async () => {
    const store = usePetStore()
    const mockPet = createMockPet()
    const mockItem = createMockItem({ quantity: 1 })

    store.pets = [mockPet]
    store.inventory = [mockItem]

    await store.useItemOnPet(mockPet.id, mockItem.id)

    expect(store.inventory).toHaveLength(0)
  })

  it('equips equipment on pet', async () => {
    const store = usePetStore()
    const mockPet = createMockPet({ strength: 10 })
    const mockEquipment = createMockEquipment({
      statBonus: { strength: 5, intelligence: 0, agility: 0, health: 0, happiness: 0 }
    })

    store.pets = [mockPet]
    store.inventory = [mockEquipment]

    await store.equipItem(mockPet.id, mockEquipment.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.equipment).toContain(mockEquipment)
    expect(updatedPet.strength).toBe(15) // 10 + 5 bonus
  })

  it('unequips equipment from pet', async () => {
    const store = usePetStore()
    const mockEquipment = createMockEquipment({
      statBonus: { strength: 5, intelligence: 0, agility: 0, health: 0, happiness: 0 }
    })
    const mockPet = createMockPet({
      strength: 15,
      equipment: [mockEquipment]
    })

    store.pets = [mockPet]

    await store.unequipItem(mockPet.id, mockEquipment.id)

    const updatedPet = store.pets[0]
    expect(updatedPet.equipment).not.toContain(mockEquipment)
    expect(updatedPet.strength).toBe(10) // 15 - 5 bonus
  })

  it('calculates pet value correctly', () => {
    const store = usePetStore()
    const mockPet = createMockPet({
      level: 5,
      rarity: 'rare',
      health: 100,
      equipment: [createMockEquipment()]
    })

    const value = store.calculatePetValue(mockPet)

    expect(value).toBeGreaterThan(0)
    expect(typeof value).toBe('number')
  })

  it('gets pets by rarity correctly', () => {
    const store = usePetStore()
    const commonPet = createMockPet({ rarity: 'common' })
    const rarePet = createMockPet({ rarity: 'rare' })

    store.pets = [commonPet, rarePet]

    const rarePets = store.getPetsByRarity('rare')
    expect(rarePets).toHaveLength(1)
    expect(rarePets[0]).toEqual(rarePet)
  })

  it('gets pets by level range correctly', () => {
    const store = usePetStore()
    const lowLevelPet = createMockPet({ level: 2 })
    const midLevelPet = createMockPet({ level: 5 })
    const highLevelPet = createMockPet({ level: 10 })

    store.pets = [lowLevelPet, midLevelPet, highLevelPet]

    const midRangePets = store.getPetsByLevelRange(3, 7)
    expect(midRangePets).toHaveLength(1)
    expect(midRangePets[0]).toEqual(midLevelPet)
  })

  it('adds item to inventory correctly', () => {
    const store = usePetStore()
    const mockItem = createMockItem()

    store.addItemToInventory(mockItem)

    expect(store.inventory).toContain(mockItem)
  })

  it('stacks identical items in inventory', () => {
    const store = usePetStore()
    const item1 = createMockItem({ id: 'food-1', quantity: 2 })
    const item2 = createMockItem({ id: 'food-1', quantity: 3 })

    store.addItemToInventory(item1)
    store.addItemToInventory(item2)

    expect(store.inventory).toHaveLength(1)
    expect(store.inventory[0].quantity).toBe(5)
  })

  it('removes item from inventory correctly', () => {
    const store = usePetStore()
    const mockItem = createMockItem()
    store.inventory = [mockItem]

    store.removeItemFromInventory(mockItem.id)

    expect(store.inventory).toHaveLength(0)
  })

  it('saves pet data to storage', async () => {
    const store = usePetStore()
    const mockPet = createMockPet()
    store.pets = [mockPet]

    const setItemSpy = vi.spyOn(localStorage, 'setItem')

    await store.savePetData()

    expect(setItemSpy).toHaveBeenCalledWith('pet_data', expect.any(String))
  })

  it('loads pet data from storage', async () => {
    const store = usePetStore()
    const mockPet = createMockPet()

    const getItemSpy = vi.spyOn(localStorage, 'getItem')
      .mockReturnValue(JSON.stringify({
        pets: [mockPet],
        currentPetId: mockPet.id,
        inventory: []
      }))

    await store.loadPetData()

    expect(store.pets).toHaveLength(1)
    expect(store.currentPet).toEqual(mockPet)
  })

  it('handles corrupted storage data gracefully', async () => {
    const store = usePetStore()

    vi.spyOn(localStorage, 'getItem').mockReturnValue('invalid json')

    await store.loadPetData()

    // Should not crash and maintain default state
    expect(store.pets).toEqual([])
    expect(store.currentPet).toBeNull()
  })

  it('respects action cooldowns', async () => {
    const store = usePetStore()
    const mockPet = createMockPet()
    store.pets = [mockPet]

    // First feed should work
    await store.feedPet(mockPet.id)
    const firstFeedTime = store.pets[0].lastFeedTime

    // Immediate second feed should be prevented by cooldown
    await store.feedPet(mockPet.id)
    const secondFeedTime = store.pets[0].lastFeedTime

    expect(secondFeedTime).toEqual(firstFeedTime) // Should be the same
  })

  it('calculates total pet stats correctly', () => {
    const store = usePetStore()
    const equipment = createMockEquipment({
      statBonus: { strength: 5, intelligence: 3, agility: 2, health: 0, happiness: 0 }
    })
    const mockPet = createMockPet({
      strength: 10,
      intelligence: 8,
      agility: 6,
      equipment: [equipment]
    })

    const totalStats = store.calculateTotalStats(mockPet)

    expect(totalStats.strength).toBe(15) // 10 + 5
    expect(totalStats.intelligence).toBe(11) // 8 + 3
    expect(totalStats.agility).toBe(8) // 6 + 2
  })
})
