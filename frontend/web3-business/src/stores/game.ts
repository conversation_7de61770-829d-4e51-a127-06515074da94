import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { dataPersistenceService } from '../services/data-persistence.service'

export interface GameSettings {
  soundEnabled: boolean
  animationEnabled: boolean
  language: 'zh-CN' | 'en-US'
  theme: 'light' | 'dark'
  tutorialCompleted: boolean
  showTutorialHints: boolean
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  unlocked: boolean
  unlockedAt?: Date
}

export interface Item {
  id: string
  name: string
  type: 'food' | 'toy' | 'equipment' | 'decoration'
  quantity: number
  description: string
  icon: string
  effect?: {
    health?: number
    happiness?: number
    experience?: number
  }
}

export const useGameStore = defineStore('game', () => {
  // 状态
  const coins = ref(1000) // 游戏内货币
  const tokens = ref('0') // 区块链代币
  const inventory = ref<Item[]>([])
  const achievements = ref<Achievement[]>([])
  const settings = ref<GameSettings>({
    soundEnabled: true,
    animationEnabled: true,
    language: 'zh-CN',
    theme: 'light',
    tutorialCompleted: false,
    showTutorialHints: true
  })

  // 教程相关状态
  const tutorialStartTime = ref<number | null>(null)
  const currentTutorialStep = ref(0)
  const isInitialized = ref(false)

  // 计算属性
  const unlockedAchievements = computed(() =>
    achievements.value.filter(achievement => achievement.unlocked)
  )

  const inventoryByType = computed(() => {
    const grouped: Record<string, Item[]> = {}
    inventory.value.forEach(item => {
      if (!grouped[item.type]) {
        grouped[item.type] = []
      }
      grouped[item.type].push(item)
    })
    return grouped
  })

  // 方法
  const addCoins = (amount: number) => {
    coins.value += amount
  }

  const spendCoins = (amount: number): boolean => {
    if (coins.value >= amount) {
      coins.value -= amount
      return true
    }
    return false
  }

  const setTokens = (amount: string) => {
    tokens.value = amount
  }

  const addItem = (item: Item) => {
    const existingItem = inventory.value.find(i => i.id === item.id)
    if (existingItem) {
      existingItem.quantity += item.quantity
    } else {
      inventory.value.push({ ...item })
    }
  }

  const removeItem = (itemId: string, quantity: number = 1): boolean => {
    const item = inventory.value.find(i => i.id === itemId)
    if (item && item.quantity >= quantity) {
      item.quantity -= quantity
      if (item.quantity === 0) {
        const index = inventory.value.findIndex(i => i.id === itemId)
        inventory.value.splice(index, 1)
      }
      return true
    }
    return false
  }

  const unlockAchievement = (achievementId: string) => {
    const achievement = achievements.value.find(a => a.id === achievementId)
    if (achievement && !achievement.unlocked) {
      achievement.unlocked = true
      achievement.unlockedAt = new Date()
    }
  }

  const updateSettings = (newSettings: Partial<GameSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
  }

  // 教程相关方法
  const setTutorialCompleted = (completed: boolean) => {
    settings.value.tutorialCompleted = completed
    if (completed) {
      unlockAchievement('tutorial_completed')
    }
  }

  const setTutorialStartTime = (timestamp: number) => {
    tutorialStartTime.value = timestamp
  }

  const setCurrentTutorialStep = (step: number) => {
    currentTutorialStep.value = step
  }

  const shouldShowTutorial = computed(() => {
    return !settings.value.tutorialCompleted && isInitialized.value
  })

  const setInitialized = (initialized: boolean) => {
    isInitialized.value = initialized
  }

  const resetGame = () => {
    coins.value = 1000
    tokens.value = '0'
    inventory.value = []
    achievements.value.forEach(achievement => {
      achievement.unlocked = false
      achievement.unlockedAt = undefined
    })
    settings.value.tutorialCompleted = false
    tutorialStartTime.value = null
    currentTutorialStep.value = 0
  }

  // 装备相关方法
  const enhanceEquipment = async (equipmentId: string, level: number): Promise<boolean> => {
    // 简单的装备强化逻辑
    const cost = level * 100
    if (spendCoins(cost)) {
      console.log(`装备 ${equipmentId} 强化到 ${level} 级`)
      return true
    }
    return false
  }

  const repairEquipment = async (equipmentId: string): Promise<boolean> => {
    // 简单的装备修理逻辑
    const cost = 50
    if (spendCoins(cost)) {
      console.log(`装备 ${equipmentId} 已修理`)
      return true
    }
    return false
  }

  // 数据持久化方法
  const saveGameData = async (): Promise<boolean> => {
    try {
      const gameData = {
        coins: coins.value,
        tokens: tokens.value,
        inventory: inventory.value,
        achievements: achievements.value,
        isInitialized: isInitialized.value
      }
      return await dataPersistenceService.saveGameData(gameData)
    } catch (error) {
      console.error('保存游戏数据失败:', error)
      return false
    }
  }

  const loadGameData = async (): Promise<boolean> => {
    try {
      const data = await dataPersistenceService.loadGameData()

      if (data) {
        if (typeof data.coins === 'number') coins.value = data.coins
        if (typeof data.tokens === 'string') tokens.value = data.tokens
        if (Array.isArray(data.inventory)) inventory.value = data.inventory
        if (Array.isArray(data.achievements)) achievements.value = data.achievements
        if (typeof data.isInitialized === 'boolean') isInitialized.value = data.isInitialized

        console.log('游戏数据加载成功')
        return true
      }

      return false
    } catch (error) {
      console.error('加载游戏数据失败:', error)
      return false
    }
  }

  const saveUserSettings = async (): Promise<boolean> => {
    try {
      return await dataPersistenceService.saveUserSettings(settings.value)
    } catch (error) {
      console.error('保存用户设置失败:', error)
      return false
    }
  }

  const loadUserSettings = async (): Promise<boolean> => {
    try {
      const savedSettings = await dataPersistenceService.loadUserSettings()

      if (savedSettings) {
        settings.value = { ...settings.value, ...savedSettings }
        console.log('用户设置加载成功')
        return true
      }

      return false
    } catch (error) {
      console.error('加载用户设置失败:', error)
      return false
    }
  }

  // 自动保存监听（防抖）
  let saveTimer: number | null = null
  const debouncedSave = () => {
    if (saveTimer) {
      clearTimeout(saveTimer)
    }
    saveTimer = window.setTimeout(() => {
      saveGameData()
    }, 1000) // 1秒防抖
  }

  // 监听数据变化自动保存
  watch([coins, tokens, inventory, achievements], () => {
    debouncedSave()
  }, { deep: true })

  // 监听设置变化自动保存
  watch(settings, () => {
    saveUserSettings()
  }, { deep: true })

  return {
    // 状态
    coins,
    tokens,
    inventory,
    achievements,
    settings,
    isInitialized,
    tutorialStartTime,
    currentTutorialStep,

    // 计算属性
    unlockedAchievements,
    inventoryByType,
    shouldShowTutorial,

    // 方法
    addCoins,
    spendCoins,
    setTokens,
    addItem,
    removeItem,
    unlockAchievement,
    updateSettings,
    setInitialized,
    resetGame,

    // 教程方法
    setTutorialCompleted,
    setTutorialStartTime,
    setCurrentTutorialStep,

    // 装备方法
    enhanceEquipment,
    repairEquipment,

    // 持久化方法
    saveGameData,
    loadGameData,
    saveUserSettings,
    loadUserSettings
  }
})
