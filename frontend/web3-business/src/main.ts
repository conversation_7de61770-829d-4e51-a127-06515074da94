import './style.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 确保类型定义被正确加载
import './types/types'

// Vant UI
import Vant from 'vant'
import '@vant/touch-emulator' // 在桌面端使用触摸事件
import 'vant/lib/index.css'

// Vue3 <PERSON><PERSON> (commented out due to import issues)
// import Vue3Lottie from 'vue3-lottie'
// import 'vue3-lottie/dist/style.css'

// 引入主题系统
import { useGlobalTheme } from './composables/useTheme'

// 创建应用实例
const app = createApp(App)

// 注册插件
app.use(createPinia())
app.use(router)
app.use(Vant)
// app.use(Vue3Lottie)

// 注册全局指令
app.directive('lazy-image', vLazyImage)

// 错误处理服务
import { errorHandler } from './services/error-handler.service'
import { networkMonitor } from './services/network-monitor.service'

// 性能优化工具
import { memoryManager } from './utils/memoryManager'
import { requestCache } from './utils/requestCache'
import { imageOptimizer, vLazyImage } from './utils/imageOptimization'
import { performanceOptimizationService } from './services/performance-optimization.service'
import { initPerformanceOptimization } from './utils/performanceInit'

// 全局错误处理
app.config.errorHandler = (error: any, instance, info) => {
  console.error('Global error handler:', error, info)

  // 使用错误处理服务处理错误
  errorHandler.handleError(error, {
    component: instance?.$?.type?.name || 'Unknown',
    errorInfo: info,
    source: 'global'
  })
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Global warning:', msg, trace)
}

// 未捕获的Promise拒绝处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)

  errorHandler.handleError(event.reason, {
    source: 'unhandledrejection',
    promise: true
  })

  // 阻止默认的控制台错误输出
  event.preventDefault()
})

// 全局错误事件处理
window.addEventListener('error', (event) => {
  console.error('Global error event:', event.error)

  errorHandler.handleError(event.error, {
    source: 'window.error',
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
})

// 启动网络监控
networkMonitor.startMonitoring()

// 初始化主题系统
const theme = useGlobalTheme()
theme.initTheme()

// 初始化性能优化
initPerformanceOptimization(app, {
  enableMemoryMonitoring: true,
  enableRequestCaching: true,
  enableImageOptimization: true,
  enableAutoOptimization: true,
  memoryThreshold: 75,
  cacheCleanupInterval: 5 * 60 * 1000, // 5分钟
  debugMode: import.meta.env.DEV
})

// 监听页面可见性变化进行优化
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    // 页面隐藏时执行内存优化
    performanceOptimizationService.optimizeMemory()
  }
})

// 监听动画质量变更事件
window.addEventListener('animationQualityChange', (event: CustomEvent) => {
  const quality = event.detail.quality
  console.log('🎬 Animation quality changed:', quality)

  // 根据质量调整全局动画设置
  document.documentElement.style.setProperty('--animation-duration',
    quality === 'low' ? '0.1s' : quality === 'medium' ? '0.2s' : '0.3s'
  )

  document.documentElement.style.setProperty('--animation-timing',
    quality === 'low' ? 'linear' : 'ease-out'
  )
})

// 预加载关键资源
const preloadCriticalResources = async () => {
  const criticalImages = [
    '/images/pets/default-pet.png',
    '/images/pets/default-pet.svg',
    '/images/wallets/metamask.svg',
    '/images/wallets/walletconnect.svg'
  ]

  try {
    await performanceOptimizationService.optimizeImages(criticalImages)
    console.log('✅ Critical images preloaded')
  } catch (error) {
    console.warn('❌ Failed to preload critical images:', error)
  }
}

// 在空闲时间预加载资源
if ('requestIdleCallback' in window) {
  requestIdleCallback(preloadCriticalResources, { timeout: 5000 })
} else {
  setTimeout(preloadCriticalResources, 100)
}

// 定期优化动画性能
setInterval(() => {
  performanceOptimizationService.optimizeAnimations()
}, 30000) // 每30秒检查一次

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  performanceOptimizationService.destroy()
  memoryManager.destroy()
  imageOptimizer.destroy()
})

// 挂载应用
app.mount('#app')
