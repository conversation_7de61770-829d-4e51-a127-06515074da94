import './style.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 确保类型定义被正确加载
import './types/types'

// Vant UI
import Vant from 'vant'
import '@vant/touch-emulator' // 在桌面端使用触摸事件
import 'vant/lib/index.css'

// Vue3 <PERSON>tie (commented out due to import issues)
// import Vue3Lottie from 'vue3-lottie'
// import 'vue3-lottie/dist/style.css'

// 创建应用实例
const app = createApp(App)

// 注册插件
app.use(createPinia())
app.use(router)
app.use(Vant)
// app.use(Vue3Lottie)

// 全局错误处理
app.config.errorHandler = (err, _instance, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
}

// 挂载应用
app.mount('#app')
