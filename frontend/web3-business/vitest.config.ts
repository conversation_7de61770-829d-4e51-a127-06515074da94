import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import path from 'node:path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: [
      'node_modules',
      'dist',
      '.idea',
      '.git',
      '.cache',
      'src/test/setup.ts',
      'src/test/utils.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        'src/main.ts',
        'src/App.vue',
        'src/assets/**',
        'src/router/**',
        'src/types/vant.d.ts',
        'src/types/vue3-lottie.d.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // Specific thresholds for critical modules
        'src/stores/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        },
        'src/services/': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        },
        'src/utils/': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    testTimeout: 10000,
    hookTimeout: 10000,
    // Parallel testing configuration
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4
      }
    },
    // Reporter configuration
    reporter: ['verbose'],
    // Watch mode configuration
    watch: {
      ignore: ['**/node_modules/**', '**/dist/**', '**/coverage/**']
    },
    // Environment variables for tests
    env: {
      NODE_ENV: 'test',
      VITE_CONTRACT_ADDRESS: '******************************************',
      VITE_CONTRACT_V2_ADDRESS: '******************************************',
      VITE_CHAIN_ID: '11155111',
      VITE_RPC_URL: 'https://sepolia.infura.io/v3/test-key'
    }
  }
})
