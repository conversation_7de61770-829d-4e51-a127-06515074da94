# 萌宠养成代币游戏 - 开发者API文档

## 目录
- [概述](#概述)
- [架构说明](#架构说明)
- [核心服务API](#核心服务api)
- [组合式函数API](#组合式函数api)
- [状态管理API](#状态管理api)
- [工具函数API](#工具函数api)
- [类型定义](#类型定义)
- [事件系统](#事件系统)
- [错误处理](#错误处理)
- [测试指南](#测试指南)

## 概述

本文档描述了萌宠养成代币游戏前端应用的API接口，包括服务层、组合式函数、状态管理和工具函数等。

### 技术栈
- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.0+
- **状态管理**: Pinia 2.0+
- **区块链**: Ethers.js 6.0+
- **UI库**: Vant 4.0+

### API设计原则
- **类型安全**: 所有API都有完整的TypeScript类型定义
- **响应式**: 基于Vue 3响应式系统
- **错误处理**: 统一的错误处理机制
- **可测试**: 所有API都可以进行单元测试

## 架构说明

```
src/
├── services/          # 业务逻辑服务层
├── composables/       # Vue组合式函数
├── stores/           # Pinia状态管理
├── utils/            # 工具函数
├── types/            # TypeScript类型定义
└── components/       # Vue组件
```

## 核心服务API

### 钱包服务 (WalletService)

#### 类定义
```typescript
class WalletService {
  // 连接钱包
  async connectWallet(walletType: WalletType): Promise<WalletConnection>
  
  // 断开连接
  async disconnectWallet(): Promise<void>
  
  // 获取账户信息
  async getAccountInfo(): Promise<AccountInfo>
  
  // 切换网络
  async switchNetwork(chainId: number): Promise<void>
  
  // 监听账户变化
  onAccountChanged(callback: (account: string) => void): void
  
  // 监听网络变化
  onNetworkChanged(callback: (chainId: number) => void): void
}
```

#### 使用示例
```typescript
import { WalletService } from '@/services/wallet.service'

const walletService = new WalletService()

// 连接MetaMask
try {
  const connection = await walletService.connectWallet('metamask')
  console.log('连接成功:', connection.address)
} catch (error) {
  console.error('连接失败:', error.message)
}
```

### 合约服务 (ContractService)

#### 类定义
```typescript
class ContractService {
  // 获取代币余额
  async getTokenBalance(address: string): Promise<string>
  
  // 转账代币
  async transferToken(to: string, amount: string): Promise<TransactionResult>
  
  // 铸造代币
  async mintToken(to: string, amount: string): Promise<TransactionResult>
  
  // 获取税收信息
  async getTaxInfo(): Promise<TaxInfo>
  
  // 监听合约事件
  onTransfer(callback: (event: TransferEvent) => void): void
  onMint(callback: (event: MintEvent) => void): void
}
```

#### 使用示例
```typescript
import { ContractService } from '@/services/contract.service'

const contractService = new ContractService()

// 获取余额
const balance = await contractService.getTokenBalance('0x...')

// 转账
const result = await contractService.transferToken('0x...', '100')
console.log('交易哈希:', result.hash)
```

### 萌宠服务 (PetService)

#### 类定义
```typescript
class PetService {
  // 创建萌宠
  createPet(config?: PetConfig): Pet
  
  // 喂食萌宠
  feedPet(pet: Pet, food: FoodItem): Pet
  
  // 训练萌宠
  trainPet(pet: Pet, training: TrainingType): Pet
  
  // 装备道具
  equipItem(pet: Pet, item: Equipment): Pet
  
  // 计算萌宠价值
  calculatePetValue(pet: Pet): number
  
  // 萌宠升级
  levelUpPet(pet: Pet): Pet
}
```

## 组合式函数API

### useWallet

#### 接口定义
```typescript
interface UseWalletReturn {
  // 状态
  isConnected: Ref<boolean>
  address: Ref<string>
  balance: Ref<string>
  chainId: Ref<number>
  
  // 方法
  connect: (walletType: WalletType) => Promise<void>
  disconnect: () => Promise<void>
  switchNetwork: (chainId: number) => Promise<void>
  
  // 计算属性
  shortAddress: ComputedRef<string>
  isCorrectNetwork: ComputedRef<boolean>
}
```

#### 使用示例
```typescript
import { useWallet } from '@/composables/useWallet'

export default defineComponent({
  setup() {
    const {
      isConnected,
      address,
      balance,
      connect,
      disconnect,
      shortAddress
    } = useWallet()
    
    const handleConnect = async () => {
      try {
        await connect('metamask')
      } catch (error) {
        console.error('连接失败:', error)
      }
    }
    
    return {
      isConnected,
      address,
      balance,
      shortAddress,
      handleConnect,
      disconnect
    }
  }
})
```

### usePetNurturing

#### 接口定义
```typescript
interface UsePetNurturingReturn {
  // 状态
  currentPet: Ref<Pet | null>
  isFeeding: Ref<boolean>
  isTraining: Ref<boolean>
  
  // 方法
  feedPet: (food: FoodItem) => Promise<void>
  trainPet: (type: TrainingType) => Promise<void>
  restPet: () => Promise<void>
  playWithPet: () => Promise<void>
  
  // 计算属性
  canFeed: ComputedRef<boolean>
  canTrain: ComputedRef<boolean>
  petValue: ComputedRef<number>
}
```

### useTokenManagement

#### 接口定义
```typescript
interface UseTokenManagementReturn {
  // 状态
  balance: Ref<string>
  isLoading: Ref<boolean>
  transactions: Ref<Transaction[]>
  
  // 方法
  refreshBalance: () => Promise<void>
  transfer: (to: string, amount: string) => Promise<TransactionResult>
  exchangePetForTokens: (pet: Pet) => Promise<TransactionResult>
  
  // 计算属性
  formattedBalance: ComputedRef<string>
  canTransfer: ComputedRef<boolean>
}
```

## 状态管理API

### Wallet Store

#### 状态定义
```typescript
interface WalletState {
  isConnected: boolean
  address: string
  balance: string
  chainId: number
  provider: ethers.BrowserProvider | null
  walletType: WalletType | null
}
```

#### Actions
```typescript
interface WalletActions {
  connectWallet(walletType: WalletType): Promise<void>
  disconnectWallet(): Promise<void>
  updateBalance(): Promise<void>
  switchNetwork(chainId: number): Promise<void>
}
```

#### 使用示例
```typescript
import { useWalletStore } from '@/stores/wallet'

const walletStore = useWalletStore()

// 连接钱包
await walletStore.connectWallet('metamask')

// 获取状态
console.log('地址:', walletStore.address)
console.log('余额:', walletStore.balance)
```

### Pet Store

#### 状态定义
```typescript
interface PetState {
  currentPet: Pet | null
  pets: Pet[]
  inventory: Item[]
  gameCoins: number
}
```

#### Actions
```typescript
interface PetActions {
  createPet(config?: PetConfig): void
  updatePet(pet: Pet): void
  feedPet(food: FoodItem): Promise<void>
  trainPet(type: TrainingType): Promise<void>
  equipItem(item: Equipment): void
  addToInventory(item: Item): void
}
```

### Game Store

#### 状态定义
```typescript
interface GameState {
  isInitialized: boolean
  currentView: GameView
  settings: GameSettings
  achievements: Achievement[]
  statistics: GameStatistics
}
```

## 工具函数API

### 存储工具 (storage.ts)

```typescript
// 保存数据到本地存储
export function saveToStorage<T>(key: string, data: T): void

// 从本地存储读取数据
export function loadFromStorage<T>(key: string, defaultValue: T): T

// 清除存储数据
export function clearStorage(key: string): void

// 获取存储大小
export function getStorageSize(): number
```

### 格式化工具 (format.ts)

```typescript
// 格式化地址
export function formatAddress(address: string, length?: number): string

// 格式化代币数量
export function formatTokenAmount(amount: string, decimals?: number): string

// 格式化时间
export function formatTime(timestamp: number): string

// 格式化百分比
export function formatPercentage(value: number): string
```

### 验证工具 (validation.ts)

```typescript
// 验证以太坊地址
export function isValidAddress(address: string): boolean

// 验证代币数量
export function isValidAmount(amount: string): boolean

// 验证萌宠名称
export function isValidPetName(name: string): boolean

// 验证输入安全性
export function sanitizeInput(input: string): string
```

## 类型定义

### 萌宠相关类型

```typescript
// 萌宠稀有度
export enum PetRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

// 萌宠接口
export interface Pet {
  id: string
  name: string
  level: number
  experience: number
  rarity: PetRarity
  health: number
  happiness: number
  equipment: Equipment[]
  appearance: PetAppearance
  createdAt: Date
  lastFeedTime: Date
}

// 萌宠外观
export interface PetAppearance {
  species: string
  color: string
  pattern: string
  accessories: string[]
}
```

### 钱包相关类型

```typescript
// 钱包类型
export type WalletType = 'metamask' | 'walletconnect'

// 钱包连接信息
export interface WalletConnection {
  address: string
  chainId: number
  provider: ethers.BrowserProvider
}

// 账户信息
export interface AccountInfo {
  address: string
  balance: string
  nonce: number
}
```

### 交易相关类型

```typescript
// 交易结果
export interface TransactionResult {
  hash: string
  status: 'pending' | 'success' | 'failed'
  gasUsed?: string
  effectiveGasPrice?: string
}

// 交易事件
export interface TransferEvent {
  from: string
  to: string
  amount: string
  blockNumber: number
  transactionHash: string
}
```

## 事件系统

### 事件总线

```typescript
// 事件类型
export interface GameEvents {
  'pet:levelUp': { pet: Pet, newLevel: number }
  'pet:fed': { pet: Pet, food: FoodItem }
  'token:received': { amount: string, from: string }
  'wallet:connected': { address: string }
  'wallet:disconnected': void
}

// 事件发射器
export class EventEmitter<T extends Record<string, any>> {
  on<K extends keyof T>(event: K, callback: (data: T[K]) => void): void
  off<K extends keyof T>(event: K, callback: (data: T[K]) => void): void
  emit<K extends keyof T>(event: K, data: T[K]): void
}
```

### 使用示例

```typescript
import { gameEvents } from '@/utils/events'

// 监听萌宠升级事件
gameEvents.on('pet:levelUp', ({ pet, newLevel }) => {
  console.log(`${pet.name} 升级到 ${newLevel} 级！`)
})

// 发射事件
gameEvents.emit('pet:levelUp', { pet: myPet, newLevel: 5 })
```

## 错误处理

### 错误类型

```typescript
// 基础错误类
export class GameError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'GameError'
  }
}

// 钱包错误
export class WalletError extends GameError {
  constructor(message: string, details?: any) {
    super(message, 'WALLET_ERROR', details)
  }
}

// 合约错误
export class ContractError extends GameError {
  constructor(message: string, details?: any) {
    super(message, 'CONTRACT_ERROR', details)
  }
}
```

### 错误处理器

```typescript
// 全局错误处理
export class ErrorHandler {
  static handle(error: Error): string {
    if (error instanceof WalletError) {
      return this.handleWalletError(error)
    }
    
    if (error instanceof ContractError) {
      return this.handleContractError(error)
    }
    
    return '发生未知错误，请稍后重试'
  }
  
  private static handleWalletError(error: WalletError): string {
    switch (error.code) {
      case 'USER_REJECTED':
        return '用户取消了操作'
      case 'INSUFFICIENT_FUNDS':
        return '余额不足'
      default:
        return '钱包连接错误'
    }
  }
}
```

## 测试指南

### 单元测试

```typescript
// 测试萌宠服务
describe('PetService', () => {
  let petService: PetService
  
  beforeEach(() => {
    petService = new PetService()
  })
  
  test('should create pet with correct properties', () => {
    const pet = petService.createPet()
    
    expect(pet.id).toBeDefined()
    expect(pet.level).toBe(1)
    expect(pet.health).toBe(100)
    expect(pet.happiness).toBe(100)
  })
  
  test('should feed pet and increase health', () => {
    const pet = petService.createPet()
    pet.health = 50
    
    const food: FoodItem = { type: 'apple', healthRestore: 20 }
    const fedPet = petService.feedPet(pet, food)
    
    expect(fedPet.health).toBe(70)
  })
})
```

### 集成测试

```typescript
// 测试钱包连接流程
describe('Wallet Integration', () => {
  test('should connect wallet and get balance', async () => {
    const { connect, getBalance } = useWallet()
    
    await connect('metamask')
    const balance = await getBalance()
    
    expect(balance).toBeDefined()
    expect(typeof balance).toBe('string')
  })
})
```

### E2E测试

```typescript
// 测试完整游戏流程
describe('Pet Nurturing Flow', () => {
  test('should complete full pet nurturing cycle', async () => {
    // 1. 连接钱包
    await page.click('[data-testid="connect-wallet"]')
    
    // 2. 创建萌宠
    await page.click('[data-testid="create-pet"]')
    
    // 3. 喂食萌宠
    await page.click('[data-testid="feed-pet"]')
    
    // 4. 验证萌宠状态
    const health = await page.textContent('[data-testid="pet-health"]')
    expect(health).toContain('100%')
  })
})
```

## API版本控制

当前API版本: `v1.0.0`

### 版本兼容性
- **主版本**: 不兼容的API更改
- **次版本**: 向后兼容的功能添加
- **修订版本**: 向后兼容的错误修复

### 迁移指南
升级到新版本时，请参考 `MIGRATION.md` 文件了解破坏性更改和迁移步骤。

---

**注意**: 本文档会随着API的更新而持续维护。如有疑问，请参考源代码或联系开发团队。