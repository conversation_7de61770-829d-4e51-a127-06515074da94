# 萌宠养成代币游戏 - 部署文档

## 目录
- [部署概述](#部署概述)
- [环境要求](#环境要求)
- [本地部署](#本地部署)
- [预发布环境部署](#预发布环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [CI/CD部署](#cicd部署)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 部署概述

萌宠养成代币游戏是一个基于Vue3的单页应用(SPA)，支持多种部署方式：

### 部署架构
```
用户 → CDN/负载均衡器 → Nginx → 静态文件
                      ↓
                   API网关 → 后端服务
                      ↓
                   区块链网络
```

### 支持的部署环境
- **开发环境**: 本地开发和测试
- **预发布环境**: 功能验证和集成测试
- **生产环境**: 正式对外服务

## 环境要求

### 基础要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **Git**: 2.0.0 或更高版本

### 服务器要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **网络**: 稳定的互联网连接

### 可选组件
- **Docker**: 20.10.0+ (容器化部署)
- **Nginx**: 1.18.0+ (反向代理)
- **SSL证书**: HTTPS支持

## 本地部署

### 1. 克隆项目
```bash
git clone https://github.com/your-org/pet-token-game.git
cd pet-token-game/frontend/web3-business
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
cp .env .env.local
# 编辑 .env.local 文件，配置必要的环境变量
```

### 4. 启动开发服务器
```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

### 5. 构建生产版本
```bash
npm run build:prod
```

构建结果将输出到 `dist/` 目录。

## 预发布环境部署

### 自动化部署脚本
```bash
# 使用部署脚本
./scripts/deploy.sh staging

# 或者手动执行步骤
npm run build:staging
# 上传到预发布服务器
```

### 手动部署步骤

#### 1. 构建应用
```bash
npm run build:staging
```

#### 2. 上传文件
```bash
# 使用rsync上传到服务器
rsync -avz --delete dist/ user@staging-server:/var/www/html/

# 或使用scp
scp -r dist/* user@staging-server:/var/www/html/
```

#### 3. 配置Nginx
```nginx
server {
    listen 80;
    server_name staging.petgame.com;
    root /var/www/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

#### 4. 重启服务
```bash
sudo systemctl reload nginx
```

### 验证部署
- 访问 `https://staging.petgame.com`
- 检查钱包连接功能
- 验证智能合约交互
- 测试核心游戏功能

## 生产环境部署

### 部署前检查清单
- [ ] 代码已合并到main分支
- [ ] 所有测试通过
- [ ] 安全审计完成
- [ ] 性能测试通过
- [ ] 备份现有版本
- [ ] 准备回滚方案

### 蓝绿部署策略

#### 1. 准备新环境
```bash
# 在新服务器或新目录部署
npm run build:prod
rsync -avz dist/ user@prod-server:/var/www/html-new/
```

#### 2. 配置负载均衡器
```nginx
upstream backend {
    server prod-server-1:80 weight=100;
    server prod-server-2:80 weight=0;  # 新版本，权重为0
}
```

#### 3. 逐步切换流量
```bash
# 切换10%流量到新版本
# 监控错误率和性能指标
# 逐步增加到100%
```

#### 4. 完成切换
```bash
# 更新DNS或负载均衡器配置
# 停止旧版本服务
```

### 滚动更新部署

#### 1. 使用PM2管理进程
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js --env production

# 更新应用
pm2 reload ecosystem.config.js --env production
```

#### 2. PM2配置文件
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'pet-token-game',
    script: 'serve',
    args: '-s dist -l 3000',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
```

## Docker部署

### 1. 构建镜像
```bash
# 构建生产镜像
docker build -t pet-token-game:latest .

# 构建预发布镜像
docker build --build-arg BUILD_ENV=staging -t pet-token-game:staging .
```

### 2. 运行容器
```bash
# 单容器运行
docker run -d -p 3000:80 --name pet-game pet-token-game:latest

# 使用Docker Compose
docker-compose up -d
```

### 3. Docker Compose配置
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    image: pet-token-game:latest
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
```

### 4. 容器编排
```bash
# 使用Docker Swarm
docker swarm init
docker stack deploy -c docker-compose.prod.yml pet-game

# 或使用Kubernetes
kubectl apply -f k8s/
```

## CI/CD部署

### GitHub Actions工作流

#### 1. 配置Secrets
在GitHub仓库设置中添加以下Secrets：
- `CONTRACT_ADDRESS`: 智能合约地址
- `CONTRACT_V2_ADDRESS`: V2合约地址
- `RPC_URL`: 区块链RPC地址
- `DEPLOY_KEY`: 部署密钥
- `SLACK_WEBHOOK`: Slack通知地址

#### 2. 自动部署流程
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main, develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Install and Build
        run: |
          npm ci
          npm run build:prod
      - name: Deploy
        run: |
          # 部署逻辑
```

### GitLab CI/CD

#### 1. 配置文件
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

test:
  stage: test
  script:
    - npm ci
    - npm run test:ci

build:
  stage: build
  script:
    - npm run build:prod
  artifacts:
    paths:
      - dist/

deploy:
  stage: deploy
  script:
    - ./scripts/deploy.sh production
  only:
    - main
```

## 监控和维护

### 应用监控

#### 1. 健康检查
```bash
# 检查应用状态
curl -f http://localhost/health

# 检查关键功能
curl -f http://localhost/api/status
```

#### 2. 日志监控
```bash
# Nginx访问日志
tail -f /var/log/nginx/access.log

# 应用错误日志
tail -f /var/log/nginx/error.log

# 系统资源监控
htop
df -h
```

#### 3. 性能监控
```javascript
// 前端性能监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)
```

### 自动化监控

#### 1. Prometheus + Grafana
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

#### 2. 告警配置
```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: 'Pet Game Alert'
```

### 备份策略

#### 1. 代码备份
```bash
# Git仓库备份
git clone --mirror https://github.com/your-org/pet-token-game.git

# 定期推送到备份仓库
git push --mirror backup-repo
```

#### 2. 配置备份
```bash
# 备份配置文件
tar -czf config-backup-$(date +%Y%m%d).tar.gz \
  /etc/nginx/ \
  /etc/ssl/ \
  .env.production
```

#### 3. 数据库备份（如果有）
```bash
# 备份用户数据
mysqldump -u root -p gamedb > backup-$(date +%Y%m%d).sql
```

## 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
npm run test:clear-cache
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version
npm --version
```

#### 2. 部署失败
```bash
# 检查文件权限
ls -la dist/
chmod -R 755 dist/

# 检查Nginx配置
nginx -t
systemctl status nginx
```

#### 3. 钱包连接问题
```javascript
// 检查网络配置
console.log('Chain ID:', window.ethereum.chainId)
console.log('Network:', window.ethereum.networkVersion)

// 检查合约地址
console.log('Contract Address:', process.env.VITE_CONTRACT_ADDRESS)
```

#### 4. 性能问题
```bash
# 检查资源使用
top
free -h
df -h

# 检查网络延迟
ping api.petgame.com
curl -w "@curl-format.txt" -o /dev/null -s "https://petgame.com"
```

### 回滚策略

#### 1. 快速回滚
```bash
# 使用符号链接快速切换
ln -sfn /var/www/html-v1.0.0 /var/www/html
systemctl reload nginx
```

#### 2. 数据库回滚
```bash
# 恢复数据库备份
mysql -u root -p gamedb < backup-20240101.sql
```

#### 3. DNS回滚
```bash
# 更新DNS记录指向旧服务器
# 等待DNS传播（通常5-30分钟）
```

### 紧急联系方式

- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急热线**: +86-xxx-xxxx-xxxx
- **Slack频道**: #emergency-response

---

**注意**: 本文档应定期更新，确保部署流程和配置信息的准确性。在执行生产环境部署前，请务必在预发布环境进行充分测试。