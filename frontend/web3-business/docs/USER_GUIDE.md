# 萌宠养成代币游戏 - 用户使用指南

## 目录
- [游戏简介](#游戏简介)
- [快速开始](#快速开始)
- [钱包连接](#钱包连接)
- [萌宠养成](#萌宠养成)
- [代币系统](#代币系统)
- [游戏商店](#游戏商店)
- [常见问题](#常见问题)
- [故障排除](#故障排除)

## 游戏简介

萌宠养成代币游戏是一款基于区块链技术的Web3游戏，玩家可以：
- 🐾 养成可爱的虚拟萌宠
- 💎 通过萌宠属性兑换代币
- 🛒 在游戏商店购买道具和装备
- 📈 管理和交易代币资产

### 游戏特色
- **去中心化**: 基于以太坊区块链，数据透明可信
- **代币奖励**: 萌宠价值可直接兑换为代币
- **丰富玩法**: 喂食、训练、装备、进化等多种养成方式
- **移动优先**: 完美适配手机和平板设备

## 快速开始

### 1. 准备工作
在开始游戏前，您需要：
- 📱 支持Web3的浏览器（推荐Chrome + MetaMask）
- 💰 少量ETH用于支付交易费用
- 🔗 连接到正确的测试网络

### 2. 首次进入游戏
1. 访问游戏网址
2. 点击"连接钱包"按钮
3. 选择您的钱包类型（MetaMask/WalletConnect）
4. 授权连接并确认网络
5. 创建您的第一只萌宠

### 3. 创建萌宠
- 系统会为您随机生成一只萌宠
- 萌宠具有不同的稀有度：普通、稀有、史诗、传说
- 每只萌宠都有独特的外观和属性

## 钱包连接

### 支持的钱包
- **MetaMask**: 最流行的以太坊钱包
- **WalletConnect**: 支持多种移动端钱包

### 连接步骤
1. 点击右上角"连接钱包"按钮
2. 选择钱包类型
3. 在钱包中确认连接请求
4. 确保网络设置正确

### 网络配置
游戏支持以下网络：
- **测试网**: Sepolia Testnet
- **主网**: Ethereum Mainnet（即将支持）

### 钱包安全提示
⚠️ **重要提醒**：
- 永远不要分享您的私钥或助记词
- 确认交易前仔细检查金额和地址
- 使用官方钱包应用，避免钓鱼网站

## 萌宠养成

### 萌宠属性
每只萌宠都有以下属性：
- **等级**: 影响萌宠的基础能力
- **经验值**: 通过各种活动获得
- **健康度**: 影响萌宠的状态和收益
- **快乐度**: 影响萌宠的成长速度
- **稀有度**: 决定萌宠的基础价值

### 养成活动

#### 🍎 喂食
- **作用**: 恢复健康度，获得经验值
- **消耗**: 游戏币或道具
- **建议**: 定期喂食保持萌宠健康

#### 🏃 训练
- **作用**: 快速获得经验值，提升等级
- **消耗**: 游戏币和时间
- **建议**: 配合休息，避免过度训练

#### 🎮 互动
- **作用**: 增加快乐度，增进感情
- **消耗**: 时间
- **建议**: 每日互动获得额外奖励

#### 😴 休息
- **作用**: 恢复状态，准备下次活动
- **消耗**: 时间
- **建议**: 合理安排休息时间

### 萌宠进化
- 达到特定等级可以进化
- 进化会改变萌宠外观和属性
- 稀有度越高，进化效果越明显

## 代币系统

### 代币类型
游戏使用两种代币：
- **游戏币**: 游戏内货币，用于日常消费
- **代币**: 区块链代币，可以交易和转账

### 代币兑换
将萌宠价值兑换为代币：

#### 兑换条件
- 萌宠等级 ≥ 5级
- 健康度 ≥ 80%
- 快乐度 ≥ 60%

#### 价值计算
代币数量 = 基础奖励 × 稀有度倍数 × 健康度加成 × 装备加成

#### 兑换流程
1. 选择要兑换的萌宠
2. 查看预估收益
3. 确认兑换交易
4. 等待区块链确认
5. 代币到账

### 代币管理
- **查看余额**: 实时显示代币数量
- **转账功能**: 向其他地址发送代币
- **交易历史**: 查看所有代币交易记录
- **税收机制**: V2版本包含交易税收

## 游戏商店

### 商品类型
- **食物**: 恢复萌宠健康度
- **玩具**: 增加萌宠快乐度
- **装备**: 提升萌宠属性
- **道具**: 特殊效果物品

### 购买流程
1. 浏览商店商品
2. 选择需要的物品
3. 确认价格和效果
4. 完成支付
5. 物品添加到背包

### 背包管理
- 查看拥有的所有物品
- 使用物品为萌宠增益
- 管理物品数量和效果

## 常见问题

### Q: 如何获得更多游戏币？
A: 通过以下方式获得：
- 完成日常任务
- 萌宠互动奖励
- 参与特殊活动
- 代币兑换（部分）

### Q: 萌宠会死亡吗？
A: 不会。萌宠只会因为缺乏照顾而状态下降，影响收益。

### Q: 可以拥有多只萌宠吗？
A: 目前每个账户只能拥有一只萌宠，未来版本会支持多萌宠。

### Q: 代币有什么用途？
A: 代币可以：
- 在游戏内购买高级道具
- 转账给其他玩家
- 在去中心化交易所交易
- 参与游戏治理（未来功能）

### Q: 如何提高萌宠稀有度？
A: 稀有度在创建时确定，无法直接提升。但可以通过装备和训练提升萌宠价值。

## 故障排除

### 连接问题
**问题**: 无法连接钱包
**解决方案**:
1. 确保钱包已解锁
2. 检查网络连接
3. 刷新页面重试
4. 清除浏览器缓存

**问题**: 网络错误
**解决方案**:
1. 检查钱包网络设置
2. 切换到正确的测试网
3. 等待网络恢复
4. 联系客服支持

### 交易问题
**问题**: 交易失败
**解决方案**:
1. 检查ETH余额是否足够支付Gas费
2. 提高Gas价格重新发送
3. 等待网络拥堵缓解
4. 检查合约地址是否正确

**问题**: 代币未到账
**解决方案**:
1. 等待更多区块确认
2. 检查交易哈希状态
3. 刷新页面更新余额
4. 查看钱包是否需要添加代币

### 游戏问题
**问题**: 萌宠状态异常
**解决方案**:
1. 检查本地存储数据
2. 尝试刷新页面
3. 重新连接钱包
4. 联系技术支持

**问题**: 页面加载缓慢
**解决方案**:
1. 检查网络连接速度
2. 清除浏览器缓存
3. 关闭其他标签页
4. 使用推荐浏览器

## 联系我们

如果您遇到其他问题或需要帮助：
- 📧 邮箱: <EMAIL>
- 💬 Telegram: @PetGameSupport
- 🐦 Twitter: @PetTokenGame
- 📖 文档: docs.petgame.com

---

**免责声明**: 本游戏涉及区块链交易，请理性参与，注意风险。代币价格可能波动，投资需谨慎。