{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*", "node_modules", "dist"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "skipLibCheck": true, "noEmit": true, "module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": true, "types": ["vite/client"]}}