#!/bin/bash

# 萌宠养成代币游戏部署脚本
# 使用方法: ./scripts/deploy.sh [staging|production]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "请指定部署环境: staging 或 production"
    echo "使用方法: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    echo "支持的环境: staging, production"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "git 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git状态..."
    
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "工作目录有未提交的更改"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
    
    # 获取当前分支和提交信息
    CURRENT_BRANCH=$(git branch --show-current)
    CURRENT_COMMIT=$(git rev-parse --short HEAD)
    
    log_info "当前分支: $CURRENT_BRANCH"
    log_info "当前提交: $CURRENT_COMMIT"
    
    # 生产环境检查
    if [ "$ENVIRONMENT" = "production" ] && [ "$CURRENT_BRANCH" != "main" ] && [ "$CURRENT_BRANCH" != "master" ]; then
        log_warning "生产环境建议从 main/master 分支部署"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    log_info "运行测试套件..."
    
    # 运行代码检查
    npm run lint
    
    # 运行类型检查
    npm run type-check
    
    # 运行单元测试
    npm run test:ci
    
    log_success "所有测试通过"
}

# 构建项目
build_project() {
    log_info "构建 $ENVIRONMENT 环境..."
    
    # 清理之前的构建
    rm -rf dist
    
    # 根据环境构建
    if [ "$ENVIRONMENT" = "staging" ]; then
        npm run build:staging
    else
        npm run build:prod
    fi
    
    log_success "构建完成"
}

# 验证构建结果
validate_build() {
    log_info "验证构建结果..."
    
    if [ ! -d "dist" ]; then
        log_error "构建目录不存在"
        exit 1
    fi
    
    if [ ! -f "dist/index.html" ]; then
        log_error "index.html 文件不存在"
        exit 1
    fi
    
    # 检查关键文件
    REQUIRED_FILES=("assets" "favicon.ico")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -e "dist/$file" ]; then
            log_warning "缺少文件: $file"
        fi
    done
    
    # 显示构建大小
    log_info "构建大小:"
    du -sh dist/*
    
    log_success "构建验证完成"
}

# 部署到服务器
deploy_to_server() {
    log_info "部署到 $ENVIRONMENT 服务器..."
    
    if [ "$ENVIRONMENT" = "staging" ]; then
        deploy_staging
    else
        deploy_production
    fi
}

# 部署到预发布环境
deploy_staging() {
    log_info "部署到预发布环境..."
    
    # 这里添加实际的部署逻辑
    # 例如: rsync, scp, 或调用云服务API
    
    # 示例: 使用rsync部署到服务器
    # rsync -avz --delete dist/ user@staging-server:/var/www/html/
    
    # 示例: 使用AWS S3部署
    # aws s3 sync dist/ s3://staging-bucket/ --delete
    
    # 示例: 使用Netlify CLI部署
    # netlify deploy --dir=dist --site=staging-site-id
    
    log_info "预发布环境部署脚本需要根据实际服务器配置"
    log_success "预发布环境部署完成"
}

# 部署到生产环境
deploy_production() {
    log_info "部署到生产环境..."
    
    # 生产环境额外确认
    log_warning "即将部署到生产环境!"
    read -p "请确认是否继续? (yes/no): " -r
    if [ "$REPLY" != "yes" ]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 这里添加实际的生产环境部署逻辑
    # 例如: 蓝绿部署, 滚动更新等
    
    # 示例: 使用rsync部署到生产服务器
    # rsync -avz --delete dist/ user@prod-server:/var/www/html/
    
    # 示例: 使用AWS S3 + CloudFront部署
    # aws s3 sync dist/ s3://prod-bucket/ --delete
    # aws cloudfront create-invalidation --distribution-id DISTRIBUTION_ID --paths "/*"
    
    # 示例: 使用Docker部署
    # docker build -t pet-token-game:$CURRENT_COMMIT .
    # docker tag pet-token-game:$CURRENT_COMMIT pet-token-game:latest
    # docker push pet-token-game:latest
    
    log_info "生产环境部署脚本需要根据实际服务器配置"
    log_success "生产环境部署完成"
}

# 部署后验证
post_deploy_verification() {
    log_info "执行部署后验证..."
    
    if [ "$ENVIRONMENT" = "staging" ]; then
        SITE_URL="https://staging.petgame.com"
    else
        SITE_URL="https://petgame.com"
    fi
    
    log_info "验证网站可访问性: $SITE_URL"
    
    # 检查网站是否可访问
    if command -v curl &> /dev/null; then
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SITE_URL" || echo "000")
        if [ "$HTTP_STATUS" = "200" ]; then
            log_success "网站访问正常 (HTTP $HTTP_STATUS)"
        else
            log_warning "网站访问异常 (HTTP $HTTP_STATUS)"
        fi
    else
        log_warning "curl 未安装，跳过网站验证"
    fi
    
    log_info "请手动验证以下功能:"
    echo "  - 钱包连接功能"
    echo "  - 萌宠创建和显示"
    echo "  - 代币兑换功能"
    echo "  - 游戏商店功能"
    
    log_success "部署后验证完成"
}

# 发送通知
send_notification() {
    log_info "发送部署通知..."
    
    # 这里可以添加通知逻辑
    # 例如: Slack, 邮件, 钉钉等
    
    DEPLOY_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    DEPLOY_USER=$(whoami)
    
    log_info "部署信息:"
    echo "  环境: $ENVIRONMENT"
    echo "  时间: $DEPLOY_TIME"
    echo "  用户: $DEPLOY_USER"
    echo "  分支: $CURRENT_BRANCH"
    echo "  提交: $CURRENT_COMMIT"
    
    # 示例: 发送Slack通知
    # curl -X POST -H 'Content-type: application/json' \
    #   --data "{\"text\":\"🚀 Pet Token Game deployed to $ENVIRONMENT by $DEPLOY_USER\"}" \
    #   $SLACK_WEBHOOK_URL
    
    log_success "通知发送完成"
}

# 主函数
main() {
    log_info "=== 萌宠养成代币游戏部署脚本 ==="
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo
    
    # 执行部署步骤
    check_dependencies
    check_git_status
    install_dependencies
    
    # 跳过测试选项（用于快速部署）
    if [ "$2" != "--skip-tests" ]; then
        run_tests
    else
        log_warning "跳过测试步骤"
    fi
    
    build_project
    validate_build
    deploy_to_server
    post_deploy_verification
    send_notification
    
    echo
    log_success "🎉 部署完成!"
    log_info "环境: $ENVIRONMENT"
    if [ "$ENVIRONMENT" = "staging" ]; then
        log_info "访问地址: https://staging.petgame.com"
    else
        log_info "访问地址: https://petgame.com"
    fi
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"