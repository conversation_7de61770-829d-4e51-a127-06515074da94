# 生产环境配置
NODE_ENV=production

# 应用配置
VITE_APP_TITLE=萌宠养成代币游戏
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于区块链的萌宠养成游戏

# 区块链网络配置 (主网)
VITE_CHAIN_ID=1
VITE_CHAIN_NAME=Ethereum Mainnet
VITE_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
VITE_EXPLORER_URL=https://etherscan.io

# 智能合约地址 (需要替换为实际部署的合约地址)
VITE_TOKEN_CONTRACT_ADDRESS=******************************************
VITE_TOKEN_V2_CONTRACT_ADDRESS=******************************************

# API配置
VITE_API_BASE_URL=https://api.petgame.com
VITE_WS_URL=wss://ws.petgame.com

# 功能开关
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_CONSOLE_LOG=false
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true

# 第三方服务
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_GA_TRACKING_ID=G-XXXXXXXXXX

# 游戏配置
VITE_MAX_PETS_PER_USER=1
VITE_DEFAULT_GAME_COINS=1000
VITE_ENABLE_MULTIPLAYER=false

# 安全配置
VITE_ENABLE_CSP=true
VITE_ALLOWED_ORIGINS=https://petgame.com,https://www.petgame.com

# 性能配置
VITE_ENABLE_PWA=true
VITE_ENABLE_SERVICE_WORKER=true
VITE_CACHE_MAX_AGE=86400

# 部署配置
VITE_BASE_URL=/
VITE_PUBLIC_PATH=/
VITE_OUTPUT_DIR=dist