/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_CONTRACT_ADDRESS: string
  readonly VITE_CONTRACT_V2_ADDRESS: string
  readonly VITE_CHAIN_ID: string
  readonly VITE_RPC_URL: string
  readonly VITE_EXPLORER_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_TOKEN_SYMBOL: string
  readonly VITE_TOKEN_ADDRESS: string
  readonly VITE_TOKEN_V2_ADDRESS: string
  readonly VITE_WALLETCONNECT_PROJECT_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare global {
  interface Window {
    ethereum?: any
    dispatchEvent(event: Event): boolean
  }
}
