#!/bin/bash

# 增加文件描述符限制
ulimit -n 65536

# 设置环境变量
export NODE_ENV=development
export VITE_DEV_MODE=true

# 清理可能的僵尸进程
pkill -f "vite\|npm.*dev\|yarn.*dev" 2>/dev/null || true

# 等待进程清理
sleep 2

# 检查并安装依赖
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    if command -v npm >/dev/null 2>&1; then
        npm install
    elif command -v yarn >/dev/null 2>&1; then
        yarn install
    elif command -v pnpm >/dev/null 2>&1; then
        pnpm install
    else
        echo "No package manager found. Please install npm, yarn, or pnpm."
        exit 1
    fi
fi

# 启动开发服务器
echo "Starting development server..."
if command -v npm >/dev/null 2>&1; then
    npm run dev
elif command -v yarn >/dev/null 2>&1; then
    yarn dev
elif command -v pnpm >/dev/null 2>&1; then
    pnpm dev
else
    echo "No package manager found. Please install npm, yarn, or pnpm."
    exit 1
fi
