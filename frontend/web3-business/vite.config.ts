import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import path from 'node:path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      // 启用响应式语法糖
      reactivityTransform: true,
      // 生产环境移除开发工具
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('lottie-')
        }
      }
    }),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  define: {
    global: 'globalThis',
    // 移除生产环境的调试代码
    __DEV__: process.env.NODE_ENV === 'development',
    __VUE_PROD_DEVTOOLS__: false
  },
  optimizeDeps: {
    include: [
      'ethers',
      'vant',
      '@vant/icons',
      'vue3-lottie',
      'lodash-es',
      '@vueuse/core',
      '@vueuse/motion'
    ],
    // 排除不需要预构建的包
    exclude: ['@walletconnect/ethereum-provider']
  },
  build: {
    // 启用 terser 压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除 console 和 debugger
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: true,
        // 移除未使用的代码
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      },
      mangle: {
        // 保留类名（用于调试）
        keep_classnames: process.env.NODE_ENV === 'development'
      }
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // 更智能的代码分割策略
          if (id.includes('node_modules')) {
            // 核心框架
            if (id.includes('vue') || id.includes('pinia') || id.includes('vue-router')) {
              return 'vue-vendor'
            }
            // UI 组件库
            if (id.includes('vant') || id.includes('@vant')) {
              return 'ui-vendor'
            }
            // 区块链相关
            if (id.includes('ethers') || id.includes('walletconnect')) {
              return 'web3-vendor'
            }
            // 工具库
            if (id.includes('lodash') || id.includes('dayjs') || id.includes('@vueuse')) {
              return 'utils-vendor'
            }
            // 动画和媒体
            if (id.includes('lottie') || id.includes('mitt')) {
              return 'animation-vendor'
            }
            // 其他第三方库
            return 'vendor'
          }

          // 按功能模块分割应用代码
          if (id.includes('/views/')) {
            if (id.includes('Pet') || id.includes('pet')) {
              return 'pet-pages'
            }
            if (id.includes('Token') || id.includes('token')) {
              return 'token-pages'
            }
            if (id.includes('Shop') || id.includes('shop')) {
              return 'shop-pages'
            }
            if (id.includes('Tutorial') || id.includes('Introduction') || id.includes('GameRules')) {
              return 'intro-pages'
            }
            return 'misc-pages'
          }

          if (id.includes('/components/')) {
            if (id.includes('pet/')) {
              return 'pet-components'
            }
            if (id.includes('shop/')) {
              return 'shop-components'
            }
            if (id.includes('common/')) {
              return 'common-components'
            }
            return 'components'
          }
        },
        // 为每个 chunk 生成更好的文件名
        chunkFileNames: (chunkInfo) => {
          const name = chunkInfo.name
          if (name.includes('vendor')) {
            return 'vendor/[name]-[hash].js'
          }
          if (name.includes('pages')) {
            return 'pages/[name]-[hash].js'
          }
          if (name.includes('components')) {
            return 'components/[name]-[hash].js'
          }
          return 'chunks/[name]-[hash].js'
        },
        // 优化资源文件名
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1]

          if (/\.(png|jpe?g|gif|svg|webp|avif)$/i.test(assetInfo.name || '')) {
            return 'images/[name]-[hash][extname]'
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return 'fonts/[name]-[hash][extname]'
          }
          if (ext === 'css') {
            return 'styles/[name]-[hash][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      },
      // 外部化大型依赖（可选）
      external: process.env.NODE_ENV === 'production' ? [] : []
    },
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 800,
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 启用源码映射（开发时）
    sourcemap: process.env.NODE_ENV === 'development',
    // 设置构建目标
    target: ['es2020', 'chrome80', 'firefox78', 'safari14'],
    // 启用 CSS 压缩
    cssMinify: true,
    // 报告压缩详情
    reportCompressedSize: true,
    // 写入文件系统缓存
    write: true
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    // 启用 HTTP/2
    https: false
  },
  // 预览服务器配置
  preview: {
    port: 4173,
    host: '0.0.0.0'
  },
  // 实验性功能
  experimental: {
    // 启用渲染内联 CSS
    renderBuiltUrl: (filename, { hostType }) => {
      if (hostType === 'js') {
        return { js: `"${filename}"` }
      } else {
        return { relative: true }
      }
    }
  },
  // 测试配置
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ]
    },
    testTimeout: 10000,
    hookTimeout: 10000
  }
})
