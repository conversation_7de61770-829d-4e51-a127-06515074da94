# Docker Compose 配置 - 萌宠养成代币游戏
version: '3.8'

services:
  # 前端应用
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_ENV: ${BUILD_ENV:-production}
    container_name: pet-token-game-frontend
    ports:
      - "${PORT:-3000}:80"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pet-game-network

  # Nginx反向代理（可选，用于多服务部署）
  nginx-proxy:
    image: nginx:alpine
    container_name: pet-token-game-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
    restart: unless-stopped
    networks:
      - pet-game-network
    profiles:
      - proxy

  # Redis缓存（可选，用于会话存储）
  redis:
    image: redis:alpine
    container_name: pet-token-game-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - pet-game-network
    profiles:
      - cache

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus
    container_name: pet-token-game-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    restart: unless-stopped
    networks:
      - pet-game-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana
    container_name: pet-token-game-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - pet-game-network
    profiles:
      - monitoring

networks:
  pet-game-network:
    driver: bridge

volumes:
  redis-data:
  prometheus-data:
  grafana-data: