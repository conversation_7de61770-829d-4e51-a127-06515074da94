# GitHub Actions 部署工作流
name: Deploy Pet Token Game

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/web3-business/package-lock.json

      - name: Install dependencies
        working-directory: frontend/web3-business
        run: npm ci

      - name: Run linter
        working-directory: frontend/web3-business
        run: npm run lint

      - name: Run type check
        working-directory: frontend/web3-business
        run: npm run type-check

      - name: Run tests
        working-directory: frontend/web3-business
        run: npm run test:ci

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: frontend/web3-business/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # 构建应用
  build:
    needs: quality-check
    runs-on: ubuntu-latest
    strategy:
      matrix:
        environment: [staging, production]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/web3-business/package-lock.json

      - name: Install dependencies
        working-directory: frontend/web3-business
        run: npm ci

      - name: Build application
        working-directory: frontend/web3-business
        run: npm run build:${{ matrix.environment }}
        env:
          VITE_CONTRACT_ADDRESS: ${{ secrets.CONTRACT_ADDRESS }}
          VITE_CONTRACT_V2_ADDRESS: ${{ secrets.CONTRACT_V2_ADDRESS }}
          VITE_RPC_URL: ${{ secrets.RPC_URL }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ matrix.environment }}
          path: frontend/web3-business/dist
          retention-days: 7

  # 构建Docker镜像
  docker-build:
    needs: quality-check
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: frontend/web3-business
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            BUILD_ENV=production

  # 部署到预发布环境
  deploy-staging:
    needs: [build, docker-build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.petgame.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-staging
          path: dist

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # 这里添加实际的部署逻辑
          # 例如: AWS S3, Netlify, Vercel等
          
      - name: Run smoke tests
        run: |
          echo "Running smoke tests..."
          # 添加冒烟测试逻辑

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  # 部署到生产环境
  deploy-production:
    needs: [build, docker-build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://petgame.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-production
          path: dist

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # 这里添加实际的生产部署逻辑
          
      - name: Run production tests
        run: |
          echo "Running production tests..."
          # 添加生产环境测试逻辑

      - name: Create release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ github.run_number }}
          release_name: Release v${{ github.run_number }}
          body: |
            Automated release from commit ${{ github.sha }}
            
            Changes in this release:
            ${{ github.event.head_commit.message }}
          draft: false
          prerelease: false

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: 'frontend/web3-business'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # 性能测试
  performance-test:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './frontend/web3-business/.lighthouserc.js'
          uploadArtifacts: true
          temporaryPublicStorage: true